package com.jygjexp.jynx.basic.back.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 预约揽收基础信息
 *
 * <AUTHOR>
 * @date 2025-06-30 19:03:25
 */
@Data
@TenantTable
@TableName("tms_collection_basic_information")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "预约揽收基础信息")
public class TmsCollectionBasicInformationEntity extends Model<TmsCollectionBasicInformationEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	@Schema(description = "跟踪单号")
	private String trackingNumber;

	@Schema(description = "发货人")
	@NotEmpty(message = "发货人不能为空！")
	private String sendContact;

	@Schema(description = "发货人电话")
	@NotEmpty(message = "发货人电话不能为空！")
	private String sendContactPhone;

	@Schema(description = "发货城市")
	@NotEmpty(message = "发货城市不能为空！")
	private String sendCity;

	@Schema(description = "发货邮编")
	@NotEmpty(message = "发货邮编不能为空！")
	private String sendPostalCode;

	@Schema(description = "发货地址")
	@NotEmpty(message = "发货地址不能为空！")
	private String sendAddress;

	@Schema(description = "收货人")
	@NotEmpty(message = "收货人不能为空！")
	private String receiveContact;

	@Schema(description = "收货人电话")
	@NotEmpty(message = "收货人电话不能为空！")
	private String receiveContactPhone;

	@Schema(description = "收货城市")
	@NotEmpty(message = "收货城市不能为空！")
	private String receiveCity;

	@Schema(description = "收货邮编")
	@NotEmpty(message = "收货邮编不能为空！")
	private String receivePostalCode;

	@Schema(description = "收货地址")
	@NotEmpty(message = "收货地址不能为空！")
	private String receiveAddress;

    @Schema(description="上门起始时间")
	@NotNull(message = "上门起始时间不能为空")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime visitStartTime;


	@Schema(description="上门结束时间时间")
	@NotNull(message = "上门结束时间不能为空")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime visitEndTime;


	@Schema(description = "发货城市(邮编转换)")
	private String sendCityCode;


	@Schema(description = "收货城市(邮编转换)")
	private String receiveCityCode;

	/**
	* 派送时间
	*/
    @Schema(description="派送时间")
	@NotNull(message = "派送时间不能为空")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dispatchTime;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;


	@TableField(exist = false)
	@Valid
	private List<TmsCollectionCargoInformationEntity> cargoInformationList;
}