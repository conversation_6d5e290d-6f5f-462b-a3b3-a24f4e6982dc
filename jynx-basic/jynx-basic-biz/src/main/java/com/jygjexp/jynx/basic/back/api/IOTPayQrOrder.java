package com.jygjexp.jynx.basic.back.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import com.alibaba.fastjson.JSON;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

public class IOTPayQrOrder {
    private static final String MID = "11112637";
    private static final String KEY = "3CGmY08vbeBjer2iNamkrelswYQtyUzr";
    private static final String USERNAME = "Neighbexp";

    public static void main(String[] args)  throws Exception {
      pay();
      //  query();
    }

    public static void pay()throws Exception{
        String url = "https://api.iotpaycloud.com/v1/create_order";
        String orderNo = "NB" + System.currentTimeMillis();
        System.out.println("订单号：" + orderNo);
        Map<String, Object> map = new HashMap<>();
        map.put("mchId", MID);
        map.put("mchOrderNo",orderNo );
        map.put("channelId", "WX_NATIVE");
        map.put("currency", "CAD");
        map.put("amount", 1);
        map.put("clientIp", "127.0.0.1");
        map.put("device", "WEB");
        map.put("notifyUrl", "http://api.neighbourexpress.com/zt/api/payment/callback");
        map.put("subject", "积分");
        map.put("body", "积分充值");

        // 特定字段结构（如 extra）
        Map<String, Object> extra = new HashMap<>();
        extra.put("productId", "");
        map.put("extra", extra.toString());

        // 生成签名
        String sign = getSign(map, KEY);
        map.put("sign", sign);

        // URLEncode subject 和 body（注意：必须在签名之后）
        map.put("subject", URLEncoder.encode((String) map.get("subject"), "UTF-8"));
        map.put("body", URLEncoder.encode((String) map.get("body"), "UTF-8"));

        // 生成 JSON 字符串
        String params = JSON.toJSONString(map);
        String fullParams = "params=" + params;

        // 发送 HTTP 请求
        OkHttpClient client = new OkHttpClient();
        RequestBody formBody = RequestBody.create(fullParams, MediaType.get("application/x-www-form-urlencoded; charset=utf-8"));

        Request request = new Request.Builder()
                .url(url)
                .post(formBody)
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                e.printStackTrace();
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                System.out.println("响应内容：" + response.body().string());
            }
        });
    }

    public static void query() {
        // 构建参数 Map（不含 sign）
        Map<String, Object> params = new HashMap<>();
        params.put("mchId", MID); // 替换为你的 mchId
        params.put("payOrderId", "WN20250725062306502816758348"); // 替换为实际订单号

        // 生成签名
        String sign = getSign(params, KEY);
        params.put("sign", sign);

        // 将整个 params Map 序列化为 JSON 字符串
        String jsonParams = JSON.toJSONString(params);

        // 构建 formBody，参数名是 "params"，值是 JSON 字符串
        FormBody formBody = new FormBody.Builder()
                .add("params", jsonParams)
                .build();

        // 构建请求
        Request request = new Request.Builder()
                .url("https://api.iotpaycloud.com/v1/query_order")
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .post(formBody)
                .build();

        OkHttpClient client = new OkHttpClient();
        // 异步请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                System.err.println("请求失败: " + e.getMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.body() != null) {
                    System.out.println("响应内容：" + response.body().string());
                } else {
                    System.out.println("响应为空");
                }
            }
        });
    }





    // === 以下是官方提供的签名方法整合 ===

    public static String getSign(Map<String, Object> map, String key) {
        ArrayList<String> list = new ArrayList<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null && !"".equals(entry.getValue()) && !"sign".equals(entry.getKey())) {
                list.add(entry.getKey() + "=" + entry.getValue() + "&");
            }
        }
        String[] arrayToSort = list.toArray(new String[0]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for (String s : arrayToSort) sb.append(s);
        sb.append("key=").append(key);
        return md5(sb.toString(), "UTF-8").toUpperCase();
    }

    public static String md5(String input, String charset) {
        try {
            byte[] data = input.getBytes(charset);
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(data);
            StringBuilder hex = new StringBuilder();
            for (byte b : digest) {
                String hexStr = Integer.toHexString(b & 0xff);
                if (hexStr.length() == 1) hex.append('0');
                hex.append(hexStr);
            }
            return hex.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5 签名失败", e);
        }
    }
}
