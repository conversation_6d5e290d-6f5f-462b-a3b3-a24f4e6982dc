package com.jygjexp.jynx.basic.back.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.google.gson.*;
import com.jygjexp.jynx.basic.back.constants.SemiCustodialConstants;
import com.jygjexp.jynx.basic.back.entity.TmsCollectionBasicInformationEntity;
import com.jygjexp.jynx.basic.back.entity.TmsCollectionCargoInformationEntity;
import com.jygjexp.jynx.basic.back.entity.TmsCollectionReservationEntity;
import com.jygjexp.jynx.basic.back.mapper.TmsCollectionBasicInformationMapper;
import com.jygjexp.jynx.basic.back.model.bo.CollectionReservationAbo;
import com.jygjexp.jynx.basic.back.service.OrderService;
import com.jygjexp.jynx.basic.back.service.TmsCollectionBasicInformationService;
import com.jygjexp.jynx.basic.back.service.TmsCollectionCargoInformationService;
import com.jygjexp.jynx.basic.back.service.TmsCollectionReservationService;
import com.jygjexp.jynx.basic.back.tools.SelectUtil;
import com.jygjexp.jynx.common.core.util.R;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 预约揽收基础信息
 *
 * <AUTHOR>
 * @date 2025-06-30 19:03:25
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TmsCollectionBasicInformationServiceImpl extends MPJBaseServiceImpl<TmsCollectionBasicInformationMapper, TmsCollectionBasicInformationEntity> implements TmsCollectionBasicInformationService {


    private final TmsCollectionCargoInformationService cargoInformationService;

    private final OrderService orderService;


    @Override
    @Transactional
    public R saveData(CollectionReservationAbo abo,String apiKey) {
        if(!ObjUtil.equal(apiKey, SemiCustodialConstants.NORMAL)){
            return R.failed("Api key error");
        }

        TmsCollectionBasicInformationEntity tmsCollectionBasicInformationEntity = BeanUtil.copyProperties(abo, TmsCollectionBasicInformationEntity.class);
        // 校验订单号重复
        List<TmsCollectionCargoInformationEntity> list = tmsCollectionBasicInformationEntity.getCargoInformationList();
        if(CollectionUtil.isNotEmpty(list)){
            // 校验入参订单号是否重复
            // 提取所有 orderNo
            List<String> orderNos = list.stream()
                    .map(TmsCollectionCargoInformationEntity::getOrderNo)
                    .filter(ObjUtil::isNotEmpty)
                    .collect(Collectors.toList());

            // 查找重复
            List<String> duplicates = orderNos.stream()
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                    .entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            // 如果存在重复(入参重复)
            if (CollectionUtil.isNotEmpty(duplicates)) {
                return R.failed(String.format("Duplicate orderNo in request: %s", duplicates));
            }

            List<String> collect = list.stream().map(TmsCollectionCargoInformationEntity::getOrderNo).collect(Collectors.toList());
            List<TmsCollectionCargoInformationEntity> entities = cargoInformationService.list(new QueryWrapper<TmsCollectionCargoInformationEntity>().lambda()
                    .in(TmsCollectionCargoInformationEntity::getOrderNo, collect));
            if(CollectionUtil.isNotEmpty(entities)){
                // 存在重复(数据库重复)
                List<String> orderNumbers = entities.stream().map(TmsCollectionCargoInformationEntity::getOrderNo).collect(Collectors.toList());
                return R.failed(String.format("orderNo already exists: %s",orderNumbers));
            }
        }

        String json = orderService.searchLocation(abo.getSendPostalCode());

        String sendCity = handlerCity(json);
        String receiveCity = "";

        if(ObjUtil.isEmpty(sendCity)){
            return R.failed("sendPostalCode is error");
        }

        if(ObjUtil.isNotEmpty(abo.getReceivePostalCode())){

            String receiveJson = orderService.searchLocation(abo.getReceivePostalCode());

            receiveCity = handlerCity(receiveJson);

            if(ObjUtil.isEmpty(receiveCity)){
                return R.failed("receivePostalCode is error");
            }
        }

        List<TmsCollectionCargoInformationEntity> cargoInformationList = tmsCollectionBasicInformationEntity.getCargoInformationList();
        String trackingNumber = generateTrackingNumber();
        tmsCollectionBasicInformationEntity.setTrackingNumber(trackingNumber);
        tmsCollectionBasicInformationEntity.setSendCityCode(sendCity);
        tmsCollectionBasicInformationEntity.setReceiveCityCode(receiveCity);
        save(tmsCollectionBasicInformationEntity);
        cargoInformationService.saveOrder(cargoInformationList,tmsCollectionBasicInformationEntity);
        return R.ok(trackingNumber,"Added successfully");
    }

    public String handlerCity(String json) {
        // 解析 JSON 字符串
        JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
        JsonArray results = jsonObject.getAsJsonArray("results");

        // 检查是否有结果
        if (!results.isEmpty()) {
            JsonObject result = results.get(0).getAsJsonObject();
            JsonArray addressComponents = result.getAsJsonArray("address_components");

            String country = null;
            String provinceAbbr = null;
            String city = null;

            // 遍历 address_components 获取国家、省/州的简称和城市信息
            for (JsonElement component : addressComponents) {
                JsonObject componentObj = component.getAsJsonObject();
                JsonArray types = componentObj.getAsJsonArray("types");
                String longName = componentObj.get("long_name").getAsString();
                String shortName = componentObj.get("short_name").getAsString();

                // 查找国家
                if (types.contains(new JsonPrimitive("country"))) {
                    country = longName;
                }
                // 查找省/州的简称
                else if (types.contains(new JsonPrimitive("administrative_area_level_1"))) {
                    provinceAbbr = shortName;  // 省/州的简称
                }
                // 查找城市
                else if (types.contains(new JsonPrimitive("locality"))) {
                    city = longName;  // 城市的全名
                }
            }

            // 返回格式化的地址信息
            if (country != null && provinceAbbr != null && city != null) {
                return String.format("%s/%s/%s", country, provinceAbbr, city);
            }
        }
        return null;  // 如果没有找到所有必要的信息，返回 null
    }



    public static String generateTrackingNumber() {
        // 固定前缀
        String prefix = "YY";

        // 当前时间戳部分 yyyyMMddHHmmss
        String timePart = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());

        // 随机 6 位数字
        int randomPart = new Random().nextInt(900000) + 100000;

        return prefix + timePart + randomPart;
    }




    @Override
    public R deleteData(List<String> ids,String apiKey) {
        if(!ObjUtil.equal(apiKey, SemiCustodialConstants.NORMAL)){
            return R.failed("Api key error");
        }
        List<TmsCollectionBasicInformationEntity> list1 = list(Wrappers.<TmsCollectionBasicInformationEntity>query().lambda().in(TmsCollectionBasicInformationEntity::getTrackingNumber, ids));
        if(CollectionUtil.isEmpty(list1)){
            return R.failed("No data found");
        }
        List<Long> longList1 = list1.stream().map(TmsCollectionBasicInformationEntity::getId).collect(Collectors.toList());
        List<TmsCollectionCargoInformationEntity> list = cargoInformationService.list(new QueryWrapper<TmsCollectionCargoInformationEntity>().lambda().in(TmsCollectionCargoInformationEntity::getInformationId, longList1));
        if(CollectionUtil.isNotEmpty(list)){
            removeByIds(list1);
            cargoInformationService.removeByIds(list);
            return R.ok(null,"Deleted successfully");
        }else {
            return R.failed("No data found");
        }

    }
}