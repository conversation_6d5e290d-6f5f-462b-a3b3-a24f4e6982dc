/*
 *    Copyright (c) 2018-2025, jynx All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: jynx
 */

package com.jygjexp.jynx.app.service.impl;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jygjexp.jynx.app.handler.LocalizedR;
import com.jygjexp.jynx.app.service.AppMobileService;
import com.jygjexp.jynx.common.core.constant.CacheConstants;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.constant.enums.LoginTypeEnum;
import com.jygjexp.jynx.common.core.exception.ErrorCodes;
import com.jygjexp.jynx.common.core.util.MsgUtils;
import com.jygjexp.jynx.common.core.util.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.apache.bcel.classfile.Code;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2018/11/14
 * <p>
 * 手机登录相关业务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class AppMobileServiceImpl implements AppMobileService {

	private final StringRedisTemplate redisTemplate;

	/**
	 * 发送手机验证码 TODO: 调用短信网关发送验证码,测试返回前端
	 * @param mobile mobile
	 * @return code
	 */
	@Override
	public R sendSmsCode(String mobile,Integer isTemplate) {
		//List<AppUser> appUsers = appUserMapper.selectList(new LambdaQueryWrapper<AppUser>().eq(AppUser::getPhone, mobile));
//		if (CollUtil.isEmpty(appUsers)) {
//			return R.failed("手机号未注册，请先注册再进行登录！！！");
//		}
		String codeObj = redisTemplate.opsForValue()
				.get(CacheConstants.DEFAULT_CODE_KEY + LoginTypeEnum.APPSMS.getType() + StringPool.AT + mobile);

		if (StrUtil.isNotBlank(codeObj)) {
			log.info("手机号验证码未过期:{}，{}", mobile, codeObj);
			return LocalizedR.failed("app.sms.often",Optional.ofNullable(null));
			//return R.ok(Boolean.FALSE, MsgUtils.getMessage(ErrorCodes.SYS_APP_SMS_OFTEN));
		}

		String code = RandomUtil.randomNumbers(Integer.parseInt(SecurityConstants.CODE_SIZE));
		log.debug("手机号生成验证码成功:{},{}", mobile, code);

		// 检查是否以 "+1" 开头
		if (mobile.startsWith("+1")) {
			String mobileTwo=null;
			// 去掉前两位
			mobileTwo = mobile.substring(2);
			redisTemplate.opsForValue().set(
					CacheConstants.DEFAULT_CODE_KEY + LoginTypeEnum.APPSMS.getType() + StringPool.AT + mobileTwo, code,
					SecurityConstants.CODE_TIME, TimeUnit.SECONDS);
		}else {
			redisTemplate.opsForValue().set(
					CacheConstants.DEFAULT_CODE_KEY + LoginTypeEnum.APPSMS.getType() + StringPool.AT + mobile, code,
					SecurityConstants.CODE_TIME, TimeUnit.SECONDS);
		}
		// 判断号码是否符合国际号码的一般格式（即以 + 开头，后面跟数字
		boolean isDomestic = mobile.startsWith("+") && mobile.substring(1).chars().allMatch(Character::isDigit);


		if (!isDomestic) {
			String template = getTemplateCode(isTemplate, isDomestic, code);
			// 国内短信的配置
			DefaultProfile profile = DefaultProfile.getProfile(
					"cn-beijing", "LTAI5tSbvRzzqJqvySa2nQC1", "******************************");
			IAcsClient client = new DefaultAcsClient(profile);
			CommonRequest request = new CommonRequest();
			request.setSysMethod(MethodType.POST);
			request.setSysDomain("dysmsapi.aliyuncs.com");
			request.setSysVersion("2017-05-25");
			request.setSysAction("SendSms");
			request.putQueryParameter("PhoneNumbers", mobile);
			request.putQueryParameter("SignName", "NBExpress");
			request.putQueryParameter("TemplateCode", template);
			String result = "{\"code\":\"" + code + "\"}";
			request.putQueryParameter("TemplateParam", result);
//			request.putQueryParameter("From", "18338581654");
			try {
				CommonResponse response = client.getCommonResponse(request);
				log.info("国内短信："+response.getData());
				String data = response.getData();
				JSONObject jsonObject = null;
				String code2 = "";
				try {
					//{"Message":"OK","RequestId":"8FDF76F0-B186-5F48-9821-B53A47022A3D","Code":"OK","BizId":"571917032528838618^0"}
					jsonObject = JSONObject.parseObject(data);
					code2 = jsonObject.getString("Code");
				} catch (JSONException e) {
					throw new RuntimeException(e);
				}
				if (!"OK".equals(code2)) {
					log.info("短信发送失败" + mobile + code2);
					return LocalizedR.failed("appuser.sms.error",mobile + code2);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}

		} else {
			String template = getTemplateCode(isTemplate, isDomestic, code);
			// 国际短信的配置
			mobile=mobile.substring(1);
																//默认cn-hangzhou
			DefaultProfile profile = DefaultProfile.getProfile("cn-beijing", "LTAI5tSbvRzzqJqvySa2nQC1", "******************************");
			IAcsClient client = new DefaultAcsClient(profile);
			CommonRequest request = new CommonRequest();
			request.setSysMethod(MethodType.POST);
			request.setSysDomain("dysmsapi.aliyuncs.com");
			request.setSysVersion("2017-05-25");
			request.setSysAction("SendMessageToGlobe");
			request.putQueryParameter("To", mobile);
			//code="【NBExpress】The verification code is:" + code + ",you are logging in, unless you operate, do not disclose.";
			request.putQueryParameter("Message", template);
			//request.putQueryParameter("From", "18338581654");
			request.putQueryParameter("From", "18773124359");
			request.putQueryParameter("Type", "OTP");
			CommonResponse response = null;
			try {
				//发送短信
				response = client.getCommonResponse(request);
				log.info("国际短信："+response.getData());
			} catch (ClientException e) {
				throw new RuntimeException(e);
			}
			String data = response.getData();
			JSONObject jsonObject = null;
			String code2 = "";
			try {
				jsonObject = JSONObject.parseObject(data);
				code2 = jsonObject.getString("Code");
			} catch (JSONException e) {
				throw new RuntimeException(e);
			}
			if (!"OK".equals(code2)) {
				log.info("短信发送失败" + mobile + code2);
				return LocalizedR.failed("appuser.sms.error",mobile + code2);
			}
		}
		return R.ok(Boolean.TRUE);
//		return R.ok(code);
	}

	@Override
	public R<Boolean> sendSms(String mobile,String code,Boolean isAbroad) {
		String template = null;
		if(!isAbroad){
			template = getTemplateCode(3, false, code);
			sendPhoneCode(mobile,code,template,false);
		}else{
			template = getTemplateCode(3, true, code);
			sendPhoneCode(mobile,code,template,true);
		}
		return R.ok(true);
	}


	private void sendPhoneCode(String mobile,String code,String template,boolean isDomestic){
		if (!isDomestic) {
			// 国内短信的配置
			DefaultProfile profile = DefaultProfile.getProfile(
					"cn-beijing", "LTAI5tSbvRzzqJqvySa2nQC1", "******************************");
			IAcsClient client = new DefaultAcsClient(profile);
			CommonRequest request = new CommonRequest();
			request.setSysMethod(MethodType.POST);
			request.setSysDomain("dysmsapi.aliyuncs.com");
			request.setSysVersion("2017-05-25");
			request.setSysAction("SendSms");
			request.putQueryParameter("PhoneNumbers", mobile);
			request.putQueryParameter("SignName", "NBExpress");
			request.putQueryParameter("TemplateCode", template);
			String result = "{\"code\":\"" + code + "\"}";
			request.putQueryParameter("TemplateParam", result);
//			request.putQueryParameter("From", "18338581654");
			try {
				CommonResponse response = client.getCommonResponse(request);
				log.info("国内短信："+response.getData());
				String data = response.getData();
				JSONObject jsonObject = null;
				String code2 = "";
				try {
					//{"Message":"OK","RequestId":"8FDF76F0-B186-5F48-9821-B53A47022A3D","Code":"OK","BizId":"571917032528838618^0"}
					jsonObject = JSONObject.parseObject(data);
					code2 = jsonObject.getString("Code");
				} catch (JSONException e) {
					throw new RuntimeException(e);
				}
				if (!"OK".equals(code2)) {
					log.info("短信发送失败" + mobile + code2);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}

		} else {
			// 国际短信的配置
			mobile=mobile.substring(1);
			//默认cn-hangzhou
			DefaultProfile profile = DefaultProfile.getProfile("cn-beijing", "LTAI5tSbvRzzqJqvySa2nQC1", "******************************");
			IAcsClient client = new DefaultAcsClient(profile);
			CommonRequest request = new CommonRequest();
			request.setSysMethod(MethodType.POST);
			request.setSysDomain("dysmsapi.aliyuncs.com");
			request.setSysVersion("2017-05-25");
			request.setSysAction("SendMessageToGlobe");
			request.putQueryParameter("To", mobile);
			//code="【NBExpress】The verification code is:" + code + ",you are logging in, unless you operate, do not disclose.";
			request.putQueryParameter("Message", template);
			//request.putQueryParameter("From", "18338581654");
			request.putQueryParameter("From", "18773124359");
			request.putQueryParameter("Type", "OTP");
			CommonResponse response = null;
			try {
				//发送短信
				response = client.getCommonResponse(request);
				log.info("国际短信："+response.getData());
			} catch (ClientException e) {
				throw new RuntimeException(e);
			}
			String data = response.getData();
			JSONObject jsonObject = null;
			String code2 = "";
			try {
				jsonObject = JSONObject.parseObject(data);
				code2 = jsonObject.getString("Code");
			} catch (JSONException e) {
				throw new RuntimeException(e);
			}
			if (!"OK".equals(code2)) {
				log.info("短信发送失败" + mobile + code2);
			}
		}
	}




	public String getTemplateCode(Integer isTemplate,boolean isDomestic,String code) {
		if (!isDomestic) {
			// 返回国内模板代码
			switch (isTemplate) {
				case 1: return "SMS_208290326";
				case 2: return "SMS_208290324";
				case 3: return "SMS_208290323";
				case 4: return "SMS_208290323";
				default: return "SMS_208290323";
			}
		} else {
			// 国外模板公共前缀
			String messagePrefix = "【NBExpress】The Verification code is: " + code + ",";
			switch (isTemplate) {
				case 1: return messagePrefix + " you are logging in, unless you operate, do not disclose.";
				case 2: return messagePrefix + " you are registering as a new user, thank you for your support!";
				//您正在修改手机号
				case 3: return messagePrefix + " you are trying to change the mobile number, please keep the account information";
				case 4: return messagePrefix + " you are trying to change the login password, please keep the account information.";
				default: return messagePrefix + " you are trying to change the login password, please keep the account information.";
			}
		}
	}

}
