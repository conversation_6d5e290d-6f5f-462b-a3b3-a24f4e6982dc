package com.jygjexp.jynx.tms.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.jygjexp.jynx.tms.annotation.ConvertType;
import com.jygjexp.jynx.tms.utils.IntegerDictsConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 中大件订单导出
 */
@Data
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 10)
@Schema(description = "中大件订单导出")
public class TmsCustomerZdjOrderExcelVo {


    @ColumnWidth(18)
    @ExcelProperty("(trackNo)跟踪单号")
    @Schema(description="委托单号")
    private String entrustedOrderNumber;

    @ColumnWidth(18)
    @ExcelProperty("(CustomerOrderNumber)客户单号")
    @Schema(description="客户单号")
    private String customerOrderNumber;



    @ColumnWidth(10)
    @ExcelProperty("(EntrustedCustomer)委托客户")
    @Schema(description="委托客户")
    private String entrustedCustomer;


    @ConvertType("receiveType")
    @ColumnWidth(10)
    @ExcelProperty(value = "(receiveType)收货方式", converter = IntegerDictsConverter.class)
    private Integer receiveType;

    @ColumnWidth(20)
    @ExcelProperty("(Origin)始发地")
    @Schema(description="始发地")
    private String origin;


    @ColumnWidth(20)
    @ExcelProperty("(Destination)目的地")
    @Schema(description="目的地")
    private String destination;


    @ConvertType("addressType")
    @ColumnWidth(10)
    @ExcelProperty(value = "(AddressType)地址类型", converter = IntegerDictsConverter.class)
    @Schema(description="地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板，4=商业地址-不需要尾板")
    private Integer addressType;

//    @ColumnWidth(10)
//    @ExcelProperty("(CustomerId)客户ID")
//    @Schema(description = "客户ID")
//    private Long customerId;

    @ColumnWidth(10)
    @ExcelProperty(value = "(OrderStatus)订单状态", converter = IntegerDictsConverter.class)
    @ConvertType("orderStatusZdj")
    private Integer orderStatus;

//    @ConvertType("auditStatus")
//    @ColumnWidth(6)
//    @ExcelProperty(value = "(AuditStatus)审核状态", converter = IntegerDictsConverter.class)
//    @Schema(description = "审核状态")
//    private Integer auditStatus;

    @ColumnWidth(8)
    @ExcelProperty("(ShipperName)发货人姓名")
    @Schema(description="发货人姓名")
    private String shipperName;

    @ColumnWidth(15)
    @ExcelProperty("(ShipperPhone)发货人电话")
    @Schema(description="发货人电话")
    private String shipperPhone;



    @ColumnWidth(10)
    @ExcelProperty("(ShipperPostalCode)发货邮编")
    @Schema(description="发货邮编")
    private String shipperPostalCode;

    @ColumnWidth(22)
    @ExcelProperty("(ShipperAddress)发货详细地址")
    @Schema(description="发货详细地址")
    private String shipperAddress;

    @ColumnWidth(18)
    @ExcelProperty("(EstimatedShippingTimeStart)预计发货时间开始")
    @Schema(description="预计发货时间开始")
    private LocalDateTime estimatedShippingTimeStart;

    @ColumnWidth(18)
    @ExcelProperty("(EstimatedShippingTimeEnd)预计发货时间结束")
    @Schema(description="预计发货时间结束")
    private LocalDateTime estimatedShippingTimeEnd;

//    @ConvertType("isTailgatePickup")
//    @ColumnWidth(10)
//    @ExcelProperty(value = "(IsTailgatePickup)尾板提货：0 No，1 Yes", converter = IntegerDictsConverter.class)
//    @Schema(description="是否尾板提货：0 否，1 是")
//    private Integer isTailgatePickup;

    @ColumnWidth(10)
    @ExcelProperty("(ReceiverName)到货人姓名")
    @Schema(description="到货人姓名")
    private String receiverName;

    @ColumnWidth(15)
    @ExcelProperty("(ReceiverPhone)到货人电话")
    @Schema(description="到货人电话")
    private String receiverPhone;



    @ColumnWidth(10)
    @ExcelProperty("(DestPostalCode)到货邮编")
    @Schema(description="到货邮编")
    private String destPostalCode;

    @ColumnWidth(22)
    @ExcelProperty("(DestAddress)到货详细地址")
    @Schema(description="到货详细地址")
    private String destAddress;

    @ColumnWidth(18)
    @ExcelProperty("(EstimatedArrivalTimeStart)预计到货时间开始")
    @Schema(description="预计到货时间开始")
    private LocalDateTime estimatedArrivalTimeStart;

    @ColumnWidth(18)
    @ExcelProperty("(EstimatedArrivalTimeEnd)预计到货时间结束")
    @Schema(description="预计到货时间结束")
    private LocalDateTime estimatedArrivalTimeEnd;


//    @ColumnWidth(10)
//    @ExcelProperty("(CarrierId)所属承运商ID")
//    @Schema(description="所属承运商")
//    private Long carrierId;

    @ConvertType("orderType")
    @ColumnWidth(10)
    @ExcelProperty(value = "(OrderType)订单类型：1 tray，2 package", converter = IntegerDictsConverter.class)
    @Schema(description="订单类型：1=托盘，2=包裹")
    private Integer orderType;

    @ConvertType("transportType")
    @ColumnWidth(10)
    @ExcelProperty(value = "(TransportType)运输类型", converter = IntegerDictsConverter.class)
    @Schema(description="运输类型：1=整车运输，2=零担运输")
    private Integer transportType;

    @ConvertType("cargoType")
    @ColumnWidth(10)
    @ExcelProperty(value = "(CargoType)货物类型", converter = IntegerDictsConverter.class)
    @Schema(description="货物类型：1=普通货物，2=危险货物")
    private Integer cargoType;


    @ConvertType("businessModel")
    @ColumnWidth(10)
    @ExcelProperty(value = "(BusinessModel)业务模式", converter = IntegerDictsConverter.class)
    @Schema(description = "业务模式：1 揽收，2 中大件，3 卡派")
    private Integer businessModel;


    @ColumnWidth(10)
    @Schema(description="货品总数量")
    @ExcelProperty("(cargoQuantity) 货物数量")
    private Integer cargoQuantity;

    @ColumnWidth(8)
    @ExcelProperty("(TotalWeight)总重量(kg)")
    @Schema(description="总重量(kg)")
    private BigDecimal totalWeight;

    @ColumnWidth(8)
    @ExcelProperty("(TotalVolume)总体积(m³)")
    @Schema(description="总体积(m³)")
    private BigDecimal totalVolume;

//    @ColumnWidth(8)
//    @ExcelProperty("(TotalFreight)运费合计")
//    @Schema(description="运费合计：订单的总运费，包含基础运费、燃油费、税费、附加费等费用信息，系统自动计算")
//    private BigDecimal totalFreight;

    @ColumnWidth(10)
    @ExcelProperty("(Operator)操作人")
    @Schema(description="操作人")
    private String operator;

    @ColumnWidth(18)
    @ExcelProperty("(Order time)下单时间")
    @Schema(description = "下单时间")
    private LocalDateTime createTime;
}
