package com.jygjexp.jynx.tms.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @Author: chenchang
 * @Description: 车辆信息导出实体
 * @Date: 2025/2/25 15:29
 */
@Data
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 10) // 设置表头字体样式
@Schema(description = "车辆信息导出实体")
public class TmsVehicleInfoExcelVo {

    @ColumnWidth(10)
    @ExcelProperty("(VehicleSource)车辆来源")
    @Schema(description="车辆来源")
    private String vehicleSource;

    @ColumnWidth(10)
    @ExcelProperty("(driverName)车主")
    @Schema(description="车主")
    private String driverName;

    @ColumnWidth(10)
    @ExcelProperty("(CarrierName)承运商名称")
    @Schema(description="承运商名称")
    private String carrierName;

    @ColumnWidth(15)
    @ExcelProperty("(ContactPhone)联系电话")
    @Schema(description="联系电话")
    private String contactPhone;

    @ColumnWidth(15)
    @ExcelProperty("(LicensePlate)车牌号")
    @Schema(description="车牌号")
    private String licensePlate;

    @ColumnWidth(10)
    @ExcelProperty("(VehicleType)车辆类型")
    @Schema(description="车辆类型")
    private String vehicleType;

    @ColumnWidth(8)
    @ExcelProperty("(VehicleColor)车辆颜色")
    @Schema(description="车辆颜色")
    private String vehicleColor;

    @Schema(description="车辆图片(存URL路径)")
    private String vehicleImageUrl;

    @ColumnWidth(10)
    @ExcelProperty("(Load capacity (ton))载重(吨)")
    @Schema(description="载重(吨)")
    private BigDecimal loadCapacity;

    @ColumnWidth(8)
    @ExcelProperty("(Volume (L))容积(升)")
    @Schema(description="容积(升)")
    private BigDecimal volume;

    @ColumnWidth(10)
    @ExcelProperty("(PurchaseDate)车辆购买日期")
    @Schema(description="车辆购买日期")
    private LocalDate purchaseDate;

    @ColumnWidth(10)
    @ExcelProperty("(RegistrationDate)车辆登记日期")
    @Schema(description="车辆登记日期")
    private LocalDate registrationDate;

    @ColumnWidth(10)
    @ExcelProperty("(InsuranceStartDate(startTime))车辆保险有限期(开始时间)")
    @Schema(description="车辆保险有限期(开始时间)")
    private LocalDate insuranceStartDate;

    @ColumnWidth(10)
    @ExcelProperty("(InsuranceEndDate(endTime))车辆保险有限期(结束时间)")
    @Schema(description="车辆保险有限期(结束时间)")
    private LocalDate insuranceEndDate;

    @ColumnWidth(15)
    @ExcelProperty("(InsuranceDocumentUrl)车辆保险单文件")
    @Schema(description="车辆保险单文件(支持图片和文件(word、PDF最大支持60MB))")
    private String insuranceDocumentUrl;

    @ColumnWidth(10)
    @ExcelProperty("(Length(m))长(m)")
    @Schema(description="长(m)")
    private BigDecimal length;

    @ColumnWidth(10)
    @ExcelProperty("(Width(m))宽(m)")
    @Schema(description="宽(m)")
    private BigDecimal width;

    @ColumnWidth(10)
    @ExcelProperty("(Height(m))高(m)")
    @Schema(description="高(m)")
    private BigDecimal height;

    @ColumnWidth(10)
    @ExcelProperty("(CargoType)可配货物类型")
    @Schema(description="可配货物类型")
    private String cargoType;

    @ColumnWidth(8)
    @ExcelProperty("(IsValid：0 Disable，1 Enable)启用状态：0 禁用，1 启用")
    @Schema(description="启用状态：0 禁用，1 启用")
    private Integer isValid;

}
