package com.jygjexp.jynx.tms.dto.app;

import com.jygjexp.jynx.tms.entity.TmsMessageEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * @Author: xiongpengfei
 * @Description: 消息通知返回参数
 * @Date: 2025/4/17 16:56
 */
@Data
@FieldNameConstants
@Schema(description = "消息通知返回参数")
public class TmsMessageDto extends TmsMessageEntity {

    /**
     * 任务类型
     */
    @Schema(description="任务类型")
    private Integer isTask ;

}
