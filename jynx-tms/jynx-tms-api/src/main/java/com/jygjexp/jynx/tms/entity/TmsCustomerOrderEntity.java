package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 卡派客户订单
 *
 * <AUTHOR>
 * @date 2025-03-13 15:09:24
 */
@Data
@TenantTable
@TableName("tms_customer_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派客户订单")
public class TmsCustomerOrderEntity extends BaseLogicEntity<TmsCustomerOrderEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 客户单号/客户参考单号(Api下推)
	*/
    @Schema(description="客户单号/客户参考单号")
    private String customerOrderNumber;


	/**
	* 委托单号
	*/
    @Schema(description="委托单号")
    private String entrustedOrderNumber;

	/**
	 * 标记单号
	 */
	@Schema(description="标记单号")
	private String jyOrderNo;


	/**
	 * 客户ID
	 */
	@Schema(description = "客户ID")
	private Long customerId;

	/**
	* 订单状态(23001=待指派、23002=已取消、23003=已指派、23004=待审批、23005=待分配、23006=已驳回、23007=已指派、23008=待运输、23009=待收货、23010=运输中、23011=已完成)
	*/
    @Schema(description="订单状态(23001=待指派、23002=已取消、23003=已指派、23004=待审批、23005=待分配、23006=已驳回、23007=已指派、23008=待运输、23009=待收货、23010=运输中、23011=已完成)")
    private Integer orderStatus;

	/**
	 * 审核状态
	 */
	@Schema(description = "审核状态")
	private Integer auditStatus;

	/**
	* 发货人姓名
	*/
    @Schema(description="发货人姓名")
    private String shipperName;

	/**
	* 发货人电话
	*/
    @Schema(description="发货人电话")
    private String shipperPhone;

	/**
	* 始发地
	*/
    @Schema(description="始发地")
    private String origin;

	/**
	* 发货邮编
	*/
    @Schema(description="发货邮编")
    private String shipperPostalCode;

	/**
	* 发货详细地址
	*/
    @Schema(description="发货详细地址")
    private String shipperAddress;

	/**
	* 预计发货时间开始
	*/
    @Schema(description="预计发货时间开始")
    private LocalDateTime estimatedShippingTimeStart;

	/**
	* 预计发货时间结束
	*/
    @Schema(description="预计发货时间结束")
    private LocalDateTime estimatedShippingTimeEnd;

	/**
	* 是否尾板提货：0 否，1 是
	*/
    @Schema(description="是否尾板提货：0 否，1 是")
    private Integer isTailgatePickup;

	/**
	* 到货人姓名
	*/
    @Schema(description="到货人姓名")
    private String receiverName;

	/**
	* 到货人电话
	*/
    @Schema(description="到货人电话")
    private String receiverPhone;

	/**
	* 目的地
	*/
    @Schema(description="目的地")
    private String destination;

	/**
	* 到货邮编
	*/
    @Schema(description="到货邮编")
    private String destPostalCode;

	/**
	* 到货详细地址
	*/
    @Schema(description="到货详细地址")
    private String destAddress;

	/**
	* 预计到货时间开始
	*/
    @Schema(description="预计到货时间开始")
    private LocalDateTime estimatedArrivalTimeStart;

	/**
	* 预计到货时间结束
	*/
    @Schema(description="预计到货时间结束")
    private LocalDateTime estimatedArrivalTimeEnd;

	/**
	* 是否尾板卸货：0 否，1 是
	*/
    @Schema(description="是否尾板卸货：0 否，1 是")
    private Integer isTailgateUnloaded;

	/**
	* 所属承运商
	*/
    @Schema(description="所属承运商")
    private Long carrierId;

	/**
	* 订单类型：1=托盘，2=包裹
	*/
    @Schema(description="订单类型：1=托盘，2=包裹")
    private Integer orderType;

	/**
	* 运输类型：1=整车运输，2=零担运输
	*/
    @Schema(description="运输类型：1=整车运输，2=零担运输")
    private Integer transportType;

	/**
	* 货物类型：1=普通货物，2=危险货物
	*/
    @Schema(description="货物类型：1=普通货物，2=危险货物")
    private Integer cargoType;

	/**
	* 地址类型
	*/
    @Schema(description="地址类型：1=住宅，2=商业地址")
    private Integer addressType;

	/**
	 * 业务模式：1 揽收，2 中大件，3 卡派
	 */
	@Schema(description = "业务模式：1 揽收，2 中大件，3 卡派")
	private Integer businessModel;

	/**
	* 货品总数量
	*/
    @Schema(description="货品总数量")
    private Integer cargoQuantity;

	/**
	* 总重量(kg)
	*/
    @Schema(description="总重量(kg)")
    private BigDecimal totalWeight;

	/**
	* 总体积(m³)
	*/
    @Schema(description="总体积(m³)")
    private BigDecimal totalVolume;

	/**
	* 运费合计：订单的总运费，包含基础运费、燃油费、税费、附加费等费用信息，系统自动计算
	*/
    @Schema(description="运费合计：订单的总运费，包含基础运费、燃油费、税费、附加费等费用信息，系统自动计算")
    private BigDecimal totalFreight;

	/**
	 * 订单派送完成时间
	 */
	@Schema(description="订单派送完成时间")
	private LocalDateTime finishTime;

	/**
	*收货方式
	*/
    @Schema(description="收货方式：1 上门揽收，2 送货到仓")
    private Integer receiveType;


	/**
	*是否子单号
	*/
    @Schema(description="是否子单号")
    private Boolean subFlag;


	/**
	* 操作人
	*/
    @Schema(description="操作人")
    private String operator;

	/**
	* 订单是否删除
	*/
    @Schema(description="订单是否删除")
    private Boolean isDelete;

	/**
	 * 货物信息
	 */
	@TableField(exist = false)
	@Schema(description = "货物信息")
	private List<TmsCargoInfoEntity> cargoInfoEntityList;

	/**
	 * 附加服务
	 */
	@TableField(exist = false)
	@Schema(description = "附加服务")
	private List<TmsAdditionalServicesEntity> additionalServicesEntityList;

	/**
	 * 承运商审核记录
	 */
	@TableField(exist = false)
	@Schema(description = "承运商审核记录")
	private List<TmsCarrierAuditRecordsEntity> carrierAuditRecordsEntityList;

	/**
	 * 入库批次号
	 */
	@Schema(description="入库批次号-废弃")
	private String storageOrderNo;

	/**
	 * 出库批次号
	 */
	@Schema(description = "出库批次号-废弃")
	private String outboundOrderNumber;

	/**
	 * 提货证明（最多6张图片，逗号分割）
	 */
	@Schema(description="提货证明（最多6张图片，逗号分割）")
	private String pickupProof;

	/**
	 * 送货证明（最多6张图片，逗号分割）
	 */
	@Schema(description="送货证明（最多6张图片，逗号分割）")
	private String deliveryProof;

	/**
	 * 揽收提货证明（最多6张图片，逗号分割）
	 */
	@Schema(description="揽收提货证明（最多6张图片，逗号分割）")
	private String collectionPickupProof;

	/**
	 * 揽收送货证明（最多6张图片，逗号分割）
	 */
	@Schema(description="揽收送货证明（最多6张图片，逗号分割）")
	private String collectionDeliveryProof;

	/**
	 * 是否扫描：0：未扫描/1:已扫描
	 */
	@Schema(description="是否扫描：0：未扫描/1:已扫描")
	private Boolean isScan;
	/**
	 * 干线单号
	 */
	@Schema(description="干线单号")
	private String lineHaulNo;

	/**
	 * 派送任务单
	 */
	@Schema(description="派送任务单")
	private String dTaskOrder;

	/**
	 * 揽收任单号
	 */
	@Schema(description="揽收任单号")
	private String pTaskOrder;


	/**
	 * 国际单位
	 */
	@Schema(description="国际单位")
	private Integer units;



	/**
	 * 发货地经纬度
	 */
	@Schema(description="发货地经纬度")
	private String shipperLatLng;

	/**
	 * 收货地经纬度
	 */
	@Schema(description="收货地经纬度")
	private String receiverLatLng;

	/**
	 * 派送仓库id(只用于中大件派送)
	 */
	@Schema(description="派送仓库id")
	private Long deliveryWarehouseId;


	/**
	 * 揽收仓库id(只用于中大件揽收)
	 */
	@Schema(description="揽收仓库id")
	private Long collectWarehouseId;

	/**
	 * 派送仓库等级(只用于中大件派送)
	 */
	@Schema(description="派送仓库等级")
	private Integer warehouseGrade;

	/**
	 * 订单需要干线运输标志
	 */
	@Schema(description="订单需要干线运输标志")
	private Boolean needLineHaul;

	/**
	 * 当前仓库id
	 */
	@Schema(description="当前仓库id")
	private Long currentWarehouseId;
	/**
	 * 临时干线目的地仓库id（用于暂存）
	 */
	@Schema(description="临时干线目的地仓库id（用于暂存）")
	private Long lineHaulDestWarehouseId;
	/**
	 * 判断此时该单是否能进行路径规划
	 */
	@Schema(description="判断此时该单是否能进行路径规划")
	private Boolean isCanPathPlan;

	/**
	 * 删除标记
	 */
	private String delFlag;

	/**
	 * 是否建立批次 0：否，1：是
	 */
	@Schema(description="是否建立批次 0：否，1：是")
	private Integer isBatch;

	/**
	 * 批次号
	 */
	@Schema(description="批次号")
	private String batchNo;

	/**
	 * 路线编号
	 */
	@Schema(description="路线编号")
	private String routeNumber;
	/**
	 * 派送是否路径规划标记
	 */
	@Schema(description="派送是否路径规划标记")
	private Boolean isDeliveryRoutePlan;
	/**
	 * 订单派送顺序号
	 */
	@Schema(description="订单派送顺序号")
	private Integer deliveryOrderSortNo;
	/**
	 * 优先派送等级
	 */
	@Schema(description="优先派送等级")
	private Integer priorityDeliveryGrade;
	/**
	 * 是否有转单标记
	 */
	@Schema(description="是否有转单标记")
	private Boolean isTransfer;
	/**
	 * 跨批次等级（当天批次：0，批次跨一天为：1，以此类推）
	 */
	@Schema(description="跨批次等级（当天批次：0，批次跨一天为：1，以此类推）")
	private Integer skipBatchGrade;
	/**
	 * 订单派送司机id
	 */
	@Schema(description="订单派送司机id")
	private Long deliveryDriverId;
	/**
	 * 所属规划名称
	 */
	@Schema(description="所属规划名称")
	private String planName;

	/**
	 * 老批次号-转单后存储老批次号-用于追溯分拣批次
	 */
	@Schema(description="批次号-转单后存储老批次号-用于追溯分拣批次")
	private String oldBatchNo;


	/**
	 * 下单方式
	 */
	@Schema(description="下单方式")
	private Integer  orderWay;

	/**
	 * 报告单号
	 */
	@Schema(description="报告单号")
	private String reportOrderNo;

	/**
	 * 派送尝试次数
	 */
	@Schema(description="派送尝试次数")
	private Integer deliveryTry;

	/**
	 * 派送失败原因
	 */
	@Schema(description="派送失败原因")
	private Integer failedReason;

	/**
	 * 派送失败证明
	 */
	@Schema(description="派送失败证明")
	private String deliveryFailedProof;


	/**
	 * 地址异常数量统计
	 */
	@Schema(description="地址异常数量统计")
	private String addressErrorCount;

	/**
	 * 推送返回订单
	 */
	@Schema(description="推送返回订单")
	private String pushOrder;

	/**
	 * 推送返回面单地址
	 */
	@Schema(description="推送返回面单地址")
	private String pushLabel;
	/**
	 * 是否一票多件（0：否，1：是）
	 */
	@Schema(description="是否一票多件")
	private Integer isOneTicketMany;

	/**
	 * 是否返仓上架扫描（0：否，1：是）
	 */
	@Schema(description="是否返仓上架扫描（0：否，1：是）")
	private Integer isRwScan;

	/**
	 * 仓库ID
	 */
	@Schema(description = "仓库ID")
	private Long warehouseId;

	/**
	 * 是否入仓（0：否，1：是）
	 */
	@Schema(description = "是否入仓（0：否，1：是）")
	private Integer isWarehouseEntry;

	/**
	 * 入仓时间
	 */
	@Schema(description = "入仓时间")
	private LocalDateTime warehouseEntryTime;


	/**
	 * 是否使用客户单号作为面单条码
	 */
	@Schema(description = "是否使用客户单号作为面单条码")
	private Boolean isCustomerLabel;

	/**
	 * 预估价格
	 */
	@Schema(description = "预估价格")
	private BigDecimal forecastedPrice;

}