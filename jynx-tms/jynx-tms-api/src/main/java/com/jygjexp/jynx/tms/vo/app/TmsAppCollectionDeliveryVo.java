package com.jygjexp.jynx.tms.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: chenchang
 * @Description: App-揽收批量上传送货证明参数
 * @Date: 2025/6/10 18:19
 */
@Data
@FieldNameConstants
@Schema(description = "App-揽收批量上传送货证明参数")
public class TmsAppCollectionDeliveryVo {

    @Schema(description="订单号主单")
    @NotEmpty(message = "订单号主单列表不能为空")
    private List<String> entrustedOrderNumbers;

    @Schema(description="送货证明,最多六张通过逗号分割")
    private String collectionDeliveryProof;

    @Schema(description="揽收标识=1")
    private Integer isTask;
}
