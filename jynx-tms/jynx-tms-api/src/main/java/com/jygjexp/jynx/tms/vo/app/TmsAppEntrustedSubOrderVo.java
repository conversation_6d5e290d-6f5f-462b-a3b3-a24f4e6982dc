package com.jygjexp.jynx.tms.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @Author: xiongpengfei
 * @Description: 卡派App-委托单子单信息参数
 * @Date: 2025/3/30 19:54
 */
@Data
@FieldNameConstants
@Schema(description = "卡派App-委托单子单信息参数")
public class TmsAppEntrustedSubOrderVo {

    // 委托单号
    @Schema(description = "委托单号")
    private String entrustedOrderNumber;

    /**
     * 是否扫描：0：未扫描/1:已扫描
     */
    @Schema(description="是否扫描：0：未扫描/1:已扫描")
    private Integer isScan;

    /**
     * 长
     */
    @Schema(description="长")
    private BigDecimal length;

    /**
     * 宽
     */
    @Schema(description="宽")
    private BigDecimal width;

    /**
     * 高
     */
    @Schema(description="高")
    private BigDecimal height;

    /**
     * 重量(kg)
     */
    @Schema(description="重量(kg)")
    private BigDecimal weight;

    /**
     * 货物数量
     */
    @Schema(description="货物数量")
    private Integer cargoQuantity;

    /**
     * 货物描述信息
     */
    private String cargoDescription;


}
