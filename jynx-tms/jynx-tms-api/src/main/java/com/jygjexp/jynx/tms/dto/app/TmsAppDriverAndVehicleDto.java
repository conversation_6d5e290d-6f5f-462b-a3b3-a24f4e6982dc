package com.jygjexp.jynx.tms.dto.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDate;

/**
 * @Author: xiongpengfei
 * @Description: App-司机and车辆信息
 * @Date: 2025/4/15 20:13
 */
@Data
@FieldNameConstants
@Schema(description = "App-司机and车辆信息")
public class TmsAppDriverAndVehicleDto {


    @Schema(description="司机信息主键")
    private Long driverId;

    /**
     * 姓名
     */
    @Schema(description="姓名")
    private String driverName;

    /**
     * 手机号码
     */
    @Schema(description="手机号码")
    private String phone;


    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String licensePlate;



}
