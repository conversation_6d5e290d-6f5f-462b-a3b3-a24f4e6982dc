package com.jygjexp.jynx.tms.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * @Author: xiongpengfei
 * @Description: 卡派-承运商导出参数
 * @Date: 2025/2/25 11:08
 */
@Data
@FieldNameConstants
@Schema(description = "承运商导出参数")
public class TmsCarrierExportVo {

    /**
     * 承运商编码
     */
    @Schema(description="承运商编码")
    @ExcelProperty(value = "(Carrier code)承运商编码")
    @ColumnWidth(25)
    private String carrierCode;

    /**
     * 承运商名称
     */
    @Schema(description="承运商名称")
    @ExcelProperty(value = "(Carrier name)承运商名称")
    @ColumnWidth(25)
    private String carrierName;

    /**
     * 承运商类型。1：自营、2：外包
     */
    @Schema(description="承运商类型。1：自营、2：外包")
    @ExcelProperty(value = "(Carrier type)承运商类型")
    private Integer carrierType;

    /**
     * 法人代表
     */
    @Schema(description="法人代表")
    @ExcelProperty(value = "(legal Name)法人代表")
    private String legalName;


    /**
     * 联系人姓名
     */
    @Schema(description="联系人姓名")
    @ExcelProperty(value = "(linkman name)联系人姓名")
    private String name;

    /**
     * 手机号
     */
    @Schema(description="手机号")
    @ExcelProperty(value = "(phone)手机号")
    @ColumnWidth(25)
    private String phone;


    /**
     * 职位
     */
    @Schema(description="职位")
    @ExcelProperty(value = "(position)职位")
    private String position;

    /**
     * 邮箱
     */
    @Schema(description="邮箱")
    @ExcelProperty(value = "(email)邮箱")
    @ColumnWidth(25)
    private String carrierEmail;

    /**
     * 启用状态：0：禁用、1：启用
     */
    @Schema(description="启用状态：0：禁用、1：启用")
    @ExcelProperty(value = "(initiate mode)启用状态")
    private Integer isValid;

}
