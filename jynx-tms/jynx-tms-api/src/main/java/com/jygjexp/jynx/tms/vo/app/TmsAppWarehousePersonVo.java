package com.jygjexp.jynx.tms.vo.app;

import com.jygjexp.jynx.tms.entity.TmsWarehouseEmployeeEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDate;

/**
 * @Author: xiongpengfei
 * @Description: App-仓库个人信息参数
 * @Date: 2025/4/27 11:54
 */
@Data
@FieldNameConstants
@Schema(description = "App-仓库个人信息参数")
public class TmsAppWarehousePersonVo extends TmsWarehouseEmployeeEntity {

    // 头像
    @Schema(description="账号头像")
    private String warehouseAvatar;

}
