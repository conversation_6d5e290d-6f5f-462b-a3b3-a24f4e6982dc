package com.jygjexp.jynx.tms.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: xiongpengfei
 * @Description: App-揽收批量上传取货证明参数
 * @Date: 2025/5/30 15:36
 */
@Data
@FieldNameConstants
@Schema(description = "App-揽收批量上传取货证明参数")
public class TmsAppCollectionPickupVo {

    @Schema(description="订单号主单")
    @NotEmpty(message = "订单号主单列表不能为空")
    private List<String> entrustedOrderNumbers;

    @Schema(description="提货证明,最多六张通过逗号分割")
    @NotBlank
    private String collectionPickupProof;

    @Schema(description="揽收、派送标识")
    private Integer isTask;
}
