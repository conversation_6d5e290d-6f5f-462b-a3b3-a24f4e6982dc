package com.jygjexp.jynx.tms.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @Author: xiongpengfei
 * @Description: 卡派App-司机送货参数
 * @Date: 2025/3/21 16:22
 */
@Data
@FieldNameConstants
@Schema(description = "卡派App-司机送货参数")
public class TmsAppDriverDeliveryVo {

    @Schema(description="跟踪单号")
    @NotBlank
    private String entrustedOrderNumber;

    @Schema(description="提货证明")
    private String pickupProof;

    @Schema(description="送货证明")
    private String deliveryProof;

    @Schema(description="揽收、派送标识")
    @NotNull
    private Integer isTask;

}
