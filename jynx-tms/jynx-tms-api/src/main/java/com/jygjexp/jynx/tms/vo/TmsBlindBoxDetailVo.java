package com.jygjexp.jynx.tms.vo;

import com.jygjexp.jynx.tms.entity.TmsBlindBoxEntity;
import com.jygjexp.jynx.tms.entity.TmsBlindBoxRuleEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 盲盒详情响应VO
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "盲盒详情响应VO")
public class TmsBlindBoxDetailVo {

    /**
     * 盲盒基本信息
     */
    @Schema(description = "盲盒基本信息")
    private TmsBlindBoxEntity blindBox;

    /**
     * 盲盒规则配置列表
     */
    @Schema(description = "盲盒规则配置列表")
    private List<TmsBlindBoxRuleEntity> rules;
}
