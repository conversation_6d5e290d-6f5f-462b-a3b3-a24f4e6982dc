package com.jygjexp.jynx.tms.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.jygjexp.jynx.tms.annotation.ConvertType;
import com.jygjexp.jynx.tms.utils.IntegerDictsConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 中大件换单列表导出
 */
@Data
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 10)
@Schema(description = "中大件换单列表导出")
public class TmsTmsThirdPartPostExcelVo {


    @ColumnWidth(18)
    @ExcelProperty("(customer name)客户名称")
    @Schema(description="客户名称")
    private String customerName;

    @ColumnWidth(18)
    @ExcelProperty("(NB Order)原单号")
    @Schema(description="原单号")
    private String nbOrderNo;



    @ColumnWidth(10)
    @ExcelProperty("(New tracking number)新单号")
    @Schema(description="新单号")
    private String channelOrderNo;


    @ColumnWidth(10)
    @ExcelProperty("(customer order number)客户单号")
    @Schema(description="客户单号")
    private String customerOrderNo;




/*    @ConvertType("customerPush")
    @ColumnWidth(10)
    @ExcelProperty(value = "(channel name)渠道名称",converter = IntegerDictsConverter.class)
    @Schema(description="渠道名称")
    private Integer channel;*/


    @ColumnWidth(18)
    @ExcelProperty("(channel name)渠道名称")
    @Schema(description="渠道名称")
    private String channel;

    @ColumnWidth(20)
    @ExcelProperty("(Time)时间")
    @Schema(description="时间")
    private String time;



}
