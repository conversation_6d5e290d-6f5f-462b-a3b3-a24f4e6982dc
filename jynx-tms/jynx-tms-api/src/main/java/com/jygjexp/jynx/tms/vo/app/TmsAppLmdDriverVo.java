package com.jygjexp.jynx.tms.vo.app;

import com.jygjexp.jynx.tms.entity.TmsLmdDriverEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @Author: xiongpengfei
 * @Description: 中大件App-司机注册参数
 * @Date: 2025/4/08 14:36
 */
@Data
@FieldNameConstants
@Schema(description = "中大件App-司机注册参数")
public class TmsAppLmdDriverVo  {
    // 司机id
    @Schema(description="司机id")
    private Long driverId;

    // 审核状态
    @Schema(description="审核状态")
    private Integer auditStatus;

    /**
     * 是否营业，0：休息，1：已营业
     */
    @Schema(description="是否营业，0：休息，1：已营业")
    private Boolean isOpen;

    /**
     * 启用状态：0：禁用、1：启用
     */
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

    //  ---------------------------------------------------- 司机入参----------------------------------------------------

    /**
     * 姓名
     */
    @Schema(description="姓名")
    private String driverName;

    /**
     * 手机号码
     */
    @Schema(description="手机号码")
    private String phone;

    /**
     * 身份证号
     */
    @Schema(description="身份证号")
    private String idNumber;

    /**
     * 国家
     */
    @Schema(description="国家")
    private String country;

    /**
     * 省份
     */
    @Schema(description="省份")
    private String province;

    /**
     * 区域id
     */
    @Schema(description="区域id")
    private String regionId;

    /**
     * 邮编
     */
    @Schema(description="邮编")
    private String zip;

    /**
     * 邮箱
     */
    @Schema(description="邮箱")
    private String email;

    /**
     * 社保号码
     */
    @Schema(description="社保号码")
    private String socialSecurityNumber;

    /**
     * 家庭住址
     */
    @Schema(description="家庭住址")
    private String homeAddress;

    /**
     * 驾照类型
     */
    @Schema(description="驾照类型")
    private String licenseType;

    /**
     * 驾照有效期开始
     */
    @Schema(description="驾照有效期开始")
    private LocalDate  licenseTimeStart;

    /**
     * 驾照有效期结束
     */
    @Schema(description="驾照有效期结束")
    private LocalDate  licenseTimeEnd;

    /**
     * 所属承运商
     */
    @Schema(description="所属承运商")
    private Long carrierId;

    /**
     * 紧急联系人
     */
    @Schema(description="紧急联系人")
    private String emergencyName;

    /**
     * 紧急联系人电话
     */
    @Schema(description="紧急联系人电话")
    private String emergencyPhone;


    /**
     * 证件照正面图片
     */
    @Schema(description="证件照正面图片")
    private String idCardFront;

    /**
     * 证件照反面图片
     */
    @Schema(description="证件照反面图片")
    private String idCardBack;

    /**
     * 驾驶证正面图片
     */
    @Schema(description="驾驶证正面图片")
    private String drivingLicenseFront;

    /**
     * 驾驶证反面图片
     */
    @Schema(description="驾驶证反面图片")
    private String drivingLicenseBack;

    /**
     * 其他资质证明文件
     */
    @Schema(description="其他资质证明文件")
    private String otherQualification;

    /**
     * 工作方式 0:全职，1：兼职，2：外包
     */
    @Schema(description="工作方式 0:全职，1：兼职，2：外包")
    private Integer workType;

    /**
     * 司机类型 0：干线，1：卡派，2：中大件
     */
    @Schema(description="司机类型 0：干线，1：卡派，2：中大件")
    private String driverType;

    /**
     * 机构编号
     */
    @Schema(description="机构编号")
    private String orgCode;

    /**
     * 运输编号
     */
    @Schema(description="运输编号")
    private String transportNumber;

    /**
     * 银行账号
     */
    @Schema(description="银行账号")
    private String bankAccount;

    /**
     * 司机号
     */
    @Schema(description="司机号")
    private String driverNum;
    //  ---------------------------------------------------- 车辆入参----------------------------------------------------


    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactPhone;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String licensePlate;

    /**
     * 车辆类型
     */
    @Schema(description = "车辆类型")
    private Integer vehicleType;


    /**
     * 车辆图片(存URL路径)
     */
    @Schema(description = "车辆图片(存URL路径)")
    private String vehicleImageUrl;

    /**
     * 车辆保险有限期(开始时间)
     */
    @Schema(description = "车辆保险有限期(开始时间)")
    private LocalDate insuranceStartDate;

    /**
     * 车辆保险有限期(结束时间)
     */
    @Schema(description = "车辆保险有限期(结束时间)")
    private LocalDate insuranceEndDate;


    /**
     * 长(m)
     */
    @Schema(description = "长(m)")
    private BigDecimal length;

    /**
     * 宽(m)
     */
    @Schema(description = "宽(m)")
    private BigDecimal width;

    /**
     * 高(m)
     */
    @Schema(description = "高(m)")
    private BigDecimal height;

    /**
     * 可配货物类型
     */
    @Schema(description = "可配货物类型")
    private String cargoType;
    
    /**
     * 货仓长(m)
     */
    @Schema(description = "货仓长(m)")
    private BigDecimal warehouseLength;

    /**
     * 货仓宽(m)
     */
    @Schema(description = "货仓宽(m)")
    private BigDecimal warehouseWidth;

    /**
     * 货仓高(m)
     */
    @Schema(description = "货仓高(m)")
    private BigDecimal warehouseHeight;

    /**
     * 车辆所属权：1个人，2公司
     */
    @Schema(description = "车辆所属权：1个人，2公司")
    private Integer ownershipType;

}
