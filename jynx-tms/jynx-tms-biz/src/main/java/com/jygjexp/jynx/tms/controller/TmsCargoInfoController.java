package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.dto.TmsCargoInfoDto;
import com.jygjexp.jynx.tms.entity.TmsCargoInfoEntity;
import com.jygjexp.jynx.tms.service.TmsCargoInfoService;
import com.jygjexp.jynx.tms.vo.SummaryResultVo;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;

/**
 * 卡派货物信息
 *
 * <AUTHOR>
 * @date 2025-03-05 19:02:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCargoInfo" )
@Tag(description = "tmsCargoInfo" , name = "卡派货物信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCargoInfoController {

    private final  TmsCargoInfoService tmsCargoInfoService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsCargoInfo 卡派货物信息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCargoInfo_view')" )
    public R getTmsCargoInfoPage(@ParameterObject Page page, @ParameterObject TmsCargoInfoEntity tmsCargoInfo) {
        LambdaQueryWrapper<TmsCargoInfoEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsCargoInfoService.page(page, wrapper));
    }


    /**
     * 通过id查询卡派货物信息
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCargoInfo_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsCargoInfoService.getById(id));
    }

    /**
     * 新增卡派货物信息
     * @param tmsCargoInfo 卡派货物信息
     * @return R
     */
    @Operation(summary = "新增卡派货物信息" , description = "新增卡派货物信息" )
    @SysLog("新增卡派货物信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCargoInfo_add')" )
    public R save(@RequestBody TmsCargoInfoEntity tmsCargoInfo) {
        return R.ok(tmsCargoInfoService.save(tmsCargoInfo));
    }

    /**
     * 修改卡派货物信息
     * @param tmsCargoInfo 卡派货物信息
     * @return R
     */
    @Operation(summary = "修改卡派货物信息" , description = "修改卡派货物信息" )
    @SysLog("修改卡派货物信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCargoInfo_edit')" )
    public R updateById(@RequestBody TmsCargoInfoEntity tmsCargoInfo) {
        return R.ok(tmsCargoInfoService.updateById(tmsCargoInfo));
    }

    /**
     * 通过id删除卡派货物信息
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派货物信息" , description = "通过id删除卡派货物信息" )
    @SysLog("通过id删除卡派货物信息" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCargoInfo_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsCargoInfoService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsCargoInfo 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsCargoInfo_export')" )
    public List<TmsCargoInfoEntity> export(TmsCargoInfoEntity tmsCargoInfo,Long[] ids) {
        return tmsCargoInfoService.list(Wrappers.lambdaQuery(tmsCargoInfo).in(ArrayUtil.isNotEmpty(ids), TmsCargoInfoEntity::getId, ids));
    }

    /**
     * 导入货物信息
     * @param file
     * @param entrustedOrderNumber
     * @return
     */
    @Operation(summary = "导入货物信息" , description = "导入货物信息" )
    @SysLog("导入货物信息" )
    @PostMapping("/importCargoInfo")
    @PreAuthorize("@pms.hasPermission('tms_tmsCargoInfo_import')")
    public R importCargoInfo(@RequestParam("file") MultipartFile file, @RequestParam("entrustedOrderNumber") String entrustedOrderNumber) {
        return tmsCargoInfoService.processFile(file,entrustedOrderNumber);
    }

    /**
     * 计算货物汇总信息
     * @param cargoInfoList
     * @return
     */
    @Operation(summary = "计算货物汇总信息", description = "计算货物的总重量，总体积，总件数")
    @PostMapping("/summary")
    public R<SummaryResultVo> calculateSummary(@RequestBody TmsCargoInfoDto cargoInfoList) {
        if (Objects.isNull(cargoInfoList) || Objects.isNull(cargoInfoList.getCargoInfoList()) || cargoInfoList.getCargoInfoList().isEmpty()) {
            return R.failed("The list of goods information cannot be empty");
        }
        SummaryResultVo result = tmsCargoInfoService.calculateSummary(cargoInfoList.getCargoInfoList());
        return R.ok(result);
    }

    @Operation(summary = "通过客户单号或委托单号查询货物信息", description = "通过客户单号或委托单号查询货物信息")
    @PostMapping("/listByOrderNumber")
    public R<List<TmsCargoInfoEntity>> listByOrderNumber(@Parameter(description = "客户单号") @NotBlank String customerOrderNumber, @Parameter(description = "委托单号") @NotBlank String entrustedOrderNumber) {
        return R.ok(tmsCargoInfoService.listByOrderNumber(customerOrderNumber, entrustedOrderNumber));
    }

}