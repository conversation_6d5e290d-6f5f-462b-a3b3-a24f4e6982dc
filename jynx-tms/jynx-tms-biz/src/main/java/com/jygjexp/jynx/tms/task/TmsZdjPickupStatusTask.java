package com.jygjexp.jynx.tms.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.tms.entity.TmsPickupEntity;
import com.jygjexp.jynx.tms.entity.TmsZdjPickupEntity;
import com.jygjexp.jynx.tms.enums.PickupStatusEnum;
import com.jygjexp.jynx.tms.service.TmsPickupService;
import com.jygjexp.jynx.tms.service.TmsZdjPickupService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * @Author: xiongpengfei
 * @Description: 【中大件】自提状态根据上架天数自动变更
 * @Date: 2025/06/06 19:12
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TmsZdjPickupStatusTask {
    private final TmsZdjPickupService pickupService;


    @SneakyThrows
    @XxlJob("tmsZdjPickupStatusTask")
    public void tmsZdjPickupStatusTask() {
           XxlJobHelper.log("定时任务：【自提状态根据上架天数自动变更】于:{}，输入参数{}", LocalDateTime.now(), "运行中");
        try {
            // 查询所有自提状态为待联系和预约待取货的自提包裹
            List<TmsZdjPickupEntity> pickupList = pickupService.list(
                    new LambdaQueryWrapper<TmsZdjPickupEntity>()
                            .in(TmsZdjPickupEntity::getPickupStatus, PickupStatusEnum.APPOINTMENT.getCode(), PickupStatusEnum.WAIT_CONTACT.getCode())
            );

            for (TmsZdjPickupEntity pickup : pickupList) {
                long daysOnShelf = ChronoUnit.DAYS.between(pickup.getShelvingTime(), LocalDateTime.now());

                if (daysOnShelf > 15) {
                    pickup.setPickupStatus(PickupStatusEnum.DESTROYED.getCode());  // 销毁状态
                    pickup.setIsExpiredDestroy(1);  // 标识是否销毁标识
                    pickupService.updateById(pickup);  // 更新状态
                }
            }

            XxlJobHelper.handleSuccess(); // 设置任务结果
            XxlJobHelper.log("定时任务：【自提状态根据上架天数自动变更】执行结束，时间: {}", LocalDateTime.now());
        } catch (Exception e) {
            log.error("【自提状态根据上架天数自动变更】定时任务执行失败：", e);
            XxlJobHelper.log("任务失败，原因：{}", e.getMessage());
            XxlJobHelper.handleFail();
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "请求过程中发生错误：" + e.getMessage(), e);
        }
    }


}
