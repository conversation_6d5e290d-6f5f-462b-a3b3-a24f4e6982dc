package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsPickupEntity;
import com.jygjexp.jynx.tms.entity.TmsShelfEntity;
import com.jygjexp.jynx.tms.vo.TmsShelfPageVo;
import com.jygjexp.jynx.tms.service.TmsPickupService;
import com.jygjexp.jynx.tms.service.TmsShelfService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;

/**
 * 卡派货架信息表
 *
 * <AUTHOR>
 * @date 2025-02-11 17:28:53
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsShelf" )
@Tag(description = "tmsShelf" , name = "卡派货架信息表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsShelfController {

    private final  TmsShelfService tmsShelfService;
    private final TmsPickupService pickupService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 卡派货架信息表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsShelf_view')" )
    public R getTmsShelfPage(@ParameterObject Page page, @ParameterObject TmsShelfPageVo vo) {
        return R.ok(tmsShelfService.search(page,vo));
    }


    /**
     * 通过id查询卡派货架信息表
     * @param shelfId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{shelfId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsShelf_view')" )
    public R getById(@PathVariable("shelfId" ) Long shelfId) {
        return R.ok(tmsShelfService.getById(shelfId));
    }

    /**
     * 新增卡派货架信息表
     * @param tmsShelf 卡派货架信息表
     * @return R
     */
    @Operation(summary = "新增卡派货架信息表" , description = "新增卡派货架信息表" )
    @SysLog("新增卡派货架信息表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsShelf_add')" )
    public R save(@RequestBody TmsShelfEntity tmsShelf) {
        // 判断货架编码是否已存在
        if (Objects.nonNull(tmsShelfService.getOne(Wrappers.<TmsShelfEntity>lambdaQuery().eq(TmsShelfEntity::getShelfCode, tmsShelf.getShelfCode())
                .eq(TmsShelfEntity::getIsValid, 1)))) {
            return R.failed("tms.shelf.code.exists", tmsShelf.getShelfCode());
        }
        return R.ok(tmsShelfService.save(tmsShelf));
    }

    /**
     * 修改卡派货架信息表
     * @param tmsShelf 卡派货架信息表
     * @return R
     */
    @Operation(summary = "修改卡派货架信息表" , description = "修改卡派货架信息表" )
    @SysLog("修改卡派货架信息表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsShelf_edit')" )
    public R updateById(@RequestBody TmsShelfEntity tmsShelf) {
        return R.ok(tmsShelfService.updateById(tmsShelf));
    }

    /**
     * 通过id删除卡派货架信息表
     * @param ids shelfId列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派货架信息表" , description = "通过id删除卡派货架信息表" )
    @SysLog("通过id删除卡派货架信息表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsShelf_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsShelfService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 查询全部货架编码
     *
     * @return R
     */
    @Operation(summary = "查询全部货架编码" , description = "查询全部货架编码" )
    @SysLog("查询全部货架编码" )
    @GetMapping("/listShelfCode")
    public R listShelfCode() {
        // 查询全部货架编码
        LambdaQueryWrapper<TmsShelfEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(TmsShelfEntity::getShelfCode, TmsShelfEntity::getIsValid).groupBy(TmsShelfEntity::getShelfId);
        return R.ok(tmsShelfService.list(wrapper));
    }

    /**
     * 根据货架编码查询在架自提包裹信息
     *
     * @return R
     */
    @Operation(summary = "根据货架编码查询在架自提包裹信息" , description = "根据货架编码查询在架自提包裹信息" )
    @SysLog("根据货架编码查询在架自提包裹信息" )
    @GetMapping("/listShelfCodeAndPackage/{shelfCode}")
    public R listShelfCodeAndPackage(@PathVariable("shelfCode") String shelfCode) {
        // 查询在架自提包裹信息
        LambdaQueryWrapper<TmsPickupEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TmsPickupEntity::getShelfCode, shelfCode);
        return R.ok(pickupService.list(wrapper));
    }

    /**
     * 包裹上架
     *
     * @return R
     */
    @Operation(summary = "包裹上架" , description = "包裹上架" )
    @SysLog("包裹上架" )
    @PostMapping("/putaway")
    public R listShelfCodeAndPackage(@RequestParam("pkgNo")  @NotBlank(message = "包裹号不能为空") String  pkgNo,
                                     @RequestParam("shelfCode") @NotBlank(message = "货架编号不能为空") String shelfCode) {
        return R.ok(tmsShelfService.packagePutaway(pkgNo, shelfCode));
    }


    /**
     * 导出excel 表格
     * @param tmsShelf 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsShelf_export')" )
    public List<TmsShelfEntity> export(TmsShelfEntity tmsShelf,Long[] ids) {
        return tmsShelfService.list(Wrappers.lambdaQuery(tmsShelf).in(ArrayUtil.isNotEmpty(ids), TmsShelfEntity::getShelfId, ids));
    }
}