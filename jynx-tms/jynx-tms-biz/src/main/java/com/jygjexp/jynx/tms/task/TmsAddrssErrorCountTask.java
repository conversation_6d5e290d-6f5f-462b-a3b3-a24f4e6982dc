package com.jygjexp.jynx.tms.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsAddressAnomalyEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsOrderTrackEntity;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.service.TmsAddressAnomalyService;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsOrderTrackService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xxl.job.core.context.XxlJobHelper;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

/**
 * @Author: xiongpengfei
 * @Description: 【中大件】地址统计计算异常任务
 * @Date: 2025/06/09 19:12
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TmsAddrssErrorCountTask {

    private final TmsCustomerOrderService customerOrderService;
    private final TmsAddressAnomalyService addressAnomalyService;

    // 将查询出的地址信息去地址异常记录表中tms_address_anomaly去匹配，统计出公寓，配送失败，赔偿，投诉，丢件等情况的数量
    @SneakyThrows
    @XxlJob("tmsAddrssErrorCountTask")
    public void tmsAddrssErrorCountTask() {
        log.info("定时任务于:{}，开始执行", LocalDateTime.now());

        try {
            int orderPageSize = 20;
            int orderPageNum = 1;
            ObjectMapper objectMapper = new ObjectMapper();

            while (true) {
                // 分页拉订单
                List<TmsCustomerOrderEntity> orderList = customerOrderService.list(
                        new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode())
                                .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE)
                                .gt(TmsCustomerOrderEntity::getCreateTime, DateUtil.offsetDay(new Date(), -1))
                                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                                .isNull(TmsCustomerOrderEntity::getAddressErrorCount)
                                .last("LIMIT " + ((orderPageNum - 1) * orderPageSize) + ", " + orderPageSize)
                );

                if (CollUtil.isEmpty(orderList)) break;

                // 提取本页关键词全集
                Set<String> keywordSet = orderList.stream()
                        .map(TmsCustomerOrderEntity::getDestAddress)
                        .filter(StrUtil::isNotBlank)
                        .flatMap(addr -> extractKeywords(addr).stream())
                        .collect(Collectors.toSet());

                if (CollUtil.isEmpty(keywordSet)) {
                    orderPageNum++;
                    continue;
                }

                // 用关键词去 address_anomaly 查数据
                LambdaQueryWrapper<TmsAddressAnomalyEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.and(w -> {
                    for (String keyword : keywordSet) {
                        w.or().like(TmsAddressAnomalyEntity::getAddress, keyword);
                    }
                });

                List<TmsAddressAnomalyEntity> anomalyList = addressAnomalyService.list(wrapper);

                // 构建当前页关键词倒排索引
                Map<String, Map<Integer, Long>> anomalyStatsMap = new HashMap<>();
                Map<String, Set<String>> keywordToAddressesMap = new HashMap<>();

                for (TmsAddressAnomalyEntity anomaly : anomalyList) {
                    String addr = anomaly.getAddress();
                    Integer type = anomaly.getType();
                    if (StrUtil.isBlank(addr) || type == null) continue;

                    anomalyStatsMap.computeIfAbsent(addr, k -> new HashMap<>()).merge(type, 1L, Long::sum);
                    for (String keyword : extractKeywords(addr)) {
                        keywordToAddressesMap.computeIfAbsent(keyword, k -> new HashSet<>()).add(addr);
                    }
                }

                List<TmsCustomerOrderEntity> toUpdateList = new ArrayList<>();

                // 匹配订单地址 → 找最相似异常地址 → 聚合异常类型 → 生成 JSON 字符串
                for (TmsCustomerOrderEntity order : orderList) {
                    String orderAddress = order.getDestAddress();
                    if (StrUtil.isBlank(orderAddress)) continue;

                    Set<String> keywords = extractKeywords(orderAddress);
                    Set<String> narrowedCandidates = new HashSet<>();
                    for (String keyword : keywords) {
                        Set<String> candidates = keywordToAddressesMap.get(keyword);
                        if (candidates != null) narrowedCandidates.addAll(candidates);
                    }

                    String matchedAddress = findMostSimilarAddress(orderAddress, narrowedCandidates);
                    Map<Integer, Long> typeCountMap = matchedAddress != null
                            ? anomalyStatsMap.getOrDefault(matchedAddress, Collections.emptyMap())
                            : Collections.emptyMap();

                    Map<String, Integer> resultJsonMap = new HashMap<>();
                    for (int i = 0; i <= 4; i++) {
                        resultJsonMap.put(String.valueOf(i), typeCountMap.getOrDefault(i, 0L).intValue());
                    }

                    try {
                        String jsonString = objectMapper.writeValueAsString(resultJsonMap);
                        order.setAddressErrorCount(jsonString);
                        toUpdateList.add(order);
                    } catch (Exception e) {
                        log.error("地址异常统计转JSON失败，地址:{}，错误:{}", orderAddress, e.getMessage());
                    }
                }

                if (CollUtil.isNotEmpty(toUpdateList)) {
                    customerOrderService.updateBatchById(toUpdateList);
                    log.info("第 {} 页已更新 {} 条订单", orderPageNum, toUpdateList.size());
                }

                // 下一页
                orderPageNum++;
            }

            XxlJobHelper.handleSuccess();
            XxlJobHelper.log("定时任务：【地址异常统计】执行完成");

        } catch (Exception e) {
            log.error("【地址异常统计任务】失败", e);
            XxlJobHelper.log("失败原因: {}", e.getMessage());
            XxlJobHelper.handleFail();
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "异常：" + e.getMessage(), e);
        }
    }

    // ================= 工具方法 =================

    // 去除特殊字符、空格、转换小写（数据统一格式化）
    private String normalizeAddress(String addr) {
        return addr == null ? "" :
                addr.toLowerCase()
                        .replaceAll("[^a-z0-9 ]", "")
                        .replaceAll("\\s+", " ")
                        .trim();
    }

    // 提取关键词
    private Set<String> extractKeywords(String address) {
        return Arrays.stream(normalizeAddress(address).split(" "))
                .filter(s -> s.length() > 2) // 忽略短词   例："dr", "st", "n", "w" 等方向、街道缩写几乎每个地址都有，没什么区分度；"on", "mb", "qc" 是省缩写
                .collect(Collectors.toSet());
    }

    // 计算 Jaccard 相似度
    private double jaccardSimilarity(String a, String b) {
        Set<String> setA = new HashSet<>(Arrays.asList(a.split(" ")));
        Set<String> setB = new HashSet<>(Arrays.asList(b.split(" ")));
        Set<String> intersection = new HashSet<>(setA);
        intersection.retainAll(setB);
        Set<String> union = new HashSet<>(setA);
        union.addAll(setB);
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }

    // 计算 Levenshtein 编辑距离
    private double levenshteinSimilarity(String a, String b) {
        LevenshteinDistance distance = new LevenshteinDistance();
        int d = distance.apply(a, b);
        int maxLen = Math.max(a.length(), b.length());
        return maxLen == 0 ? 1.0 : 1.0 - (double) d / maxLen;
    }

    private String findMostSimilarAddress(String target, Set<String> candidateSet) {
        if (candidateSet == null || candidateSet.isEmpty()) return null;

        String normalizedTarget = normalizeAddress(target);
        String bestMatch = null;
        double bestScore = 0.0;

        for (String candidate : candidateSet) {
            String normalizedCandidate = normalizeAddress(candidate);
            double jac = jaccardSimilarity(normalizedTarget, normalizedCandidate);
            double lev = levenshteinSimilarity(normalizedTarget, normalizedCandidate);
            double score = (jac + lev) / 2.0;

            if (score > bestScore) {
                bestScore = score;
                bestMatch = candidate;
            }

            // 一旦相似度过高（比如 0.85）就不在匹配后面的地址
            if (bestScore >= 0.85) {
                break;
            }
        }

        return bestScore > 0.77 ? bestMatch : null; // 匹配率(0.77大概五个字符容错空间)
    }
}

