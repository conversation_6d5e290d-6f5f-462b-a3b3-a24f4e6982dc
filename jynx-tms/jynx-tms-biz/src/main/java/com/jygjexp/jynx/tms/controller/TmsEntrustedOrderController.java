package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.dto.TmsCargoInfoDto;
import com.jygjexp.jynx.tms.entity.TmsCarrierEntity;
import com.jygjexp.jynx.tms.entity.TmsEntrustedOrderEntity;
import com.jygjexp.jynx.tms.service.TmsCarrierService;
import com.jygjexp.jynx.tms.service.TmsEntrustedOrderService;
import com.jygjexp.jynx.tms.vo.SummaryResultVo;
import com.jygjexp.jynx.tms.vo.TmsEntrustedOrderPageVo;
import com.jygjexp.jynx.tms.vo.TmsEntrustedOrderVo;
import com.jygjexp.jynx.tms.vo.excel.TmsEntrustedOrderExcelVo;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * 卡派委托订单
 *
 * <AUTHOR>
 * @date 2025-03-04 18:43:07
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsEntrustedOrder" )
@Tag(description = "tmsEntrustedOrder" , name = "卡派委托订单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsEntrustedOrderController {

    private final  TmsEntrustedOrderService tmsEntrustedOrderService;

    /**
     * 委托订单分页查询
     * @param page 分页对象
     * @param tmsEntrustedOrder 卡派委托订单
     * @return
     */
    @Operation(summary = "委托订单分页查询" , description = "委托订单分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsEntrustedOrder_view')" )
    public R getTmsEntrustedOrderPage(@ParameterObject Page page, @ParameterObject TmsEntrustedOrderPageVo tmsEntrustedOrder) {
        return R.ok(tmsEntrustedOrderService.search(page, tmsEntrustedOrder));
    }

    /**
     * 车辆调度分页查询
     * @param page 分页对象
     * @param tmsEntrustedOrder 卡派委托订单
     * @return
     */
    @Operation(summary = "车辆调度分页查询" , description = "车辆调度分页查询" )
    @GetMapping("/dispatchPage" )
    @PreAuthorize("@pms.hasPermission('tms_dispatch_view')" )
    public R getTmsDispatchPage(@ParameterObject Page page, @ParameterObject TmsEntrustedOrderPageVo tmsEntrustedOrder) {
        return R.ok(tmsEntrustedOrderService.dispatchSearch(page, tmsEntrustedOrder));
    }


    /**
     * 通过id查询卡派委托订单
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsEntrustedOrder_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsEntrustedOrderService.getById(id));
    }

    /**
     * 通过id查询子委托单
     * @param entrustedOrderNumber
     * @return R
     */
    @Operation(summary = "通过id查询子委托单" , description = "通过id查询子委托单" )
    @PostMapping("/getSubOrderByNo" )
    @Inner(value = false)
    public R getSubOrderByNo(@Parameter(description = "委托主单号") @NotBlank String entrustedOrderNumber) {
        return R.ok(tmsEntrustedOrderService.getSubOrderByNo(entrustedOrderNumber));
    }

//    /**
//     * 新增卡派委托订单
//     * @param tmsEntrustedOrder 卡派委托订单
//     * @return R
//     */
//    @Operation(summary = "新增卡派委托订单" , description = "新增卡派委托订单" )
//    @SysLog("新增卡派委托订单" )
//    @PostMapping
//    @Inner(value = false)
////    @PreAuthorize("@pms.hasPermission('tms_tmsEntrustedOrder_add')" )
//    public R save(@RequestBody TmsEntrustedOrderEntity tmsEntrustedOrder) {
//        return R.ok(tmsEntrustedOrderService.create(tmsEntrustedOrder));
//    }

    /**
     * 修改卡派委托订单
     * @param tmsEntrustedOrder 卡派委托订单
     * @return R
     */
    @Operation(summary = "修改卡派委托订单" , description = "修改卡派委托订单" )
    @SysLog("修改卡派委托订单" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsEntrustedOrder_edit')" )
    public R updateById(@RequestBody TmsEntrustedOrderEntity tmsEntrustedOrder) {
        return R.ok(tmsEntrustedOrderService.updateById(tmsEntrustedOrder));
    }

    /**
     * 通过id删除卡派委托订单
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派委托订单" , description = "通过id删除卡派委托订单" )
    @SysLog("通过id删除卡派委托订单" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsEntrustedOrder_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsEntrustedOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 卡派委托订单导出
     * @param vo 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @SysLog("卡派委托订单导出")
    @Operation(summary = "卡派委托订单导出" , description = "卡派委托订单导出")
    @PreAuthorize("@pms.hasPermission('tms_tmsEntrustedOrder_export')" )
    public List<TmsEntrustedOrderExcelVo> export(TmsEntrustedOrderPageVo vo, Long[] ids) {
        return tmsEntrustedOrderService.getExcel(vo, ids);
    }

    /**
     * 承运商接单审批
     */
    @Operation(summary = "承运商接单审批", description = "承运商接单审批")
    @PostMapping("/orderApproval")
    public R<Boolean> orderApproval(@Parameter(description = "客户单号-多个单号用逗号分隔") @NotBlank String entrustedOrderNumbers,
    @Parameter(description = "审核状态：1：审核通过，2审核拒绝") @NotNull Integer auditStatus,
    @Parameter(description = "审核备注", required = false) String auditRemark) {
        return R.ok(tmsEntrustedOrderService.orderApproval(entrustedOrderNumbers, auditStatus, auditRemark));
    }

    /**
     * 车辆调度-分配司机-同步创建运输单
     * @param entrustedOrderNumbers
     * @param driverId
     * @return
     */
    @Operation(summary = "车辆调度-分配司机-同步创建运输单", description = "车辆调度-分配司机-同步创建运输单")
    @PostMapping("/assignDrivers")
    @Inner(value = false)
    public R assignDrivers(@Parameter(description = "委托单号-多个单号用逗号分割", required = true) @RequestParam String entrustedOrderNumbers,
                           @Parameter(description = "司机ID", required = true) @RequestParam Long driverId) {
        return R.ok(tmsEntrustedOrderService.assignDrivers(entrustedOrderNumbers, driverId));
    }

    /**
     * 车辆调度-查询区域内所有订单
     * @return
     */
    @Operation(summary = "查询区域内所有订单", description = "查询区域内所有订单")
    @PostMapping("/pageOrderWithRegion")
    public R pageOrderWithRegion(@ParameterObject Page page, @ParameterObject TmsEntrustedOrderPageVo tmsEntrustedOrder) {
        return R.ok(tmsEntrustedOrderService.pageEntrustedOrderWithRegion(page, tmsEntrustedOrder));
    }

    /**
     * 查询主单下的的子单信息
     *
     * @return R
     */
    @Operation(summary = "app-查询主单下的的子单信息" , description = "app-查询主单下的的子单信息" )
    @GetMapping("/app/getListBySubOrder/{orderNo}" )
    public R getListBySubOrder(@PathVariable("orderNo" ) String orderNo) {
        return tmsEntrustedOrderService.getListBySubOrder(orderNo);
    }

    /**
     * 批量查询主单下的的子单信息
     *
     * @return R
     */
    @Operation(summary = "app-批量查询主单下的的子单信息" , description = "app-批量查询主单下的的子单信息" )
    @PostMapping("/app/getListBySubOrderList" )
    public R getListBySubOrderList(@RequestParam("orderNo" ) List<String> orderNo) {
        return tmsEntrustedOrderService.getListBySubOrderList(orderNo);
    }

    /**
     * 转单
     * @param entrustedOrderNumbers
     * @return
     */
    @Operation(summary = "转单", description = "转单")
    @PostMapping("/transferOrder")
    @Inner(value = false)
    public R transferOrder(@Parameter(description = "委托单号-多单号用逗号分割", required = true) @RequestParam String entrustedOrderNumbers) {
        return R.ok(tmsEntrustedOrderService.transferOrder(entrustedOrderNumbers));
    }

}