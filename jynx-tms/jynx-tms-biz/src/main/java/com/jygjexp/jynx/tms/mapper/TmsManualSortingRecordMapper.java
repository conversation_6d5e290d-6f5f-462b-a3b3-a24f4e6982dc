package com.jygjexp.jynx.tms.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.tms.entity.TmsManualSortingRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TmsManualSortingRecordMapper extends JynxBaseMapper<TmsManualSortingRecordEntity> {

    // 查询出最新的分拣记录
    List<Long> selectLatestRecordIdsByEntrustedOrderNo(@Param("entrustedOrderNo") String entrustedOrderNo,
                                                       @Param("sortingGridCode") String sortingGridCode,
                                                       @Param("warehouseId") Long warehouseId,
                                                       @Param("sortingStartTime") Date sortingStartTime,
                                                       @Param("sortingEndTime") Date sortingEndTime,
                                                       IPage<?> page);

}