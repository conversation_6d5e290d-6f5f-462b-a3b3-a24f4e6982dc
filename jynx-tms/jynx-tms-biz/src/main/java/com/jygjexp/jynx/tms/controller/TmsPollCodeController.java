package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.entity.TmsPollCodeEntity;
import com.jygjexp.jynx.tms.service.TmsPollCodeService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 卡派注册码
 *
 * <AUTHOR>
 * @date 2025-02-28 16:10:53
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsPollCode" )
@Tag(description = "tmsPollCode" , name = "卡派注册码管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsPollCodeController {

    private final  TmsPollCodeService tmsPollCodeService;

    /**
     * 注册码分页查询
     * @param page 分页对象
     * @param tmsPollCode 卡派注册码
     * @return
     */
    @Operation(summary = "注册码分页查询" , description = "注册码分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPollCode_view')" )
    public R getTmsPollCodePage(@ParameterObject Page page, @ParameterObject TmsPollCodeEntity tmsPollCode) {
        LambdaQueryWrapper<TmsPollCodeEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsPollCodeService.page(page, wrapper));
    }


    /**
     * 通过id查询卡派注册码
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPollCode_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsPollCodeService.getById(id));
    }

    /**
     * 新增卡派注册码
     * @param tmsPollCode 卡派注册码
     * @return R
     */
    @Operation(summary = "新增卡派注册码" , description = "新增卡派注册码" )
    @SysLog("新增卡派注册码" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPollCode_add')" )
    public R save(@RequestBody TmsPollCodeEntity tmsPollCode) {
        tmsPollCode.setAdminUserId(SecurityUtils.getUser().getId());    //管理员用户ID
        return R.ok(tmsPollCodeService.save(tmsPollCode));
    }

    /**
     * 修改卡派注册码
     * @param tmsPollCode 卡派注册码
     * @return R
     */
    @Operation(summary = "修改卡派注册码" , description = "修改卡派注册码" )
    @SysLog("修改卡派注册码" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPollCode_edit')" )
    public R updateById(@RequestBody TmsPollCodeEntity tmsPollCode) {
        return R.ok(tmsPollCodeService.updateById(tmsPollCode));
    }

    /**
     * 通过id删除卡派注册码
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派注册码" , description = "通过id删除卡派注册码" )
    @SysLog("通过id删除卡派注册码" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPollCode_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsPollCodeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsPollCode 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsPollCode_export')" )
    public List<TmsPollCodeEntity> export(TmsPollCodeEntity tmsPollCode,Long[] ids) {
        return tmsPollCodeService.list(Wrappers.lambdaQuery(tmsPollCode).in(ArrayUtil.isNotEmpty(ids), TmsPollCodeEntity::getId, ids));
    }
}