package com.jygjexp.jynx.tms.api.aspect;

import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.api.annotation.Debounce;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
public class DebounceAspect {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Around("@annotation(debounce)")
    public Object around(ProceedingJoinPoint joinPoint, Debounce debounce) throws Throwable {
        // 生成 Redis key（用用户 ID 或 IP + 方法名）
        String key = debounce.key();
        if (key.isEmpty()) {
            key = joinPoint.getSignature().toShortString();
        }

        // 检查是否短时间重复请求
        Boolean exists = redisTemplate.hasKey(key);
        if (exists) {
            //throw new RuntimeException("请求过于频繁，请稍后重试！");
            return R.failed("请求过于频繁，请稍后重试！");
        }

        // 设置防抖时间
        redisTemplate.opsForValue().set(key, "1", debounce.timeout(), TimeUnit.MILLISECONDS);
        return joinPoint.proceed();
    }
}
