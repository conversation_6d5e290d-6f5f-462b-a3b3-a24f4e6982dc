package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.app.api.dto.AppUserDTO;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsAppUserService;
import com.jygjexp.jynx.tms.entity.TmsDriverEntity;
import com.jygjexp.jynx.tms.entity.TmsWarehouseEmployeeEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsWarehouseEmployeeService;
import com.jygjexp.jynx.tms.vo.TmsWarehouseEmployeeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 仓库员工
 *
 * <AUTHOR>
 * @date 2024-10-30 21:24:45
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tms")
@Tag(description = "tkzjZtPostEmployee", name = "仓库员工管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsWarehouseEmployeeController {

    private final TmsWarehouseEmployeeService tmsWarehouseEmployeeService;
    private final RemoteTmsAppUserService remoteTmsAppUserService;


    /**
     * 分页查询
     *
     * @param page         分页对象
     * @param postEmployee 仓库员工
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('tms_tkzjZtPostEmployee_view')" )
    public R getPostEmployeePage(@ParameterObject Page page, @ParameterObject TmsWarehouseEmployeeEntity postEmployee) {
        return R.ok(tmsWarehouseEmployeeService.getPage(page, postEmployee));
    }


    /**
     * 通过id查询仓库员工
     *
     * @param employeeId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{employeeId}")
    @PreAuthorize("@pms.hasPermission('tms_tkzjZtPostEmployee_view')")
    public R getById(@PathVariable("employeeId") Integer employeeId) {
        return R.ok(tmsWarehouseEmployeeService.getById(employeeId));
    }

    /**
     * 新增仓库员工
     *
     * @param postEmployee 仓库员工
     * @return R
     */
    @Operation(summary = "新增仓库员工", description = "新增仓库员工")
    @SysLog("新增仓库员工")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tkzjZtPostEmployee_add')" )
    public R save(@RequestBody TmsWarehouseEmployeeEntity postEmployee) {
        TmsWarehouseEmployeeEntity one = tmsWarehouseEmployeeService.getOne(new LambdaQueryWrapper<TmsWarehouseEmployeeEntity>().eq(TmsWarehouseEmployeeEntity::getMobile, postEmployee.getMobile())
                .eq(TmsWarehouseEmployeeEntity::getIsValid, true));
        if (null != one){
             return R.failed("The mobile number already exists");
        }
        postEmployee.setIsValid(1);
        postEmployee.setCreateDate(LocalDateTime.now());
        if (StrUtil.isNotBlank(postEmployee.getPassword())){
            postEmployee.setPassword(postEmployee.getPassword());
        }
//        R<AppUserInfo> info = remoteTmsAppUserService.info(postEmployee.getMobile());
//        if (info.getCode() == 0) {
//            return LocalizedR.failed("tms.app.driver.register.phone.repetition", postEmployee.getMobile());
//        }

        // 同步创建 APP 账号
        AppUserDTO appUserDTO = new AppUserDTO();
        appUserDTO.setRole(Collections.singletonList(4L));
        appUserDTO.setPhone(postEmployee.getMobile());
        appUserDTO.setUsername(postEmployee.getRealname());
        appUserDTO.setNickname(postEmployee.getRealname());
        appUserDTO.setName(postEmployee.getRealname());
        appUserDTO.setPassword("123456");
        R saveAppUser = remoteTmsAppUserService.save(appUserDTO);
        if (saveAppUser.getCode() == 1) {
            return LocalizedR.failed("tms.app.warehouse.register.error", saveAppUser.getMsg());
        }
        return R.ok(tmsWarehouseEmployeeService.save(postEmployee));
    }

    /**
     * 修改仓库员工
     *
     * @param postEmployee 仓库员工
     * @return R
     */
    @Operation(summary = "修改仓库员工", description = "修改仓库员工")
    @SysLog("修改仓库员工")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tkzjZtPostEmployee_edit')" )
    public R updateById(@RequestBody TmsWarehouseEmployeeEntity postEmployee) {
        //查询手机号是否存在
        TmsWarehouseEmployeeEntity one = tmsWarehouseEmployeeService.getOne(new LambdaQueryWrapper<TmsWarehouseEmployeeEntity>().eq(TmsWarehouseEmployeeEntity::getMobile, postEmployee.getMobile())
                .eq(TmsWarehouseEmployeeEntity::getIsValid, true));
        if (null != one && !one.getEmployeeId().equals(postEmployee.getEmployeeId())){
            return R.failed("The mobile number already exists");
        }


        // 同步修改app账号
        R<AppUserInfo> info = remoteTmsAppUserService.info(postEmployee.getMobile());
        if (info.getCode()==1){
            return R.failed("Account does not exist");
        }
        // 同步创建 APP 账号
        AppUserDTO appUserDTO = new AppUserDTO();
        appUserDTO.setUserId(info.getData().getAppUser().getUserId());
        appUserDTO.setRole(Collections.singletonList(4L));
        appUserDTO.setNewPhone(postEmployee.getMobile());
        appUserDTO.setPhone(postEmployee.getMobile());
        appUserDTO.setUsername(postEmployee.getRealname());
        R saveAppUser = remoteTmsAppUserService.updateById(appUserDTO);
        if (saveAppUser.getCode() == 1) {
            return LocalizedR.failed("tms.app.warehouse.update.error", "");
        }

        return R.ok(tmsWarehouseEmployeeService.updateById(postEmployee));
    }

    /**
     * 通过id删除仓库员工
     *
     * @param ids employeeId列表
     * @return R
     */
    @Operation(summary = "通过id删除仓库员工", description = "通过id删除仓库员工")
    @SysLog("通过id删除仓库员工")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tkzjZtPostEmployee_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        // 根据ids批量查询信息
        List<TmsWarehouseEmployeeEntity> employeeEntities = tmsWarehouseEmployeeService.listByIds(CollUtil.toList(ids));
        for (TmsWarehouseEmployeeEntity employee : employeeEntities) {
            // 根据手机号查询系统后台用户账号
            R<AppUserInfo> info = remoteTmsAppUserService.info(employee.getMobile());
            if (info.getCode() == 0) {
                remoteTmsAppUserService.deleteRoles(new Long[]{info.getData().getAppUser().getUserId()},"APP_WAREHOUSE");
            }
        }
        return R.ok(tmsWarehouseEmployeeService.removeBatchByIds(CollUtil.toList(ids)));
    }



    @Operation(summary = "通过手机号查询", description = "通过手机号查询")
    @GetMapping("/getByPhone/{phone}")
    public R getByPhone(@PathVariable("phone") String phone) {
        return R.ok(tmsWarehouseEmployeeService.getByPhone(phone));
    }


    /**
     * 导出excel 表格
     *
     * @param postEmployee 查询条件
     * @param ids          导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tkzjZtPostEmployee_export')")
    public List<TmsWarehouseEmployeeVo> export(TmsWarehouseEmployeeEntity postEmployee, Integer[] ids) {
        return tmsWarehouseEmployeeService.getExcel(postEmployee,ids);
    }
}