package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsDriverLocationEntity;
import com.jygjexp.jynx.tms.service.TmsDriverLocationService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 司机实时经纬度
 *
 * <AUTHOR>
 * @date 2025-03-21 13:51:32
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDriverLocation" )
@Tag(description = "tmsDriverLocation" , name = "司机实时经纬度管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDriverLocationController {

    private final  TmsDriverLocationService tmsDriverLocationService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsDriverLocation 司机实时经纬度
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverLocation_view')" )
    public R getTmsDriverLocationPage(@ParameterObject Page page, @ParameterObject TmsDriverLocationEntity tmsDriverLocation) {
        LambdaQueryWrapper<TmsDriverLocationEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsDriverLocationService.page(page, wrapper));
    }


    /**
     * 通过id查询司机实时经纬度
     * @param driverId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{driverId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverLocation_view')" )
    public R getById(@PathVariable("driverId" ) Long driverId) {
        return R.ok(tmsDriverLocationService.getById(driverId));
    }

    /**
     * 新增司机实时经纬度
     * @param tmsDriverLocation 司机实时经纬度
     * @return R
     */
    @Operation(summary = "新增司机实时经纬度" , description = "新增司机实时经纬度" )
    @SysLog("新增司机实时经纬度" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverLocation_add')" )
    public R save(@RequestBody TmsDriverLocationEntity tmsDriverLocation) {
        return R.ok(tmsDriverLocationService.save(tmsDriverLocation));
    }

    /**
     * 修改司机实时经纬度
     * @param tmsDriverLocation 司机实时经纬度
     * @return R
     */
    @Operation(summary = "修改司机实时经纬度" , description = "修改司机实时经纬度" )
    @SysLog("修改司机实时经纬度" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverLocation_edit')" )
    public R updateById(@RequestBody TmsDriverLocationEntity tmsDriverLocation) {
        return R.ok(tmsDriverLocationService.updateById(tmsDriverLocation));
    }

    /**
     * 通过id删除司机实时经纬度
     * @param ids driverId列表
     * @return R
     */
    @Operation(summary = "通过id删除司机实时经纬度" , description = "通过id删除司机实时经纬度" )
    @SysLog("通过id删除司机实时经纬度" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverLocation_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsDriverLocationService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsDriverLocation 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsDriverLocation_export')" )
    public List<TmsDriverLocationEntity> export(TmsDriverLocationEntity tmsDriverLocation,Long[] ids) {
        return tmsDriverLocationService.list(Wrappers.lambdaQuery(tmsDriverLocation).in(ArrayUtil.isNotEmpty(ids), TmsDriverLocationEntity::getDriverId, ids));
    }
}