package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.app.api.feign.RemoteAppUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.TmsMessageTypeConstants;
import com.jygjexp.jynx.tms.dto.TmsCageLineDto;
import com.jygjexp.jynx.tms.dto.TmsCustomerOrderDto;
import com.jygjexp.jynx.tms.dto.TmsScheduleAppointDto;
import com.jygjexp.jynx.tms.dto.TransferLineHaulOrderDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.*;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.*;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.SnowflakeIdGenerator;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.excel.TmsLineHaulOrderExcelVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 干线任务单
 *
 * <AUTHOR>
 * @date 2025-04-07 18:36:05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TmsLineHaulOrderServiceImpl extends ServiceImpl<TmsLineHaulOrderMapper, TmsLineHaulOrderEntity> implements TmsLineHaulOrderService {
    private final TmsLineHaulOrderMapper tmsLineHaulOrderMapper;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsLmdDriverMapper lmdDriverMapper;
    private final TmsOrderTrackService orderTrackService;
    private final TmsLabelMapper labelMapper;
    private final TmsStorageandorderMapper storageandorderMapper;
    private final TmsOutboundRecordMapper outboundRecordMapper;
    private final TmsInventoryManagementMapper inventoryManagementMapper;
    private final TmsLineHaulOrderMapper lineHaulOrderMapper;
    private final TmsCageAndOrderMapper cageAndOrderMapper;
    private final TmsSiteMapper tmsSiteMapper;
    private final TmsVehicleInfoMapper vehicleInfoMapper;
    private final TmsOrderLineHaulRelationMapper orderLineHaulRelationMapper;
    private final TmsOrderLineHaulRelationService relationService;
    private final TmsVehicleDriverRelationMapper vehicleDriverRelationMapper;
    private final TmsTransferOrderRecordService  transferOrderRecordService;
    private final RemoteAppUserService remoteAppUserService;
    private final TmsMessageMapper  messageMapper;
    private final TmsDriverAssignHistoryService tmsDriverAssignHistoryService;
    // 自定义雪花 ID 生成器
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    // 干线任务单分页查询
    @Override
    public Page<TmsLineHaulOrderPageVo> search(Page page, TmsLineHaulOrderPageVo vo) {
        MPJLambdaWrapper<TmsLineHaulOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsLineHaulOrderEntity.class)
                .like(StrUtil.isNotBlank(vo.getLineHaulNo()), TmsLineHaulOrderEntity::getLineHaulNo, vo.getLineHaulNo())    // 干线单号
                .eq(ObjectUtil.isNotNull(vo.getTransportType()), TmsLineHaulOrderEntity::getTransportType, vo.getTransportType())   // 运输类型：1=整车运输，2=零担运输
                .eq(ObjectUtil.isNotNull(vo.getTaskStatus()), TmsLineHaulOrderEntity::getTaskStatus, vo.getTaskStatus())    // 任务状态
                .eq(ObjectUtil.isNotNull(vo.getDriverId()), TmsLineHaulOrderEntity::getDriverId, vo.getDriverId())  // 司机ID
                .eq(ObjectUtil.isNotNull(vo.getOriginWarehouseId()), TmsLineHaulOrderEntity::getOriginWarehouseId, vo.getOriginWarehouseId())    // 始发地仓库
                .eq(ObjectUtil.isNotNull(vo.getDestWarehouseId()), TmsLineHaulOrderEntity::getDestWarehouseId, vo.getDestWarehouseId())    // 目的地仓库
                .between(ObjectUtil.isNotNull(vo.getPlannedDepartureStartTime()) && ObjectUtil.isNotNull(vo.getPlannedDepartureEndTime()),  // 计划出发时间起止
                        TmsLineHaulOrderEntity::getPlannedDepartureStartTime, vo.getPlannedDepartureStartTime(), vo.getPlannedDepartureEndTime())
                .between(ObjectUtil.isNotNull(vo.getCreateBeginTime()) && ObjectUtil.isNotNull(vo.getCreateEndTime()),            // 搜索框 创建时间
                        TmsLineHaulOrderEntity::getCreateTime, vo.getCreateBeginTime(), vo.getCreateEndTime())
                .like(StrUtil.isNotBlank(vo.getCreateBy()), TmsLineHaulOrderEntity::getCreateBy, vo.getCreateBy())  // 创建人
                .selectAs(TmsLmdDriverEntity::getDriverName, TmsLineHaulOrderPageVo.Fields.driverName)  // 司机
                .selectAs(TmsLmdDriverEntity::getPhone, TmsLineHaulOrderPageVo.Fields.driverPhone)      // 手机号
                .selectAs(TmsVehicleInfoEntity::getLicensePlate, TmsLineHaulOrderPageVo.Fields.licensePlate)    // 车牌号
                .like(StrUtil.isNotBlank(vo.getDriverName()), TmsLineHaulOrderPageVo::getDriverName, vo.getDriverName())
                .like(StrUtil.isNotBlank(vo.getDriverPhone()), TmsLineHaulOrderPageVo::getDriverPhone, vo.getDriverPhone())
                .like(StrUtil.isNotBlank(vo.getContactPhone()), TmsLineHaulOrderPageVo::getContactPhone, vo.getContactPhone())  // 联系电话
                .like(StrUtil.isNotBlank(vo.getLicensePlate()), TmsLineHaulOrderPageVo::getLicensePlate, vo.getLicensePlate())
                .leftJoin(TmsLmdDriverEntity.class, TmsLmdDriverEntity::getDriverId, TmsLineHaulOrderEntity::getDriverId)
                .leftJoin(TmsVehicleDriverRelationEntity.class, TmsVehicleDriverRelationEntity::getDriverId, TmsLmdDriverEntity::getDriverId)
                .leftJoin(TmsVehicleInfoEntity.class, TmsVehicleInfoEntity::getId, TmsVehicleDriverRelationEntity::getVehicleId)
                .orderByDesc(TmsLineHaulOrderEntity::getCreateTime);
        return tmsLineHaulOrderMapper.selectJoinPage(page, TmsLineHaulOrderPageVo.class, wrapper);
    }

    /**
     * 新增干线任务单
     *
     * @param lineOrder
     * @return
     */
    @Override
    public R saveLineHaulOrder(TmsLineHaulOrderAddVo lineOrder) {
        TmsLineHaulOrderEntity taskOrder = new TmsLineHaulOrderEntity();
        String taskOrderNo = null;
        try {
            // 筛选待指派的干线单信息
            List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, lineOrder.getEntrustedOrderNumber())   // 多个跟踪单号
                    .isNull(TmsCustomerOrderEntity::getLineHaulNo)
                    .eq(TmsCustomerOrderEntity::getSubFlag, false));    // 主单

            if (orderList.isEmpty()) {
                return LocalizedR.failed("tms.not.exist.line.task.order", "");
            }

            taskOrder.setId(null);
            // 根据业务类型生成任务单号
            taskOrderNo = generateTaskOrderNo();
            taskOrder.setLineHaulNo(taskOrderNo);

            // 订单绑定干线任务单号
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, lineOrder.getEntrustedOrderNumber())   // 多个跟踪单号
                    .set(TmsCustomerOrderEntity::getLineHaulNo, taskOrderNo)
            );

            // 汇总总体积、数量、重量
            SummaryResultVo summaryResultVo = calculateSummary(orderList);
            taskOrder.setTotalVolume(summaryResultVo.getTotalVolume());
            taskOrder.setTotalWeight(summaryResultVo.getTotalWeight());
            taskOrder.setTotalQuantity(summaryResultVo.getTotalQuantity());

            // 判断始发地仓库和目的地仓库不能相同
            if (lineOrder.getOriginWarehouseId().equals(lineOrder.getDestWarehouseId())) {
                return LocalizedR.failed("tms.origin.warehouse.equals.dest.warehouse", "");
            }

            // 设置任务状态
            taskOrder.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode()); // 待提货
            taskOrder.setCreateTime(LocalDateTime.now());

            // 保存轨迹
/*            for (TmsCustomerOrderEntity order : orderList) {
                orderTrackService.saveTrack(order.getEntrustedOrderNumber(), order.getCustomerOrderNumber(),
                        NewOrderStatus.IN_TRANSIT.getValue(), "", "In transit - generating trunk line tasks", "", 1);
            }*/

            tmsLineHaulOrderMapper.insert(taskOrder);
        } catch (Exception e) {
            log.error("干线任务单创建失败！", e);
            // 回滚：将已更新的订单单号置空（如果有）
            if (taskOrderNo != null) {
                customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, lineOrder.getEntrustedOrderNumber())
                        .set(TmsCustomerOrderEntity::getLineHaulNo, null));
            }
            // 将任务状态和单号置空
            taskOrder.setLineHaulNo(null);
            taskOrder.setTaskStatus(null);
            return LocalizedR.failed("tms.line.task.order.failed", "");
        }
        return LocalizedR.ok("tms.line.task.order.successful", "");
    }

    /**
     * 生成符合规则的任务单号
     * 格式：[客户标识]-[单据类型]-[日期时间戳]-[序列号]
     */
    private String generateTaskOrderNo() {
        String customerCode = "N";

        // 确定单据类型 干线单号默认是T
        String taskTypeCode = "T";
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250330,250331
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6); // 取后 6 位

        // 组合单号
        return String.format("%s%s%s%s", customerCode, taskTypeCode, datePart, shortId); // NT250330645213
    }

    /**
     * 修改干线任务单
     *
     * @param lineOrder
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateLineOrderById(TmsLineHaulOrderAddVo lineOrder) {
        // 新加了的货物信息中的跟踪单信息，需要先查询出来，再对这些订单进行设置单号操作 这些操作前提是必须在待提货状态方可编辑货物操作
        // 先根据干线单号， 查询是否有lineHaulNo单号为空的客户订单的记录，如果有则表示为新增货物了，需要更新
        // 如果没有，则表示减掉货物了，需要置空该减去的货物的客户订单所对应的lineHaulNo
        // 1. 获取当前任务单信息
        TmsLineHaulOrderEntity taskOrder = tmsLineHaulOrderMapper.selectById(lineOrder.getId());
        if (null == taskOrder) {
            return LocalizedR.failed("tms.line.task.order.not.exist", "");
        }
        if (!Objects.equals(taskOrder.getTaskStatus(), TransportTaskStatus.PENDING_PICKUP.getCode())
                && !Objects.equals(taskOrder.getTaskStatus(), TransportTaskStatus.CANCELLED.getCode())) {
            return LocalizedR.failed("tms.line.task.only.pending.allocation", "");
        }

        try {
            // 2. 同步基础字段
            Optional.ofNullable(lineOrder.getTransportType()).ifPresent(taskOrder::setTransportType);
            Optional.ofNullable(lineOrder.getOriginWarehouseId()).ifPresent(taskOrder::setOriginWarehouseId);
            Optional.ofNullable(lineOrder.getDestWarehouseId()).ifPresent(taskOrder::setDestWarehouseId);
            // 判断始发地仓库和目的地仓库不能相同
            if (lineOrder.getOriginWarehouseId().equals(lineOrder.getDestWarehouseId())) {
                return LocalizedR.failed("tms.origin.warehouse.equals.dest.warehouse", "");
            }

            Optional.ofNullable(lineOrder.getPlannedDepartureStartTime()).ifPresent(taskOrder::setPlannedDepartureStartTime);
            Optional.ofNullable(lineOrder.getPlannedDepartureEndTime()).ifPresent(taskOrder::setPlannedDepartureEndTime);

            // 3. 处理订单变更（核心逻辑）
            if (CollectionUtils.isNotEmpty(lineOrder.getEntrustedOrderNumber())) {
                // 3.1 获取当前绑定的所有订单
//                List<TmsCustomerOrderEntity> currentOrders = customerOrderMapper.selectList(
//                        new LambdaQueryWrapper<TmsCustomerOrderEntity>()
//                                .eq(TmsCustomerOrderEntity::getLineHaulNo, taskOrder.getLineHaulNo())
//                );
                // 3.1 获取当前绑定的所有订单
                List<TmsCustomerOrderEntity> currentOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, relationService.lambdaQuery().eq(TmsOrderLineHaulRelationEntity::getLineHaulNo, taskOrder.getLineHaulNo())
                                .list().stream().map(TmsOrderLineHaulRelationEntity::getEntrustedOrderNumber).collect(Collectors.toList()))
                );

                // 3.2 计算需要解绑的订单（当前有但新列表中没有的）
                List<String> currentOrderNos = currentOrders.stream()
                        .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                        .collect(Collectors.toList());
                List<String> ordersToUnbind = currentOrderNos.stream()
                        .filter(no -> !lineOrder.getEntrustedOrderNumber().contains(no))
                        .collect(Collectors.toList());

                // 3.3 计算需要绑定的订单（新列表中有但当前没有的）
                List<String> ordersToBind = lineOrder.getEntrustedOrderNumber().stream()
                        .filter(no -> !currentOrderNos.contains(no))
                        .collect(Collectors.toList());

                // 3.4 执行解绑操作（减掉货物）
                if (CollectionUtils.isNotEmpty(ordersToUnbind)) {
//                    customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
//                            .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, ordersToUnbind)
//                            .set(TmsCustomerOrderEntity::getLineHaulNo, null)
//                    );
                    relationService.lambdaUpdate()
                            .in(TmsOrderLineHaulRelationEntity::getEntrustedOrderNumber, ordersToUnbind)
                            .eq(TmsOrderLineHaulRelationEntity::getLineHaulNo, taskOrder.getLineHaulNo())
                            .remove();
                }

                // 3.5 执行绑定操作（新增货物）
                if (CollectionUtils.isNotEmpty(ordersToBind)) {
                    // 验证订单可绑定（未被绑定且是主单）
                    List<TmsCustomerOrderEntity> bindableOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, ordersToBind)
                            .eq(TmsCustomerOrderEntity::getSubFlag, false)
                    );

                    if (bindableOrders.size() != ordersToBind.size()) {
                        return LocalizedR.failed("tms.order.some.not.bindable", "");
                    }

//                    customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
//                            .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, ordersToBind)
//                            .set(TmsCustomerOrderEntity::getLineHaulNo, taskOrder.getLineHaulNo())
//                    );
                    List<TmsOrderLineHaulRelationEntity> newRelations = bindableOrders.stream()
                            .map(order -> {
                                TmsOrderLineHaulRelationEntity relation = new TmsOrderLineHaulRelationEntity();
                                relation.setCustomerOrderId(order.getId());
                                relation.setCustomerOrderNumber(order.getCustomerOrderNumber());
                                relation.setEntrustedOrderNumber(order.getEntrustedOrderNumber());
                                relation.setLineHaulNo(taskOrder.getLineHaulNo());
                                return relation;
                            }).collect(Collectors.toList());

                    relationService.saveBatch(newRelations);
                }

                // 3.6 重新计算总量（自动更新）
//                List<TmsCustomerOrderEntity> updatedOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
//                        .eq(TmsCustomerOrderEntity::getLineHaulNo, taskOrder.getLineHaulNo())
//                );
                List<TmsCustomerOrderEntity> updatedOrders = customerOrderMapper.selectBatchIds(
                        relationService.lambdaQuery().eq(TmsOrderLineHaulRelationEntity::getLineHaulNo, taskOrder.getLineHaulNo())
                                .list().stream().map(TmsOrderLineHaulRelationEntity::getCustomerOrderId).collect(Collectors.toList())
                );

                // 计算新的总量
                int totalQuantity = updatedOrders.stream().mapToInt(TmsCustomerOrderEntity::getCargoQuantity).sum();
                BigDecimal totalWeight = updatedOrders.stream().map(TmsCustomerOrderEntity::getTotalWeight)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalVolume = updatedOrders.stream().map(TmsCustomerOrderEntity::getTotalVolume)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 更新干线单总量
                taskOrder.setTotalQuantity(totalQuantity);
                taskOrder.setTotalWeight(totalWeight);
                taskOrder.setTotalVolume(totalVolume);
            }

            // 4. 验证总量数据
            if (taskOrder.getTotalQuantity() != null && taskOrder.getTotalQuantity() < 0) {
                return LocalizedR.failed("tms.line.task.not.greater.than.zero", "");
            }
            if (taskOrder.getTotalWeight() != null && taskOrder.getTotalWeight().compareTo(BigDecimal.ZERO) < 0) {
                return LocalizedR.failed("tms.line.task.not.greater.than.zero", "");
            }
            if (taskOrder.getTotalVolume() != null && taskOrder.getTotalVolume().compareTo(BigDecimal.ZERO) < 0) {
                return LocalizedR.failed("tms.line.task.not.greater.than.zero", "");
            }

            // 5. 更新任务单
            taskOrder.setUpdateTime(LocalDateTime.now());
            tmsLineHaulOrderMapper.updateById(taskOrder);

        } catch (Exception e) {
            log.error("干线任务单更新失败", e);
            return LocalizedR.failed("tms.line.task.order.update.failed", "");
        }

        return LocalizedR.ok("tms.line.task.order.update.success", "");
    }

    // 根据id查询干线详情
    @Override
    public R selectById(Long id) {
//        TmsLineHaulOrderEntity taskOrder = tmsLineHaulOrderMapper.selectById(id);
//
//        // 根据干线单号查询订单信息
//        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(new MPJLambdaWrapper<TmsCustomerOrderEntity>()
//                .eq(TmsCustomerOrderEntity::getLineHaulNo, taskOrder.getLineHaulNo())
//        );
//
//        // 获取司机车辆信息
//        MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = new MPJLambdaWrapper<>();
//        wrapper.eq(TmsLmdDriverEntity::getDriverId, taskOrder.getDriverId())
//                .leftJoin(TmsVehicleInfoEntity.class, TmsVehicleInfoEntity::getDriverId, TmsLmdDriverEntity::getDriverId);
//        TmsLmdDriverVehicleDTO driver = lmdDriverMapper.selectJoinOne(TmsLmdDriverVehicleDTO.class, wrapper);
//
//        Map<String, Object> driverInfoMap = new LinkedHashMap<>();  // 保持字段顺序
//        driverInfoMap.put("driverName", driver != null ? driver.getDriverName() : null);
//        driverInfoMap.put("contactPhone", driver != null ? driver.getContactPhone() : null);
//        driverInfoMap.put("licensePlate", driver != null ? driver.getLicensePlate() : null);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("taskOrder", taskOrder);
//        result.put("driverInfo", driverInfoMap);
//        if (CollUtil.isNotEmpty(orderList)) {
//            result.put("orderList", orderList);
//        }
//
//        return R.ok(result);

        // 1. 查询干线任务单基本信息
        TmsLineHaulOrderEntity taskOrder = tmsLineHaulOrderMapper.selectById(id);
        if (taskOrder == null) {
            return R.failed("干线任务单不存在");
        }

        // 2. 通过关联表查询关联的订单ID列表
        List<Long> orderIds = relationService.lambdaQuery()
                .eq(TmsOrderLineHaulRelationEntity::getLineHaulNo, taskOrder.getLineHaulNo())
                .list()
                .stream()
                .map(TmsOrderLineHaulRelationEntity::getCustomerOrderId)
                .collect(Collectors.toList());

        // 3. 查询订单详细信息
        List<TmsCustomerOrderEntity> orderList = CollectionUtils.isEmpty(orderIds) ? Collections.emptyList() : customerOrderMapper.selectBatchIds(orderIds);

        // 4. 获取司机车辆信息
        MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.eq(TmsLmdDriverEntity::getDriverId, taskOrder.getDriverId())
                .selectAs(TmsVehicleInfoEntity::getLicensePlate,TmsLmdDriverVehicleDTO::getLicensePlate)
                .selectAs(TmsLmdDriverEntity::getDriverName,TmsLmdDriverVehicleDTO::getDriverName)
                .selectAs(TmsLmdDriverEntity::getPhone,TmsLmdDriverVehicleDTO::getContactPhone)
                .leftJoin(TmsVehicleDriverRelationEntity.class, TmsVehicleDriverRelationEntity::getDriverId, TmsLmdDriverEntity::getDriverId)
                .leftJoin(TmsVehicleInfoEntity.class, TmsVehicleInfoEntity::getId, TmsVehicleDriverRelationEntity::getVehicleId);
        TmsLmdDriverVehicleDTO driver = lmdDriverMapper.selectJoinOne(TmsLmdDriverVehicleDTO.class, wrapper);

        // 5. 组装结果
        Map<String, Object> driverInfoMap = new LinkedHashMap<>();  // 保持字段顺序
        driverInfoMap.put("driverName", driver != null ? driver.getDriverName() : null);
        driverInfoMap.put("contactPhone", driver != null ? driver.getContactPhone() : null);
        driverInfoMap.put("licensePlate", driver != null ? driver.getLicensePlate() : null);

        Map<String, Object> result = new HashMap<>();
        result.put("taskOrder", taskOrder);
        result.put("driverInfo", driverInfoMap);
        if (CollUtil.isNotEmpty(orderList)) {
            result.put("orderList", orderList);
        }

        return R.ok(result);
    }

    /**
     * 汇总总体积、数量、重量
     */
    private SummaryResultVo calculateSummary(List<TmsCustomerOrderEntity> customerList) {
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalVolume = BigDecimal.ZERO;
        Integer totalQuantity = 0;
        for (TmsCustomerOrderEntity customerOrder : customerList) {
            // 直接累加货物的重量
            totalWeight = totalWeight.add(customerOrder.getTotalWeight());
            // 直接累加货物的体积
            totalVolume = totalVolume.add(customerOrder.getTotalVolume());
            // 直接累加货物的数量
            totalQuantity = totalQuantity + customerOrder.getCargoQuantity();
        }
        SummaryResultVo result = new SummaryResultVo();
        result.setTotalWeight(totalWeight);
        result.setTotalVolume(totalVolume);
        result.setTotalQuantity(totalQuantity);

        return result;
    }

    /**
     * 干线任务跟踪单列表分页
     *
     * @return
     */
    @Override
    public Page<TmsCustomerOrderDto> listCustomerOrder(Page page, TmsCustomerOrderDto vo) {
//        // 筛选待指派的干线单信息
//        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
//        wrapper.selectAll(TmsCustomerOrderEntity.class)
//                .eq(TmsCustomerOrderEntity::getSubFlag, false)  // 主单
//                .like(StrUtil.isNotBlank(vo.getCustomerOrderNumber()), TmsCustomerOrderDto::getCustomerOrderNumber, vo.getCustomerOrderNumber())     // 客户单号
//                .like(StrUtil.isNotBlank(vo.getEntrustedOrderNumber()), TmsCustomerOrderDto::getEntrustedOrderNumber, vo.getEntrustedOrderNumber())  // 跟踪单号
//                .like(StrUtil.isNotBlank(vo.getOriginWarehouse()), TmsCustomerOrderDto::getOriginWarehouse, vo.getOriginWarehouse())    // 始发地仓库
//                .like(StrUtil.isNotBlank(vo.getDestWarehouse()), TmsCustomerOrderDto::getDestWarehouse, vo.getDestWarehouse())          // 目的地仓库
//                .leftJoin(TmsLineHaulOrderEntity.class, TmsLineHaulOrderEntity::getLineHaulNo, TmsCustomerOrderEntity::getLineHaulNo)   // 干线单号
//                .like(StrUtil.isNotBlank(vo.getOrigin()), TmsCustomerOrderEntity::getOrigin, vo.getOrigin())    // 始发地
//                .like(StrUtil.isNotBlank(vo.getDestination()), TmsCustomerOrderEntity::getDestination, vo.getDestination()) // 目的地
//                .isNull(TmsLineHaulOrderEntity::getLineHaulNo);
//        return customerOrderMapper.selectJoinPage(page, TmsCustomerOrderDto.class, wrapper);

        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();

        // 查询未关联任何干线单的订单（通过关联表判断）
        wrapper.selectAll(TmsCustomerOrderEntity.class)
                .eq(TmsCustomerOrderEntity::getSubFlag, false)  // 主单
                .notInSql(TmsCustomerOrderEntity::getId,
                        "SELECT customer_order_id FROM tms_order_line_haul_relation") // 未关联的订单
                .like(StrUtil.isNotBlank(vo.getCustomerOrderNumber()), TmsCustomerOrderDto::getCustomerOrderNumber, vo.getCustomerOrderNumber())
                .like(StrUtil.isNotBlank(vo.getEntrustedOrderNumber()), TmsCustomerOrderDto::getEntrustedOrderNumber, vo.getEntrustedOrderNumber())
                .like(StrUtil.isNotBlank(vo.getOriginWarehouse()), TmsCustomerOrderDto::getOriginWarehouse, vo.getOriginWarehouse())
                .like(StrUtil.isNotBlank(vo.getDestWarehouse()), TmsCustomerOrderDto::getDestWarehouse, vo.getDestWarehouse())
                .like(StrUtil.isNotBlank(vo.getOrigin()), TmsCustomerOrderEntity::getOrigin, vo.getOrigin())
                .like(StrUtil.isNotBlank(vo.getDestination()), TmsCustomerOrderEntity::getDestination, vo.getDestination())
                .orderByDesc(TmsCustomerOrderEntity::getCreateTime);

        return customerOrderMapper.selectJoinPage(page, TmsCustomerOrderDto.class, wrapper);
    }

    /**
     * 取消干线任务
     *
     * @param ids
     * @return
     */
    @Override
    public R cancelLineHaulOrder(List<Long> ids) {
        // 校验只有在待提货状态才能取消
        for (Long id : ids) {
            TmsLineHaulOrderEntity taskOrder = tmsLineHaulOrderMapper.selectById(id);
            if (taskOrder.getTaskStatus() != TransportTaskStatus.PENDING_PICKUP.getCode()) {
                return LocalizedR.failed("tms.line.task.only.pending.allocation", "");
            }
            taskOrder.setTaskStatus(TransportTaskStatus.CANCELLED.getCode());
            tmsLineHaulOrderMapper.updateById(taskOrder);
        }
        return R.ok(Boolean.TRUE);
    }

    /**
     * 新增多个干线任务单
     *
     * @param lineOrder
     * @return
     */
    @Override
    public R saveManyLineHaulOrder(TmsLineHaulOrderAddVo lineOrder) {
        // 1. 验证订单是否存在且是主单
        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, lineOrder.getEntrustedOrderNumber())
                        .eq(TmsCustomerOrderEntity::getSubFlag, false));

        if (orderList.isEmpty()) {
            return LocalizedR.failed("tms.not.exist.line.task.order", "");
        }

        // 2. 生成唯一干线单号
        String taskOrderNo = generateTaskOrderNo();
        try {
            // 3. 创建干线任务单
            TmsLineHaulOrderEntity taskOrder = new TmsLineHaulOrderEntity();
            taskOrder.setLineHaulNo(taskOrderNo);
            taskOrder.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());

            // 4. 仓库校验
            if (ObjectUtil.equal(lineOrder.getOriginWarehouseId(), lineOrder.getDestWarehouseId())) {
                return LocalizedR.failed("tms.origin.warehouse.equals.dest.warehouse", "");
            }
            taskOrder.setOriginWarehouseId(lineOrder.getOriginWarehouseId());
            taskOrder.setDestWarehouseId(lineOrder.getDestWarehouseId());

            // 5. 计算运输数据
            SummaryResultVo summary = calculateSummary(orderList);
            taskOrder.setTotalVolume(summary.getTotalVolume());
            taskOrder.setTotalWeight(summary.getTotalWeight());
            taskOrder.setTotalQuantity(summary.getTotalQuantity());
            // 运输类型
            taskOrder.setTransportType(lineOrder.getTransportType());

            // 6. 保存干线单
            tmsLineHaulOrderMapper.insert(taskOrder);

            // 7. 创建关联关系（不检查任何绑定限制）
            List<Long> orderIds = orderList.stream().map(TmsCustomerOrderEntity::getId).collect(Collectors.toList());

            relationService.batchCreateRelation(orderIds, taskOrderNo, 1); // 1表示主任务单

            // 保存轨迹
/*            for (TmsCustomerOrderEntity order : orderList) {
                orderTrackService.saveTrack(order.getEntrustedOrderNumber(), order.getCustomerOrderNumber(),
                        NewOrderStatus.IN_TRANSIT.getValue(), "", "In transit - generating trunk line tasks", "", 1);
            }*/

            return LocalizedR.ok("tms.line.task.order.successful", "");

        } catch (Exception e) {
            log.error("干线任务单创建失败", e);
            // 回滚关联关系
            relationService.deleteRelationsByLineHaulNo(taskOrderNo);
            return LocalizedR.failed("tms.line.task.order.failed", e.getMessage());
        }

    }

    @Override
    public R uploadLineProof(String lineHaulOrderNo, String pickupProof,String deliveryProof) {
        //找到该标签，将其设置为干线已扫描提货状态
        TmsLabelEntity tmsLabel = labelMapper.selectOne(new LambdaUpdateWrapper<TmsLabelEntity>().eq(TmsLabelEntity::getLabelCode, lineHaulOrderNo));

        //更新标签状态为干线已扫描提货
        tmsLabel.setIsPickupProof(1);
        labelMapper.updateById(tmsLabel);

        //干线任务
        TmsLineHaulOrderEntity tmsLineHaulOrderEntity = lineHaulOrderMapper.selectOne(new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                .eq(TmsLineHaulOrderEntity::getLineHaulNo, lineHaulOrderNo), false);

        if(ObjectUtil.isNotNull(tmsLineHaulOrderEntity)&& StrUtil.isNotBlank(pickupProof) &&StrUtil.isBlank(pickupProof)){
            //只上传提货证明
            tmsLineHaulOrderEntity.setPickupTime(LocalDateTime.now());
            tmsLineHaulOrderEntity.setPickupProof(pickupProof);
            tmsLineHaulOrderEntity.setTaskStatus(TransportTaskStatus.IN_TRANSIT.getCode());
        }else if(ObjectUtil.isNotNull(tmsLineHaulOrderEntity)&& StrUtil.isBlank(pickupProof) &&StrUtil.isNotBlank(pickupProof)){
            //只上传干线派送证明
            tmsLineHaulOrderEntity.setDeliveryTime(LocalDateTime.now());
            tmsLineHaulOrderEntity.setDeliveryProof(deliveryProof);
            //完成状态
            tmsLineHaulOrderEntity.setTaskStatus(TransportTaskStatus.DELIVERED.getCode());
        }
        else if(ObjectUtil.isNotNull(tmsLineHaulOrderEntity)&& StrUtil.isBlank(pickupProof) &&StrUtil.isBlank(pickupProof)){
            throw new CustomBusinessException("两者证明必须上传一个！");
        }
        else if(ObjectUtil.isNotNull(tmsLineHaulOrderEntity)&& StrUtil.isNotBlank(pickupProof) &&StrUtil.isNotBlank(pickupProof)){
            //提货和派送同时上传
            tmsLineHaulOrderEntity.setPickupTime(LocalDateTime.now());
            tmsLineHaulOrderEntity.setPickupProof(pickupProof);
            tmsLineHaulOrderEntity.setDeliveryTime(LocalDateTime.now());
            tmsLineHaulOrderEntity.setDeliveryProof(deliveryProof);
            //完成状态
            tmsLineHaulOrderEntity.setTaskStatus(TransportTaskStatus.DELIVERED.getCode());
        }
        //更新干线任务
        lineHaulOrderMapper.updateById(tmsLineHaulOrderEntity);
        return R.ok();
    }


    // 根据订单号获取司机
    @Override
    public List<TmsLmdDriverPageVo> getDriverByOrderNo(String orderNo) {
        //查询干线关联表对应的单号
        List<String> lineHaulerOrderNo = getLineHaulerOrderNo(orderNo);
        if (CollectionUtils.isEmpty(lineHaulerOrderNo)){
            return Collections.emptyList();
        }
        MPJLambdaWrapper<TmsLineHaulOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(TmsLmdDriverEntity::getDriverName, TmsLmdDriverEntity::getPhone)
                .select(TmsVehicleInfoEntity::getLicensePlate)
                .in(TmsLineHaulOrderEntity::getLineHaulNo, lineHaulerOrderNo)
                .leftJoin(TmsVehicleDriverRelationEntity.class, TmsVehicleDriverRelationEntity::getDriverId, TmsLineHaulOrderEntity::getDriverId)
                .leftJoin(TmsVehicleInfoEntity.class, TmsVehicleInfoEntity::getId, TmsVehicleDriverRelationEntity::getVehicleId)
                .leftJoin(TmsLmdDriverEntity.class, TmsLmdDriverEntity::getDriverId, TmsLineHaulOrderEntity::getDriverId);
        return lineHaulOrderMapper.selectJoinList(TmsLmdDriverPageVo.class, wrapper);
    }


    //获取干线单号
    private List<String>  getLineHaulerOrderNo(String orderNo){
        LambdaQueryWrapper<TmsOrderLineHaulRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsOrderLineHaulRelationEntity::getEntrustedOrderNumber, orderNo);
        List<TmsOrderLineHaulRelationEntity> list = relationService.list(wrapper);
        return list.stream().map(TmsOrderLineHaulRelationEntity::getLineHaulNo).collect(Collectors.toList());
    }

    /**
     * 干线任务转单
     * @param transferLineHaulOrderDto
     * @return
     */
    @Transactional
    @Override
    public R transferLineHaulOrder(TransferLineHaulOrderDto transferLineHaulOrderDto) {
        //根据任务单号查询勾选的所有任务单
        List<TmsLineHaulOrderEntity> tmsLineHaulOrders = lineHaulOrderMapper.selectList(new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                .in(TmsLineHaulOrderEntity::getLineHaulNo, transferLineHaulOrderDto.getLineHaulNos()));
        if(ObjectUtil.isNull(tmsLineHaulOrders) || tmsLineHaulOrders.isEmpty()){
            return R.failed(LocalizedR.getMessage("error.lineHaulOrder.notEmpty",null));
        }
        //判断任务单集合中是否有任务单状态为已完成状态的
        boolean isContainsCompletedTask = tmsLineHaulOrders.stream()
                .anyMatch(tmsLineHaulOrderEntity -> tmsLineHaulOrderEntity.getTaskStatus().equals(TransportTaskStatus.DELIVERED.getCode()));
        if(isContainsCompletedTask){
            return R.failed(LocalizedR.getMessage("error.lineHaulOrder.completedExists",null));
        }
        //判断是否全部已经指派了司机，即司机id不能为空
        boolean isAllAssigned = tmsLineHaulOrders.stream()
                .allMatch(tmsLineHaulOrderEntity -> ObjectUtil.isNotNull(tmsLineHaulOrderEntity.getDriverId()));
        if(!isAllAssigned){
            return R.failed(LocalizedR.getMessage("error.unassigned.driver",null));
        }
        //根据司机id获取其司机相关信息
        TmsLmdDriverEntity tmsLmdDriverEntity = lmdDriverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverId, transferLineHaulOrderDto.getDriverId()));
        TmsVehicleInfoEntity vehicleInfo=null;
        if(ObjectUtil.isNotNull(tmsLmdDriverEntity)){
            //获取司机的所属车辆信息
            TmsVehicleDriverRelationEntity tmsVehicleDriverRelation = vehicleDriverRelationMapper.selectOne(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                    .eq(TmsVehicleDriverRelationEntity::getDriverId, tmsLmdDriverEntity.getDriverId()), false);
            vehicleInfo = vehicleInfoMapper.selectOne(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                    .eq(TmsVehicleInfoEntity::getId, tmsVehicleDriverRelation.getVehicleId()));
        }
        //根据司机对干线任务进行分组
        Map<Long, List<TmsLineHaulOrderEntity>> driverIdToLineHaulOrdersMap = tmsLineHaulOrders.stream()
                .collect(Collectors.groupingBy(TmsLineHaulOrderEntity::getDriverId));
        //干线任务包含的司机
        Set<Long> driverIds = driverIdToLineHaulOrdersMap.keySet();
        Map<Long, TmsLmdDriverEntity> driverMap = lmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .in(TmsLmdDriverEntity::getDriverId, driverIds)).stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));

        for (Long driverId : driverIds) {
            //保存转单轨迹记录
            //从干线任务与订单关系表中找到该任务批次下的所有订单
            if(ObjectUtil.isNotNull(driverIdToLineHaulOrdersMap.get(driverId)) && !driverIdToLineHaulOrdersMap.get(driverId).isEmpty()){
                List<TmsLineHaulOrderEntity> tmsLineHaulOrderList = driverIdToLineHaulOrdersMap.get(driverId);
                List<String> lineHaulNos = tmsLineHaulOrderList.stream().map(TmsLineHaulOrderEntity::getLineHaulNo).collect(Collectors.toList());
                Set<String> entrustedOrderNumbers=new HashSet<>();
                if(ObjectUtil.isNotNull(lineHaulNos) && !lineHaulNos.isEmpty()){
                    entrustedOrderNumbers= orderLineHaulRelationMapper.selectList(new LambdaQueryWrapper<TmsOrderLineHaulRelationEntity>()
                                    .in(TmsOrderLineHaulRelationEntity::getLineHaulNo, lineHaulNos))
                            .stream().map(TmsOrderLineHaulRelationEntity::getEntrustedOrderNumber).collect(Collectors.toSet());
                    if(ObjectUtil.isNotNull(entrustedOrderNumbers) && !entrustedOrderNumbers.isEmpty()){
                        //根据跟踪单号查询出所有客户单(主单)
                        List<TmsCustomerOrderEntity> customerOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE)
                                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));
                        //记录转单轨迹
                        for (TmsCustomerOrderEntity customerOrder : customerOrders) {
                            //保存转单的轨迹
                            orderTrackService.saveTrack(customerOrder.getEntrustedOrderNumber(), customerOrder.getCustomerOrderNumber(),
                                    NewOrderStatus.IN_TRANSIT.getValue(), "", "Change the main line driver from 【"+driverMap.get(driverId).getDriverName()+"】 to 【"+tmsLmdDriverEntity.getDriverName()+"】"
                                    ,"Change the main line driver from 【"+driverMap.get(driverId).getDriverName()+"】 to 【"+tmsLmdDriverEntity.getDriverName()+"】", 1);
                        }
                    }


                    //干线任务转单记录
                    List<TmsTransferOrderRecordEntity> tmsTransferOrderRecords = new ArrayList<>();
                    for (TmsLineHaulOrderEntity tmsLineHaulOrderEntity : tmsLineHaulOrderList) {
                        TmsTransferOrderRecordEntity tmsTransferOrderRecordEntity = new TmsTransferOrderRecordEntity();
                        tmsTransferOrderRecordEntity.setOrderNo(tmsLineHaulOrderEntity.getLineHaulNo());
                        //原司机
                        tmsTransferOrderRecordEntity.setOldDriverId(driverMap.get(driverId).getDriverId());
                        tmsTransferOrderRecordEntity.setOldDriverName(driverMap.get(driverId).getDriverName());
                        //当前司机
                        tmsTransferOrderRecordEntity.setCurrentDriverId(tmsLmdDriverEntity.getDriverId());
                        tmsTransferOrderRecordEntity.setCurrentDriverName(tmsLmdDriverEntity.getDriverName());
                        //干线类型
                        tmsTransferOrderRecordEntity.setType(TaskType.DRYLINE.getCode());
                        tmsTransferOrderRecordEntity.setReason("干线转单");
                        tmsTransferOrderRecordEntity.setCreateBy(SecurityUtils.getUser().getUsername());
                        tmsTransferOrderRecordEntity.setCreateTime(LocalDateTime.now());
                        tmsTransferOrderRecords.add(tmsTransferOrderRecordEntity);
                    }
                    transferOrderRecordService.saveBatch(tmsTransferOrderRecords);

                }
            }

        }
        //转单更换司机及其相关信息
        for (TmsLineHaulOrderEntity tmsLineHaulOrder : tmsLineHaulOrders) {
            tmsLineHaulOrder.setDriverId(transferLineHaulOrderDto.getDriverId());
            //司机联系方式
            tmsLineHaulOrder.setContactPhone(tmsLmdDriverEntity.getPhone());
            //车牌号
            if(ObjectUtil.isNotNull(vehicleInfo)){
                tmsLineHaulOrder.setLicensePlate(vehicleInfo.getLicensePlate());
            }
        }
        //批量更新任务单中司机信息
        boolean updated = this.updateBatchById(tmsLineHaulOrders);

        if(updated){
            return R.ok(Boolean.TRUE);
        }else{
            return R.failed("转单失败！");
        }
    }

    /**
     * 根据单号查询干线详情
     */
    @Override
    public R getTaskOrderByLineHaulNo(String lineHaulNo) {
        TmsScheduleMainLineVo tmsScheduleMainLineVo = new TmsScheduleMainLineVo();
        TmsLineHaulOrderEntity tmsLineHaulOrder = this.getOne(new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                .eq(TmsLineHaulOrderEntity::getLineHaulNo, lineHaulNo));
        //起点仓库
        TmsSiteEntity OriginWarehouse = tmsSiteMapper.selectOne(new LambdaQueryWrapper<TmsSiteEntity>()
                .eq(TmsSiteEntity::getId, tmsLineHaulOrder.getOriginWarehouseId()));
        //终点仓库
        TmsSiteEntity DestinationWarehouse = tmsSiteMapper.selectOne(new LambdaQueryWrapper<TmsSiteEntity>()
                .eq(TmsSiteEntity::getId, tmsLineHaulOrder.getDestWarehouseId()));
        if(ObjectUtil.isNotNull(OriginWarehouse)){
            tmsScheduleMainLineVo.setOriginWarehouseName(OriginWarehouse.getSiteName());
            tmsScheduleMainLineVo.setOriginWarehouseId(OriginWarehouse.getId());
        }
        if(ObjectUtil.isNotNull(DestinationWarehouse)){
            tmsScheduleMainLineVo.setDestWarehouseName(DestinationWarehouse.getSiteName());
            tmsScheduleMainLineVo.setDestWarehouseId(DestinationWarehouse.getId());
        }

        //根据客户单号查询出所有客户单
        List<String> entrustedOrderNumbers = cageAndOrderMapper.selectList(new LambdaQueryWrapper<TmsCageAndOrderEntity>()
                .eq(TmsCageAndOrderEntity::getCageCode, lineHaulNo)).stream().map(TmsCageAndOrderEntity::getOrderNo).collect(Collectors.toList());
        List<TmsCustomerOrderEntity> allCustomerOrders= new ArrayList<>();
        if(ObjectUtil.isNotNull(entrustedOrderNumbers) && CollUtil.isNotEmpty(entrustedOrderNumbers)){
            //根据客户单号查询出所有客户单（主单主单和子单）
            allCustomerOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers)
                    .eq(TmsCustomerOrderEntity::getSubFlag, false)
            );
        }
        BeanUtils.copyProperties(tmsLineHaulOrder, tmsScheduleMainLineVo);
        tmsScheduleMainLineVo.setOrderList(allCustomerOrders);
        return R.ok(tmsScheduleMainLineVo);
    }

    @Override
    public R getLineHaulList(Page page, TmsCageLineDto tmsCageLineDto) {
        //查询所有待指派的干线任务
        LambdaQueryWrapper<TmsLineHaulOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotNull(tmsCageLineDto.getOriginWarehouseId()), TmsLineHaulOrderEntity::getOriginWarehouseId, tmsCageLineDto.getOriginWarehouseId())
                .eq(ObjectUtil.isNotNull(tmsCageLineDto.getDestWarehouseId()), TmsLineHaulOrderEntity::getDestWarehouseId, tmsCageLineDto.getDestWarehouseId())
                //容器编码(干线任务单号)
                .eq(StrUtil.isNotBlank(tmsCageLineDto.getCageCode()),TmsLineHaulOrderEntity::getLineHaulNo, tmsCageLineDto.getCageCode())
                //待提货
                .eq(TmsLineHaulOrderEntity::getTaskStatus, TransportTaskStatus.PENDING_PICKUP.getCode())
                //未指派司机的
                .isNull(TmsLineHaulOrderEntity::getDriverId)
                .between(ObjectUtil.isNotNull(tmsCageLineDto.getCreateStartTime()) && ObjectUtil.isNotNull(tmsCageLineDto.getCreateFinishTime()),
                        TmsLineHaulOrderEntity::getCreateTime, tmsCageLineDto.getCreateStartTime(), tmsCageLineDto.getCreateFinishTime())
                //倒序
                .orderByDesc(TmsLineHaulOrderEntity::getCreateTime);
        Page newPage = page(page, wrapper);
        List<TmsLineHaulOrderEntity> records = newPage.getRecords();
        List<TmsCageLineVo> tmsCageLineVos = new ArrayList<>();
        if(ObjectUtil.isNotNull(records) && !records.isEmpty()){
            //起点仓库
            List<Long> originWarehouseIds = records.stream().map(TmsLineHaulOrderEntity::getOriginWarehouseId).collect(Collectors.toList());
            Map<Long, TmsSiteEntity> originWarehouseMap = tmsSiteMapper.selectList(new LambdaQueryWrapper<TmsSiteEntity>()
                    .in(TmsSiteEntity::getId, originWarehouseIds)).stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));
            //终点仓库
            List<Long> destWarehouseIds = records.stream().map(TmsLineHaulOrderEntity::getDestWarehouseId).collect(Collectors.toList());
            Map<Long, TmsSiteEntity> destWarehouseMap = tmsSiteMapper.selectList(new LambdaQueryWrapper<TmsSiteEntity>()
                    .in(TmsSiteEntity::getId, destWarehouseIds)).stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));
            records.forEach(tmsLineHaulOrderEntity -> {
                TmsCageLineVo tmsCageLineVo = new TmsCageLineVo();
                BeanUtils.copyProperties(tmsLineHaulOrderEntity, tmsCageLineVo);
                if(ObjectUtil.isNotNull(originWarehouseMap.get(tmsLineHaulOrderEntity.getOriginWarehouseId()))){
                    //起点仓库名称
                    tmsCageLineVo.setOriginWarehouseName(originWarehouseMap.get(tmsLineHaulOrderEntity.getOriginWarehouseId()).getSiteName());
                }
                if(ObjectUtil.isNotNull(destWarehouseMap.get(tmsLineHaulOrderEntity.getDestWarehouseId()))){
                    //终点仓库名称
                    tmsCageLineVo.setDestWarehouseName(destWarehouseMap.get(tmsLineHaulOrderEntity.getDestWarehouseId()).getSiteName());
                }
                tmsCageLineVos.add(tmsCageLineVo);
            });

        }
        return R.ok(newPage.setRecords(tmsCageLineVos));
    }

    /**
     * 干线指派
     */
    @Transactional
    @Override
    public R mainLineAppoint(TmsScheduleAppointDto tmsScheduleAppointDto) {
        //查询勾选的干线任务单
        List<TmsLineHaulOrderEntity> tmsLineHaulOrders = this.list(new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                .in(TmsLineHaulOrderEntity::getLineHaulNo,tmsScheduleAppointDto.getCageNoList()));
        TmsVehicleDriverRelationEntity tmsVehicleDriverRelation = vehicleDriverRelationMapper.selectOne(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                .eq(TmsVehicleDriverRelationEntity::getDriverId, tmsScheduleAppointDto.getDriverId()));
        TmsVehicleInfoEntity vehicleInfo = vehicleInfoMapper.selectOne(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                .in(TmsVehicleInfoEntity::getId, tmsVehicleDriverRelation.getVehicleId()));
        TmsLmdDriverEntity driver = lmdDriverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverId, tmsScheduleAppointDto.getDriverId()));
        if(ObjectUtil.isNull(driver)){
            return R.failed("司机不存在！");
        }
//        if(driver.getIsOpen().equals(false)){
//            return R.failed("司机已休息，请重新选择司机！");
//        }
        if(ObjectUtil.isNotNull(tmsLineHaulOrders) && !tmsLineHaulOrders.isEmpty()){
            for (TmsLineHaulOrderEntity tmsLineHaulOrder : tmsLineHaulOrders) {
                //指派司机
                tmsLineHaulOrder.setDriverId(tmsScheduleAppointDto.getDriverId());
                if(ObjectUtil.isNotNull(vehicleInfo)){
                    //车牌号
                    tmsLineHaulOrder.setLicensePlate(vehicleInfo.getLicensePlate());
                }
                //司机联系方式
                tmsLineHaulOrder.setContactPhone(driver.getPhone());
                //干线默认是整车运输方式
                tmsLineHaulOrder.setTransportType(1);

                //指派成功，发送站内信息给司机
                // 处理成功之后会发送站内信消息通知给司机
                TmsMessageEntity message = new TmsMessageEntity();
                R<AppUserInfo> userInfo = null;
                if (ObjectUtil.isNotNull(driver)){
                    userInfo = remoteAppUserService.info(driver.getPhone());

                    message.setUserId(userInfo.getData().getAppUser().getUserId());
                    message.setOrderNo(tmsLineHaulOrder.getLineHaulNo());
                    message.setMessage("You have a new trunk order, please check！");
                    message.setMessageType(TmsMessageTypeConstants.TRUNK_ORDER);
                    // 新增消息记录
                    messageMapper.insert(message);
                }
            }
            //更新
            this.updateBatchById(tmsLineHaulOrders);
            //查询这些干线任务包含了哪些订单
            Set<String> lineHaulNos = tmsLineHaulOrders.stream()
                    .map(TmsLineHaulOrderEntity::getLineHaulNo).collect(Collectors.toSet());
            List<TmsOrderLineHaulRelationEntity> tmsOrderLineHaulRelations = orderLineHaulRelationMapper.selectList(new LambdaQueryWrapper<TmsOrderLineHaulRelationEntity>()
                    .in(TmsOrderLineHaulRelationEntity::getLineHaulNo,lineHaulNos));
            //过滤出此时所有的订单
            Set<String> entrustedOrderNumbers = tmsOrderLineHaulRelations.stream()
                    .map(TmsOrderLineHaulRelationEntity::getEntrustedOrderNumber).collect(Collectors.toSet());
            //查询这批订单
            List<TmsCustomerOrderEntity> tmsCustomerOrders = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber,entrustedOrderNumbers));

            //指派后将这批订单设置为不需要干线的状态(分页时就看不到了)
            List<Long> customerOrderIds = tmsCustomerOrders.stream()
                    .map(TmsCustomerOrderEntity::getId).collect(Collectors.toList());
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getId,customerOrderIds)
                    .set(TmsCustomerOrderEntity::getNeedLineHaul,Boolean.FALSE));

            // 记录干线指派的司机信息
            List<TmsDriverAssignHistoryEntity> tmsDriverAssignHistoryEntityList = entrustedOrderNumbers.stream()
                    .map(item -> {
                        TmsDriverAssignHistoryEntity tmsDriverAssignHistory = new TmsDriverAssignHistoryEntity();
                        tmsDriverAssignHistory.setDriverId(tmsScheduleAppointDto.getDriverId());
                        tmsDriverAssignHistory.setDriverName(driver.getDriverName());
                        tmsDriverAssignHistory.setDriverNum(driver.getDriverNum());
                        tmsDriverAssignHistory.setOrderNo(item);
                        tmsDriverAssignHistory.setDescription("干线指派");
                        tmsDriverAssignHistory.setAssignType(TaskType.DRYLINE.getCode());
                        return tmsDriverAssignHistory;
                    }).collect(Collectors.toList());
            tmsDriverAssignHistoryService.saveBatch(tmsDriverAssignHistoryEntityList);
            return R.ok() ;
        }else{
            return R.failed("没有找到相关任务单-指派失败！");
        }
    }

    @Override
    public R getFinishDryLineTask(Page page) {
        List<TmsLargeTaskTrailVo> tmsLargeTaskTrailVos = new ArrayList<>();
        //查询已完成的揽收任务
        LambdaQueryWrapper<TmsLineHaulOrderEntity> wrapper = new LambdaQueryWrapper<TmsLineHaulOrderEntity>()
                .eq(TmsLineHaulOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode())
                .orderByDesc(TmsLineHaulOrderEntity::getCreateTime);
        Page newPage = tmsLineHaulOrderMapper.selectPage(page, wrapper);
        List<TmsLineHaulOrderEntity> records = newPage.getRecords();
        if(ObjectUtil.isNotNull(records) && !records.isEmpty()){
            //司机信息
            List<Long> driverIds = records.stream()
                    .map(TmsLineHaulOrderEntity::getDriverId).collect(Collectors.toList());
            Map<Long, TmsLmdDriverEntity> driverMap = lmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                            .in(TmsLmdDriverEntity::getDriverId, driverIds))
                    .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
            //车辆信息
            Map<Long, TmsVehicleInfoEntity> vehicleMap = vehicleInfoMapper.selectList(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                            .in(TmsVehicleInfoEntity::getDriverId, driverIds))
                    .stream().collect(Collectors.toMap(TmsVehicleInfoEntity::getDriverId, Function.identity()));
            //遍历回填数据
            records.forEach(task -> {
                TmsLargeTaskTrailVo tmsLargeTaskTrailVo = new TmsLargeTaskTrailVo();
                tmsLargeTaskTrailVo.setLineHaulOrder(task);
                //干线标志
                tmsLargeTaskTrailVo.setTaskType(TaskType.DRYLINE.getCode());
                //司机信息
                if(ObjectUtil.isNotNull(driverMap.get(task.getDriverId()))){
                    tmsLargeTaskTrailVo.setDriverName(driverMap.get(task.getDriverId()).getDriverName());
                }
                //车牌
                if(ObjectUtil.isNotNull(vehicleMap.get(task.getDriverId()))){
                    tmsLargeTaskTrailVo.setLicensePlate(vehicleMap.get(task.getDriverId()).getLicensePlate());
                }
                tmsLargeTaskTrailVos.add(tmsLargeTaskTrailVo);

            });
        }
        return R.ok(newPage.setRecords(tmsLargeTaskTrailVos));
    }

    @Override
    public List<TmsLineHaulOrderExcelVo> export(TmsLineHaulOrderPageVo vo) {
        if (vo == null) {
            vo = new TmsLineHaulOrderPageVo();
        }
        LambdaQueryWrapper<TmsLineHaulOrderEntity> wrapper = new LambdaQueryWrapper<>();
                // 干线单号
        wrapper.like(StrUtil.isNotBlank(vo.getLineHaulNo()), TmsLineHaulOrderEntity::getLineHaulNo, vo.getLineHaulNo())
                // 运输类型：1=整车运输，2=零担运输
                .eq(ObjectUtil.isNotNull(vo.getTransportType()), TmsLineHaulOrderEntity::getTransportType, vo.getTransportType())
                // 任务状态
                .eq(ObjectUtil.isNotNull(vo.getTaskStatus()), TmsLineHaulOrderEntity::getTaskStatus, vo.getTaskStatus())
                // 始发地仓库
                .eq(ObjectUtil.isNotNull(vo.getOriginWarehouseId()), TmsLineHaulOrderEntity::getOriginWarehouseId, vo.getOriginWarehouseId())
                // 目的地仓库
                .eq(ObjectUtil.isNotNull(vo.getDestWarehouseId()), TmsLineHaulOrderEntity::getDestWarehouseId, vo.getDestWarehouseId())
                // 搜索框 创建时间
                .between(ObjectUtil.isNotNull(vo.getCreateBeginTime()) && ObjectUtil.isNotNull(vo.getCreateEndTime()),
                        TmsLineHaulOrderEntity::getCreateTime, vo.getCreateBeginTime(), vo.getCreateEndTime())
                .orderByDesc(TmsLineHaulOrderEntity::getCreateTime);
        List<TmsLineHaulOrderEntity> tmsLineHaulOrderEntityList = tmsLineHaulOrderMapper.selectList(wrapper);
        List<TmsLineHaulOrderExcelVo> tmsLineHaulOrderExcelVos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(tmsLineHaulOrderEntityList)){
            //涉及到的司机
            List<Long> driverIds = tmsLineHaulOrderEntityList.stream().map(TmsLineHaulOrderEntity::getDriverId).collect(Collectors.toList());
            //仓库id
            List<Long> originWarehouseIds = tmsLineHaulOrderEntityList.stream().map(TmsLineHaulOrderEntity::getOriginWarehouseId).collect(Collectors.toList());
            List<Long> destWarehouseIds = tmsLineHaulOrderEntityList.stream().map(TmsLineHaulOrderEntity::getDestWarehouseId).collect(Collectors.toList());
            Set<Long> warehouseIdSet = new HashSet<>();
            warehouseIdSet.addAll(originWarehouseIds);
            warehouseIdSet.addAll(destWarehouseIds);
            //仓库信息
            Map<Long, TmsSiteEntity> warehouseMap = tmsSiteMapper.selectList(new LambdaQueryWrapper<TmsSiteEntity>()
                    .in(TmsSiteEntity::getId, warehouseIdSet)).stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));

            Map<Long, TmsLmdDriverEntity> driverMap=new HashMap<>();
            Map<Long, TmsVehicleInfoEntity> vehicleMap=new HashMap<>();
            Map<Long, TmsVehicleDriverRelationEntity> vehicleDriverRelationMap=new HashMap<>();
            if(CollectionUtil.isNotEmpty(driverIds)){
                driverMap = lmdDriverMapper.selectBatchIds(driverIds).stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
                //根据司机id查询车辆信息
                List<TmsVehicleDriverRelationEntity> tmsVehicleDriverRelationEntities = vehicleDriverRelationMapper.selectList(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                        .eq(TmsVehicleDriverRelationEntity::getDriverId, driverIds));
                List<Long> vehicleIdList = tmsVehicleDriverRelationEntities.stream().map(TmsVehicleDriverRelationEntity::getVehicleId).collect(Collectors.toList());
                vehicleDriverRelationMap = tmsVehicleDriverRelationEntities
                        .stream().collect(Collectors.toMap(TmsVehicleDriverRelationEntity::getDriverId, Function.identity()));
                //车辆信息
                if(CollectionUtil.isNotEmpty(vehicleIdList)){
                    vehicleMap = vehicleInfoMapper.selectBatchIds(vehicleIdList).stream().collect(Collectors.toMap(TmsVehicleInfoEntity::getId, Function.identity()));
                }
            }
            for (TmsLineHaulOrderEntity tmsLineHaulOrderEntity : tmsLineHaulOrderEntityList) {
                TmsLineHaulOrderExcelVo tmsLineHaulOrderExcelVo = new TmsLineHaulOrderExcelVo();
                BeanUtils.copyProperties(tmsLineHaulOrderEntity, tmsLineHaulOrderExcelVo);
                //司机
                if(ObjectUtil.isNotNull(driverMap.get(tmsLineHaulOrderEntity.getDriverId()))){
                    //司机名称
                    tmsLineHaulOrderExcelVo.setDriver(driverMap.get(tmsLineHaulOrderEntity.getDriverId()).getDriverName());
                    if(ObjectUtil.isNotNull(vehicleDriverRelationMap.get(tmsLineHaulOrderEntity.getDriverId()))){
                        //车牌号
                        TmsVehicleDriverRelationEntity tmsVehicleDriverRelationEntity = vehicleDriverRelationMap.get(tmsLineHaulOrderEntity.getDriverId());
                        tmsLineHaulOrderExcelVo.setLicensePlate(vehicleMap.get(tmsVehicleDriverRelationEntity.getVehicleId()).getLicensePlate());
                    }
                }
                //始发仓库
                if(ObjectUtil.isNotNull(warehouseMap.get(tmsLineHaulOrderEntity.getOriginWarehouseId()))){
                    tmsLineHaulOrderExcelVo.setOriginWarehouse(warehouseMap.get(tmsLineHaulOrderEntity.getOriginWarehouseId()).getSiteName());
                }
                //目的地仓库
                if(ObjectUtil.isNotNull(warehouseMap.get(tmsLineHaulOrderEntity.getDestWarehouseId()))){
                    tmsLineHaulOrderExcelVo.setDestWarehouse(warehouseMap.get(tmsLineHaulOrderEntity.getDestWarehouseId()).getSiteName());
                }
                switch (tmsLineHaulOrderEntity.getTransportType()) {
                    case 1:
                        tmsLineHaulOrderExcelVo.setTransportTypeName("整车运输");
                        break;
                    case 2:
                        tmsLineHaulOrderExcelVo.setTransportTypeName("零担运输");
                        break;

                    default:
                        tmsLineHaulOrderExcelVo.setTransportTypeName("");
                        break;
                }
                switch (tmsLineHaulOrderEntity.getTaskStatus()) {
                    case 25001:
                        tmsLineHaulOrderExcelVo.setTaskStatusName("待提货");
                        break;
                    case 25002:
                        tmsLineHaulOrderExcelVo.setTaskStatusName("配送中");
                        break;
                    case 25003:
                        tmsLineHaulOrderExcelVo.setTaskStatusName("已完成");
                        break;
                    case 25004:
                        tmsLineHaulOrderExcelVo.setTaskStatusName("已取消");
                        break;
                    default:
                    tmsLineHaulOrderExcelVo.setTaskStatusName("");
                    break;
                }
                tmsLineHaulOrderExcelVos.add(tmsLineHaulOrderExcelVo);

            }

        }
        return tmsLineHaulOrderExcelVos;
    }

    /**
     * 生成出库单号
     * 格式：CK + 年月日 + Snowflake ID
     *
     * @return
     */
    private synchronized String generateOutboundOrderNumber() {
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250330,250331
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6); // 取后 6 位
        return "CK" + datePart + shortId;
    }

}