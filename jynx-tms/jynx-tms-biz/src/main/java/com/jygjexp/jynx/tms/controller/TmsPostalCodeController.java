package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.dto.TmsPostalCodeDTO;
import com.jygjexp.jynx.tms.entity.TmsPostalCodeEntity;
import com.jygjexp.jynx.tms.service.TmsPostalCodeService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡派邮编
 *
 * <AUTHOR>
 * @date 2025-02-28 16:35:00
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsPostalCode" )
@Tag(description = "tmsPostalCode" , name = "卡派邮编管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsPostalCodeController {

    private final  TmsPostalCodeService tmsPostalCodeService;

    /**
     * 邮编分页查询
     * @param page 分页对象
     * @param entity 卡派邮编
     * @return
     */
    @Operation(summary = "邮编分页查询" , description = "邮编分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPostalCode_view')")
    public R getTmsPostalCodePage(@ParameterObject Page page, @ParameterObject TmsPostalCodeEntity entity) {
        LambdaQueryWrapper<TmsPostalCodeEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(ObjectUtil.isNotNull(entity.getPostalCode()), TmsPostalCodeEntity::getPostalCode, entity.getPostalCode())
                .like(StrUtil.isNotBlank(entity.getCountryName()), TmsPostalCodeEntity::getCountryName, entity.getCountryName())
                .like(StrUtil.isNotBlank(entity.getStatesName()), TmsPostalCodeEntity::getStatesName, entity.getStatesName())
                .like(StrUtil.isNotBlank(entity.getCityName()), TmsPostalCodeEntity::getCityName, entity.getCityName())
                .orderByDesc(TmsPostalCodeEntity::getId);
        return R.ok(tmsPostalCodeService.page(page, wrapper));
    }


    /**
     * 通过id查询卡派邮编
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPostalCode_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsPostalCodeService.getById(id));
    }

    /**
     * 新增卡派邮编
     * @param tmsPostalCode 卡派邮编
     * @return R
     */
    @Operation(summary = "新增卡派邮编" , description = "新增卡派邮编" )
    @SysLog("新增卡派邮编" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPostalCode_add')" )
    public R save(@RequestBody TmsPostalCodeEntity tmsPostalCode) {
        double[] latLng = tmsPostalCodeService.getLatLng(tmsPostalCode.getCityName());
        tmsPostalCode.setLat(BigDecimal.valueOf(latLng[0]));
        tmsPostalCode.setLng(BigDecimal.valueOf(latLng[1]));
        return R.ok(tmsPostalCodeService.save(tmsPostalCode));
    }

    /**
     * 修改卡派邮编
     * @param tmsPostalCode 卡派邮编
     * @return R
     */
    @Operation(summary = "修改卡派邮编" , description = "修改卡派邮编" )
    @SysLog("修改卡派邮编" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPostalCode_edit')" )
    public R updateById(@RequestBody TmsPostalCodeEntity tmsPostalCode) {
        return R.ok(tmsPostalCodeService.updateById(tmsPostalCode));
    }

    /**
     * 通过id删除卡派邮编
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派邮编" , description = "通过id删除卡派邮编" )
    @SysLog("通过id删除卡派邮编" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPostalCode_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsPostalCodeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 查询全部邮编覆盖省份-城市
     *
     * @return R
     */
    @Operation(summary = "查询全部邮编覆盖省份-城市" , description = "查询全部邮编覆盖省份-城市" )
    @GetMapping("/listPostAllCity" )
    public R listPostAllCity() {
        // 查询出所有省份，城市，并返回省份-城市格式
        return R.ok(tmsPostalCodeService.list().stream()
                .map(entity -> new TmsPostalCodeDTO(entity.getStatesName(), entity.getCityName(), entity.getIsValid())).distinct().collect(Collectors.toList()));
    }


    @Inner(value = false)
    @Operation(summary = "根据城市获取经纬度", description = "根据城市获取经纬度")
    @PostMapping("/city/searchLocation")
    public R searchLocation(@RequestParam String postalCode) {
        return R.ok(tmsPostalCodeService.getLatLng(postalCode));
    }


    /**
     * 导出excel 表格
     * @param tmsPostalCode 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsPostalCode_export')" )
    public List<TmsPostalCodeEntity> export(TmsPostalCodeEntity tmsPostalCode,Long[] ids) {
        return tmsPostalCodeService.list(Wrappers.lambdaQuery(tmsPostalCode).in(ArrayUtil.isNotEmpty(ids), TmsPostalCodeEntity::getId, ids));
    }
}