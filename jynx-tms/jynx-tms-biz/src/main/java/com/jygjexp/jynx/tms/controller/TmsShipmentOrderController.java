package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsDriverEntity;
import com.jygjexp.jynx.tms.entity.TmsShipmentOrderEntity;
import com.jygjexp.jynx.tms.service.TmsShipmentOrderService;
import com.jygjexp.jynx.tms.vo.TmsShipmentOrderPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 卡派-运输单
 *
 * <AUTHOR>
 * @date 2025-03-13 10:56:31
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsShipmentOrder" )
@Tag(description = "tmsShipmentOrder" , name = "卡派-运输单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsShipmentOrderController {

    private final  TmsShipmentOrderService tmsShipmentOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 卡派-运输单
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsShipmentOrder_view')" )
    public R getTmsShipmentOrderPage(@ParameterObject Page page, @ParameterObject TmsShipmentOrderPageVo vo) {
        return R.ok(tmsShipmentOrderService.search(page, vo));
    }


    /**
     * 通过id查询卡派-运输单
     * @param shipmentId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{shipmentId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsShipmentOrder_view')" )
    public R getById(@PathVariable("shipmentId" ) Long shipmentId) {
        return R.ok(tmsShipmentOrderService.getById(shipmentId));
    }

    /**
     * 新增卡派-运输单
     * @param tmsShipmentOrder 卡派-运输单
     * @return R
     */
    @Operation(summary = "新增卡派-运输单" , description = "新增卡派-运输单" )
    @SysLog("新增卡派-运输单" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsShipmentOrder_add')" )
    public R save(@RequestBody TmsShipmentOrderEntity tmsShipmentOrder) {
        return R.ok(tmsShipmentOrderService.save(tmsShipmentOrder));
    }

    /**
     * 修改卡派-运输单
     * @param tmsShipmentOrder 卡派-运输单
     * @return R
     */
    @Operation(summary = "修改卡派-运输单" , description = "修改卡派-运输单" )
    @SysLog("修改卡派-运输单" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsShipmentOrder_edit')" )
    public R updateById(@RequestBody TmsShipmentOrderEntity tmsShipmentOrder) {
        return R.ok(tmsShipmentOrderService.updateById(tmsShipmentOrder));
    }

    /**
     * 通过id删除卡派-运输单
     * @param ids shipmentId列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派-运输单" , description = "通过id删除卡派-运输单" )
    @SysLog("通过id删除卡派-运输单" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsShipmentOrder_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsShipmentOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 通过id查询卡派-运输单详情信息
     * @param shipmentId id
     * @return R
     */
    @Operation(summary = "通过id查询-运输单详情信息" , description = "通过id查询-运输单详情信息" )
    @GetMapping("/getListById/{shipmentId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsShipmentOrder_view')" )
    public R getListById(@PathVariable("shipmentId" ) Long shipmentId) {
        return R.ok(tmsShipmentOrderService.getListById(shipmentId));
    }

    /**
     * 查询在途包裹
     *
     * @return R
     */
    @Operation(summary = "查询在途包裹" , description = "查询在途包裹" )
    @GetMapping("/getListTransitOrder" )
    //@PreAuthorize("@pms.hasPermission('tms_tmsShipmentOrder_view')" )
    public R getListTransitOrder() {
        return R.ok(tmsShipmentOrderService.getListTransitOrder());
    }


    /**
     * 导出excel 表格
     * @param tmsShipmentOrder 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsShipmentOrder_export')" )
    public List<TmsShipmentOrderEntity> export(TmsShipmentOrderEntity tmsShipmentOrder,Long[] ids) {
        return tmsShipmentOrderService.list(Wrappers.lambdaQuery(tmsShipmentOrder).in(ArrayUtil.isNotEmpty(ids), TmsShipmentOrderEntity::getShipmentId, ids));
    }

    // 查询已完成的运输单
    @Operation(summary = "查询已完成的运输单" , description = "查询已完成的运输单" )
    @GetMapping("/getListCompletedOrder" )
    public R getListCompletedOrder() {
        return R.ok(tmsShipmentOrderService.getListCompletedOrder());
    }


    // 司机app相关操作-------------------------------------------------------------

    /**
     * 根据状态查询运输单信息
     *
     * @return R
     */
    @Operation(summary = "app-根据状态查询运输单信息" , description = "app-根据状态查询运输单信息" )
    @GetMapping("/app/getListByStatus/{status}/{driverId}" )
    public R getListByStatus(@PathVariable("status" ) Integer status, @PathVariable("driverId" ) Long driverId) {
        if (Objects.isNull(status)) {
            return R.failed("status not null");
        }
        return tmsShipmentOrderService.getListByStatus(status,driverId);
    }

    /**
     * 查询运输单下的委托单信息
     *
     * @return R
     */
    @Operation(summary = "app-查询运输单下的委托单信息" , description = "app-查询运输单下的委托单信息" )
    @GetMapping("/app/getListByShipmentId/{shipmentId}" )
    public R getListByShipmentId(@PathVariable("shipmentId" ) Long shipmentId) {
        return tmsShipmentOrderService.getListByShipmentId(shipmentId);
    }
}