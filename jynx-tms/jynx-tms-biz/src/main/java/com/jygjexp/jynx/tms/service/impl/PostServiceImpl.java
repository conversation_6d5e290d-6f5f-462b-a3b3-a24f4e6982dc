package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsPhotoEntity;
import com.jygjexp.jynx.tms.entity.TmsPostEntity;
import com.jygjexp.jynx.tms.mapper.PostMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.PhotoService;
import com.jygjexp.jynx.tms.service.PostService;
import com.jygjexp.jynx.tms.vo.PostPageVo;
import com.jygjexp.jynx.tms.vo.excel.PostExcelVo;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 驿站
 *
 * <AUTHOR>
 * @date 2024-10-12 22:38:12
 */
@Service
public class PostServiceImpl extends ServiceImpl<PostMapper, TmsPostEntity> implements PostService {
    @Autowired
    PostMapper postMapper;
    @Autowired
    private PhotoService photoService;



    @Override
    public TmsPostEntity getPostByPostNo(String postNo) {
        // 使用 QueryWrapper 构建查询条件
        QueryWrapper<TmsPostEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("post_no", postNo)
                .eq("is_valid", 1)
                .last("limit 1");
        // 查询并返回结果
        return postMapper.selectOne(queryWrapper);
    }


    //分页查询
    public IPage<PostPageVo> getPage(Page page, TmsPostEntity post) {
        MPJLambdaWrapper<TmsPostEntity> wrapper = getConditionWrapper(post);
        Page<PostPageVo> postPage = postMapper.selectJoinPage(page, PostPageVo.class, wrapper);

        // 提取 records，直接操作 Page 对象中的数据
        List<PostPageVo> posts = postPage.getRecords();
        if (posts != null && !posts.isEmpty()) {
            // 提取所有非空的 postId
            List<Integer> postIds = posts.stream()
                    .map(PostPageVo::getPostId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!postIds.isEmpty()) {
                // 一次性查询所有照片并按 postId 分组
                Map<Integer, List<TmsPhotoEntity>> photosGroupedByPostId = photoService.list(
                        new QueryWrapper<TmsPhotoEntity>().in("post_id", postIds)
                ).stream().collect(Collectors.groupingBy(TmsPhotoEntity::getPostId));
                posts.forEach(p -> {
                    Integer postId = p.getPostId();
                    if (postId != null) {
                        List<TmsPhotoEntity> photos = photosGroupedByPostId.getOrDefault(postId, Collections.emptyList());
                        p.setPhotoList(photos);
                    }
                });
            }
        }
        return postPage;

    }

    //通过驿站组ID获取驿站
    @Override
    public List<TmsPostEntity> getPosts(Integer postGroupId) {
        QueryWrapper<TmsPostEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("group_id", postGroupId);
        wrapper.eq("is_valid", 1);
        return postMapper.selectList(wrapper);
    }



    //根据地址获取经纬度
    public String getLatLngByAddress(String address) {
        // 构造 HTTP 客户端
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
        clientBuilder.proxy(proxy);
        OkHttpClient client = clientBuilder.build();

        HttpUrl url = new HttpUrl.Builder()
                .scheme("https")
                .host("maps.googleapis.com")
                .addPathSegment("maps")
                .addPathSegment("api")
                .addPathSegment("geocode")
                .addPathSegment("json")
                .addQueryParameter("address", address)
                .addQueryParameter("key", "AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk")
                .build();
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        String locationString = "";
        try (Response execute = client.newCall(request).execute()) {
            if (execute.isSuccessful() && execute.body() != null) {
                JSONObject jsonObject = JSON.parseObject(execute.body().string());
                if ("OK".equals(jsonObject.getString("status"))) {
                    // 提取 results 数组
                    JSONArray results = jsonObject.getJSONArray("results");
                    if (results != null && !results.isEmpty()) {
                        // 获取第一个结果对象
                        JSONObject result = results.getJSONObject(0);
                        // 提取 geometry 对象
                        JSONObject geometry = result.getJSONObject("geometry");
                        if (geometry != null) {
                            // 提取 location 对象
                            JSONObject location = geometry.getJSONObject("location");
                            if (location != null) {
                                double lat = location.getDouble("lat");
                                double lng = location.getDouble("lng");
                                locationString = lat + "," + lng;
                            }
                        }
                    }
                }
            } else {
                throw new RuntimeException("谷歌授权令牌获取失败");
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return locationString;
    }





    //新增驿站
    @Override
    public R savePost(TmsPostEntity post) {
        //查询驿站名称是否存在
        TmsPostEntity postByPostName = getPostByPostName(post.getPostName());
        if (postByPostName != null) {
            return LocalizedR.failed("tms.Unique.post.names.required", Optional.ofNullable(null));
        }
        //查询驿站代码是否存在
        TmsPostEntity postByPostNo = getPostByPostNo(post.getPostNo());
        if (postByPostNo != null) {
            return LocalizedR.failed("tms.Unique.post.code.required", Optional.ofNullable(null));
        }
        post.setCreateDate(LocalDateTime.now());
        boolean postSaved = this.save(post);
        // 保存图片
        if (postSaved) {
            Integer postId = post.getPostId();
            List<TmsPhotoEntity> photoList = post.getPhotoList();
            // 设置每个图片的post_id
            for (TmsPhotoEntity photo : photoList) {
                photo.setPostId(postId);
            }
            // 批量保存图片
            return R.ok(photoService.saveBatch(photoList));
        }
        return R.failed();
    }

    //编辑驿站
    @Override
    public R updatePost(TmsPostEntity post) {
        //查询驿站名称是否存在
        TmsPostEntity postByPostName = getPostByPostName(post.getPostName());
        if (postByPostName != null && !postByPostName.getPostId().equals(post.getPostId())) {
            return LocalizedR.failed("tms.Unique.post.names.required", Optional.ofNullable(null));
        }
        //查询驿站代码是否存在
        TmsPostEntity postByPostNo = getPostByPostNo(post.getPostNo());
        if (postByPostNo != null && !postByPostNo.getPostId().equals(post.getPostId())) {
            return LocalizedR.failed("tms.Unique.post.code.required", Optional.ofNullable(null));
        }

        boolean postUpdated = this.updateById(post);
        if (postUpdated) {
            Integer postId = post.getPostId();
            List<TmsPhotoEntity> photos = post.getPhotoList();
            for (TmsPhotoEntity photo : photos) {
                //不存在则保存
                photo.setPostId(postId);
                QueryWrapper<TmsPhotoEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("post_id", postId);
                queryWrapper.eq("sub_type", photo.getSubType());
                TmsPhotoEntity TmsPhotoEntity = photoService.getOne(queryWrapper);
                if (TmsPhotoEntity == null) {
                    photoService.save(photo);
                } else {
                    //存在则更新
                    UpdateWrapper<TmsPhotoEntity> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("post_id", postId);
                    updateWrapper.eq("sub_type", photo.getSubType());
                    photo.setFilePath(photo.getFilePath());
                    photoService.update(photo, updateWrapper);
                }
            }
            return R.ok();

        }
        return R.failed();
    }


    //根据驿站名称查询驿站
    public TmsPostEntity getPostByPostName(String postName) {
        LambdaQueryWrapper<TmsPostEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsPostEntity::getPostName, postName)
                .last("limit 1");
        return postMapper.selectOne(wrapper);
    }


    //根据驿站代码查询驿站
    public TmsPostEntity getPostByPostCode(String postCode) {
        LambdaQueryWrapper<TmsPostEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsPostEntity::getPostNo, postCode)
                .last("limit 1");
        return postMapper.selectOne(wrapper);
    }



    //根据活动ID获取已分配的驿站
    @Override
    public List<TmsPostEntity> getPostByActivityId(Integer activityId) {
        LambdaQueryWrapper<TmsPostEntity> wrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotNull(activityId)) {
            wrapper.eq(TmsPostEntity::getActivityId, activityId);
            return this.list(wrapper);
        }
        return null;
    }

    //导出
    @Override
    public List<PostExcelVo> getExcel(TmsPostEntity post, Integer[] ids) {
        MPJLambdaWrapper<TmsPostEntity> wrapper = getConditionWrapper(post);
        wrapper.in(ObjectUtil.isNotNull(ids) && ids.length > 0, TmsPostEntity::getPostId, ids);
        List<PostPageVo> list = postMapper.selectJoinList(PostPageVo.class, wrapper);
        List<PostExcelVo> postExcelVos = new ArrayList<>();
        for (TmsPostEntity TmsPostEntity : list) {
            PostExcelVo postExcelVo = new PostExcelVo();
            BeanUtils.copyProperties(TmsPostEntity, postExcelVo);
            postExcelVo.setIsValid(TmsPostEntity.getIsValid() ? 1 : 0);
            postExcelVo.setIsWarehouse(TmsPostEntity.getIsWarehouse() ? 1 : 0);
            postExcelVos.add(postExcelVo);
        }
        return postExcelVos;
    }

    private MPJLambdaWrapper<TmsPostEntity> getConditionWrapper(TmsPostEntity post) {
        // 创建 MPJLambdaWrapper 对象
        MPJLambdaWrapper<TmsPostEntity> wrapper = new MPJLambdaWrapper<TmsPostEntity>()
                .selectAll(TmsPostEntity.class)
                .eq(StringUtils.isNotBlank(post.getPostNo()), PostPageVo::getPostNo, post.getPostNo())
                .like(StringUtils.isNotBlank(post.getPostName()), PostPageVo::getPostName, post.getPostName())
                .eq(ObjectUtil.isNotNull(post.getWarehouseId()), PostPageVo::getWarehouseId, post.getWarehouseId())
                .eq(ObjectUtil.isNotNull(post.getGroupId()), PostPageVo::getGroupId, post.getGroupId())
                .eq(ObjectUtil.isNotNull(post.getIsValid()), PostPageVo::getIsValid, post.getIsValid())
                .eq(ObjectUtil.isNotNull(post.getPostModel()), PostPageVo::getPostModel, post.getPostModel())
                .eq(ObjectUtil.isNotNull(post.getIsOperation()), PostPageVo::getIsOperation, post.getIsOperation())
                .eq(ObjectUtil.isNotNull(post.getIsWarehouse()), PostPageVo::getIsWarehouse, post.getIsWarehouse())
                .eq(ObjectUtil.isNotNull(post.getIsMonthlyBill()), PostPageVo::getIsMonthlyBill, post.getIsMonthlyBill())
                .eq(ObjectUtil.isNotNull(post.getDefaultDriverId()), PostPageVo::getDefaultDriverId, post.getDefaultDriverId())
                .eq(ObjectUtil.isNotNull(post.getParentWarehouseId()), PostPageVo::getParentWarehouseId, post.getParentWarehouseId())
                .orderByDesc(TmsPostEntity::getCreateDate);
        return wrapper;
    }


}