package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsAdditionalServicesEntity;
import com.jygjexp.jynx.tms.entity.TmsCargoInfoEntity;
import com.jygjexp.jynx.tms.service.TmsAdditionalServicesService;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * 卡派-附加服务
 *
 * <AUTHOR>
 * @date 2025-03-07 16:18:31
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsAdditionalServices" )
@Tag(description = "tmsAdditionalServices" , name = "卡派-附加服务管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsAdditionalServicesController {

    private final  TmsAdditionalServicesService tmsAdditionalServicesService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsAdditionalServices 卡派-附加服务
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_view')" )
    public R getTmsAdditionalServicesPage(@ParameterObject Page page, @ParameterObject TmsAdditionalServicesEntity tmsAdditionalServices) {
        LambdaQueryWrapper<TmsAdditionalServicesEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsAdditionalServicesService.page(page, wrapper));
    }


    /**
     * 通过id查询卡派-附加服务
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsAdditionalServicesService.getById(id));
    }

    /**
     * 新增卡派-附加服务
     * @param tmsAdditionalServices 卡派-附加服务
     * @return R
     */
    @Operation(summary = "新增卡派-附加服务" , description = "新增卡派-附加服务" )
    @SysLog("新增卡派-附加服务" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_add')" )
    public R save(@RequestBody TmsAdditionalServicesEntity tmsAdditionalServices) {
        return R.ok(tmsAdditionalServicesService.save(tmsAdditionalServices));
    }

    /**
     * 修改卡派-附加服务
     * @param tmsAdditionalServices 卡派-附加服务
     * @return R
     */
    @Operation(summary = "修改卡派-附加服务" , description = "修改卡派-附加服务" )
    @SysLog("修改卡派-附加服务" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_edit')" )
    public R updateById(@RequestBody TmsAdditionalServicesEntity tmsAdditionalServices) {
        return R.ok(tmsAdditionalServicesService.updateById(tmsAdditionalServices));
    }

    /**
     * 通过id删除卡派-附加服务
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派-附加服务" , description = "通过id删除卡派-附加服务" )
    @SysLog("通过id删除卡派-附加服务" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsAdditionalServicesService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsAdditionalServices 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsAdditionalServices_export')" )
    public List<TmsAdditionalServicesEntity> export(TmsAdditionalServicesEntity tmsAdditionalServices,Long[] ids) {
        return tmsAdditionalServicesService.list(Wrappers.lambdaQuery(tmsAdditionalServices).in(ArrayUtil.isNotEmpty(ids), TmsAdditionalServicesEntity::getId, ids));
    }

    @Operation(summary = "按客户/委托单号查询附加服务", description = "按客户/委托单号查询附加服务")
    @PostMapping("/listByOrderNumber")
    public R<List<TmsAdditionalServicesEntity>> listByOrderNumber(
            @Parameter(description = "客户单号") @NotBlank String customerOrderNumber,
            @Parameter(description = "委托单号") @NotBlank String entrustedOrderNumber) {
        return R.ok(tmsAdditionalServicesService.listByOrderNumber(customerOrderNumber, entrustedOrderNumber));
    }

}