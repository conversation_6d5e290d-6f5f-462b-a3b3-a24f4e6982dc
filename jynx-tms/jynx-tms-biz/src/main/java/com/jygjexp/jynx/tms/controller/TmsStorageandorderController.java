package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsStorageandorderEntity;
import com.jygjexp.jynx.tms.service.TmsStorageandorderService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 入库批次与订单记录表
 *
 * <AUTHOR>
 * @date 2025-04-16 14:15:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStorageandorder" )
@Tag(description = "tmsStorageandorder" , name = "入库批次与订单记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStorageandorderController {

    private final  TmsStorageandorderService tmsStorageandorderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsStorageandorder 入库批次与订单记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageandorder_view')" )
    public R getTmsStorageandorderPage(@ParameterObject Page page, @ParameterObject TmsStorageandorderEntity tmsStorageandorder) {
        LambdaQueryWrapper<TmsStorageandorderEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsStorageandorderService.page(page, wrapper));
    }


    /**
     * 通过id查询入库批次与订单记录表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageandorder_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsStorageandorderService.getById(id));
    }

    /**
     * 新增入库批次与订单记录表
     * @param tmsStorageandorder 入库批次与订单记录表
     * @return R
     */
    @Operation(summary = "新增入库批次与订单记录表" , description = "新增入库批次与订单记录表" )
    @SysLog("新增入库批次与订单记录表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageandorder_add')" )
    public R save(@RequestBody TmsStorageandorderEntity tmsStorageandorder) {
        return R.ok(tmsStorageandorderService.save(tmsStorageandorder));
    }

    /**
     * 修改入库批次与订单记录表
     * @param tmsStorageandorder 入库批次与订单记录表
     * @return R
     */
    @Operation(summary = "修改入库批次与订单记录表" , description = "修改入库批次与订单记录表" )
    @SysLog("修改入库批次与订单记录表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageandorder_edit')" )
    public R updateById(@RequestBody TmsStorageandorderEntity tmsStorageandorder) {
        return R.ok(tmsStorageandorderService.updateById(tmsStorageandorder));
    }

    /**
     * 通过id删除入库批次与订单记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除入库批次与订单记录表" , description = "通过id删除入库批次与订单记录表" )
    @SysLog("通过id删除入库批次与订单记录表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageandorder_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsStorageandorderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsStorageandorder 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorageandorder_export')" )
    public List<TmsStorageandorderEntity> export(TmsStorageandorderEntity tmsStorageandorder,Long[] ids) {
        return tmsStorageandorderService.list(Wrappers.lambdaQuery(tmsStorageandorder).in(ArrayUtil.isNotEmpty(ids), TmsStorageandorderEntity::getId, ids));
    }
}