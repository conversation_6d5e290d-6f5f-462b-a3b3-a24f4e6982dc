package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsVehicleRouteVisitEntity;
import com.jygjexp.jynx.tms.service.TmsVehicleRouteVisitService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 车辆路线访问点
 *
 * <AUTHOR>
 * @date 2025-03-17 15:40:29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsVehicleRouteVisit" )
@Tag(description = "tmsVehicleRouteVisit" , name = "车辆路线访问点管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsVehicleRouteVisitController {

    private final  TmsVehicleRouteVisitService tmsVehicleRouteVisitService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsVehicleRouteVisit 车辆路线访问点
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteVisit_view')" )
    public R getTmsVehicleRouteVisitPage(@ParameterObject Page page, @ParameterObject TmsVehicleRouteVisitEntity tmsVehicleRouteVisit) {
        LambdaQueryWrapper<TmsVehicleRouteVisitEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsVehicleRouteVisitService.page(page, wrapper));
    }


    /**
     * 通过id查询车辆路线访问点
     * @param vehicleRouteVisitId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{vehicleRouteVisitId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteVisit_view')" )
    public R getById(@PathVariable("vehicleRouteVisitId" ) Long vehicleRouteVisitId) {
        return R.ok(tmsVehicleRouteVisitService.getById(vehicleRouteVisitId));
    }

    /**
     * 新增车辆路线访问点
     * @param tmsVehicleRouteVisit 车辆路线访问点
     * @return R
     */
    @Operation(summary = "新增车辆路线访问点" , description = "新增车辆路线访问点" )
    @SysLog("新增车辆路线访问点" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteVisit_add')" )
    public R save(@RequestBody TmsVehicleRouteVisitEntity tmsVehicleRouteVisit) {
        return R.ok(tmsVehicleRouteVisitService.save(tmsVehicleRouteVisit));
    }

    /**
     * 修改车辆路线访问点
     * @param tmsVehicleRouteVisit 车辆路线访问点
     * @return R
     */
    @Operation(summary = "修改车辆路线访问点" , description = "修改车辆路线访问点" )
    @SysLog("修改车辆路线访问点" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteVisit_edit')" )
    public R updateById(@RequestBody TmsVehicleRouteVisitEntity tmsVehicleRouteVisit) {
        return R.ok(tmsVehicleRouteVisitService.updateById(tmsVehicleRouteVisit));
    }

    /**
     * 通过id删除车辆路线访问点
     * @param ids vehicleRouteVisitId列表
     * @return R
     */
    @Operation(summary = "通过id删除车辆路线访问点" , description = "通过id删除车辆路线访问点" )
    @SysLog("通过id删除车辆路线访问点" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteVisit_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsVehicleRouteVisitService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsVehicleRouteVisit 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRouteVisit_export')" )
    public List<TmsVehicleRouteVisitEntity> export(TmsVehicleRouteVisitEntity tmsVehicleRouteVisit,Long[] ids) {
        return tmsVehicleRouteVisitService.list(Wrappers.lambdaQuery(tmsVehicleRouteVisit).in(ArrayUtil.isNotEmpty(ids), TmsVehicleRouteVisitEntity::getVehicleRouteVisitId, ids));
    }
}