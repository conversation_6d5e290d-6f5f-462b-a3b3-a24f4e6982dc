package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsOrderTrackEntity;
import com.jygjexp.jynx.tms.vo.TmsOrderTrackVo;
import com.jygjexp.jynx.tms.vo.TmsRegionPageVo;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.List;

public interface TmsOrderTrackService extends IService<TmsOrderTrackEntity> {

    /**
     * 保存订单轨迹
     *
     */
    void saveTrack(String orderNo,String customerNo, String orderStatus, String site, String locationDescription,String externalDescription
            , Integer trackType) ;

    // 根据轨迹节点维护内容-保存订单轨迹
    void saveTrack(String orderNo,String customerNo, String orderStatus,Integer trackLink) ;

    // 根据经纬度查询城市
    String searchCity(BigDecimal lat, BigDecimal lng);

    // 根据客户单号删除轨迹
    Boolean deleteByCustomerOrderNo(String customerOrderNo);

    // 分页查询
    Page<TmsOrderTrackVo> search(Page page, TmsOrderTrackVo vo);

    // 根据订单号查询轨迹列表中状态码最大的那条记录
    TmsOrderTrackEntity getMaxStatusCodeByOrderNo(String orderNo);

    public String getAccessTokenWithCache();

    // ups查询轨迹
    R queryUpsTracking(String inquiryNumber);

     // ups查询轨迹
     String queryUpsTrackingForJson(String inquiryNumber);


    // 17track查询Nb中大件轨迹
    R getSqTrack(String orderNo);

    // ==================== 迁移自zxoms的轨迹查询接口 ====================

    /**
     * 批量订单轨迹查询 - 迁移自zxoms
     * @param pkgNos 包裹号列表，逗号分隔
     * @param zip 邮编（可选）
     * @return 批量轨迹查询结果
     */
    R getTracksFromZxoms(String pkgNos, String zip);

}