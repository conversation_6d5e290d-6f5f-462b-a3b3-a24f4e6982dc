package com.jygjexp.jynx.tms.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.core.util.SpringContextHolder;
import com.jygjexp.jynx.tms.api.controller.UPSClient;
import com.jygjexp.jynx.tms.constants.TrackTypeConstant;
import com.jygjexp.jynx.tms.dto.TmsOrderPathDto;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsOrderTrackEntity;
import com.jygjexp.jynx.tms.entity.TmsPickupEntity;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsCustomerService;
import com.jygjexp.jynx.tms.service.TmsOrderTrackService;
import com.jygjexp.jynx.tms.service.TmsPickupService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @Author: xiongpengfei
 * @Description: 【中大件】ups同步轨迹
 * @Date: 2025/06/09 19:12
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TmsUpsTrackTask {

    private final TmsCustomerOrderService customerOrderService;
    private final TmsOrderTrackService orderTrackService;

    @SneakyThrows
    @XxlJob("tmsUpsTrackTaskHandler")
    public void tmsUpsTrackTaskHandler() {
        // 查询我们系统的ups单       todo :  后续记得替换跟踪单号为ups单号
        LambdaQueryWrapper<TmsCustomerOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode());
        queryWrapper.eq(TmsCustomerOrderEntity::getSubFlag,Boolean.FALSE);
        queryWrapper.eq(TmsCustomerOrderEntity::getEntrustedOrderNumber,"N2507P0000016");       // 测试单号
        queryWrapper.isNotNull(TmsCustomerOrderEntity::getEntrustedOrderNumber);
        List<TmsCustomerOrderEntity> upsList = customerOrderService.list(queryWrapper);

        if (CollUtil.isEmpty(upsList)) {
            log.info("无未签收UPS运单，不执行同步。");
            return;
        }

        // 提取全部的ups单号
        List<String> trackingNos = upsList.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());

        for (TmsCustomerOrderEntity ups : upsList) {
            // 获取ups单号
            String trackingNo = ups.getEntrustedOrderNumber();
            try {
                // 获取ups单返回轨迹数据
                R r = orderTrackService.queryUpsTracking(trackingNo);
                // json解析数据
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode result = objectMapper.valueToTree(r.getData());;
                JsonNode shipment = result.path("trackResponse").path("shipment").get(0);
                JsonNode pkg = shipment.path("package").get(0);
                JsonNode activityList = pkg.path("activity");

                for (JsonNode activity : activityList) {
                    String statusCode = activity.path("status").path("statusCode").asText();
                    String statusDesc = activity.path("status").path("description").asText();
                    String date = activity.path("date").asText();
                    String time = activity.path("time").asText();
                    String trackTimeStr = date + time;
                    LocalDateTime trackTime = LocalDateTime.parse(trackTimeStr, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

                    // 判断该轨迹是否已存在
                    boolean exists = isExistTrack(trackingNo, statusCode, statusDesc);
                    if (!exists) {
                        // 插入轨迹
                        batchSyncTrack(trackingNo,ups.getCustomerOrderNumber(),  statusCode, statusDesc, trackTime);
                        log.info("新增轨迹：{} - {} - {}", trackingNo, statusCode, statusDesc);
                    }
                }

            } catch (Exception e) {
                log.error("UPS轨迹同步失败，trackingNo={}，错误：{}", trackingNo, e.getMessage());
            }
        }
    }



    // 判断轨迹是否已经存在
    private Boolean isExistTrack(String trackingNo, String statusCode,String statusDesc) {
        LambdaQueryWrapper<TmsOrderTrackEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(TmsOrderTrackEntity::getOrderNo, trackingNo)
                .eq(TmsOrderTrackEntity::getOrderStatus, statusCode)
                .like(TmsOrderTrackEntity::getExternalDescription, statusDesc)
                .last("limit 1");
        return orderTrackService.count(wrapper)>0;
    }


    // 批量同步轨迹主子单
    private void batchSyncTrack(String orderNo,String customerNo, String statusCode,String description,LocalDateTime trackTime) {

       // 判断是否为主单号
       boolean isMasterOrder = checkIfMasterOrder(orderNo);

        if (isMasterOrder) {
            // 查询主单号的所有主子单号
            List<TmsCustomerOrderEntity> subOrderNos = customerOrderService.list(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                            .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.TRUE));


            if (CollUtil.isNotEmpty(subOrderNos)) {
                // 轨迹列表
                List<TmsOrderTrackEntity> trackList = new ArrayList<>();

                // 创建轨迹（子单）
                for (TmsCustomerOrderEntity subOrder : subOrderNos) {
                    TmsOrderTrackEntity track = new TmsOrderTrackEntity();
                    track.setOrderNo(subOrder.getEntrustedOrderNumber());
                    track.setCustomerOrderNo(subOrder.getCustomerOrderNumber());
                    track.setOrderStatus(statusCode);
                    //track.setStatusCode(statusCode);
                    track.setLocationDescription(description);
                    track.setExternalDescription(description);
                    track.setTrackType(TrackTypeConstant.EXTERNAL);
                    track.setAddTime(trackTime);
                    // 操作时间-本地北京时间
                    track.setLocalAddTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
                    // 设计默认操作人
                    track.setCreateBy("UPS");

                    trackList.add(track);
                }

                // 批量插入轨迹
                if (!trackList.isEmpty()) {
                    orderTrackService.saveBatch(trackList);
                }
            } else {
                // 直接新增一条轨迹
                TmsOrderTrackEntity track = new TmsOrderTrackEntity();
                track.setOrderNo(orderNo);
                track.setCustomerOrderNo(customerNo);
                track.setOrderStatus(statusCode);
                //track.setStatusCode(statusCode);
                track.setLocationDescription(description);
                track.setExternalDescription(description);
                track.setTrackType(TrackTypeConstant.EXTERNAL);
                track.setAddTime(trackTime);
                // 操作时间-本地北京时间
                track.setLocalAddTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
                // 设计默认操作人
                track.setCreateBy("UPS");
                orderTrackService.save(track);
            }

        }
    }


    /**
     * 判断是否为主单号的方法
     * 规则：主单号长度为13位，子单号长度是16位
     */
    private boolean checkIfMasterOrder(String orderNo) {
        return orderNo.trim().length() <= 14;
    }
}
