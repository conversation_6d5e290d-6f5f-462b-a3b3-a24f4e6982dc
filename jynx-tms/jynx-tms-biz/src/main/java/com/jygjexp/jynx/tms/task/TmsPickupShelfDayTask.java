package com.jygjexp.jynx.tms.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.tms.entity.TmsPickupEntity;
import com.jygjexp.jynx.tms.service.TmsPickupService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * @Author: xiongpengfei
 * @Description: 【卡派】计算自提包裹上架天数
 * @Date: 2025/02/12 18:05
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TmsPickupShelfDayTask {
    private final TmsPickupService tmsPickupService;


    @SneakyThrows
    @XxlJob("tmsPickupShelfDay")
    public void tmsPickupShelfDay() {
           XxlJobHelper.log("定时任务：【计算自提包裹上架天数】于:{}，输入参数{}", LocalDateTime.now(), "运行中");
        try {
            // 设置所有自提记录的上架天数
            List<TmsPickupEntity> pickupList = tmsPickupService.list(new LambdaQueryWrapper<TmsPickupEntity>().notIn(TmsPickupEntity::getPickupStatus, 3,4));

            if (pickupList.isEmpty()) {
                XxlJobHelper.log("未找到符合条件的自提包裹，无需更新");
                XxlJobHelper.handleSuccess();
                return;
            }

            for (TmsPickupEntity pickup : pickupList) {
                int daysOnShelf = (int) ChronoUnit.DAYS.between(pickup.getShelfTime(), LocalDateTime.now());
                // 防止天数计算出现负值（例如上架时间大于当前时间）
                daysOnShelf = Math.max(daysOnShelf, 0);
                pickup.setShelfDay(daysOnShelf);
            }
            // 批量保存上架天数
            tmsPickupService.updateBatchById(pickupList);

            XxlJobHelper.handleSuccess(); // 设置任务结果
            XxlJobHelper.log("定时任务：【计算自提包裹上架天数】执行结束，时间: {}", LocalDateTime.now());
        } catch (Exception e) {
            log.error("【计算自提包裹上架天数】定时任务执行失败：", e);
            XxlJobHelper.log("任务失败，原因：{}", e.getMessage());
            XxlJobHelper.handleFail();
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "请求过程中发生错误：" + e.getMessage(), e);
        }
    }


}
