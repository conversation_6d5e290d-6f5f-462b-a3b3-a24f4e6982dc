package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.TransportTaskStatus;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsOrderLineHaulRelationMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.vo.TmsLabePageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签信息
 *
 * <AUTHOR>
 * @date 2025-04-20 20:10:07
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsLabel" )
@Tag(description = "tmsLabel" , name = "标签信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsLabelController {

    private final  TmsLabelService tmsLabelService;
    private final TmsCageService tmsCageService;
    private final TmsCageAndOrderService cageAndOrderService;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsOrderLineHaulRelationMapper tmsOrderLineHaulRelationMapper;
    private final TmsLineHaulOrderService  tmsLineHaulOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsLabel 标签信息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsLabel_view')" )
    public R getTmsLabelPage(@ParameterObject Page page, @ParameterObject TmsLabePageVo tmsLabel) {
        return R.ok(tmsLabelService.search(page, tmsLabel));
    }


    /**
     * 通过id查询标签信息
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsLabel_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return tmsLabelService.selectByLaberId(id);
    }

    @Operation(summary = "通过订单号删除笼车与订单绑定记录" , description = "通过订单号删除笼车与订单绑定记录" )
    @SysLog("通过订单号删除笼车与订单绑定记录" )
    @PostMapping("/deleteByOrderNos")
    public R deleteByOrderNos(@RequestParam String orderNos, @RequestParam String labelCode) {
        return tmsLabelService.deleteByOrderNos(orderNos, labelCode);
    }


    @Operation(summary = "通过编码查询标签详情" , description = "通过编码查询标签详情" )
    @GetMapping("/app/{labelCode}" )
    public R getAppByLabelCode(@PathVariable("labelCode" ) String labelCode) {
        return tmsLabelService.selectByLaberCode(labelCode);
    }

    /**
     * 新增标签信息
     * @param tmsLabel 标签信息
     * @return R
     */
    @Operation(summary = "新增标签信息" , description = "新增标签信息" )
    @SysLog("新增标签信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLabel_add')" )
    public R save(@RequestBody TmsLabelEntity tmsLabel) {
        // 生成标签编码
        tmsLabel.setLabelCode(generateLabelCode(tmsLabel.getCageType(),tmsLabel.getCageId()));
        // 判断编号是否已存在数据库
        if (tmsLabelService.getOne(new LambdaQueryWrapper<TmsLabelEntity>()
                .eq(TmsLabelEntity::getLabelCode, tmsLabel.getLabelCode())) != null) {
            return LocalizedR.failed("tms.label.code.clash","");
        }
        return R.ok(tmsLabelService.save(tmsLabel));
    }

    /**
     * 修改标签信息
     * @param tmsLabel 标签信息
     * @return R
     */
    @Operation(summary = "修改标签信息" , description = "修改标签信息" )
    @SysLog("修改标签信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLabel_edit')" )
    public R updateById(@RequestBody TmsLabelEntity tmsLabel) {
        return R.ok(tmsLabelService.updateById(tmsLabel));
    }

    /**
     * 通过id删除标签信息
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除标签信息" , description = "通过id删除标签信息" )
    @SysLog("通过id删除标签信息" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLabel_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsLabelService.removeBatchByIds(CollUtil.toList(ids)));
    }

    // 标签编号生成规则
    public String generateLabelCode(Integer cageType, Long cageId) {
        // 获取当前日期字符串
        LocalDate today = LocalDate.now();
        String dateStr = today.format(DateTimeFormatter.ofPattern("yyMMdd"));

        // 查询今天已经生成的标签数量（按日期过滤）
        long count = tmsLabelService.count(
                new LambdaQueryWrapper<TmsLabelEntity>()
                        .apply("DATE(create_time) = CURDATE()") // 只统计今天
        );

        // 当日顺序号（从001开始）
        String seq = String.format("%03d", count + 1);

        // 拼接编码（去掉随机数，如需保留可加上）
        return "G" + dateStr + seq;
    }


    // 根据标签编码查询标签信息
    @GetMapping("/getByLabelCode")
    public List<TmsLabelEntity> getByLabelCode(@RequestParam("labelCode") String labelCode) {
        List<String> labelCodeList = Arrays.stream(labelCode.trim().split(","))
                .map(String::trim)
                .collect(Collectors.toList());

        List<TmsLabelEntity> list = tmsLabelService.list(new LambdaQueryWrapper<TmsLabelEntity>()
                .in(TmsLabelEntity::getLabelCode, labelCodeList));
        return list;
    }

    /**
     * 导出excel 表格
     * @param tmsLabel 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsLabel_export')" )
    public List<TmsLabelEntity> export(TmsLabelEntity tmsLabel,Long[] ids) {
        return tmsLabelService.list(Wrappers.lambdaQuery(tmsLabel).in(ArrayUtil.isNotEmpty(ids), TmsLabelEntity::getId, ids));
    }
}