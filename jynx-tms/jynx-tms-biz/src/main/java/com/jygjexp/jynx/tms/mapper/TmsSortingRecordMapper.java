package com.jygjexp.jynx.tms.mapper;

import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.tms.dto.DeliveryRecordGroupDTO;
import com.jygjexp.jynx.tms.entity.TmsSortingRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface TmsSortingRecordMapper extends JynxBaseMapper<TmsSortingRecordEntity> {

    List<DeliveryRecordGroupDTO> getDeliveryRecordByScanTimeGroup(@Param("scanTimeStart") LocalDateTime scanTimeStart,
                                                                  @Param("scanTimeEnd") LocalDateTime scanTimeEnd);

    List<TmsSortingRecordEntity> getDeliveryRecordByMain(@Param("orderNo") String orderNo);
}
