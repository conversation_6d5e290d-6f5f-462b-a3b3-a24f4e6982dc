package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsCageEntity;
import com.jygjexp.jynx.tms.entity.TmsLmdDriverEntity;
import com.jygjexp.jynx.tms.service.TmsCageService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 容器信息
 *
 * <AUTHOR>
 * @date 2025-04-18 14:10:07
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCage" )
@Tag(description = "tmsCage" , name = "容器信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCageController {

    private final  TmsCageService tmsCageService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsCage 笼车信息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCage_view')" )
    public R getTmsCagePage(@ParameterObject Page page, @ParameterObject TmsCageEntity tmsCage) {
        LambdaQueryWrapper<TmsCageEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Objects.nonNull(tmsCage.getId()), TmsCageEntity::getId, tmsCage.getId());
        wrapper.like(StrUtil.isNotBlank(tmsCage.getCageCode()), TmsCageEntity::getCageCode, tmsCage.getCageCode());
        wrapper.like(StrUtil.isNotBlank(tmsCage.getCageName()), TmsCageEntity::getCageName, tmsCage.getCageName());
        wrapper.eq(ObjectUtil.isNotNull(tmsCage.getIsValid()), TmsCageEntity::getIsValid, tmsCage.getIsValid());
        wrapper.eq(ObjectUtil.isNotNull(tmsCage.getSiteType()), TmsCageEntity::getSiteType, tmsCage.getSiteType());
        return R.ok(tmsCageService.page(page, wrapper));
    }


    /**
     * 通过id查询笼车信息
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsCage_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsCageService.getById(id));
    }

    /**
     * 新增笼车信息
     * @param tmsCage 笼车信息
     * @return R
     */
    @Operation(summary = "新增笼车信息" , description = "新增笼车信息" )
    @SysLog("新增笼车信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCage_add')" )
    public R save(@RequestBody TmsCageEntity tmsCage) {
        if (!checkCageCodeAndName(tmsCage)){
            return R.failed("笼车编码或名称重复");
        }
        // 获取该仓库已有笼车数量
        Long count = tmsCageService.count(new LambdaQueryWrapper<TmsCageEntity>().eq(TmsCageEntity::getSiteId, tmsCage.getSiteId()));
        tmsCage.setCageId(count+1);
        return R.ok(tmsCageService.save(tmsCage));
    }

    /**
     * 修改笼车信息
     * @param tmsCage 笼车信息
     * @return R
     */
    @Operation(summary = "修改笼车信息" , description = "修改笼车信息" )
    @SysLog("修改笼车信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCage_edit')" )
    public R updateById(@RequestBody TmsCageEntity tmsCage) {
        if (!checkCageCodeAndNameUpdate(tmsCage)){
            return R.failed("笼车编码或名称重复");
        }
        return R.ok(tmsCageService.updateById(tmsCage));
    }

    /**
     * 通过id删除笼车信息
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除笼车信息" , description = "通过id删除笼车信息" )
    @SysLog("通过id删除笼车信息" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsCage_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsCageService.removeBatchByIds(CollUtil.toList(ids)));
    }


    // 新增校验笼车编码和名称是否重复
    public Boolean checkCageCodeAndName(TmsCageEntity tmsCage) {
        LambdaQueryWrapper<TmsCageEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.and(wrapper1 -> wrapper1
                .eq(TmsCageEntity::getIsValid, true)
                .and(w -> w.eq(TmsCageEntity::getCageCode, tmsCage.getCageCode())
                        .or().eq(TmsCageEntity::getCageName, tmsCage.getCageName()))
                );

        TmsCageEntity tmsCageEntity = tmsCageService.getOne(wrapper, false);
        return tmsCageEntity == null;
    }

    // 编辑校验笼车编码和名称是否重复
    public Boolean checkCageCodeAndNameUpdate(TmsCageEntity tmsCage) {
        LambdaQueryWrapper<TmsCageEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TmsCageEntity::getIsValid, true)
                .ne(TmsCageEntity::getId, tmsCage.getId())
                .and(w -> w.eq(TmsCageEntity::getCageCode, tmsCage.getCageCode())
                        .or().eq(TmsCageEntity::getCageName, tmsCage.getCageName()));

        TmsCageEntity tmsCageEntity = tmsCageService.getOne(wrapper, false);
        return tmsCageEntity == null;
    }


    // 查新全部容器
    @GetMapping("/listCage")
    public R listCage() {
        LambdaQueryWrapper<TmsCageEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(TmsCageEntity::getCageCode,TmsCageEntity::getCageName,TmsCageEntity::getCageId,TmsCageEntity::getId,TmsCageEntity::getCageType);
        return R.ok(tmsCageService.list(wrapper));
    }

    /**
     * 导出excel 表格
     * @param tmsCage 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsCage_export')" )
    public List<TmsCageEntity> export(TmsCageEntity tmsCage,Long[] ids) {
        return tmsCageService.list(Wrappers.lambdaQuery(tmsCage).in(ArrayUtil.isNotEmpty(ids), TmsCageEntity::getId, ids));
    }
}