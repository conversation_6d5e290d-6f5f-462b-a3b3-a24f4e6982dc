package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsLineHaulOrderCageEntity;
import com.jygjexp.jynx.tms.service.TmsLineHaulOrderCageService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 干线批次容器关联表
 *
 * <AUTHOR>
 * @date 2025-04-21 10:43:01
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsLineHaulOrderCage" )
@Tag(description = "tmsLineHaulOrderCage" , name = "干线批次容器关联表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsLineHaulOrderCageController {

    private final  TmsLineHaulOrderCageService tmsLineHaulOrderCageService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsLineHaulOrderCage 干线批次容器关联表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrderCage_view')" )
    public R getTmsLineHaulOrderCagePage(@ParameterObject Page page, @ParameterObject TmsLineHaulOrderCageEntity tmsLineHaulOrderCage) {
        LambdaQueryWrapper<TmsLineHaulOrderCageEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsLineHaulOrderCageService.page(page, wrapper));
    }


    /**
     * 通过id查询干线批次容器关联表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrderCage_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsLineHaulOrderCageService.getById(id));
    }

    /**
     * 新增干线批次容器关联表
     * @param tmsLineHaulOrderCage 干线批次容器关联表
     * @return R
     */
    @Operation(summary = "新增干线批次容器关联表" , description = "新增干线批次容器关联表" )
    @SysLog("新增干线批次容器关联表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrderCage_add')" )
    public R save(@RequestBody TmsLineHaulOrderCageEntity tmsLineHaulOrderCage) {
        return R.ok(tmsLineHaulOrderCageService.save(tmsLineHaulOrderCage));
    }

    /**
     * 修改干线批次容器关联表
     * @param tmsLineHaulOrderCage 干线批次容器关联表
     * @return R
     */
    @Operation(summary = "修改干线批次容器关联表" , description = "修改干线批次容器关联表" )
    @SysLog("修改干线批次容器关联表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrderCage_edit')" )
    public R updateById(@RequestBody TmsLineHaulOrderCageEntity tmsLineHaulOrderCage) {
        return R.ok(tmsLineHaulOrderCageService.updateById(tmsLineHaulOrderCage));
    }

    /**
     * 通过id删除干线批次容器关联表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除干线批次容器关联表" , description = "通过id删除干线批次容器关联表" )
    @SysLog("通过id删除干线批次容器关联表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrderCage_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsLineHaulOrderCageService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsLineHaulOrderCage 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsLineHaulOrderCage_export')" )
    public List<TmsLineHaulOrderCageEntity> export(TmsLineHaulOrderCageEntity tmsLineHaulOrderCage,Long[] ids) {
        return tmsLineHaulOrderCageService.list(Wrappers.lambdaQuery(tmsLineHaulOrderCage).in(ArrayUtil.isNotEmpty(ids), TmsLineHaulOrderCageEntity::getId, ids));
    }
}