package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.entity.TmsOrderTrackEntity;
import com.jygjexp.jynx.tms.service.TmsOrderTrackService;
import com.jygjexp.jynx.tms.vo.TmsOrderTrackVo;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;

/**
 * 订单节点轨迹
 *
 * <AUTHOR>
 * @date 2025-03-14 17:23:05
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsOrderTrack" )
@Tag(description = "tmsOrderTrack" , name = "订单节点轨迹管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsOrderTrackController {

    private final  TmsOrderTrackService tmsOrderTrackService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsOrderTrack 卡派-订单节点轨迹
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsOrderTrack_view')" )
    public R getTmsOrderTrackPage(@ParameterObject Page page, @ParameterObject TmsOrderTrackVo tmsOrderTrack) {
        return R.ok(tmsOrderTrackService.search(page,tmsOrderTrack));
    }


    /**
     * 通过id查询卡派-订单节点轨迹
     * @param trackId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{trackId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderTrack_view')" )
    public R getById(@PathVariable("trackId" ) Long trackId) {
        return R.ok(tmsOrderTrackService.getById(trackId));
    }

    /**
     * 新增卡派-订单节点轨迹
     * @param tmsOrderTrack 卡派-订单节点轨迹
     * @return R
     */
    @Operation(summary = "新增-订单节点轨迹" , description = "新增-订单节点轨迹" )
    @SysLog("新增-订单节点轨迹" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderTrack_add')" )
    public R save(@RequestBody TmsOrderTrackEntity tmsOrderTrack) {
        // 校验新增轨迹是否存在重复
        Boolean isCodeDuplicated = checkTrackCode(tmsOrderTrack);
        if (!isCodeDuplicated){
            return R.failed("轨迹已存在");
        }
        tmsOrderTrack.setOperatorId(SecurityUtils.getUser().getId());
        tmsOrderTrack.setDriverId(0l);
        tmsOrderTrack.setIsAuto("手动");
        // 操作时间-服务器时间
        tmsOrderTrack.setAddTime(LocalDateTime.now());
        // 操作时间-本地北京时间
        tmsOrderTrack.setLocalAddTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
        return R.ok(tmsOrderTrackService.save(tmsOrderTrack));
    }

    // "校验新增轨迹是否存在重复
    private Boolean checkTrackCode(TmsOrderTrackEntity tmsOrderTrack) {
        // 校验新增轨迹是否存在重复
        LambdaQueryWrapper<TmsOrderTrackEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TmsOrderTrackEntity::getOrderNo, tmsOrderTrack.getOrderNo());
        wrapper.eq(TmsOrderTrackEntity::getNodeName, tmsOrderTrack.getNodeName());
        wrapper.like(TmsOrderTrackEntity::getLocationDescription, tmsOrderTrack.getLocationDescription());
        if (tmsOrderTrackService.count(wrapper) > 0) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 修改卡派-订单节点轨迹
     * @param tmsOrderTrack 卡派-订单节点轨迹
     * @return R
     */
    @Operation(summary = "修改卡派-订单节点轨迹" , description = "修改卡派-订单节点轨迹" )
    @SysLog("修改卡派-订单节点轨迹" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderTrack_edit')" )
    public R updateById(@RequestBody TmsOrderTrackEntity tmsOrderTrack) {
        return R.ok(tmsOrderTrackService.updateById(tmsOrderTrack));
    }

    /**
     * 通过id删除卡派-订单节点轨迹
     * @param ids trackId列表
     * @return R
     */
    @Operation(summary = "通过id删除卡派-订单节点轨迹" , description = "通过id删除卡派-订单节点轨迹" )
    @SysLog("通过id删除卡派-订单节点轨迹" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderTrack_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsOrderTrackService.removeBatchByIds(CollUtil.toList(ids)));
    }


    @Operation(summary = "根据经纬度获取地址", description = "根据经纬度获取地址")
    @PostMapping("/getLatLng/searchCity")
    public ResponseEntity<String> searchCity(@RequestParam BigDecimal lat, @RequestParam BigDecimal lng) {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(tmsOrderTrackService.searchCity(lat,lng));
    }

    // 根据客户单号删除轨迹
    @Operation(summary = "根据客户单号删除轨迹", description = "根据客户单号删除轨迹")
    @PostMapping("/deleteByCustomerOrderNo")
    public R deleteByCustomerOrderNo(@RequestParam String customerOrderNo) {
        return R.ok(tmsOrderTrackService.deleteByCustomerOrderNo(customerOrderNo));
    }

    // 根据订单号查询轨迹列表中状态码最大的那条记录
    @Operation(summary = "根据订单号查询轨迹列表中状态码最大的那条记录", description = "根据订单号查询轨迹列表中状态码最大的那条记录")
    @GetMapping("/getMaxStatusCodeByOrderNo")
    public R getMaxStatusCodeByOrderNo(@RequestParam String orderNo) {
        return R.ok(tmsOrderTrackService.getMaxStatusCodeByOrderNo(orderNo));
    }


    /**
     * 导出excel 表格
     * @param tmsOrderTrack 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsOrderTrack_export')" )
    public List<TmsOrderTrackEntity> export(TmsOrderTrackEntity tmsOrderTrack,Long[] ids) {
        return tmsOrderTrackService.list(Wrappers.lambdaQuery(tmsOrderTrack).in(ArrayUtil.isNotEmpty(ids), TmsOrderTrackEntity::getTrackId, ids));
    }

    // ==================== 迁移自zxoms的轨迹查询接口 ====================

    /**
     * 批量订单轨迹查询 - 迁移自zxoms
     * @param pkgNos 包裹号列表，逗号分隔
     * @param zip 邮编（可选）
     * @return 批量轨迹查询结果
     */
    @Operation(summary = "批量订单轨迹查询", description = "批量订单轨迹查询 - 迁移自zxoms")
    @GetMapping("/tracks")
    public R getTracks(@RequestParam("pkgNos") String pkgNos,
                      @RequestParam(value = "zip", required = false) String zip) {
        return tmsOrderTrackService.getTracksFromZxoms(pkgNos, zip);
    }
}