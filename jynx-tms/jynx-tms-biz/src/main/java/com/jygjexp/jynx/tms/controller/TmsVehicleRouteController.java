package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsVehicleRouteEntity;
import com.jygjexp.jynx.tms.service.TmsVehicleRouteService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 车辆路线规划
 *
 * <AUTHOR>
 * @date 2025-03-17 15:36:10
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsVehicleRoute" )
@Tag(description = "tmsVehicleRoute" , name = "车辆路线规划管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsVehicleRouteController {

    private final  TmsVehicleRouteService tmsVehicleRouteService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsVehicleRoute 车辆路线规划
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRoute_view')" )
    public R getTmsVehicleRoutePage(@ParameterObject Page page, @ParameterObject TmsVehicleRouteEntity tmsVehicleRoute) {
        LambdaQueryWrapper<TmsVehicleRouteEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsVehicleRouteService.page(page, wrapper));
    }


    /**
     * 通过id查询车辆路线规划
     * @param vehicleRouteId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{vehicleRouteId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRoute_view')" )
    public R getById(@PathVariable("vehicleRouteId" ) Long vehicleRouteId) {
        return R.ok(tmsVehicleRouteService.getById(vehicleRouteId));
    }

    /**
     * 新增车辆路线规划
     * @param tmsVehicleRoute 车辆路线规划
     * @return R
     */
    @Operation(summary = "新增车辆路线规划" , description = "新增车辆路线规划" )
    @SysLog("新增车辆路线规划" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRoute_add')" )
    public R save(@RequestBody TmsVehicleRouteEntity tmsVehicleRoute) {
        return R.ok(tmsVehicleRouteService.save(tmsVehicleRoute));
    }

    /**
     * 修改车辆路线规划
     * @param tmsVehicleRoute 车辆路线规划
     * @return R
     */
    @Operation(summary = "修改车辆路线规划" , description = "修改车辆路线规划" )
    @SysLog("修改车辆路线规划" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRoute_edit')" )
    public R updateById(@RequestBody TmsVehicleRouteEntity tmsVehicleRoute) {
        return R.ok(tmsVehicleRouteService.updateById(tmsVehicleRoute));
    }

    /**
     * 通过id删除车辆路线规划
     * @param ids vehicleRouteId列表
     * @return R
     */
    @Operation(summary = "通过id删除车辆路线规划" , description = "通过id删除车辆路线规划" )
    @SysLog("通过id删除车辆路线规划" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRoute_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsVehicleRouteService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsVehicleRoute 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsVehicleRoute_export')" )
    public List<TmsVehicleRouteEntity> export(TmsVehicleRouteEntity tmsVehicleRoute,Long[] ids) {
        return tmsVehicleRouteService.list(Wrappers.lambdaQuery(tmsVehicleRoute).in(ArrayUtil.isNotEmpty(ids), TmsVehicleRouteEntity::getVehicleRouteId, ids));
    }
}