package com.jygjexp.jynx.tms.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.TrackTypeConstant;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsOrderTrackMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsCargoInfoService;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsCustomerService;
import com.jygjexp.jynx.tms.service.TmsOrderTrackService;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.excel.TmsCustomerOrderExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsCustomerZdjOrderExcelVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户端客户订单管理
 *
 * <AUTHOR>
 * @date 2025-03-31 15:09:24
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/client/tmsCustomerOrder" )
@Tag(description = "tmsCustomerOrder" , name = "客户端客户订单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsClientCustomerOrderController {

    private final  TmsCustomerOrderService tmsCustomerOrderService;
    private final TmsCustomerService tmsCustomerService;
    private final TmsOrderTrackService tmsOrderTrackService;
    private final TmsOrderTrackMapper orderTrackMapper;
    private final TmsCargoInfoService tmsCargoInfoService;

    /**
     * 客户端客户订单分页查询
     * @param orderPageVo 卡派客户订单
     * @return
     */
    @Operation(summary = "客户端客户订单分页查询" , description = "客户端客户订单分页查询" )
    @PostMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_view')" )
    public R getTmsCustomerOrderPage(@RequestBody TmsCustomerOrderPageRequest orderPageVo) {
        return R.ok(tmsCustomerOrderService.clientSearch(new Page(orderPageVo.getCurrent(), orderPageVo.getSize(),orderPageVo.getTotal()),
                orderPageVo.getRequestVo(),Boolean.FALSE));
    }

    /**
     * 客户端轨迹查询
     * @param page 分页对象
     * @param tmsOrderTrack 卡派-订单节点轨迹
     * @return
     */
    @Operation(summary = "客户端轨迹查询" , description = "客户端轨迹查询" )
    @GetMapping("/orderTrackPage" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsOrderTrack_view')" )
    public R getTmsOrderTrackPage(Page page, TmsOrderTrackVo tmsOrderTrack) {
        MPJLambdaWrapper<TmsOrderTrackEntity> wrapper = new MPJLambdaWrapper<>();

        // 获取当前查询的订单号（箱单号）
        String orderNo = (tmsOrderTrack.getOrderNo() == null) ? "0000" : tmsOrderTrack.getOrderNo();
        wrapper.selectAll(TmsOrderTrackEntity.class);
        wrapper.select(TmsLmdDriverEntity::getDriverName);
        wrapper.eq(TmsOrderTrackEntity::getOrderNo, orderNo);
        //客户端只能看外部轨迹
        wrapper.eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL);
        wrapper.leftJoin(TmsLmdDriverEntity.class,TmsLmdDriverEntity::getDriverId, TmsOrderTrackEntity::getDriverId);
        wrapper.orderByDesc(TmsOrderTrackEntity::getAddTime);

        return R.ok(orderTrackMapper.selectJoinPage(page, TmsOrderTrackVo.class, wrapper));
    }


    // 客户端轨迹查询--中大件版本
    @Operation(summary = "中大件-客户端轨迹查询", description = "中大件-客户端轨迹查询")
    @SysLog("中大件-轨迹查询" )
    @PostMapping("/zdj/getTrack")
    public R getZdjClientTrack(@RequestParam("orderNo") String orderNo) {
        return tmsCustomerOrderService.getZdjClientTrack(orderNo);
    }

    // 客户端根据箱号查询对应轨迹节点记录--中大件版本
    @Operation(summary = "中大件客户端-根据箱号查询对应轨迹节点记录", description = "中大件客户端-根据箱号查询对应轨迹节点记录")
    @GetMapping("zdj/clientTrack/list/{subOrderNo}")
    public R getZdjClientTrackList(@PathVariable("subOrderNo") String subOrderNo) {
        return tmsCustomerOrderService.getZdjClientTrackList(subOrderNo);
    }


    /**
     * 客户端订单详情箱号查询轨迹
     *
     * @return R
     */
    @Operation(summary = "客户端订单详情箱号查询轨迹", description = "客户端订单详情箱号查询轨迹")
    @GetMapping("/box/getTrack")
    public R getClientBoxTrack(@RequestParam String orderNo) {
        return R.ok(tmsCustomerOrderService.getClientBoxTrack(orderNo));
    }


    /**
     * 通过id查询卡派客户订单
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        TmsCustomerOrderEntity tmsCustomerOrderEntity = tmsCustomerOrderService.getById(id);
        //如果数据中的customerOrderNumber为KH开头则置空
        if (StringUtils.startsWith(tmsCustomerOrderEntity.getCustomerOrderNumber(), "KH")) {
            tmsCustomerOrderEntity.setCustomerOrderNumber(null);
        }
        return R.ok(tmsCustomerOrderEntity);
    }

    /**
     * 新增客户端客户订单
     * @param tmsCustomerOrder 新增客户端客户订单
     * @return R
     */
    @Operation(summary = "新增客户端客户订单" , description = "新增客户端客户订单" )
    @SysLog("新增客户端客户订单" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R save(@RequestBody TmsCustomerOrderEntity tmsCustomerOrder) {
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if(ObjectUtil.isNull(customer)){
            return R.failed(LocalizedR.getMessage("account.not.opened",null));
        }
        tmsCustomerOrder.setCustomerId(customer.getId());
        return R.ok(tmsCustomerOrderService.create(tmsCustomerOrder));
    }

    /**
     * 修改客户端客户订单
     * @param tmsCustomerOrder 修改客户端客户订单
     * @return R
     */
    @Operation(summary = "修改客户端客户订单" , description = "修改客户端客户订单" )
    @SysLog("修改客户端客户订单" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_edit')" )
    public R updateById(@RequestBody TmsCustomerOrderEntity tmsCustomerOrder) {
        if (tmsCustomerOrder.getCargoInfoEntityList() != null && !tmsCustomerOrder.getCargoInfoEntityList().isEmpty()) {
            R result = tmsCustomerOrderService.updateCustomerOrderZdj(tmsCustomerOrder);
            if(result.getCode()==0){
                return R.ok();
            }else{
                return R.failed(result.getMsg());
            }
        }else{
            return R.failed(LocalizedR.getMessage("tms.cargo.info.not.null",null));
        }
    }

    /**
     * 通过id删除客户端客户订单
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除客户端客户订单" , description = "通过id删除客户端客户订单" )
    @SysLog("通过id删除客户端客户订单" )
    @Transactional
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_del')" )
    public R removeById(@RequestBody List<Long> ids) {
        if(ObjectUtil.isNull(ids) || ids.isEmpty()){
            return R.failed(LocalizedR.getMessage("selected.order.not.null",null));
        }
        //删除对应货物信息
        List<TmsCustomerOrderEntity> tmsCustomerOrderEntities = tmsCustomerOrderService.listByIds(ids);
        List<String> entrustedOrderNumbers = tmsCustomerOrderEntities.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .collect(Collectors.toList());
        if(ObjectUtil.isNotNull(entrustedOrderNumbers) && !entrustedOrderNumbers.isEmpty()){
            tmsCargoInfoService.remove(new LambdaQueryWrapper<TmsCargoInfoEntity>()
                    .in(TmsCargoInfoEntity::getEntrustedOrderNumber,entrustedOrderNumbers));
        }
        //删除订单
        boolean removed = tmsCustomerOrderService.removeBatchByIds(ids);
        if(removed){
            return R.ok();
        }else{
            return R.failed();
        }

    }


    /**
     * 卡派客户订单导出
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
    @SysLog("中大件客户订单导出")
    @Operation(summary = "中大件客户订单导出" , description = "中大件客户订单导出" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_export')" )
    public List<TmsCustomerZdjOrderExcelVo> export(@RequestBody TmsCustomerOrderPageRequest request) {
       return tmsCustomerOrderService.getZdjExcel(request.getRequestVo(), request.getIds());
    }

    /**
     * 卡派客户端订单询价
     * @param vo id列表
     * @return R
     */
    @Operation(summary = "卡派客户端订单询价" , description = "卡派客户端订单询价" )
    @SysLog("卡派客户端订单询价" )
    @PostMapping("/getOrderPrice")
    //@PreAuthorize("@pms.hasPermission('tms_tmsEntrustedOrder_orderPrice')" )
    public R getOrderPrice(@Valid @RequestBody TmsEntrustedOrderVo vo) {
        return tmsCustomerOrderService.getOrderPrice(vo);
    }

    /**
     * 客户端订单调度-分配承运商-同步生成委托订单
     * @param customerOrderNumber
     * @param carrierId
     * @return
     */
    @Operation(summary = "客户端订单调度-分配承运商", description = "客户端订单调度-分配承运商")
    @PostMapping("/assignCarrier")
    public R assignCarrier(@Parameter(description = "客户单号-多个单号用逗号分割", required = true) @RequestParam String customerOrderNumber,
                           @Parameter(description = "承运商ID", required = true) @RequestParam Long carrierId) {
        return R.ok(tmsCustomerOrderService.assignCarrier(customerOrderNumber, carrierId));
    }

    /**
     * 客户端订单调度-判断承运商区域(经纬度)
     * @param vo
     * @return
     */
    @Operation(summary = "客户端订单调度-判断承运商区域(经纬度)", description = "客户端订单调度-判断承运商区域(经纬度)")
    @PostMapping("/isCarrierArea")
    public Boolean isCarrierArea(@RequestBody TmsIsCarrierAreaVo vo) {
        return tmsCustomerOrderService.isCarrierArea(vo);
    }

    /**
     * 客户端订单调度-判断承运商区域(三字邮编)
     * @param vo
     * @return
     */
    @Operation(summary = "客户端订单调度-判断承运商区域(三字邮编)", description = "客户端订单调度-判断承运商区域(三字邮编)")
    @PostMapping("/isPostCarrierArea")
    public Boolean isPostCarrierArea(@RequestBody TmsIsCarrierAreaVo vo) {
        return tmsCustomerOrderService.isPostCarrierArea(vo);
    }

    @Operation(summary = "客户端取消下单", description = "客户端取消下单")
    @PostMapping("/{id}/cancelOrder")
    public R cancelOrder(@PathVariable("id") Long id) {
        return R.ok(tmsCustomerOrderService.cancelOrder(id));
    }

}