package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsThirdPartPostEntity;
import com.jygjexp.jynx.tms.service.TmsThirdPartPostService;
import com.jygjexp.jynx.tms.vo.ExchangeOrders;
import com.jygjexp.jynx.tms.vo.ExchangeVo;
import com.jygjexp.jynx.tms.vo.TmsThirdPartPostPageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsTmsThirdPartPostExcelVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 换单列表
 *
 * <AUTHOR>
 * @date 2025-06-12 18:04:28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsThirdPartPost" )
@Tag(description = "tmsThirdPartPost" , name = "换单列表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsThirdPartPostController {

    private final  TmsThirdPartPostService tmsThirdPartPostService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsThirdPartPost 换单列表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsThirdPartPost_view')" )
    public R getTmsThirdPartPostPage(@ParameterObject Page page, @ParameterObject TmsThirdPartPostPageVo tmsThirdPartPost) {
        return R.ok(tmsThirdPartPostService.search(page, tmsThirdPartPost));
    }



    /**
     * 批量查询是否有单号需要换单
     * @return
     */
    @Operation(summary = "批量查询是否有单号需要换单" , description = "批量查询是否有单号需要换单" )
    @PostMapping("/getOrderNos" )
    public R getOrderNos(@RequestBody ExchangeOrders orderNos) {
       return tmsThirdPartPostService.getOrderNos(orderNos);
    }


    /**
     * 通过id查询换单列表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsThirdPartPost_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsThirdPartPostService.getById(id));
    }


    /**
     * 修改换单列表
     * @param tmsThirdPartPost 换单列表
     * @return R
     */
    @Operation(summary = "修改换单列表" , description = "修改换单列表" )
    @SysLog("修改换单列表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsThirdPartPost_edit')" )
    public R updateById(@RequestBody TmsThirdPartPostEntity tmsThirdPartPost) {
        return R.ok(tmsThirdPartPostService.updateById(tmsThirdPartPost));
    }

    /**
     * 通过id删除换单列表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除换单列表" , description = "通过id删除换单列表" )
    @SysLog("通过id删除换单列表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsThirdPartPost_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsThirdPartPostService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsThirdPartPost 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsThirdPartPost_export')" )
    public List<TmsTmsThirdPartPostExcelVo> export(TmsThirdPartPostPageVo tmsThirdPartPost, Long[] ids) {
        return tmsThirdPartPostService.getThirdPartPostExcel(tmsThirdPartPost, ids);
    }
}