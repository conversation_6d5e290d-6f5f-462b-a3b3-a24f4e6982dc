package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsAddressAnomalyEntity;
import com.jygjexp.jynx.tms.service.TmsAddressAnomalyService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 地址异常记录
 *
 * <AUTHOR>
 * @date 2025-06-12 18:03:40
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsAddressAnomaly" )
@Tag(description = "tmsAddressAnomaly" , name = "地址异常记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsAddressAnomalyController {

    private final  TmsAddressAnomalyService tmsAddressAnomalyService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsAddressAnomaly 地址异常记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressAnomaly_view')" )
    public R getTmsAddressAnomalyPage(@ParameterObject Page page, @ParameterObject TmsAddressAnomalyEntity tmsAddressAnomaly) {
        LambdaQueryWrapper<TmsAddressAnomalyEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsAddressAnomalyService.page(page, wrapper));
    }


    /**
     * 通过id查询地址异常记录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressAnomaly_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsAddressAnomalyService.getById(id));
    }

    /**
     * 新增地址异常记录
     * @param tmsAddressAnomaly 地址异常记录
     * @return R
     */
    @Operation(summary = "新增地址异常记录" , description = "新增地址异常记录" )
    @SysLog("新增地址异常记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressAnomaly_add')" )
    public R save(@RequestBody TmsAddressAnomalyEntity tmsAddressAnomaly) {
        return R.ok(tmsAddressAnomalyService.save(tmsAddressAnomaly));
    }

    /**
     * 修改地址异常记录
     * @param tmsAddressAnomaly 地址异常记录
     * @return R
     */
    @Operation(summary = "修改地址异常记录" , description = "修改地址异常记录" )
    @SysLog("修改地址异常记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressAnomaly_edit')" )
    public R updateById(@RequestBody TmsAddressAnomalyEntity tmsAddressAnomaly) {
        return R.ok(tmsAddressAnomalyService.updateById(tmsAddressAnomaly));
    }

    /**
     * 通过id删除地址异常记录
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除地址异常记录" , description = "通过id删除地址异常记录" )
    @SysLog("通过id删除地址异常记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressAnomaly_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsAddressAnomalyService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsAddressAnomaly 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsAddressAnomaly_export')" )
    public List<TmsAddressAnomalyEntity> export(TmsAddressAnomalyEntity tmsAddressAnomaly,Long[] ids) {
        return tmsAddressAnomalyService.list(Wrappers.lambdaQuery(tmsAddressAnomaly).in(ArrayUtil.isNotEmpty(ids), TmsAddressAnomalyEntity::getId, ids));
    }
}