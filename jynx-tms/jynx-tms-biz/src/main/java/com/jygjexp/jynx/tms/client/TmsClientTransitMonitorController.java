package com.jygjexp.jynx.tms.client;

import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.service.TmsEntrustedOrderService;
import com.jygjexp.jynx.tms.service.TmsShipmentOrderService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户端在途监控
 *
 * <AUTHOR>
 * @date 2025-03-31 15:42:43
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/client/transitMonitor" )
public class TmsClientTransitMonitorController {

    private final TmsShipmentOrderService tmsShipmentOrderService;
    private final TmsEntrustedOrderService tmsEntrustedOrderService;

    /**
     * 客户端查询在途运输单信息
     *
     * @return R
     */
    @Operation(summary = "客户端查询在途运输单信息" , description = "客户端查询在途运输单信息" )
    @GetMapping("/getListTransitOrder" )
    //@PreAuthorize("@pms.hasPermission('tms_tmsShipmentOrder_view')" )
    public R getListTransitOrder() {
        return R.ok(tmsShipmentOrderService.getClientListTransitOrder());
    }

}
