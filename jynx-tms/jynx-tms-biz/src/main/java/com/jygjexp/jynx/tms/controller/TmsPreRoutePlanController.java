package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.TmsPreRoutePlanEntity;
import com.jygjexp.jynx.tms.service.TmsPreRoutePlanService;
import com.jygjexp.jynx.tms.utils.GeoDistanceUtil;
import com.jygjexp.jynx.tms.vo.TmsTransportTaskOrderPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * web端中大件路线规划
 *
 * <AUTHOR>
 * @date 2025-04-07 14:46:30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsPreRoutePlan" )
@Tag(description = "tmsPreRoutePlan" , name = "web端中大件路线规划管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsPreRoutePlanController {

    private final  TmsPreRoutePlanService tmsPreRoutePlanService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsPreRoutePlanPageDto web端预先路线规划
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_view')" )
    public R getTmsPreRoutePlanPage(Page page, TmsPreRoutePlanPageDto tmsPreRoutePlanPageDto) {
        return tmsPreRoutePlanService.pageSearch(page, tmsPreRoutePlanPageDto);
    }

    /**
     * 根据批次号查询该批次下由路线编号分组的订单信息
     */
    @Operation(summary = "根据批次号查询该批次下由路线编号分组的订单信息" , description = "根据批次号查询该批次下由路线编号分组的订单信息" )
    @GetMapping("/getGroupOrderByBatchNo" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_get_gOrder')" )
    public R getGroupOrderByBatchNo(@RequestParam(required = true) String batchNo,@RequestParam(required = false)Integer orderStatus,@RequestParam(required = false)Boolean planStatus) {
        return tmsPreRoutePlanService.getGroupOrderByBatchNo(batchNo,orderStatus,planStatus);
    }

    /**
     * 根据批次号查询该批次下由路线编号分组的订单信息
     */
    @Operation(summary = "根据批次号查询该批次下由路线编号分组的订单信息New" , description = "根据批次号查询该批次下由路线编号分组的订单信息New" )
    @GetMapping("/getGroupOrderByBatchNoNew" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_get_gOrder')" )
    public R getGroupOrderByBatchNoNew(@RequestParam(required = true) String batchNos,@RequestParam(required = false)Integer orderStatus,@RequestParam(required = false)Boolean planStatus) {
        return tmsPreRoutePlanService.getGroupOrderByBatchNoNew(batchNos,orderStatus,planStatus);
    }

    /**
     * 根据司机号模糊查询司机信息
     */
    @Operation(summary = "根据司机号模糊查询司机信息" , description = "根据司机号模糊查询司机信息" )
    @GetMapping("/getDriverByDriverNo" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_get_driver')" )
    public R getDriverByDriverNo(@RequestParam String diverNo) {
        return tmsPreRoutePlanService.getDriverByDriverNo(diverNo);
    }
    /**
     * 订单查询（分页）
     * @param page 订单查询（分页）
     * @return
     */
    @Operation(summary = "订单查询" , description = "订单查询" )
    @GetMapping("/queryOrder" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_get_order')" )
    public R queryOrder(Page page, TmsPreRoutePlanOrderDto tmsPreRoutePlanOrderDto) {
        return tmsPreRoutePlanService.queryOrder(page, tmsPreRoutePlanOrderDto);
    }

    /**
     * 新增web端预先路线规划
     */
    @Operation(summary = "新增web端中大件路线规划" , description = "新增web端中大件路线规划" )
    @SysLog("新增web端中大件路线规划" )
    @PostMapping("/addRoutePlan")
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_add')" )
    public R addPreRoutePlan(@RequestBody TmsPreRoutePlanDto tmsPreRoutePlanDto) {
        return tmsPreRoutePlanService.addPreRoutePlan(tmsPreRoutePlanDto);
    }

    /**
     * 司机列表路径规划数据查询
     */
    @Operation(summary = "司机列表路径规划数据查询" , description = "司机列表路径规划数据查询" )
    @GetMapping("/getDriverListRoutePlans" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_drpdata')" )
    public R getDriverListRoutePlans(@RequestParam(value = "batchNo", required = true) String batchNo,
                                     @RequestParam(value = "routeNos", required = false) String routeNos,
                                     @RequestParam(value = "startSequenceNo", required = false) Integer startSequenceNo,
                                     @RequestParam(value = "endSequenceNo", required = false) Integer endSequenceNo,
                                     @RequestParam(value = "planName", required = false) String planName) {
        return tmsPreRoutePlanService.getDriverListRoutePlans(batchNo,routeNos,startSequenceNo,endSequenceNo,planName);
    }

    /**
     * 司机列表路径规划数据查询
     */
    @Operation(summary = "司机列表路径规划数据查询New" , description = "司机列表路径规划数据查询New" )
    @GetMapping("/getDriverListRoutePlansNew" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_drpdata')" )
    public R getDriverListRoutePlansNew(@RequestParam(value = "routeNos", required = false) String routeNos,
                                     @RequestParam(value = "startSequenceNo", required = false) Integer startSequenceNo,
                                     @RequestParam(value = "endSequenceNo", required = false) Integer endSequenceNo,
                                     @RequestParam(value = "planName", required = false) String planName) {
        return tmsPreRoutePlanService.getDriverListRoutePlansNew(routeNos,startSequenceNo,endSequenceNo,planName);
    }

    /**
     * 撤销路径规划
     */
    @Operation(summary = "撤销路径规划" , description = "撤销路径规划" )
    @SysLog("撤销路径规划" )
    @DeleteMapping("/cancelRoutePlan")
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_cancel')" )
    public R cancelRoutePlan(String planName) {
        return tmsPreRoutePlanService.cancelRoutePlan(planName);
    }

    /**
     * 路径规划-派送快速转单
     */
    @Operation(summary = "路径规划-派送快速转单" , description = "路径规划-派送快速转单" )
    @SysLog("路径规划-派送快速转单" )
    @PostMapping("/transferDeliveryOrders")
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_transfer')" )
    public R transferDeliveryOrders(@RequestBody TmsPreRoutePlanTransferOrderDto tmsPreRoutePlanTransferOrderDto) {
        return tmsPreRoutePlanService.transferDeliveryOrders(tmsPreRoutePlanTransferOrderDto);
    }
    /**
     * 路径规划-派送快速转单
     */
    @Operation(summary = "路径规划-派送快速转单New" , description = "路径规划-派送快速转单New" )
    @SysLog("路径规划-派送快速转单" )
    @PostMapping("/transferDeliveryOrdersNew")
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_transfer')" )
    public R transferDeliveryOrdersNew(@RequestBody TmsPreRoutePlanTransferOrderDto tmsPreRoutePlanTransferOrderDto) {
        return tmsPreRoutePlanService.transferDeliveryOrdersNew(tmsPreRoutePlanTransferOrderDto);
    }

    /**
     * 路径规划-司机-规划-转移订单
     */
    @Operation(summary = "路径规划-司机-规划-转移订单" , description = "路径规划-司机-规划-转移订单" )
    @SysLog("路径规划-司机-规划-转移订单" )
    @PostMapping("/dTransferDeliveryOrders")
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_dTransfer')" )
    public R dTransferDeliveryOrders(@RequestBody TmsPreRoutePlanDriverTransferOrderDto tmsPreRoutePlanDriverTransferOrderDto) {
        return tmsPreRoutePlanService.dTransferDeliveryOrders(tmsPreRoutePlanDriverTransferOrderDto);
    }

    /**
     * 路径规划-司机-规划-转移订单
     */
    @Operation(summary = "路径规划-司机-规划-转移订单New" , description = "路径规划-司机-规划-转移订单New" )
    @SysLog("路径规划-司机-规划-转移订单" )
    @PostMapping("/dTransferDeliveryOrdersNew")
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_dTransfer')" )
    public R dTransferDeliveryOrdersNew(@RequestBody TmsPreRoutePlanDriverTransferOrderDto tmsPreRoutePlanDriverTransferOrderDto) {
        return tmsPreRoutePlanService.dTransferDeliveryOrdersNew(tmsPreRoutePlanDriverTransferOrderDto);
    }


    /**
     * 根据路径规划id查询相应路线详情信息
     */
    @Operation(summary = "根据路径规划id查询相应路线详情信息" , description = "根据路径规划id查询相应路线详情信息" )
    @GetMapping("/getPreRoutePlanDetailsById/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_view')" )
    public R getPreRoutePlanDetailsById(@PathVariable Long id) {
        return tmsPreRoutePlanService.getPreRoutePlanDetailsById(id);
    }

    /**
     * 根据跟踪单号查询派送任务单详情信息（路径规划详情中查询任务单）
     */
    @Operation(summary = "根据跟踪单号查询派送任务单详情信息" , description = "根据跟踪单号查询派送任务单详情信息" )
    @GetMapping("/getOrderDetailsByEntrustedOrderNumber" )
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_view')" )
    public R getOrderDetailsByEntrustedOrderNumber(String entrustedOrderNumber) {
        return tmsPreRoutePlanService.getTaskOrderDetailsByTaskNo(entrustedOrderNumber);
    }


    /**
     * 查询司机此时的派送订单
     */
    @Operation(summary = "查询司机此时的派送订单" , description = "查询司机此时的派送订单" )
    @GetMapping("/getDriverDeliveryOrder" )
    public R getDriverDeliveryOrder(@RequestParam(value = "driverId",required = true) Long  driverId,@RequestParam(value = "orderStatus",required = true) Integer orderStatus) {
        return tmsPreRoutePlanService.getDriverDeliveryOrder(driverId,orderStatus);
    }

    /**
     * 通过id删除web端预先路线规划
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除web端预先路线规划" , description = "通过id删除web端预先路线规划" )
    @SysLog("通过id删除web端预先路线规划" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsPreRoutePlanService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 获取路径规划订单详细信息
     */
    @Operation(summary = "获取路径规划订单详细信息",description = "获取路径规划订单详细信息")
    @GetMapping("/getPreRoutePlanOrderDetails")
    public R getPreRoutePlanOrderDetails(@RequestParam(value = "entrustedOrderNumber",required = true) String entrustedOrderNumber) {
        return tmsPreRoutePlanService.getPreRoutePlanOrderDetails(entrustedOrderNumber);
    }

    /**
     * 导出excel 表格
     * @param tmsPreRoutePlan 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsPreRoutePlan_export')" )
    public List<TmsPreRoutePlanEntity> export(TmsPreRoutePlanEntity tmsPreRoutePlan,Long[] ids) {
        return tmsPreRoutePlanService.list(Wrappers.lambdaQuery(tmsPreRoutePlan).in(ArrayUtil.isNotEmpty(ids), TmsPreRoutePlanEntity::getId, ids));
    }



    /**
     * 测试工具
     * @return
     */
    @Operation(summary = "测试工具" , description = "测试工具" )
    @GetMapping("/test" )
    public R test() {

        List<GeoDistanceUtil.LatLng> candidates = new ArrayList<>();
        //3
        GeoDistanceUtil.LatLng order1 = GeoDistanceUtil.LatLng.from(new BigDecimal("49.1624251"), new BigDecimal("-123.1510485"), "N2507D0000188");
        //4
        GeoDistanceUtil.LatLng order2 = GeoDistanceUtil.LatLng.from(new BigDecimal("49.1649288"), new BigDecimal("-123.1403392"), "N2507D0000177");
        //5
        GeoDistanceUtil.LatLng order3 = GeoDistanceUtil.LatLng.from(new BigDecimal("49.1530807"), new BigDecimal("-123.1439381"), "N2507D0000476");
        //6
        GeoDistanceUtil.LatLng order4 = GeoDistanceUtil.LatLng.from(new BigDecimal("49.147595"), new BigDecimal("-123.122542"), "N2507D0000532");
        candidates.add(order1);
        candidates.add(order2);
        candidates.add(order3);
        candidates.add(order4);
        GeoDistanceUtil.LatLng nearest = GeoDistanceUtil.findNearest(new BigDecimal("49.154164"), new BigDecimal("-123.142318"), candidates);
        System.out.println("最近订单号：" + nearest.orderNo);
        return R.ok(nearest.orderNo);
    }
}