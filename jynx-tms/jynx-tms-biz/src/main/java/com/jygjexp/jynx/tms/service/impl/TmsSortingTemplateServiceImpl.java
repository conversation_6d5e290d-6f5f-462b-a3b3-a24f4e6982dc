package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.StoreConstants;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.BusinessType;
import com.jygjexp.jynx.tms.enums.SortingRuleType;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsSortingTemplateMapper;
import com.jygjexp.jynx.tms.request.ConditionGroup;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.BooleanUtil;
import com.jygjexp.jynx.tms.utils.BuildSpElUtil;
import com.jygjexp.jynx.tms.utils.SpelRuleUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.mail.Store;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分拣模版表
 *
 * <AUTHOR>
 * @date 2025-06-12 19:58:08
 */
@RequiredArgsConstructor
@Service
public class TmsSortingTemplateServiceImpl extends ServiceImpl<TmsSortingTemplateMapper, TmsSortingTemplateEntity> implements TmsSortingTemplateService {
    private final TmsSortingTemplateMapper tmsSortingTemplateMapper;
    private final TmsSortingRuleService tmsSortingRuleService;
    private final TmsOverAreaService tmsOverAreaService;
    private final TmsSiteService  tmsSiteService;
    private final TmsSortingGridService tmsSortingGridService;
    private final TmsSortingTemplateChangeRecordService changeRecordService;


    /**
     * 新增分拣模版表
     */
    @Transactional
    @Override
    public R add(TmsSortingTemplateDto tmsSortingTemplateDto) {
        if(ObjectUtil.isNull(tmsSortingTemplateDto.getTemplateName())){
            throw new CustomBusinessException("模版名称不能为空！");
        }

        if(tmsSortingTemplateDto.getBusinessType().equals(BusinessType.COLLECTION.getCode())
                && tmsSortingTemplateDto.getSortingRuleType().equals(SortingRuleType.ROUTE_NUMBER.getCode())){
            throw new CustomBusinessException("业务为揽收退件不支持按路线号分拣！");
        }
        if(tmsSortingTemplateDto.getBusinessType().equals(BusinessType.DELIVERY.getCode())
                && tmsSortingTemplateDto.getSortingRuleType().equals(SortingRuleType.MERCHANT.getCode())){
            throw new CustomBusinessException("业务为正向派件不支持按商家分拣！");
        }
        //查询一下模版是否存在
        TmsSortingTemplateEntity checkTemplate = this.getOne(new LambdaQueryWrapper<TmsSortingTemplateEntity>()
                .eq(TmsSortingTemplateEntity::getTemplateName, tmsSortingTemplateDto.getTemplateName()));

        if(ObjectUtil.isNotNull(checkTemplate)){
            throw new CustomBusinessException("分拣模版-"+checkTemplate.getTemplateName()+"已经存在！");
        }
        //UUID生成一个模版编码
        tmsSortingTemplateDto.setTemplateCode(UUID.randomUUID().toString());
        //将模版相关的数据copy
        TmsSortingTemplateEntity tmsSortingTemplateEntity = new TmsSortingTemplateEntity();
        BeanUtils.copyProperties(tmsSortingTemplateDto, tmsSortingTemplateEntity);
        //新增模版-默认禁用，只有启用后才能使用-并且模版只会启用一份！
        tmsSortingTemplateEntity.setIsEnable(Boolean.FALSE);
        if(ObjectUtil.isNull(tmsSortingTemplateEntity.getCreateTime())){
            //新增
            tmsSortingTemplateEntity.setCreateTime(LocalDateTime.now());
            tmsSortingTemplateEntity.setCreateBy(SecurityUtils.getUser().getUsername());
            tmsSortingTemplateEntity.setUpdateTime(LocalDateTime.now());
            tmsSortingTemplateEntity.setUpdateBy(SecurityUtils.getUser().getUsername());
        }
//        //保存一份模版-生成回填id
//        tmsSortingTemplateEntity.setId(IdUtil.getSnowflake().nextId());
//        //默认
//        tmsSortingTemplateEntity.setDelFlag("0");
        tmsSortingTemplateMapper.insert(tmsSortingTemplateEntity);
        //保存规则数据集合
        List<TmsSortingRuleEntity> tmsSortingRuleList = new ArrayList<>();
        // businessType :5 半托管模板，无规则配置
        if(!BusinessType.HALF_TRUSTEESHIP.getCode().equals(tmsSortingTemplateDto.getBusinessType())){
            //该模版下的规则数据
            // 揽收退件-按商家
            if(tmsSortingTemplateDto.getBusinessType().equals(BusinessType.COLLECTION.getCode())
                    &&tmsSortingTemplateDto.getSortingRuleType().equals(SortingRuleType.MERCHANT.getCode())){
                //校验所选的格口id是否有相同的，不允许相同，如果所选的格口id有相同的，则抛出异常
                List<Long> gridIdList = tmsSortingTemplateDto.getTmsSortingMerchantDtoList().stream().map(TmsSortingMerchantDto::getGridId).collect(Collectors.toList());
                if(CollectionUtil.isEmpty(gridIdList)){
                    throw new CustomBusinessException("所选的格口不能为空！");
                }
                Set<Long> gridIdSet = new HashSet<>(gridIdList);
                boolean hasGridDuplicate = gridIdSet.size() < gridIdList.size();
                if(hasGridDuplicate){
                    throw new CustomBusinessException("所配置的格口有重复的，请检查！");
                }

                //判断格口的配置商家是否有重复的
                List<Long> merchantCodeList = new ArrayList<>();
                for (TmsSortingMerchantDto tmsSortingMerchantDto : tmsSortingTemplateDto.getTmsSortingMerchantDtoList()) {
                    List<Long> tempMerchantCodeList = tmsSortingMerchantDto.getMerchantCodeList();
                    if(CollectionUtil.isEmpty(tempMerchantCodeList)){
                        throw new CustomBusinessException("有格口所配置的商家为空，请检查！");
                    }
                    merchantCodeList.addAll(tempMerchantCodeList);
                }
                Set<Long> merchantCodeSet = new HashSet<>(merchantCodeList);
                boolean hasMerchantDuplicate = merchantCodeSet.size() < merchantCodeList.size();
//            if(hasMerchantDuplicate){
//                throw new CustomBusinessException("所配置的格口中有商家重复交叉的，请检查！");
//            }

                //获取前端传进来的商家数据
                tmsSortingTemplateDto.getTmsSortingMerchantDtoList().forEach(tmsSortingMerchantDto -> {

                    TmsSortingRuleEntity tmsSortingRuleEntity = new TmsSortingRuleEntity();
                    //模版的关联id
                    tmsSortingRuleEntity.setTemplateId(tmsSortingTemplateEntity.getId());
                    //格口id
                    tmsSortingRuleEntity.setGridId(tmsSortingMerchantDto.getGridId());
                    //业务类型-揽收退件
                    tmsSortingRuleEntity.setBusinessType(tmsSortingTemplateDto.getBusinessType());
                    //分拣规则
                    tmsSortingRuleEntity.setSortingRuleType(tmsSortingTemplateDto.getSortingRuleType());
                    //根据商家id转字符串list
                    List<String> merchantCodes = tmsSortingMerchantDto.getMerchantCodeList().stream().map(Object::toString).collect(Collectors.toList());
                    //将merchantCodes中商家id元素按照,分隔，组成id字符串（1,3,5）
                    String merchantCode = String.join(",", merchantCodes);
                    tmsSortingRuleEntity.setMerchantCode(merchantCode);
//                tmsSortingRuleEntity.setMerchantName(tmsSortingMerchantDto.getMerchantName());
                    tmsSortingRuleList.add(tmsSortingRuleEntity);
                });
            }
            // 正向派件-按路线号
            else if(tmsSortingTemplateDto.getBusinessType().equals(BusinessType.DELIVERY.getCode())
                    &&tmsSortingTemplateDto.getSortingRuleType().equals(SortingRuleType.ROUTE_NUMBER.getCode())){
                //校验所选的格口id是否有相同的，不允许相同，如果所选的格口id有相同的，则抛出异常
                List<Long> gridIdList = tmsSortingTemplateDto.getTmsSortingRouteNumberDtoList().stream().map(TmsSortingRouteNumberDto::getGridId).collect(Collectors.toList());
                if(CollectionUtil.isEmpty(gridIdList)){
                    throw new CustomBusinessException("所选的格口不能为空！");
                }
                Set<Long> gridIdSet = new HashSet<>(gridIdList);
                boolean hasDuplicate = gridIdSet.size() < gridIdList.size();
                if(hasDuplicate){
                    throw new CustomBusinessException("所配置的格口有重复的，请检查！");
                }

                //判断格口的配置路线号是否有重复的
                List<String> routeNumberList = new ArrayList<>();
                for (TmsSortingRouteNumberDto tmsSortingRouteNumberDto : tmsSortingTemplateDto.getTmsSortingRouteNumberDtoList()) {
                    List<String> tempRouteNumberList = tmsSortingRouteNumberDto.getRouteNumberList();
                    if(CollectionUtil.isEmpty(tempRouteNumberList)){
                        throw new CustomBusinessException("有格口所配置的路线号为空，请检查！");
                    }
                    routeNumberList.addAll(tempRouteNumberList);
                }
                Set<String> routeNumberSet = new HashSet<>(routeNumberList);
                boolean hasRouteNumberDuplicate = routeNumberSet.size() < routeNumberList.size();
//            if(hasRouteNumberDuplicate){
//                throw new CustomBusinessException("所配置的格口中有路线号重复交叉的，请检查！");
//            }
                //获取前端传进来的路线号数据
                tmsSortingTemplateDto.getTmsSortingRouteNumberDtoList().forEach(tmsSortingRouteNumberDto -> {

                    TmsSortingRuleEntity tmsSortingRuleEntity = new TmsSortingRuleEntity();
                    //模版的关联id
                    tmsSortingRuleEntity.setTemplateId(tmsSortingTemplateEntity.getId());
                    //格口id
                    tmsSortingRuleEntity.setGridId(tmsSortingRouteNumberDto.getGridId());
                    //业务类型-正向派送
                    tmsSortingRuleEntity.setBusinessType(tmsSortingTemplateDto.getBusinessType());
                    //分拣规则
                    tmsSortingRuleEntity.setSortingRuleType(tmsSortingTemplateDto.getSortingRuleType());
                    //根据商家将路线号和仓库名称放入
                    List<String> finalRouteNumberList = tmsSortingRouteNumberDto.getRouteNumberList();
                    //将路线号按照,分隔，组成id字符串（YVR001,YVR002,YVR003）
                    String routeNumber = String.join(",", finalRouteNumberList);
                    tmsSortingRuleEntity.setRouteNumber(routeNumber);
                    tmsSortingRuleList.add(tmsSortingRuleEntity);
                });
            }
            //按条件(揽收退件和正向派送均支持)
            else if(tmsSortingTemplateDto.getSortingRuleType().equals(SortingRuleType.CONDITION.getCode())){
                //校验所选的格口id是否有相同的，不允许相同，如果所选的格口id有相同的，则抛出异常
                List<Long> gridIdList = tmsSortingTemplateDto.getTmsSortingConditionDtoList().stream().map(TmsSortingConditionDto::getGridId).collect(Collectors.toList());
                if(CollectionUtil.isEmpty(gridIdList)){
                    throw new CustomBusinessException("所选的格口不能为空！");
                }
                Set<Long> gridIdSet = new HashSet<>(gridIdList);
                boolean hasDuplicate = gridIdSet.size() < gridIdList.size();
                if(hasDuplicate){
                    throw new CustomBusinessException("所配置的格口有重复的，请检查！");
                }
                //判断格口的配置条件是否有重复的(语义层面)
                List<ConditionGroup> conditionGroupList = tmsSortingTemplateDto.getTmsSortingConditionDtoList()
                        .stream().map(TmsSortingConditionDto::getConditionGroup).collect(Collectors.toList());
                if(CollectionUtil.isEmpty(conditionGroupList)){
                    throw new CustomBusinessException("有格口所配置的条件为空，请检查！");
                }
                List<String> spElList = new ArrayList<>();
                for (ConditionGroup conditionGroup : conditionGroupList) {
                    String jsonString = JSON.toJSONString(conditionGroup);
                    JSONObject jsonObject = JSON.parseObject(jsonString);
                    String spEl = BuildSpElUtil.buildSpEL(jsonObject);
                    spElList.add(spEl);
                }
                boolean isHasDuplicate = SpelRuleUtils.hasDuplicates(spElList);
                if(isHasDuplicate){
                    throw new CustomBusinessException("所配置的格口中有重复交叉的条件，请检查！");
                }

                //获取前端传进来的路线号数据
                tmsSortingTemplateDto.getTmsSortingConditionDtoList().forEach(tmsSortingConditionDto -> {

                    TmsSortingRuleEntity tmsSortingRuleEntity = new TmsSortingRuleEntity();
                    //模版的关联id
                    tmsSortingRuleEntity.setTemplateId(tmsSortingTemplateEntity.getId());
                    //格口id
                    tmsSortingRuleEntity.setGridId(tmsSortingConditionDto.getGridId());
                    //业务类型-正向派送
                    tmsSortingRuleEntity.setBusinessType(tmsSortingTemplateDto.getBusinessType());
                    //分拣规则
                    tmsSortingRuleEntity.setSortingRuleType(tmsSortingTemplateDto.getSortingRuleType());
                    //将条件转SpEL字符串表达式
                    String jsonString = JSON.toJSONString(tmsSortingConditionDto.getConditionGroup());
                    JSONObject jsonObject = JSON.parseObject(jsonString);
                    //利用工具类生成SpEL表达式
                    String spel = BuildSpElUtil.buildSpEL(jsonObject);
                    //将SpEL表达式存储
                    tmsSortingRuleEntity.setConditionSpel(spel);
                    //将其转json存储，后期根据此json生成SpEL表达式，并且将该json存储到数据库中，用于给查询时给前端返回解析展示
                    tmsSortingRuleEntity.setConditions(jsonString);

                    tmsSortingRuleList.add(tmsSortingRuleEntity);
                });
            }
        }else{
            // 半托管验证
            List<Long> gridIds = tmsSortingTemplateDto.getGridIds();
            if(CollUtil.isEmpty(gridIds)){
                throw new CustomBusinessException("请配置格口!");
            }
            Set<Long> uniqueIds = new HashSet<>(gridIds);
            boolean hasDuplicates = gridIds.size() != uniqueIds.size();
            if(hasDuplicates){
                throw new CustomBusinessException("所配置的格口有重复的，请检查!");
            }
            gridIds.forEach(e->{
                TmsSortingRuleEntity sortingRule = new TmsSortingRuleEntity();
                sortingRule.setTemplateId(tmsSortingTemplateEntity.getId());
                sortingRule.setGridId(e);
                sortingRule.setBusinessType(BusinessType.HALF_TRUSTEESHIP.getCode());
                sortingRule.setSortingRuleType(StoreConstants.NEGATIVE_ONE);
                tmsSortingRuleList.add(sortingRule);
            });
        }
        // 最终统一保存
        if (!tmsSortingRuleList.isEmpty()) {
            tmsSortingRuleService.saveBatch(tmsSortingRuleList);
        } else{
            throw new CustomBusinessException("请配置分拣规则！");
        }
        return R.ok();
    }

    @Override
    public R getRouteNumberPage(Page page,String routeNumber, Long warehouseNameId) {
        //根据路线号和仓库id查询覆盖区域中的路线号
        LambdaQueryWrapper<TmsOverAreaEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(routeNumber),TmsOverAreaEntity::getRouteNumber, routeNumber)
                .eq(ObjectUtil.isNotNull(warehouseNameId),TmsOverAreaEntity::getWarehouseId, warehouseNameId);
        Page newPage = tmsOverAreaService.page(page, wrapper);
        List<TmsOverAreaEntity> records = newPage.getRecords();
        if(CollectionUtil.isNotEmpty(records)){
            //获取覆盖区域中的仓库id
            List<Long> warehouseIds = records.stream().map(TmsOverAreaEntity::getWarehouseId).collect(Collectors.toList());
            //根据仓库id查询仓库
            Map<Long, TmsSiteEntity> warehouseMNap = tmsSiteService.listByIds(warehouseIds).stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));
            for (TmsOverAreaEntity record : records) {
                //回填仓库名称
                if(ObjectUtil.isNotNull(warehouseMNap.get(record.getWarehouseId()).getSiteName())){
                    record.setWarehouseName(warehouseMNap.get(record.getWarehouseId()).getSiteName());
                }
            }
        }
        return R.ok(newPage.setRecords(records));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateRuleById(TmsSortingTemplateDto tmsSortingTemplateDto) {
        if(ObjectUtil.isNull(tmsSortingTemplateDto.getId())){
            throw  new CustomBusinessException("id参数丢失！");
        }
        //模版保留
        TmsSortingTemplateEntity sortingTemplate = this.getById(tmsSortingTemplateDto.getId());
        TmsSortingTemplateEntity oldSortingTemplate = BeanUtil.toBean(sortingTemplate, TmsSortingTemplateEntity.class);
        List<TmsSortingRuleEntity> oldTmsSortingRuleList = tmsSortingRuleService.getAllByTemplateId(sortingTemplate.getId());
        //为了方便，修改的逻辑修改为先删除原来规则再新增--->修改即先删后新增---删除规则
        tmsSortingRuleService.remove(new LambdaQueryWrapper<TmsSortingRuleEntity>().eq(TmsSortingRuleEntity::getTemplateId,sortingTemplate.getId()));
        sortingTemplate.setUpdateBy(SecurityUtils.getUser().getUsername());
        sortingTemplate.setUpdateTime(LocalDateTime.now());
        sortingTemplate.setTemplateName(tmsSortingTemplateDto.getTemplateName());
        sortingTemplate.setSortingRuleType(tmsSortingTemplateDto.getSortingRuleType());
        sortingTemplate.setBusinessType(tmsSortingTemplateDto.getBusinessType());

        //确保分拣模版名称不重复
        TmsSortingTemplateEntity now = this.getOne(new LambdaQueryWrapper<TmsSortingTemplateEntity>()
                .eq(TmsSortingTemplateEntity::getTemplateCode, tmsSortingTemplateDto.getTemplateCode()));

        if(!sortingTemplate.getId().equals(now.getId())){
            //不是自己的情况
            throw new CustomBusinessException("分拣模版名称已存在");
        }
        this.updateById(sortingTemplate);
        //保存规则数据集合
        List<TmsSortingRuleEntity> tmsSortingRuleList = new ArrayList<>();
        if(!BusinessType.HALF_TRUSTEESHIP.getCode().equals(tmsSortingTemplateDto.getBusinessType())){
            //该模版下的规则数据
            // 揽收退件-按商家
            if(tmsSortingTemplateDto.getBusinessType().equals(BusinessType.COLLECTION.getCode())
                    &&tmsSortingTemplateDto.getSortingRuleType().equals(SortingRuleType.MERCHANT.getCode())){
                //校验所选的格口id是否有相同的，不允许相同，如果所选的格口id有相同的，则抛出异常
                List<Long> gridIdList = tmsSortingTemplateDto.getTmsSortingMerchantDtoList().stream().map(TmsSortingMerchantDto::getGridId).collect(Collectors.toList());
                if(CollectionUtil.isEmpty(gridIdList)){
                    throw new CustomBusinessException("所选的格口不能为空！");
                }
                Set<Long> gridIdSet = new HashSet<>(gridIdList);
                boolean hasGridDuplicate = gridIdSet.size() < gridIdList.size();
                if(hasGridDuplicate){
                    throw new CustomBusinessException("所配置的格口有重复的，请检查！");
                }

                //判断格口的配置商家是否有重复的
                List<Long> merchantCodeList = new ArrayList<>();
                for (TmsSortingMerchantDto tmsSortingMerchantDto : tmsSortingTemplateDto.getTmsSortingMerchantDtoList()) {
                    List<Long> tempMerchantCodeList = tmsSortingMerchantDto.getMerchantCodeList();
                    if(CollectionUtil.isEmpty(tempMerchantCodeList)){
                        throw new CustomBusinessException("有格口所配置的商家为空，请检查！");
                    }
                    merchantCodeList.addAll(tempMerchantCodeList);
                }
                Set<Long> merchantCodeSet = new HashSet<>(merchantCodeList);
                boolean hasMerchantDuplicate = merchantCodeSet.size() < merchantCodeList.size();
//            if(hasMerchantDuplicate){
//                throw new CustomBusinessException("所配置的格口中有商家重复交叉的，请检查！");
//            }

                //获取前端传进来的商家数据
                tmsSortingTemplateDto.getTmsSortingMerchantDtoList().forEach(tmsSortingMerchantDto -> {

                    TmsSortingRuleEntity tmsSortingRuleEntity = new TmsSortingRuleEntity();
                    //模版的关联id
                    tmsSortingRuleEntity.setTemplateId(sortingTemplate.getId());
                    //格口id
                    tmsSortingRuleEntity.setGridId(tmsSortingMerchantDto.getGridId());
                    //业务类型-揽收退件
                    tmsSortingRuleEntity.setBusinessType(tmsSortingTemplateDto.getBusinessType());
                    //分拣规则
                    tmsSortingRuleEntity.setSortingRuleType(tmsSortingTemplateDto.getSortingRuleType());
                    //根据商家id转字符串list
                    List<String> merchantCodes = tmsSortingMerchantDto.getMerchantCodeList().stream().map(Object::toString).collect(Collectors.toList());
                    //将merchantCodes中商家id元素按照,分隔，组成id字符串（1,3,5）
                    String merchantCode = String.join(",", merchantCodes);
                    tmsSortingRuleEntity.setMerchantCode(merchantCode);
//                tmsSortingRuleEntity.setMerchantName(tmsSortingMerchantDto.getMerchantName());
                    tmsSortingRuleList.add(tmsSortingRuleEntity);
                });
            }
            // 正向派件-按路线号
            else if(tmsSortingTemplateDto.getBusinessType().equals(BusinessType.DELIVERY.getCode())
                    &&tmsSortingTemplateDto.getSortingRuleType().equals(SortingRuleType.ROUTE_NUMBER.getCode())){
                //校验所选的格口id是否有相同的，不允许相同，如果所选的格口id有相同的，则抛出异常
                List<Long> gridIdList = tmsSortingTemplateDto.getTmsSortingRouteNumberDtoList().stream().map(TmsSortingRouteNumberDto::getGridId).collect(Collectors.toList());
                if(CollectionUtil.isEmpty(gridIdList)){
                    throw new CustomBusinessException("所选的格口不能为空！");
                }
                Set<Long> gridIdSet = new HashSet<>(gridIdList);
                boolean hasDuplicate = gridIdSet.size() < gridIdList.size();
                if(hasDuplicate){
                    throw new CustomBusinessException("所配置的格口有重复的，请检查！");
                }

                //判断格口的配置路线号是否有重复的
                List<String> routeNumberList = new ArrayList<>();
                for (TmsSortingRouteNumberDto tmsSortingRouteNumberDto : tmsSortingTemplateDto.getTmsSortingRouteNumberDtoList()) {
                    List<String> tempRouteNumberList = tmsSortingRouteNumberDto.getRouteNumberList();
                    if(CollectionUtil.isEmpty(tempRouteNumberList)){
                        throw new CustomBusinessException("有格口所配置的路线号为空，请检查！");
                    }
                    routeNumberList.addAll(tempRouteNumberList);
                }
                Set<String> routeNumberSet = new HashSet<>(routeNumberList);
                boolean hasRouteNumberDuplicate = routeNumberSet.size() < routeNumberList.size();
//            if(hasRouteNumberDuplicate){
//                throw new CustomBusinessException("所配置的格口中有路线号重复交叉的，请检查！");
//            }


                //获取前端传进来的路线号数据
                tmsSortingTemplateDto.getTmsSortingRouteNumberDtoList().forEach(tmsSortingRouteNumberDto -> {

                    TmsSortingRuleEntity tmsSortingRuleEntity = new TmsSortingRuleEntity();
                    //模版的关联id
                    tmsSortingRuleEntity.setTemplateId(sortingTemplate.getId());
                    //格口id
                    tmsSortingRuleEntity.setGridId(tmsSortingRouteNumberDto.getGridId());
                    //业务类型-正向派送
                    tmsSortingRuleEntity.setBusinessType(tmsSortingTemplateDto.getBusinessType());
                    //分拣规则
                    tmsSortingRuleEntity.setSortingRuleType(tmsSortingTemplateDto.getSortingRuleType());
                    //根据商家将路线号和仓库名称放入
                    List<String> finalRouteNumberList = tmsSortingRouteNumberDto.getRouteNumberList();
                    //将路线号按照,分隔，组成id字符串（YVR001,YVR002,YVR003）
                    String routeNumber = String.join(",", finalRouteNumberList);
                    tmsSortingRuleEntity.setRouteNumber(routeNumber);
                    tmsSortingRuleList.add(tmsSortingRuleEntity);
                });
            }
            //按条件(揽收退件和正向派送均支持)
            else if(tmsSortingTemplateDto.getSortingRuleType().equals(SortingRuleType.CONDITION.getCode())){
                //校验所选的格口id是否有相同的，不允许相同，如果所选的格口id有相同的，则抛出异常
                List<Long> gridIdList = tmsSortingTemplateDto.getTmsSortingConditionDtoList().stream().map(TmsSortingConditionDto::getGridId).collect(Collectors.toList());
                if(CollectionUtil.isEmpty(gridIdList)){
                    throw new CustomBusinessException("所选的格口不能为空！");
                }
                Set<Long> gridIdSet = new HashSet<>(gridIdList);
                boolean hasDuplicate = gridIdSet.size() < gridIdList.size();
                if(hasDuplicate){
                    throw new CustomBusinessException("所配置的格口有重复的，请检查！");
                }
                //判断格口的配置条件是否有重复的(语义层面)
                List<ConditionGroup> conditionGroupList = tmsSortingTemplateDto.getTmsSortingConditionDtoList()
                        .stream().map(TmsSortingConditionDto::getConditionGroup).collect(Collectors.toList());
                if(CollectionUtil.isEmpty(conditionGroupList)){
                    throw new CustomBusinessException("有格口所配置的条件为空，请检查！");
                }
                List<String> spElList = new ArrayList<>();
                for (ConditionGroup conditionGroup : conditionGroupList) {
                    String jsonString = JSON.toJSONString(conditionGroup);
                    JSONObject jsonObject = JSON.parseObject(jsonString);
                    String spEl = BuildSpElUtil.buildSpEL(jsonObject);
                    spElList.add(spEl);
                }
                boolean isHasDuplicate = SpelRuleUtils.hasDuplicates(spElList);
                if(isHasDuplicate){
                    throw new CustomBusinessException("所配置的格口中有重复交叉的条件，请检查！");
                }

                //获取前端传进来的路线号数据
                tmsSortingTemplateDto.getTmsSortingConditionDtoList().forEach(tmsSortingConditionDto -> {

                    TmsSortingRuleEntity tmsSortingRuleEntity = new TmsSortingRuleEntity();
                    //模版的关联id
                    tmsSortingRuleEntity.setTemplateId(sortingTemplate.getId());
                    //格口id
                    tmsSortingRuleEntity.setGridId(tmsSortingConditionDto.getGridId());
                    //业务类型-正向派送
                    tmsSortingRuleEntity.setBusinessType(tmsSortingTemplateDto.getBusinessType());
                    //分拣规则
                    tmsSortingRuleEntity.setSortingRuleType(tmsSortingTemplateDto.getSortingRuleType());
                    //将条件转SpEL字符串表达式
                    String jsonString = JSON.toJSONString(tmsSortingConditionDto.getConditionGroup());
                    JSONObject jsonObject = JSON.parseObject(jsonString);
                    //利用工具类生成SpEL表达式
                    String spel = BuildSpElUtil.buildSpEL(jsonObject);
                    //将SpEL表达式存储
                    tmsSortingRuleEntity.setConditionSpel(spel);
                    //将其转json存储，后期根据此json生成SpEL表达式，并且将该json存储到数据库中，用于给查询时给前端返回解析展示
                    tmsSortingRuleEntity.setConditions(jsonString);

                    tmsSortingRuleList.add(tmsSortingRuleEntity);
                });
            }
        }else{
            // 半托管验证
            List<Long> gridIds = tmsSortingTemplateDto.getGridIds();
            if(CollUtil.isEmpty(gridIds)){
                throw new CustomBusinessException("请配置格口!");
            }
            Set<Long> uniqueIds = new HashSet<>(gridIds);
            boolean hasDuplicates = gridIds.size() != uniqueIds.size();
            if(hasDuplicates){
                throw new CustomBusinessException("所配置的格口有重复的，请检查!");
            }
            gridIds.forEach(e->{
                TmsSortingRuleEntity sortingRule = new TmsSortingRuleEntity();
                sortingRule.setTemplateId(sortingTemplate.getId());
                sortingRule.setGridId(e);
                sortingRule.setBusinessType(BusinessType.HALF_TRUSTEESHIP.getCode());
                sortingRule.setSortingRuleType(StoreConstants.NEGATIVE_ONE);
                tmsSortingRuleList.add(sortingRule);
            });
        }
        // 最终统一保存
        if (!tmsSortingRuleList.isEmpty()) {
            tmsSortingRuleService.saveBatch(tmsSortingRuleList);
        }else{
            throw new CustomBusinessException("请配置分拣规则！");
        }
        // 记录修改日志
        TmsSortingTemplateChangeRecordEntity changeRecordEntity = changeRecordService.structChangeRecordEntity(sortingTemplate,
                oldSortingTemplate,tmsSortingRuleList,oldTmsSortingRuleList,
                StoreEnums.TemplateChangeRecord.OperationType.MODIFY.getValue(),
                SecurityUtils.getUser().getUsername());
        changeRecordService.createTemplateChangeRecordAsync(changeRecordEntity);
        return R.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeEnable(Long id, Integer isEnable) {
        TmsSortingTemplateEntity sortingTemplateEntity = baseMapper.selectById(id);
        // 启用
        if(BooleanUtil.toBoolean(isEnable)){
            //先查询是否用其他启用的模版
            List<TmsSortingTemplateEntity> enableTemplateList = baseMapper.selectList(
                    new LambdaQueryWrapper<TmsSortingTemplateEntity>()
                            .eq(TmsSortingTemplateEntity::getIsEnable, Boolean.TRUE));
            if(CollUtil.isNotEmpty(enableTemplateList)){
                throw new CustomBusinessException("存在已启用的分拣模版,请先禁用启用的模版再启用新的！");
            }
        }

        TmsSortingTemplateEntity updateEntity = new TmsSortingTemplateEntity();
        updateEntity.setId(id);
        updateEntity.setIsEnable(BooleanUtil.toBoolean(isEnable));
        baseMapper.updateById(updateEntity);

        // 记录日志
        TmsSortingTemplateChangeRecordEntity changeRecordEntity = new TmsSortingTemplateChangeRecordEntity();
        changeRecordEntity.setTemplateId(sortingTemplateEntity.getId());
        changeRecordEntity.setTemplateCode(sortingTemplateEntity.getTemplateCode());
        changeRecordEntity.setTemplateName(sortingTemplateEntity.getTemplateName());
        changeRecordEntity.setOperateName(SecurityUtils.getUser().getUsername());
        changeRecordEntity.setTemplateBusinessType(sortingTemplateEntity.getBusinessType());
        if(BooleanUtil.toBoolean(isEnable)){ // 启用
            changeRecordEntity.setOperationType(StoreEnums.TemplateChangeRecord.OperationType.ENABLE.getValue());
        }else{
            changeRecordEntity.setOperationType(StoreEnums.TemplateChangeRecord.OperationType.DISABLE.getValue());
        }
        changeRecordService.createTemplateChangeRecordAsync(changeRecordEntity);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeTemplate(Long id) {
        TmsSortingTemplateEntity sortingTemplateEntity = baseMapper.selectById(id);
        if(sortingTemplateEntity.getIsEnable().equals(Boolean.TRUE)){
            throw  new CustomBusinessException("启用的模版的模版不可删除,请先禁用！");
        }
        List<TmsSortingRuleEntity> tmsSortingRuleList = tmsSortingRuleService.getAllByTemplateId(sortingTemplateEntity.getId());
        baseMapper.deleteById(sortingTemplateEntity.getId());
        tmsSortingRuleService.deleteByTemplateId(sortingTemplateEntity.getId());
        // 记录日志
        TmsSortingTemplateChangeRecordEntity changeRecordEntity = changeRecordService.structChangeRecordEntity(sortingTemplateEntity, sortingTemplateEntity,
                tmsSortingRuleList, tmsSortingRuleList,
                StoreEnums.TemplateChangeRecord.OperationType.DELETE.getValue(),
                SecurityUtils.getUser().getUsername()
        );
        changeRecordService.createTemplateChangeRecordAsync(changeRecordEntity);
        return true;
    }

    @Override
    public R pageSearch(Page page, TmsSortingTemplatePageDto tmsSortingTemplatePageDto) {
        LambdaQueryWrapper<TmsSortingTemplateEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(tmsSortingTemplatePageDto.getTemplateName()),TmsSortingTemplateEntity::getTemplateName, tmsSortingTemplatePageDto.getTemplateName())
                //模版代码
                .eq(StrUtil.isNotBlank(tmsSortingTemplatePageDto.getTemplateCode()),TmsSortingTemplateEntity::getTemplateCode, tmsSortingTemplatePageDto.getTemplateCode())
                //创建时间
                .between(ObjectUtil.isNotNull(tmsSortingTemplatePageDto.getCreateStartTime())&& ObjectUtil.isNotNull(tmsSortingTemplatePageDto.getCreateEndTime())
                        ,TmsSortingTemplateEntity::getCreateTime, tmsSortingTemplatePageDto.getCreateStartTime(), tmsSortingTemplatePageDto.getCreateEndTime())
                //修改时间
                .between(ObjectUtil.isNotNull(tmsSortingTemplatePageDto.getUpdateStartTime())&& ObjectUtil.isNotNull(tmsSortingTemplatePageDto.getUpdateEndTime())
                        ,TmsSortingTemplateEntity::getCreateTime, tmsSortingTemplatePageDto.getUpdateStartTime(), tmsSortingTemplatePageDto.getUpdateEndTime())
                //创建人
                .eq(StrUtil.isNotBlank(tmsSortingTemplatePageDto.getCreateBy()),TmsSortingTemplateEntity::getCreateBy, tmsSortingTemplatePageDto.getCreateBy())
                //修改人
                .eq(StrUtil.isNotBlank(tmsSortingTemplatePageDto.getUpdateBy()),TmsSortingTemplateEntity::getUpdateBy, tmsSortingTemplatePageDto.getUpdateBy())
                .orderByDesc(TmsSortingTemplateEntity::getCreateTime);
        Page newPage = this.page(page, wrapper);
        List<TmsSortingTemplateEntity> records = newPage.getRecords();
        List<TmsSortingTemplateDto> tmsSortingTemplateDtos = new ArrayList<>();
        //拿到分页记录填充返回数据
        if(CollectionUtil.isNotEmpty(records)){
            List<Long> templateIds = records.stream().map(TmsSortingTemplateEntity::getId).collect(Collectors.toList());
            //根据templateIds查询这些模版里的所有规则
            Map<Long, List<TmsSortingRuleEntity>> sortingRuleMap = tmsSortingRuleService.list(new LambdaQueryWrapper<TmsSortingRuleEntity>()
                            .in(TmsSortingRuleEntity::getTemplateId, templateIds))
                    .stream().collect(Collectors.groupingBy(TmsSortingRuleEntity::getTemplateId));

            for (TmsSortingTemplateEntity record : records) {
                //填充模版里的规则
                TmsSortingTemplateDto tmsSortingTemplateDto = new TmsSortingTemplateDto();
                BeanUtils.copyProperties(record,tmsSortingTemplateDto);
                if(CollectionUtil.isNotEmpty(sortingRuleMap.get(record.getId()))){
                    //按商家-条件集合
                    List<TmsSortingMerchantDto> tmsSortingMerchantDtoList=new ArrayList<>();
                    //按路线-条件集合
                    List<TmsSortingRouteNumberDto> tmsSortingRouteNumberDtoList=new ArrayList<>();
                    //按条件集合
                    List<TmsSortingConditionDto> tmsSortingConditionDtoList=new ArrayList<>();
                    //获取改模版里的分拣规则数据
                    List<TmsSortingRuleEntity> tmsSortingRuleEntityList = sortingRuleMap.get(record.getId());

                    for (TmsSortingRuleEntity tmsSortingRuleEntity : tmsSortingRuleEntityList) {
                        if(tmsSortingRuleEntity.getSortingRuleType().equals(SortingRuleType.MERCHANT.getCode())){
                            //按照商家
                            TmsSortingMerchantDto tmsSortingMerchantDto = new TmsSortingMerchantDto();
                            BeanUtils.copyProperties(tmsSortingRuleEntity,tmsSortingMerchantDto);
                            //将商家authId按,分隔成集合（1,3,5）
                            List<Long> merchantCodeList = Arrays.stream(tmsSortingRuleEntity.getMerchantCode().split(","))
                                    .map(String::trim)
                                    .map(Long::valueOf)
                                    .collect(Collectors.toList());
                            tmsSortingMerchantDto.setMerchantCodeList(merchantCodeList);
                            tmsSortingMerchantDtoList.add(tmsSortingMerchantDto);
                        }
                        if(tmsSortingRuleEntity.getSortingRuleType().equals(SortingRuleType.ROUTE_NUMBER.getCode())){
                            //按照路线号
                            TmsSortingRouteNumberDto tmsSortingRouteNumberDto = new TmsSortingRouteNumberDto();
                            BeanUtils.copyProperties(tmsSortingRuleEntity,tmsSortingRouteNumberDto);
                            //将路线号按,分隔成集合（YVR001，YVR002）
                            List<String> routeNumberList = Arrays.stream(tmsSortingRuleEntity.getRouteNumber().split(",")).collect(Collectors.toList());
                            tmsSortingRouteNumberDto.setRouteNumberList(routeNumberList);
                            tmsSortingRouteNumberDtoList.add(tmsSortingRouteNumberDto);
                        }
                        if(tmsSortingRuleEntity.getSortingRuleType().equals(SortingRuleType.CONDITION.getCode())){
                            //按照条件-json字符串
                            String conditions = tmsSortingRuleEntity.getConditions();
                            ConditionGroup conditionGroup = JSON.parseObject(conditions, ConditionGroup.class);
                            TmsSortingConditionDto tmsSortingConditionDto = new TmsSortingConditionDto();
                            BeanUtils.copyProperties(tmsSortingRuleEntity,tmsSortingConditionDto);
                            tmsSortingConditionDto.setConditionGroup(conditionGroup);
                            tmsSortingConditionDtoList.add(tmsSortingConditionDto);
                        }
                    }
                    //回填数据
                    tmsSortingTemplateDto.setTmsSortingRouteNumberDtoList(tmsSortingRouteNumberDtoList);
                    tmsSortingTemplateDto.setTmsSortingMerchantDtoList(tmsSortingMerchantDtoList);
                    tmsSortingTemplateDto.setTmsSortingConditionDtoList(tmsSortingConditionDtoList);
                }
                //累加集合
                tmsSortingTemplateDtos.add(tmsSortingTemplateDto);
            }
        }
        return R.ok(newPage.setRecords(tmsSortingTemplateDtos));
    }

    @Transactional
    @Override
    public R removeBatchByIdList(List<Long> ids) {
        boolean deleteTemplate = this.removeBatchByIds(ids);
        //删除规则
        boolean deleteRule = tmsSortingRuleService.remove(new LambdaQueryWrapper<TmsSortingRuleEntity>()
                .in(TmsSortingRuleEntity::getTemplateId, ids));
        if(deleteTemplate && deleteRule){
            return R.ok();
        }
        return R.failed();
    }

    @Override
    public R getGridAndTemplate() {
        return R.ok();
    }

    @Override
    public R addGridAndSortingTemplate(List<TmsGridAndSortingTemplateDto> tmsGridAndSortingTemplateDtos) {
        //校验每一个格口id对应的模版id是否一致-每个模版只能被一个格口使用
        List<Long> templateIds = tmsGridAndSortingTemplateDtos.stream().map(TmsGridAndSortingTemplateDto::getSortingTemplateId).collect(Collectors.toList());
        return null;
    }

    @Override
    public R getAllRouteNumber() {
        //查询所有路线号
        MPJLambdaQueryWrapper<TmsOverAreaEntity> wrapper = new MPJLambdaQueryWrapper<>();
        wrapper.select(TmsOverAreaEntity::getRouteNumber)
                .select(TmsOverAreaEntity::getWarehouseId);
        List<TmsOverAreaEntity> records = tmsOverAreaService.list(wrapper);
        if(CollectionUtil.isNotEmpty(records)){
            //获取覆盖区域中的仓库id
            List<Long> warehouseIds = records.stream().map(TmsOverAreaEntity::getWarehouseId).collect(Collectors.toList());
            //根据仓库id查询仓库
            Map<Long, TmsSiteEntity> warehouseMNap = tmsSiteService.listByIds(warehouseIds).stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));
            for (TmsOverAreaEntity record : records) {
                //回填仓库名称
                if(ObjectUtil.isNotNull(warehouseMNap.get(record.getWarehouseId()).getSiteName())){
                    record.setWarehouseName(warehouseMNap.get(record.getWarehouseId()).getSiteName());
                }
            }
        }
        return R.ok(records);
    }

    @Override
    public R getSortingTemplateById(Long id) {
        TmsSortingTemplateEntity sortingTemplateEntity = this.getById(id);
        //根据分拣模版id查询其规则
        List<TmsSortingRuleEntity> tmsSortingRuleEntityList = tmsSortingRuleService.list(new LambdaQueryWrapper<TmsSortingRuleEntity>()
                .eq(TmsSortingRuleEntity::getTemplateId,id));
        TmsSortingTemplateDto tmsSortingTemplateDto = new TmsSortingTemplateDto();
        //填充模版里的规则
        BeanUtils.copyProperties(sortingTemplateEntity,tmsSortingTemplateDto);
        if(CollectionUtil.isNotEmpty(tmsSortingRuleEntityList)){
            //按商家-条件集合
            List<TmsSortingMerchantDto> tmsSortingMerchantDtoList=new ArrayList<>();
            //按路线-条件集合
            List<TmsSortingRouteNumberDto> tmsSortingRouteNumberDtoList=new ArrayList<>();
            //按条件集合
            List<TmsSortingConditionDto> tmsSortingConditionDtoList=new ArrayList<>();
            List<Long> gridIds = new ArrayList<>(tmsSortingRuleEntityList.size());
            for (TmsSortingRuleEntity tmsSortingRuleEntity : tmsSortingRuleEntityList) {
                if(tmsSortingRuleEntity.getBusinessType().equals(BusinessType.HALF_TRUSTEESHIP.getCode())
                && StoreConstants.NEGATIVE_ONE == tmsSortingRuleEntity.getSortingRuleType()){
                    gridIds.add(tmsSortingRuleEntity.getGridId());
                }
                if(tmsSortingRuleEntity.getSortingRuleType().equals(SortingRuleType.MERCHANT.getCode())){
                    //按照商家
                    TmsSortingMerchantDto tmsSortingMerchantDto = new TmsSortingMerchantDto();
                    BeanUtils.copyProperties(tmsSortingRuleEntity,tmsSortingMerchantDto);
                    //将商家authId按,分隔成集合（1,3,5）
                    List<Long> merchantCodeList = Arrays.stream(tmsSortingRuleEntity.getMerchantCode().split(","))
                            .map(String::trim)
                            .map(Long::valueOf)
                            .collect(Collectors.toList());
                    tmsSortingMerchantDto.setMerchantCodeList(merchantCodeList);
                    tmsSortingMerchantDtoList.add(tmsSortingMerchantDto);
                }
                if(tmsSortingRuleEntity.getSortingRuleType().equals(SortingRuleType.ROUTE_NUMBER.getCode())){
                    //按照路线号
                    TmsSortingRouteNumberDto tmsSortingRouteNumberDto = new TmsSortingRouteNumberDto();
                    BeanUtils.copyProperties(tmsSortingRuleEntity,tmsSortingRouteNumberDto);
                    //将路线号按,分隔成集合（YVR001，YVR002）
                    List<String> routeNumberList = Arrays.stream(tmsSortingRuleEntity.getRouteNumber().split(",")).collect(Collectors.toList());
                    tmsSortingRouteNumberDto.setRouteNumberList(routeNumberList);
                    tmsSortingRouteNumberDtoList.add(tmsSortingRouteNumberDto);
                }
                if(tmsSortingRuleEntity.getSortingRuleType().equals(SortingRuleType.CONDITION.getCode())){
                    //按照条件-json字符串
                    String conditions = tmsSortingRuleEntity.getConditions();
                    ConditionGroup conditionGroup = JSON.parseObject(conditions, ConditionGroup.class);
                    TmsSortingConditionDto tmsSortingConditionDto = new TmsSortingConditionDto();
                    BeanUtils.copyProperties(tmsSortingRuleEntity,tmsSortingConditionDto);
                    tmsSortingConditionDto.setConditionGroup(conditionGroup);
                    tmsSortingConditionDtoList.add(tmsSortingConditionDto);
                }
            }
            //回填数据
            tmsSortingTemplateDto.setTmsSortingRouteNumberDtoList(tmsSortingRouteNumberDtoList);
            tmsSortingTemplateDto.setTmsSortingMerchantDtoList(tmsSortingMerchantDtoList);
            tmsSortingTemplateDto.setTmsSortingConditionDtoList(tmsSortingConditionDtoList);
            tmsSortingTemplateDto.setGridIds(gridIds);
        }
        return R.ok(tmsSortingTemplateDto);
    }

    @Override
    public TmsSortingTemplateDto getEnableSortingTemplate() {
        //查询此时正在启用的模版
        TmsSortingTemplateEntity tmsSortingTemplateEntity = this.getOne(new LambdaQueryWrapper<TmsSortingTemplateEntity>()
                .eq(TmsSortingTemplateEntity::getIsEnable, Boolean.TRUE));
        if (tmsSortingTemplateEntity == null){
            return new TmsSortingTemplateDto();
        }
        TmsSortingTemplateDto tmsSortingTemplateDto = new TmsSortingTemplateDto();
        //根据分拣模版id查询其规则
        List<TmsSortingRuleEntity> tmsSortingRuleEntityList = tmsSortingRuleService.list(new LambdaQueryWrapper<TmsSortingRuleEntity>()
                .eq(TmsSortingRuleEntity::getTemplateId, tmsSortingTemplateEntity.getId()));
        //填充模版里的规则
        BeanUtils.copyProperties(tmsSortingTemplateEntity, tmsSortingTemplateDto);
        if (CollectionUtil.isNotEmpty(tmsSortingRuleEntityList)) {
            //按商家-条件集合
            List<TmsSortingMerchantDto> tmsSortingMerchantDtoList = new ArrayList<>();
            //按路线-条件集合
            List<TmsSortingRouteNumberDto> tmsSortingRouteNumberDtoList = new ArrayList<>();
            //按条件集合
            List<TmsSortingConditionDto> tmsSortingConditionDtoList = new ArrayList<>();
            List<Long> gridIds = new ArrayList<>(tmsSortingRuleEntityList.size());
            for (TmsSortingRuleEntity tmsSortingRuleEntity : tmsSortingRuleEntityList) {
                if(tmsSortingRuleEntity.getBusinessType().equals(BusinessType.HALF_TRUSTEESHIP.getCode())
                        && StoreConstants.NEGATIVE_ONE == tmsSortingRuleEntity.getSortingRuleType()){
                    gridIds.add(tmsSortingRuleEntity.getGridId());
                }
                if (tmsSortingRuleEntity.getSortingRuleType().equals(SortingRuleType.MERCHANT.getCode())) {
                    //按照商家
                    TmsSortingMerchantDto tmsSortingMerchantDto = new TmsSortingMerchantDto();
                    BeanUtils.copyProperties(tmsSortingRuleEntity, tmsSortingMerchantDto);
                    //将商家authId按,分隔成集合（1,3,5）
                    List<Long> merchantCodeList = Arrays.stream(tmsSortingRuleEntity.getMerchantCode().split(","))
                            .map(String::trim)
                            .map(Long::valueOf)
                            .collect(Collectors.toList());
                    tmsSortingMerchantDto.setMerchantCodeList(merchantCodeList);
                    tmsSortingMerchantDtoList.add(tmsSortingMerchantDto);
                }
                if (tmsSortingRuleEntity.getSortingRuleType().equals(SortingRuleType.ROUTE_NUMBER.getCode())) {
                    //按照路线号
                    TmsSortingRouteNumberDto tmsSortingRouteNumberDto = new TmsSortingRouteNumberDto();
                    BeanUtils.copyProperties(tmsSortingRuleEntity, tmsSortingRouteNumberDto);
                    //将路线号按,分隔成集合（YVR001，YVR002）
                    List<String> routeNumberList = Arrays.stream(tmsSortingRuleEntity.getRouteNumber().split(",")).collect(Collectors.toList());
                    tmsSortingRouteNumberDto.setRouteNumberList(routeNumberList);
                    tmsSortingRouteNumberDtoList.add(tmsSortingRouteNumberDto);
                }
                if (tmsSortingRuleEntity.getSortingRuleType().equals(SortingRuleType.CONDITION.getCode())) {
                    //按照条件-json字符串
                    String conditions = tmsSortingRuleEntity.getConditions();
                    ConditionGroup conditionGroup = JSON.parseObject(conditions, ConditionGroup.class);
                    TmsSortingConditionDto tmsSortingConditionDto = new TmsSortingConditionDto();
                    BeanUtils.copyProperties(tmsSortingRuleEntity, tmsSortingConditionDto);
                    tmsSortingConditionDto.setConditionGroup(conditionGroup);
                    tmsSortingConditionDtoList.add(tmsSortingConditionDto);
                }
            }
            //回填数据
            tmsSortingTemplateDto.setTmsSortingRouteNumberDtoList(tmsSortingRouteNumberDtoList);
            tmsSortingTemplateDto.setTmsSortingMerchantDtoList(tmsSortingMerchantDtoList);
            tmsSortingTemplateDto.setTmsSortingConditionDtoList(tmsSortingConditionDtoList);
            tmsSortingTemplateDto.setGridIds(gridIds);
            tmsSortingTemplateDto.setId(tmsSortingTemplateEntity.getId());
        }
        return tmsSortingTemplateDto;
    }
}
