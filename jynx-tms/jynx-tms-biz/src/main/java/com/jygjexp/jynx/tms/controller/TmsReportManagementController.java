package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsReportManagementEntity;
import com.jygjexp.jynx.tms.service.TmsReportManagementService;
import com.jygjexp.jynx.tms.vo.TmsRoutePlanReportVo;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 中大件揽收派送报告
 *
 * <AUTHOR>
 * @date 2025-06-05 11:47:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsReportManagement" )
@Tag(description = "tmsReportManagement" , name = "中大件揽收派送报告管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsReportManagementController {

    private final  TmsReportManagementService tmsReportManagementService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsReportManagement 中大件揽收派送报告
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsReportManagement_view')" )
    public R getTmsReportManagementPage(@ParameterObject Page page, @ParameterObject TmsReportManagementEntity tmsReportManagement) {
        LambdaQueryWrapper<TmsReportManagementEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsReportManagementService.page(page, wrapper));
    }


    /**
     * 通过id查询中大件揽收派送报告
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsReportManagement_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsReportManagementService.getById(id));
    }

    /**
     * 新增中大件揽收派送报告
     * @param tmsReportManagement 中大件揽收派送报告
     * @return R
     */
    @Operation(summary = "新增中大件揽收派送报告" , description = "新增中大件揽收派送报告" )
    @SysLog("新增中大件揽收派送报告" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsReportManagement_add')" )
    public R save(@RequestBody TmsReportManagementEntity tmsReportManagement) {
        return R.ok(tmsReportManagementService.save(tmsReportManagement));
    }

    /**
     * 修改中大件揽收派送报告
     * @param tmsReportManagement 中大件揽收派送报告
     * @return R
     */
    @Operation(summary = "修改中大件揽收派送报告" , description = "修改中大件揽收派送报告" )
    @SysLog("修改中大件揽收派送报告" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsReportManagement_edit')" )
    public R updateById(@RequestBody TmsReportManagementEntity tmsReportManagement) {
        return R.ok(tmsReportManagementService.updateById(tmsReportManagement));
    }

    /**
     * 通过id删除中大件揽收派送报告
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除中大件揽收派送报告" , description = "通过id删除中大件揽收派送报告" )
    @SysLog("通过id删除中大件揽收派送报告" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsReportManagement_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsReportManagementService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsReportManagement 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsReportManagement_export')" )
    public List<TmsReportManagementEntity> export(TmsReportManagementEntity tmsReportManagement,Long[] ids) {
        return tmsReportManagementService.list(Wrappers.lambdaQuery(tmsReportManagement).in(ArrayUtil.isNotEmpty(ids), TmsReportManagementEntity::getId, ids));
    }

    // 路径规划报告查询
    @Operation(summary = "查询路径规划报告", description = "查询路径规划报告")
    @GetMapping("/listRoutePlans/report")
    @PreAuthorize("@pms.hasPermission('tms_RoutePlans_export')" )
    public R listRoutePlans(@ParameterObject Page page, @ParameterObject TmsRoutePlanReportVo vo){
        return R.ok(tmsReportManagementService.listRoutePlans(page,vo));
    }

    // 根据报告单号查询详情
    @Operation(summary = "根据报告单号查询详情", description = "根据报告单号查询详情")
    @GetMapping("/getDetailByReportNo")
    public R getDetailByReportNo(@RequestParam("reportNo") String reportNo,@RequestParam("type") Integer type){
        return tmsReportManagementService.getDetailByReportNo(reportNo,type);
    }

    /**
     * 查询跟踪单详情列表(未扫/已扫明细)
     * @param isScan 是否扫描：0：未扫描/1:已扫描
     * @return
     */
//    @Operation(summary = "查询跟踪单详情列表(未扫/已扫明细)", description = "查询跟踪单详情列表(未扫/已扫明细)")
//    @SysLog("查询跟踪单详情列表(未扫/已扫明细)")
//    @GetMapping("/listEntrustedOrder/{isScan}/{id}")
//    public R listEntrustedOrder(@PathVariable("isScan") Boolean isScan, @PathVariable("id" ) Long id) {
//        return R.ok(tmsReportManagementService.listEntrustedOrder(isScan, id));
//    }

}