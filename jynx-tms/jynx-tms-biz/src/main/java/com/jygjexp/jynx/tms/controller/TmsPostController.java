package com.jygjexp.jynx.tms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsPhotoEntity;
import com.jygjexp.jynx.tms.entity.TmsPostEntity;
import com.jygjexp.jynx.tms.service.PhotoService;
import com.jygjexp.jynx.tms.service.PostService;
import com.jygjexp.jynx.tms.vo.excel.PostExcelVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 驿站
 *
 * <AUTHOR>
 * @date 2025-04-02 22:38:12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/post")
@Tag(description = "post", name = "驿站管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsPostController {

    private final PostService postService;

    private final PhotoService photoService;

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('zdj_back_Post_view')")
    public R getPostPage(@ParameterObject Page page, @ParameterObject TmsPostEntity post) {
        return R.ok(postService.getPage(page, post));
    }

    @Operation(summary = "查询所有有效驿站", description = "查询所有有效驿站")
    @GetMapping("/getValidPostInfo")
    public R getValidPostInfo() {
        LambdaQueryWrapper<TmsPostEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TmsPostEntity::getIsValid, true);
        return R.ok(postService.list(wrapper));
    }


    @Operation(summary = "查询所有未分配活动的驿站", description = "查询所有未分配活动的驿站")
    @GetMapping("/getNoActivityPost")
    public R getNoActivityPost() {
        LambdaQueryWrapper<TmsPostEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TmsPostEntity::getIsValid, true)
                .isNull(TmsPostEntity::getActivityId);
        return R.ok(postService.list(wrapper));
    }

    @Operation(summary = "查询所有已分配活动的驿站", description = "查询所有已分配活动的驿站")
    @GetMapping("/getActivityPost")
    public R getActivityPost() {
        LambdaQueryWrapper<TmsPostEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TmsPostEntity::getIsValid, true)
                .isNotNull(TmsPostEntity::getActivityId);
        return R.ok(postService.list(wrapper));
    }


    @Operation(summary = "查询驿站组未关联驿站", description = "查询驿站组未关联驿站")
    @GetMapping("/getNoRelationPostInfo")
    @PreAuthorize("@pms.hasPermission('zdj_back_PostGroup_info')")
    public R getNoRelationPostInfo(@RequestParam Integer warehouseId) {
        LambdaQueryWrapper<TmsPostEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TmsPostEntity::getIsValid, true);
        wrapper.eq(TmsPostEntity::getGroupId, 0);
        wrapper.eq(TmsPostEntity::getWarehouseId, warehouseId);
        return R.ok(postService.list(wrapper));
    }


    /**
     * 通过id查询驿站
     *
     * @param postId id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{postId}")
    public R getById(@PathVariable("postId") Integer postId) {
        return R.ok(photoService.getDetail(postId));
    }

    /**
     * 新增驿站
     *
     * @param post 驿站
     * @return R
     */
    @Operation(summary = "新增驿站", description = "新增驿站")
    @SysLog("新增驿站")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zdj_back_Post_add')")
    public R save(@RequestBody TmsPostEntity post) {
        return postService.savePost(post);
    }

    /**
     * 修改驿站
     *
     * @param post 驿站
     * @return R
     */
    @Operation(summary = "修改驿站", description = "修改驿站")
    @SysLog("修改驿站")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zdj_back_Post_edit')")
    public R updateById(@RequestBody TmsPostEntity post) {
        return postService.updatePost(post);
    }

    /**
     * 通过id删除驿站
     *
     * @return R
     */
    @Operation(summary = "通过id删除驿站", description = "通过id删除驿站")
    @SysLog("通过id删除驿站")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zdj_back_Post_del')")
    public R removeById(@RequestParam("postId") String postId) {
        postService.removeById(postId);
        photoService.remove(new QueryWrapper<TmsPhotoEntity>().eq("post_id", postId));
        return R.ok(postService.removeById(postId));
    }

    @Operation(summary = "通过PhotoId删除指定图片", description = "通过PhotoId删除指定图片")
    @SysLog("通过PhotoId删除指定图片")
    @DeleteMapping("/removeByPhotoId")
    public R removeByPhotoId(@RequestParam("photoIds") String photoIds) {
        return photoService.deleteBatchIds(photoIds);
    }




    @Operation(summary = "查询全部驿站名称" , description = "查询全部驿站名称" )
    @GetMapping("/postNameList")
    public R driverNameList() {
        LambdaQueryWrapper<TmsPostEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(TmsPostEntity::getPostId, TmsPostEntity::getPostName, TmsPostEntity::getIsValid).groupBy(TmsPostEntity::getPostId);
        return R.ok(postService.list(wrapper));
    }


    /**
     * 导出excel 表格
     *
     * @param post 查询条件
     * @param ids  导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zdj_back_Post_export')")
    public List<PostExcelVo> export(TmsPostEntity post, Integer[] ids) {
        return postService.getExcel(post, ids);
    }
}