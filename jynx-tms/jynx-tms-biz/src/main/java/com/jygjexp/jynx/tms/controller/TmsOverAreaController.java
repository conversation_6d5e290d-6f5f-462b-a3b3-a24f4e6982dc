package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.entity.TmsCarrierEntity;
import com.jygjexp.jynx.tms.entity.TmsOverAreaEntity;
import com.jygjexp.jynx.tms.entity.TmsSiteEntity;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsOverAreaService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 覆盖区域
 *
 * <AUTHOR>
 * @date 2025-04-07 20:31:03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsOverArea" )
@Tag(description = "tmsOverArea" , name = "覆盖区域管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsOverAreaController {

    private final  TmsOverAreaService tmsOverAreaService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsOverArea 覆盖区域
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOverArea_view')" )
    public R getTmsOverAreaPage(@ParameterObject Page page, @ParameterObject TmsOverAreaEntity tmsOverArea) {
        return tmsOverAreaService.getPage(page, tmsOverArea);
    }


    /**
     * 通过id查询覆盖区域
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsOverArea_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(tmsOverAreaService.getById(id));
    }

    /**
     * 新增覆盖区域
     * @param tmsOverArea 覆盖区域
     * @return R
     */
    @Operation(summary = "新增覆盖区域" , description = "新增覆盖区域" )
    @SysLog("新增覆盖区域" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOverArea_add')" )
    public R save(@RequestBody TmsOverAreaEntity tmsOverArea) {
       return tmsOverAreaService.saveOverArea(tmsOverArea);
    }

    /**
     * 修改覆盖区域
     * @param tmsOverArea 覆盖区域
     * @return R
     */
    @Operation(summary = "修改覆盖区域" , description = "修改覆盖区域" )
    @SysLog("修改覆盖区域" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOverArea_edit')" )
    public R updateById(@RequestBody TmsOverAreaEntity tmsOverArea) {
       return tmsOverAreaService.updateOverArea(tmsOverArea);
    }

    /**
     * 通过id删除覆盖区域
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除覆盖区域" , description = "通过id删除覆盖区域" )
    @SysLog("通过id删除覆盖区域" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsOverArea_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(tmsOverAreaService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsOverArea 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsOverArea_export')" )
    public List<TmsOverAreaEntity> export(TmsOverAreaEntity tmsOverArea,Integer[] ids) {
        return tmsOverAreaService.list(Wrappers.lambdaQuery(tmsOverArea).in(ArrayUtil.isNotEmpty(ids), TmsOverAreaEntity::getId, ids));
    }

    /**
     * 查询所有区域
     * @param
     * @return
     */
    @Operation(summary = "查询所有区域" , description = "查询所有区域" )
    @GetMapping("/getAllArea" )
//    @PreAuthorize("@pms.hasPermission('tms_tmsOverArea_view')" )
    public R getAllArea() {
        return R.ok(tmsOverAreaService.list());
    }

    /**
     * 通过路线编号获取仓库
     * @param routeNumber
     * @return
     */
    @Operation(summary = "通过路线编号获取仓库" , description = "通过路线编号获取仓库" )
    @PostMapping("/getWarehouseByRouteNo" )
    public R getWarehouseByRouteNo(String routeNumber) {
        return R.ok(tmsOverAreaService.getWarehouseByRouteNo(routeNumber));
    }

}