package com.jygjexp.jynx.tms.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.app.api.feign.RemoteAppUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.enums.TaskType;
import com.jygjexp.jynx.tms.enums.TransportTaskStatus;
import com.jygjexp.jynx.tms.mapper.*;
import com.jygjexp.jynx.tms.mongo.entity.TmsLargeDriverRealTimeLocation;
import com.jygjexp.jynx.tms.mongo.service.TmsLargeDriverRealTimeLocationService;
import com.jygjexp.jynx.tms.request.PickupCustomerOrderRequest;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.SnowflakeIdGenerator;
import com.jygjexp.jynx.tms.vo.*;
import com.mongoplus.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/schedule")
@Tag(description = "schedule", name = "调度管理")
@Slf4j
public class TmsScheduleController {
    private final TmsTransportTaskOrderService tmsTransportTaskOrderService;
    private final TmsLmdDriverService tmsLmdDriverService;
    private final TmsLineHaulOrderService tmsLineHaulOrderService;
    private final TmsCustomerOrderService tmsCustomerOrderService;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final RemoteAppUserService remoteAppUserService;
    private final TmsMessageMapper messageMapper;
    private final TmsVehicleDriverRelationMapper vehicleDriverRelationMapper;
    // 自定义雪花 ID 生成器
    private final SnowflakeIdGenerator snowflakeIdGenerator;


    @Operation(summary = "揽收指派-生成揽收任务", description = "揽收指派-生成揽收任务")
    @SysLog("揽收指派-生成揽收任务")
    @PostMapping("/collectAssigned")
    @PreAuthorize("@pms.hasPermission('tms_schedule_collect_assigned')")
    public R collectAppointNew(@RequestBody TmsScheduleAppointDto appointDto) {
        return tmsTransportTaskOrderService.collectAppointNew(appointDto);
    }


    // 揽收指派列表(排除送货到仓且发货地三字邮编对应一级仓情况，不可揽收)
    @Operation(summary = "揽收指派列表", description = "揽收指派列表")
    @PostMapping("/list/collection/assign")
    @PreAuthorize("@pms.hasPermission('tms_schedule_list_pickup_order')")
    public R listCollectionAssign(@RequestBody PickupCustomerOrderRequest request) {
        return R.ok(tmsCustomerOrderService.listCollectionAssign(new Page(request.getCurrent(), request.getSize()), request.getQuery()));
    }

    // 揽收指派列表详情
    @Operation(summary = "揽收指派列表详情", description = "揽收指派列表详情")
    @GetMapping("/list/collection/assign/detail")
    public R listCollectionAssignDetail(@ParameterObject Page page, @ParameterObject TmsCollectionAssignDetailPageVo vo) {
        return R.ok(tmsCustomerOrderService.listCollectionAssignDetail(page, vo));
    }

    // 揽收指派-可揽收任务单列表(排除送货到仓且发货地三字邮编对应一级仓情况，不可揽收)
    @Operation(summary = "揽收指派-可揽收客户订单列表", description = "揽收指派-可揽收客户订单列表")
    @PostMapping("/getAllPickupCustomerOrder")
    //@PreAuthorize("@pms.hasPermission('tms_schedule_list_pickup_order')")
    public R getAllPickupCustomerOrder(@RequestBody PickupCustomerOrderRequest request) {
        return R.ok(tmsCustomerOrderService.getAllPickupCustomerOrder(new Page(request.getCurrent(), request.getSize()), request.getQuery()));
    }

    /**
     * 查询揽收任务单
     */
    @Operation(summary = "查询揽收任务单" , description = "查询揽收任务单" )
    @SysLog("查询揽收任务单")
    @GetMapping("/getAllTmsTransportTaskOrder")
    @PreAuthorize("@pms.hasPermission('tms_schedule_query_collect_order')" )
    public R getAllTmsTransportTaskOrder(Page page,TmsTransportTaskOrderVo tmsTransportTaskOrderVo) {
        return tmsTransportTaskOrderService.getAllTmsTransportTaskOrder(page,tmsTransportTaskOrderVo);
    }

    /**
     * 根据客户单号查询订单详情
     */
    @Operation(summary = "根据客户单号查询订单详情" , description = "根据客户单号查询订单详情" )
    @SysLog("根据客户单号查询订单详情" )
    @GetMapping("/getCustomerOrder")
    @PreAuthorize("@pms.hasPermission('tms_schedule_query_order_detail')" )
    public R getCustomerOrderByCustomerOrderNumber(@RequestParam String customerOrderNumber, @RequestParam String siteId) {
        return tmsTransportTaskOrderService.getCustomerOrderByCustomerOrderNumber(customerOrderNumber,siteId);
    }

    /**
     * 根据单号查询干线详情
     */
    @Operation(summary = "根据单号查询干线详情" , description = "根据单号查询干线详情" )
    @SysLog("根据单号查询干线详情" )
    @GetMapping("/getLineHaulOrder")
    @PreAuthorize("@pms.hasPermission('tms_schedule_query_ml_detail')" )
    public R getTaskOrderByLineHaulNo(String lineHaulNo) {
       return tmsLineHaulOrderService.getTaskOrderByLineHaulNo(lineHaulNo);
    }
    /**
     * 查询待指派干线客户单列表
     */
    @Operation(summary = "查询待指派干线客户单列表" , description = "查询待指派干线客户单列表" )
    @SysLog("查询待指派干线客户单列表" )
    @GetMapping("/getLineHaulList")
    @PreAuthorize("@pms.hasPermission('tms_schedule_querylist')" )
    public R getLineHaulList(Page page, TmsCageLineDto tmsCageLineDto) {
       return tmsLineHaulOrderService.getLineHaulList(page,tmsCageLineDto);
    }
    /**
     * 干线指派
     */
    @Operation(summary = "干线指派" , description = "干线指派" )
    @SysLog("干线指派" )
    @PostMapping("/mainLineAppoint")
    @PreAuthorize("@pms.hasPermission('tms_schedule_mainline_appoint')" )
    public R mainLineAppoint(@RequestBody TmsScheduleAppointDto tmsScheduleAppointDto) {
      return tmsLineHaulOrderService.mainLineAppoint(tmsScheduleAppointDto);

    }
    /**
     * 查询所有司机信息
     */
    @Operation(summary = "查询所有司机信息" , description = "查询所有司机信息" )
    @SysLog("查询所有司机信息" )
    @GetMapping("/getDriverinfo")
    public R getDriverinfo(Page page, TmsScheduleDriverDto tmsScheduleDriverDto) {
        return tmsLmdDriverService.getDriverinfo(page,tmsScheduleDriverDto);
    }

    /**
     * 上传任务维度的司机车辆经纬度（任务开始到任务结束的经纬度记录）
     */
    @Operation(summary = "上传任务维度的司机车辆经纬度（任务开始到任务结束的经纬度记录）" , description = "上传任务维度的司机车辆经纬度（任务开始到任务结束的经纬度记录）" )
    @SysLog("上传任务维度的司机车辆经纬度（任务开始到任务结束的经纬度记录）" )
    @PostMapping("/uploadDriverCarLocation")
    public R uploadDriverCarLocation(@RequestBody TmsLargeDriverRealTimeLocationDto tmsLargeDriverRealTimeLocationDto) {
       return tmsLmdDriverService.uploadDriverCarLocation(tmsLargeDriverRealTimeLocationDto);
    }

    /**
     * 根据任务单号和任务类型查询任务单车辆轨迹
     */
    @Operation(summary = "根据任务单号和任务类型查询任务单车辆轨迹" , description = "根据任务单号和任务类型查询任务单车辆轨迹" )
    @SysLog("根据任务单号和任务类型查询任务单车辆轨迹" )
    @GetMapping("/getVehicleTrailByTaskNo")
    public R getVehicleTrailByTaskNo(String taskNo, Integer taskType) {
       return tmsLmdDriverService.getVehicleTrailByTaskNo(taskNo,taskType);
    }

    /**
     * 根据任务类型查询已完成的揽收任务或者派送任务
     */
    @Operation(summary = "根据任务类型查询已完成的揽收任务或者派送任务" , description = "根据任务类型查询已完成的揽收任务或者派送任务" )
    @SysLog("根据任务类型查询已完成的揽收任务或者派送任务" )
    @GetMapping("/getFinishCollectOrDeliveryTask")
    public R getFinishCollectOrDeliveryTask(Page page,Integer taskType) {
       return tmsTransportTaskOrderService.getFinishCollectOrDeliveryTask(page,taskType);
    }

    /**
     * 查询已完成的干线任务
     */
    @Operation(summary = "查询已完成的干线任务" , description = "查询已完成的干线任务" )
    @SysLog("查询已完成的干线任务" )
    @GetMapping("/getFinishDryLineTask")
    public R getFinishDryLineTask(Page page) {
        return tmsLineHaulOrderService.getFinishDryLineTask(page);
    }

    /**
     * 生成符合规则的任务单号
     * 格式：[客户标识]-[单据类型]-[日期时间戳]-[序列号]
     */
    private String generateTaskOrderNo() {
        String customerCode = "N";

        // 确定单据类型 干线单号默认是T
        String taskTypeCode = "T";
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250330,250331
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6); // 取后 6 位

        // 组合单号
        return String.format("%s%s%s%s", customerCode, taskTypeCode, datePart, shortId); // NT250330645213
    }
}
