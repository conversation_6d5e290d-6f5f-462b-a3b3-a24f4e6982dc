package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsWarehouseLocationEntity;
import com.jygjexp.jynx.tms.enums.EnableEnum;
import com.jygjexp.jynx.tms.service.TmsWarehouseLocationService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 仓位信息
 *
 * <AUTHOR>
 * @date 2025-06-06 14:36:15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsWarehouseLocation" )
@Tag(description = "tmsWarehouseLocation" , name = "仓位信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsWarehouseLocationController {

    private final  TmsWarehouseLocationService tmsWarehouseLocationService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 仓位信息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsWarehouseLocation_view')" )
    public R getTmsWarehouseLocationPage(@ParameterObject Page page, @ParameterObject TmsWarehouseLocationEntity vo) {
        return R.ok(tmsWarehouseLocationService.search(page, vo));
    }


    /**
     * 通过id查询仓位信息
     * @param locationId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{locationId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsWarehouseLocation_view')" )
    public R getById(@PathVariable("locationId" ) Long locationId) {
        return R.ok(tmsWarehouseLocationService.getById(locationId));
    }

    /**
     * 新增仓位信息
     * @param tmsWarehouseLocation 仓位信息
     * @return R
     */
    @Operation(summary = "新增仓位信息" , description = "新增仓位信息" )
    @SysLog("新增仓位信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsWarehouseLocation_add')" )
    public R save(@RequestBody TmsWarehouseLocationEntity tmsWarehouseLocation) {
        // 校验仓位是否重复
        if (!checkLocationName(tmsWarehouseLocation)){
            return R.failed("仓位已存在");
        }
        return R.ok(tmsWarehouseLocationService.save(tmsWarehouseLocation));
    }

    /**
     * 修改仓位信息
     * @param tmsWarehouseLocation 仓位信息
     * @return R
     */
    @Operation(summary = "修改仓位信息" , description = "修改仓位信息" )
    @SysLog("修改仓位信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsWarehouseLocation_edit')" )
    public R updateById(@RequestBody TmsWarehouseLocationEntity tmsWarehouseLocation) {
        // 校验仓位是否重复
        if (!checkLocationName(tmsWarehouseLocation)){
            return R.failed("仓位已存在");
        }
        return R.ok(tmsWarehouseLocationService.updateById(tmsWarehouseLocation));
    }

    /**
     * 通过id删除仓位信息
     * @param ids locationId列表
     * @return R
     */
    @Operation(summary = "通过id删除仓位信息" , description = "通过id删除仓位信息" )
    @SysLog("通过id删除仓位信息" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsWarehouseLocation_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsWarehouseLocationService.removeBatchByIds(CollUtil.toList(ids)));
    }

    // 根据仓库id查询全部仓位信息
    @Operation(summary = "根据仓库id查询仓位信息" , description = "根据仓库id查询仓位信息" )
    @GetMapping("/list/warehouseLocation")
    public R list(@RequestParam Long warehouseId) {
        // 添加排序条件，并查询全部
        LambdaQueryWrapper<TmsWarehouseLocationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsWarehouseLocationEntity::getWarehouseId, warehouseId);
        queryWrapper.orderByDesc(TmsWarehouseLocationEntity::getCreateTime);
        return R.ok(tmsWarehouseLocationService.list(queryWrapper));
    }


    /**
     * 仓位的禁用和启用
     * @param locationId 仓位的禁用和启用
     * @return R
     */
    @Operation(summary = "仓位的禁用和启用" , description = "仓位的禁用和启用" )
    @SysLog("仓位的禁用和启用" )
    @PutMapping("/enable/{locationId}")
    @PreAuthorize("@pms.hasPermission('tms_tmsWarehouseLocation_edit')" )
    public R enableOrDisable(@PathVariable Long locationId) {
        TmsWarehouseLocationEntity warehouseLocation = tmsWarehouseLocationService.getById(locationId);
        if (warehouseLocation.getIsValid().equals(EnableEnum.ENABLE.getCode())) {
            warehouseLocation.setIsValid(EnableEnum.DISABLE.getCode());
        } else {
            warehouseLocation.setIsValid(EnableEnum.ENABLE.getCode());
        }
        boolean update = tmsWarehouseLocationService.updateById(warehouseLocation);
        return update ? R.ok() : R.failed();
    }

    // 校验仓位名称是否重复
    public Boolean checkLocationName(TmsWarehouseLocationEntity tmsWarehouseLocation) {
        LambdaQueryWrapper<TmsWarehouseLocationEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TmsWarehouseLocationEntity::getWarehouseId, tmsWarehouseLocation.getWarehouseId())
                .eq(TmsWarehouseLocationEntity::getLocationCode, tmsWarehouseLocation.getLocationCode())
                .ne(ObjectUtil.isNotNull(tmsWarehouseLocation.getLocationId()),TmsWarehouseLocationEntity::getLocationId, tmsWarehouseLocation.getLocationId());
        return tmsWarehouseLocationService.count(wrapper) == 0;
    }


    /**
     * 导出excel 表格
     * @param tmsWarehouseLocation 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsWarehouseLocation_export')" )
    public List<TmsWarehouseLocationEntity> export(TmsWarehouseLocationEntity tmsWarehouseLocation,Long[] ids) {
        return tmsWarehouseLocationService.list(Wrappers.lambdaQuery(tmsWarehouseLocation).in(ArrayUtil.isNotEmpty(ids), TmsWarehouseLocationEntity::getLocationId, ids));
    }
}