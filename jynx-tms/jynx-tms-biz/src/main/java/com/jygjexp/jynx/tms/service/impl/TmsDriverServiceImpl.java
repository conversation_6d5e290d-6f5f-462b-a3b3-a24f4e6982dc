package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.app.api.dto.AppUserDTO;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.common.core.constant.CommonConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsAppUserService;
import com.jygjexp.jynx.tms.dto.TmsEntrustedOrderDTO;
import com.jygjexp.jynx.tms.dto.TmsShipmentAndEntrustedOrderDTO;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.CustomerOrderStatus;
import com.jygjexp.jynx.tms.enums.EntrustedOrderStatus;
import com.jygjexp.jynx.tms.enums.ShipmentOrderEnum;
import com.jygjexp.jynx.tms.mapper.*;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.vo.TmsDriverLocationVo;
import com.jygjexp.jynx.tms.vo.TmsDriverPageVo;
import com.jygjexp.jynx.tms.vo.TmsDriverVehicleInfoVo;
import com.jygjexp.jynx.tms.vo.app.TmsAppDriverPersonVo;
import com.jygjexp.jynx.tms.vo.app.TmsAppDriverVo;
import com.jygjexp.jynx.tms.vo.app.TmsAppEntrustedOrderInfoVo;
import com.jygjexp.jynx.tms.vo.excel.TmsDriverExportVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 司机信息记录
 *
 * <AUTHOR>
 * @date 2025-02-21 16:14:03
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TmsDriverServiceImpl extends ServiceImpl<TmsDriverMapper, TmsDriverEntity> implements TmsDriverService {

    private  final TmsDriverMapper driverMapper;
    private final TmsLmdDriverMapper lmdDriverMapper;
    private final TmsVehicleInfoMapper vehicleInfoMapper;
    private final TmsOrderTrackService orderTrackService;
    private final TmsEntrustedOrderService entrustedOrderService;
    private final TmsShipmentOrderService shipmentOrderService;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsDriverLocationMapper driverLocationMapper;
    private final TmsEntrustedOrderMapper entrustedOrderMapper;
    private final TmsShipmentOrderMapper shipmentOrderMapper;
    private final TmsAdditionalServicesMapper additionalServicesMapper;
    private final RemoteTmsAppUserService remoteTmsAppUserService;
    private final TmsRoutePlanService tmsRoutePlanService;

    //分页查询
    @Override
    public Page<TmsDriverPageVo> search(Page page, TmsDriverPageVo vo) {
        MPJLambdaWrapper<TmsDriverEntity> wrapper = getWrapper(vo,null);
        return driverMapper.selectJoinPage(page,TmsDriverPageVo.class,wrapper);
    }

    // 司机新增
    @Override
    public R saveDriver(TmsDriverEntity tmsDriver) {
        // 新增之前需先判断手机号是否已存在
        if (!checkPhone(tmsDriver.getPhone())){
            return LocalizedR.failed("tms.app.driver.register.phone.repetition", tmsDriver.getPhone());
        }
        R<AppUserInfo> info = remoteTmsAppUserService.info(tmsDriver.getPhone());
        if (info.getCode()==0){
            return LocalizedR.failed("tms.app.driver.register.phone.repetition","");
        }
        // 同步创建app账号
        AppUserDTO appUserDTO = new AppUserDTO();
        appUserDTO.setRole(Collections.singletonList(1l));
        appUserDTO.setPhone(tmsDriver.getPhone());
        appUserDTO.setUsername(tmsDriver.getDriverName());
        appUserDTO.setPassword("123456");
        R save = remoteTmsAppUserService.save(appUserDTO);
        if (save.getCode()==1){
            return LocalizedR.failed("tms.app.driver.register.error", save.getMsg());
        }

        return R.ok(driverMapper.insert(tmsDriver));
    }

    // 司机修改
    @Override
    public R updateDriver(TmsDriverEntity tmsDriver) {
        // 修改之前需先判断手机号是否已存在
        if (!checkPhoneUpdate(tmsDriver.getPhone(), tmsDriver.getDriverId())){
            return LocalizedR.failed("tms.app.driver.register.phone.repetition", tmsDriver.getPhone());
        }
        // 先查出司机信息
        TmsDriverEntity tmsDriverEntity = driverMapper.selectById(tmsDriver.getDriverId());
        // 同步修改app账号
        R<AppUserInfo> info = remoteTmsAppUserService.info(tmsDriverEntity.getPhone());
        if (info.getCode()==1){
            return R.failed("Account does not exist");
        }
        AppUserDTO appUserDTO = new AppUserDTO();
        appUserDTO.setPhone(tmsDriver.getPhone());
        appUserDTO.setNewPhone(tmsDriverEntity.getPhone());
        appUserDTO.setUsername(tmsDriver.getDriverName());
        appUserDTO.setRole(Collections.singletonList(1l));
        appUserDTO.setUserId(info.getData().getAppUser().getUserId());
        R r = remoteTmsAppUserService.updateById(appUserDTO);
        if (r.getCode()==1){
            return LocalizedR.failed("tms.app.driver.update.error", "");
        }
        return R.ok(driverMapper.updateById(tmsDriver));
    }

    // 司机启用停用
    @Override
    public R businessSwitch(Long driverId, Integer isValid) {
        if (null != driverId && null != isValid)
        {
            TmsDriverEntity tmsDriverEntity = driverMapper.selectById(driverId);
            if (isValid == 1){
                // 启用之前需先判断手机号是否已存在
                if (!checkPhone(tmsDriverEntity.getPhone())){
                    return LocalizedR.failed("tms.app.driver.register.phone.repetition", tmsDriverEntity.getPhone());
                }
            }
            tmsDriverEntity.setIsValid(isValid);
            driverMapper.updateById(tmsDriverEntity);
        }
        return R.ok(Boolean.TRUE);
    }

    // 校验司机手机号是否存在
    private Boolean checkPhone(String phone) {
        TmsDriverEntity tmsDriverEntity = driverMapper.selectOne(new LambdaQueryWrapper<TmsDriverEntity>()
                .eq(TmsDriverEntity::getPhone, phone)
                .eq(TmsDriverEntity::getIsValid, true), false);
        if (null != tmsDriverEntity){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    // 校验司机手机号是否存在
    private Boolean checkPhoneUpdate(String phone, Long driverId) {
        TmsDriverEntity tmsDriverEntity = driverMapper.selectOne(new LambdaQueryWrapper<TmsDriverEntity>()
                .eq(TmsDriverEntity::getPhone, phone)
                .eq(TmsDriverEntity::getIsValid, true)
                .ne(TmsDriverEntity::getDriverId, driverId), false);
        if (null != tmsDriverEntity){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public List<TmsDriverExportVo> getExcel(TmsDriverPageVo vo, Long[] ids) {
        MPJLambdaWrapper<TmsDriverEntity> wrapper = getWrapper(vo,ids);
        return driverMapper.selectJoinList(TmsDriverExportVo.class,wrapper);

    }

    private MPJLambdaWrapper getWrapper(TmsDriverPageVo vo, Long[] ids){
        MPJLambdaWrapper<TmsDriverEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.like(StrUtil.isNotBlank(vo.getCarrierCode()),TmsCarrierEntity::getCarrierCode,vo.getCarrierCode())
                .like(StrUtil.isNotBlank(vo.getCarrierName()),TmsCarrierEntity::getCarrierName,vo.getCarrierName())
                .eq(ObjUtil.isNotNull(vo.getIsValid()),TmsDriverEntity::getIsValid,vo.getIsValid())
                .like(StrUtil.isNotBlank(vo.getPhone()),TmsDriverEntity::getPhone,vo.getPhone())
                .selectAll(TmsDriverEntity.class)
                .select(TmsCarrierEntity::getCarrierName)
                .leftJoin(TmsCarrierEntity.class,TmsCarrierEntity::getCarrierId,TmsDriverEntity::getCarrierId)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, TmsDriverEntity::getDriverId, ids)
                .orderByDesc(TmsDriverEntity::getDriverId);
        return wrapper;
    }

    // 查询启用且是开工状态的司机车辆列表-查询空闲中和运送中司机信息
    @Override
    public Map<String, List<TmsDriverVehicleInfoVo>> listDriverInfo() {
        MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsLmdDriverEntity.class)
                .selectAs(TmsVehicleInfoEntity::getVehicleType, TmsDriverVehicleInfoVo.Fields.vehicleType)
                .selectAs(TmsVehicleInfoEntity::getCarrierId, TmsDriverVehicleInfoVo.Fields.carrierId)
                .selectAs(TmsVehicleInfoEntity::getContactPhone, TmsDriverVehicleInfoVo.Fields.contactPhone)
                .selectAs(TmsVehicleInfoEntity::getLicensePlate, TmsDriverVehicleInfoVo.Fields.licensePlate)
                .selectAs(TmsVehicleInfoEntity::getVehicleColor, TmsDriverVehicleInfoVo.Fields.vehicleColor)
                .selectAs(TmsVehicleInfoEntity::getVehicleImageUrl, TmsDriverVehicleInfoVo.Fields.vehicleImageUrl)
                .selectAs(TmsVehicleInfoEntity::getLoadCapacity, TmsDriverVehicleInfoVo.Fields.loadCapacity)
                .selectAs(TmsVehicleInfoEntity::getVolume, TmsDriverVehicleInfoVo.Fields.volume)
                .selectAs(TmsVehicleInfoEntity::getPurchaseDate, TmsDriverVehicleInfoVo.Fields.purchaseDate)
                .selectAs(TmsVehicleInfoEntity::getRegistrationDate, TmsDriverVehicleInfoVo.Fields.registrationDate)
                .selectAs(TmsVehicleInfoEntity::getInsuranceStartDate, TmsDriverVehicleInfoVo.Fields.insuranceStartDate)
                .selectAs(TmsVehicleInfoEntity::getInsuranceEndDate, TmsDriverVehicleInfoVo.Fields.insuranceEndDate)
                .selectAs(TmsVehicleInfoEntity::getInsuranceDocumentUrl, TmsDriverVehicleInfoVo.Fields.insuranceDocumentUrl)
                .selectAs(TmsVehicleInfoEntity::getLength, TmsDriverVehicleInfoVo.Fields.length)
                .selectAs(TmsVehicleInfoEntity::getWidth, TmsDriverVehicleInfoVo.Fields.width)
                .selectAs(TmsVehicleInfoEntity::getHeight, TmsDriverVehicleInfoVo.Fields.height)
                .selectAs(TmsVehicleInfoEntity::getCargoType, TmsDriverVehicleInfoVo.Fields.cargoType)
                .selectAs(TmsShipmentOrderEntity::getShipmentId, TmsDriverVehicleInfoVo.Fields.shipmentId) // 运输单ID
                .selectAs(TmsShipmentOrderEntity::getShipmentStatus, TmsDriverVehicleInfoVo.Fields.shipmentStatus) // 运输单状态
                .leftJoin(TmsVehicleDriverRelationEntity.class,  TmsVehicleDriverRelationEntity::getDriverId, TmsLmdDriverEntity::getDriverId)
                .leftJoin(TmsVehicleInfoEntity.class, TmsVehicleInfoEntity::getId, TmsVehicleDriverRelationEntity::getVehicleId)
                .leftJoin(TmsShipmentOrderEntity.class, TmsShipmentOrderEntity::getDriverId, TmsLmdDriverEntity::getDriverId)
                .eq(TmsLmdDriverEntity::getIsValid, 1)     // 已启用
                .eq(TmsLmdDriverEntity::getIsOpen, true);  // 已营业

        // 查询司机及其车辆信息
        List<TmsDriverVehicleInfoVo> records = lmdDriverMapper.selectJoinList(TmsDriverVehicleInfoVo.class, wrapper);

        // 去重处理：以司机ID为唯一标识
        Map<Long, TmsDriverVehicleInfoVo> driverMap = new HashMap<>();
        for (TmsDriverVehicleInfoVo record : records) {
            Long driverId = record.getDriverId();
            if (!driverMap.containsKey(driverId)) {
                driverMap.put(driverId, record);
            } else {
                // 如果司机已经有记录，且当前记录的运输单状态为运送中，则更新状态
                TmsDriverVehicleInfoVo existingRecord = driverMap.get(driverId);
                if (record.getShipmentStatus() == 0 || record.getShipmentStatus() == 1) {
                    existingRecord.setDriverStatus(1);
                    existingRecord.setShipmentId(record.getShipmentId());
                    existingRecord.setShipmentStatus(record.getShipmentStatus());
                }
            }
        }

        // 分类：空闲中和运送中
        List<TmsDriverVehicleInfoVo> idleDrivers = new ArrayList<>(); // 空闲中
        List<TmsDriverVehicleInfoVo> busyDrivers = new ArrayList<>(); // 运送中
        for (TmsDriverVehicleInfoVo record : driverMap.values()) {
            // 判断司机是否有运输单，且运输单状态为待提货（0）或运输中（1）
            if (record.getShipmentId() != null && (record.getShipmentStatus() == 0 || record.getShipmentStatus() == 1)) {
                busyDrivers.add(record);
            } else {
                idleDrivers.add(record);
            }
        }

        // 返回分类结果
        Map<String, List<TmsDriverVehicleInfoVo>> result = new HashMap<>();
        result.put("idleDrivers", idleDrivers);
        result.put("busyDrivers", busyDrivers);
        return result;
    }



    /*
    *   app司机注册
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R register(TmsAppDriverVo tmsAppDriverVo) {
        // 先校验司机手机号是否重复
        Boolean isExist = driverMapper.selectCount(new LambdaQueryWrapper<TmsDriverEntity>()
                .eq(TmsDriverEntity::getPhone, tmsAppDriverVo.getPhone()).eq(TmsDriverEntity::getIsValid, true)) > 0;
        if (isExist) {
            return LocalizedR.failed("tms.app.driver.register.phone.repetition", tmsAppDriverVo.getPhone());
        }

        try {
            // 司机信息新增
            TmsDriverEntity driver = new TmsDriverEntity();
            driver.setCarrierId(tmsAppDriverVo.getCarrierId());
            driver.setDriverName(tmsAppDriverVo.getDriverName());
            driver.setSex(tmsAppDriverVo.getSex());
            driver.setPhone(tmsAppDriverVo.getPhone());
            driver.setIdNumber(tmsAppDriverVo.getIdNumber());
            driver.setEmergencyName(tmsAppDriverVo.getEmergencyName());
            driver.setEmergencyPhone(tmsAppDriverVo.getEmergencyPhone());
            driver.setLicenseType(tmsAppDriverVo.getLicenseType());
            driver.setLicenseTimeStart(tmsAppDriverVo.getLicenseTimeStart());
            driver.setLicenseTimeEnd(tmsAppDriverVo.getLicenseTimeEnd());
            driver.setHomeAddress(tmsAppDriverVo.getHomeAddress());
            driver.setIdCardFront(tmsAppDriverVo.getIdCardFront());
            driver.setIdCardBack(tmsAppDriverVo.getIdCardBack());
            driver.setDrivingLicenseFront(tmsAppDriverVo.getDrivingLicenseFront());
            driver.setDrivingLicenseBack(tmsAppDriverVo.getDrivingLicenseBack());
            driver.setOtherQualification(tmsAppDriverVo.getOtherQualification());
            int driverResult = driverMapper.insert(driver);
            if (driverResult <= 0) {
                throw new RuntimeException("Failed to add driver information.");
            }

            // 车辆信息新增
            TmsVehicleInfoEntity vehicleInfo = new TmsVehicleInfoEntity();
            vehicleInfo.setCarrierId(tmsAppDriverVo.getCarrierId());
            vehicleInfo.setDriverId(driver.getDriverId());
            vehicleInfo.setContactPhone(tmsAppDriverVo.getPhone());
            vehicleInfo.setLicensePlate(tmsAppDriverVo.getLicensePlate());
            vehicleInfo.setVehicleType(tmsAppDriverVo.getVehicleType());
            vehicleInfo.setPurchaseDate(tmsAppDriverVo.getPurchaseDate());
            vehicleInfo.setRegistrationDate(tmsAppDriverVo.getRegistrationDate());
            vehicleInfo.setLoadCapacity(tmsAppDriverVo.getLoadCapacity());
            vehicleInfo.setVolume(tmsAppDriverVo.getVolume());
            vehicleInfo.setLength(tmsAppDriverVo.getLength());
            vehicleInfo.setWidth(tmsAppDriverVo.getWidth());
            vehicleInfo.setHeight(tmsAppDriverVo.getHeight());

            int vehicleResult = vehicleInfoMapper.insert(vehicleInfo);
            if (vehicleResult <= 0) {
                throw new RuntimeException("Failed to add vehicle information.");
            }
            return R.ok(Boolean.TRUE);
        } catch (Exception e) {
            throw new RuntimeException("Driver registration failure: " + e.getMessage(), e); // 确保事务回滚
        }
    }

    // 司机修改个人信息
    @Override
    public R driverInfoUpdate(TmsAppDriverPersonVo driverInfo) {
        if (driverInfo != null) {
            // 判断手机号是否重复
            TmsDriverEntity tmsDriverEntity = driverMapper.selectOne(new LambdaQueryWrapper<TmsDriverEntity>()
                    .eq(TmsDriverEntity::getPhone, driverInfo.getPhone())
                    .eq(TmsDriverEntity::getIsValid, true)
                    .ne(TmsDriverEntity::getDriverId, driverInfo.getDriverId()), false);
            if (ObjectUtil.isNotNull(tmsDriverEntity)){
                return LocalizedR.failed("tms.app.driver.register.phone.repetition", driverInfo.getPhone());
            }
            //修改app账号信息
            R<AppUserInfo> info = remoteTmsAppUserService.info(driverInfo.getPhone());
            if (info.getCode()==1){
                return R.failed("Account does not exist");
            }
            AppUserDTO appUserDTO = new AppUserDTO();
            appUserDTO.setPhone(driverInfo.getPhone());
            appUserDTO.setNewPhone(driverInfo.getPhone());
            appUserDTO.setUsername(driverInfo.getDriverName());
            appUserDTO.setRole(Collections.singletonList(1l));
            appUserDTO.setUserId(info.getData().getAppUser().getUserId());
            appUserDTO.setAvatar(driverInfo.getDriverAvatar());
            R r = remoteTmsAppUserService.updateDriverInfo(appUserDTO);
            if (r.getCode()==1){
                return LocalizedR.failed("tms.app.driver.update.error", "");
            }
            TmsDriverEntity driverInfoEntity = new TmsDriverEntity();
            BeanUtil.copyProperties(driverInfo, driverInfoEntity);
            driverMapper.updateById(driverInfoEntity);
            return R.ok(Boolean.TRUE);
        }
        return R.failed(Boolean.FALSE);
    }



    // app司机配送失败
    @Override
    public R deliveryFailed(String entrustedOrderNumber) {
        // 根据委托单号查询委托单信息
        TmsEntrustedOrderEntity tmsEntrustedOrderEntity = entrustedOrderService.getOne(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber), false);
        // 将订单状态改为失败
        boolean update = entrustedOrderService.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                .set(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.INCOMPLETE.getCode())
                .set(TmsEntrustedOrderEntity::getShipmentNo, null));      //将委托订单从当前运输单中移除
        if (update) {
            // 更新子单状态为已失败
            entrustedOrderService.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                    .likeRight(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                    .eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 1)
                    .set(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.INCOMPLETE.getCode())
                    .set(TmsEntrustedOrderEntity::getShipmentNo, null));
            // 记录轨迹
            orderTrackService.saveTrack(entrustedOrderNumber,tmsEntrustedOrderEntity.getCustomerOrderNumber(), EntrustedOrderStatus.INCOMPLETE.getValue(), ""
                    , "司机送货失败","", 1);

            // 判断运输单是否还有未完成的订单
            if (tmsEntrustedOrderEntity.getShipmentNo() != null) {
                boolean hasUnfinishedOrders = entrustedOrderService.count(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                        .eq(TmsEntrustedOrderEntity::getShipmentNo, tmsEntrustedOrderEntity.getShipmentNo())
                        .ne(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.DELIVERED.getCode())) > 0;

                // 如果没有未完成的订单，则将运输单状态改为已完成
                if (!hasUnfinishedOrders) {
                    shipmentOrderService.update(new LambdaUpdateWrapper<TmsShipmentOrderEntity>()
                            .eq(TmsShipmentOrderEntity::getShipmentNo, tmsEntrustedOrderEntity.getShipmentNo())
                            .set(TmsShipmentOrderEntity::getShipmentStatus, ShipmentOrderEnum.COMPLETED.getType()));
                }
            }
        } else {
            return R.failed(Boolean.FALSE);
        }
        return R.ok(Boolean.TRUE);
    }

    // app司机扫描取货
    @Override
    public R scanPick(String entrustedOrderNo, Long driverId) {
        // 根据委托单号查询司机信息，检查该订单司机是否与当前司机有关联
        MPJLambdaWrapper wrapper = new MPJLambdaWrapper<TmsShipmentOrderEntity>()
                .select(TmsShipmentOrderEntity::getDriverId)
                .leftJoin(TmsEntrustedOrderEntity.class, TmsEntrustedOrderEntity::getShipmentNo, TmsShipmentOrderEntity::getShipmentNo)
                .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNo)
                .eq(TmsShipmentOrderEntity::getDriverId, driverId);

        TmsShipmentOrderEntity tmsShipmentOrder = shipmentOrderMapper.selectOne(wrapper, false);

        if (ObjectUtil.isNull(tmsShipmentOrder)){
            return LocalizedR.failed("tms.app.driver.scan.pick.error","");
        }

        // 扫描成功后修改委托单扫描状态为已扫描
        int update = entrustedOrderMapper.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNo)
                .set(TmsEntrustedOrderEntity::getIsScan, true));

        if (update > 0){
            // **根据子单号提取主单号**
            String mainOrderNo = entrustedOrderNo.substring(0, 13);  // 提取前 13 位作为主单号
            // 判断主单是否还有无未扫描的子单号
            long count = entrustedOrderService.count(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                    .likeRight(TmsEntrustedOrderEntity::getEntrustedOrderNumber, mainOrderNo) // 匹配所有以 mainOrderNo 开头的子单
                    .eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 1)
                    .ne(TmsEntrustedOrderEntity::getIsScan, 1));
            if (count == 0) {
                // 如果没有未扫描的子单，则修改主单状态为已扫描
                entrustedOrderService.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                        .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, mainOrderNo)
                        .eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 0)
                        .set(TmsEntrustedOrderEntity::getIsScan, 1));
            }
        }


        return LocalizedR.ok("tms.app.driver.scan.pick.succeed","");
    }

    // app 司机上传取货证明
    @Override
    public R uploadPickupProof(String entrustedOrderNumber, String pickupProof) {
        // 根据委托单号查询委托单信息
        TmsEntrustedOrderEntity tmsEntrustedOrder = entrustedOrderService.getOne(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber), false);

        // 判断委托单的扫描状态是否为已扫描
        if (tmsEntrustedOrder.getIsScan() == 0){
            return LocalizedR.failed("tms.app.driver.order.scan",entrustedOrderNumber);
        }

        if (StrUtil.isNotBlank(pickupProof)){
            tmsEntrustedOrder.setPickupProof(pickupProof);
        }

        // 取货成功修改订单状态   修改委托单为运输中
        tmsEntrustedOrder.setOrderStatus(EntrustedOrderStatus.IN_TRANSIT.getCode());
        boolean b = entrustedOrderService.updateById(tmsEntrustedOrder);

        if (b) {
            // 更新子单状态为运输中
            entrustedOrderService.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                    .likeRight(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                     .eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 1)
                    .set(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.IN_TRANSIT.getCode()));

            // 保存取货节点的时间
            tmsRoutePlanService.saveActualArrivalTime(entrustedOrderNumber, 1, LocalDateTime.now());

            // 修改客户订单状态为待收货
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getCustomerOrderNumber, tmsEntrustedOrder.getCustomerOrderNumber())
                    .set(TmsCustomerOrderEntity::getOrderStatus, CustomerOrderStatus.PENDING_RECEIPT.getCode()));

            // 记录轨迹
            orderTrackService.saveTrack(entrustedOrderNumber,tmsEntrustedOrder.getCustomerOrderNumber(),EntrustedOrderStatus.IN_TRANSIT.getValue(), ""
                    , "司机已扫描取货","", 1);

            // 将运输单状态改为运输中
            shipmentOrderService.update(new LambdaUpdateWrapper<TmsShipmentOrderEntity>()
                    .eq(TmsShipmentOrderEntity::getShipmentNo, tmsEntrustedOrder.getShipmentNo())
                    .set(TmsShipmentOrderEntity::getShipmentStatus, ShipmentOrderEnum.IN_TRANSIT.getType()));
        } else {
            return R.failed(Boolean.FALSE);
        }
        return R.ok(Boolean.TRUE);
    }

    // 司机送货成功
    @Override
    public R deliverySuccess(String entrustedOrderNumber, String pickupProof, String deliveryProof) {
        // 根据委托单号查询委托单信息
        TmsEntrustedOrderEntity tmsEntrustedOrder = entrustedOrderService.getOne(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber), false);
        if (StrUtil.isNotBlank(deliveryProof)){
            tmsEntrustedOrder.setDeliveryProof(deliveryProof);
        }
        tmsEntrustedOrder.setDeliveryProof(deliveryProof);
        tmsEntrustedOrder.setOrderStatus(EntrustedOrderStatus.DELIVERED.getCode());
        boolean b = entrustedOrderService.updateById(tmsEntrustedOrder);

        if (b){
/*            // **根据子单号提取主单号**
            String mainOrderNo = entrustedOrderNumber.substring(0, 13);  // 提取前 13 位作为主单号

            // 判断主单是否还有未完成的子单号
            long  count = entrustedOrderService.count(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                    .likeRight(TmsEntrustedOrderEntity::getEntrustedOrderNumber, mainOrderNo) // 匹配所有以 mainOrderNo 开头的子单
                    .eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 1)  // 仅查询子单
                    .ne(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.DELIVERED.getCode()));
            if ( count <= 0) {
                // 如果没有未完成的子单，则修改主单状态为已完成
                entrustedOrderService.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                        .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, mainOrderNo)  // 主单号
                        .eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 0)  // 确保是主单
                        .set(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.DELIVERED.getCode()));
            }*/

            // 更新子单状态为已完成
            entrustedOrderService.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                    .likeRight(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                    .eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 1)
                    .set(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.DELIVERED.getCode()));

            // 保存取货节点的时间
            tmsRoutePlanService.saveActualArrivalTime(entrustedOrderNumber, 0, LocalDateTime.now());

            // 修改客户订单状态为已完成
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getCustomerOrderNumber, tmsEntrustedOrder.getCustomerOrderNumber())
                    .set(TmsCustomerOrderEntity::getOrderStatus, CustomerOrderStatus.DELIVERED.getCode()));

            // 记录轨迹
            orderTrackService.saveTrack(entrustedOrderNumber,tmsEntrustedOrder.getCustomerOrderNumber(), EntrustedOrderStatus.DELIVERED.getValue(), ""
                    , "司机送货完成","", 1);

            // 判断运输单是否还有未完成的订单
            boolean hasUnfinishedOrders = entrustedOrderService.count(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                    .eq(TmsEntrustedOrderEntity::getShipmentNo, tmsEntrustedOrder.getShipmentNo())
                    .ne(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.DELIVERED.getCode())) > 0;

            // 如果没有未完成的订单，则将运输单状态改为已完成
            if (!hasUnfinishedOrders) {
                shipmentOrderService.update(new LambdaUpdateWrapper<TmsShipmentOrderEntity>()
                        .eq(TmsShipmentOrderEntity::getShipmentNo, tmsEntrustedOrder.getShipmentNo())
                        .set(TmsShipmentOrderEntity::getShipmentStatus, ShipmentOrderEnum.COMPLETED.getType()));
            }

        }else {
            return R.failed(Boolean.FALSE);
        }
        return R.ok(Boolean.TRUE);
    }

    // 根据运输单号查询委托单 提货信息
    @Override
    public R getPickupList(String shipmentOrderNo,Boolean isSeanAndGet) {
        MPJLambdaWrapper<TmsEntrustedOrderEntity> wrapper = new MPJLambdaWrapper<TmsEntrustedOrderEntity>()
                .select(TmsEntrustedOrderEntity::getEntrustedOrderNumber)
                .selectCount(TmsCargoInfoEntity::getId, "totalQuantity")
                .selectSum(TmsEntrustedOrderEntity::getTotalWeight)
                .selectSum(TmsEntrustedOrderEntity::getTotalVolume)
                .select(TmsEntrustedOrderEntity::getIsScan)
                .select(TmsEntrustedOrderEntity::getOrderStatus)
                .select(TmsEntrustedOrderEntity::getPickupProof)
                .select(TmsEntrustedOrderEntity::getOrderType)
                .eq(TmsEntrustedOrderEntity::getShipmentNo, shipmentOrderNo)
                .eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 0)
                .leftJoin(TmsCargoInfoEntity.class, TmsCargoInfoEntity::getCustomerOrderNumber, TmsEntrustedOrderEntity::getCustomerOrderNumber)
                .groupBy(TmsEntrustedOrderEntity::getEntrustedOrderNumber);

        // true 为查询送货信息
        if (isSeanAndGet){
            wrapper.select(TmsEntrustedOrderEntity::getReceiverPhone);
            wrapper.select(TmsEntrustedOrderEntity::getReceiverName);
        }else{
            // false 为查询提货信息
            wrapper.select(TmsEntrustedOrderEntity::getShipperPhone);
            wrapper.select(TmsEntrustedOrderEntity::getShipperName);
        }

        List<TmsShipmentAndEntrustedOrderDTO> tmsShipmentAndEntrustedOrderDTOS = entrustedOrderMapper.selectJoinList(TmsShipmentAndEntrustedOrderDTO.class, wrapper);
        return R.ok(tmsShipmentAndEntrustedOrderDTOS);
    }

    // 根据委托单号获取委托单信息
    @Override
    public R getEntrustedOrder(String entrustedOrderNumber) {
        MPJLambdaWrapper<TmsEntrustedOrderEntity> wrapper = new MPJLambdaWrapper<TmsEntrustedOrderEntity>()
                .select(TmsEntrustedOrderEntity::getEntrustedOrderNumber)
                .select(TmsEntrustedOrderEntity::getCustomerOrderNumber)
                .select(TmsEntrustedOrderEntity::getOrderStatus)
                .select(TmsEntrustedOrderEntity::getOrderType)
                .select(TmsEntrustedOrderEntity::getCargoType)
                .select(TmsEntrustedOrderEntity::getAddressType)
                .select(TmsEntrustedOrderEntity::getShipperAddress)
                .select(TmsEntrustedOrderEntity::getShipperName)
                .select(TmsEntrustedOrderEntity::getShipperPhone)
                .select(TmsEntrustedOrderEntity::getEstimatedShippingTimeStart)
                .select(TmsEntrustedOrderEntity::getEstimatedShippingTimeEnd)
                .select(TmsEntrustedOrderEntity::getDestAddress)
                .select(TmsEntrustedOrderEntity::getReceiverName)
                .select(TmsEntrustedOrderEntity::getReceiverPhone)
                .select(TmsEntrustedOrderEntity::getEstimatedArrivalTimeEnd)
                .select(TmsEntrustedOrderEntity::getEstimatedArrivalTimeStart)
                .select(TmsEntrustedOrderEntity::getTotalWeight)
                .select(TmsEntrustedOrderEntity::getTotalVolume)
                .select(TmsEntrustedOrderEntity::getCargoQuantity)      // 计算主单货品总数量
                .selectCount(TmsCargoInfoEntity::getId,"totalQuantity")  // 计算 COUNT(*) 托盘数量
                .select(TmsEntrustedOrderEntity::getPickupProof)
                .select(TmsEntrustedOrderEntity::getDeliveryProof)
                .select(TmsEntrustedOrderEntity::getRefuseTime)
                .select(TmsEntrustedOrderEntity::getRefuseReasons)
                .select(TmsEntrustedOrderEntity::getIsSubOrderNo)
                .select(TmsExceptionManagementEntity::getExceptionType)
                .select(TmsExceptionManagementEntity::getHandlingPlan)
                .leftJoin(TmsExceptionManagementEntity.class, TmsExceptionManagementEntity::getDispatchOrderNo, TmsEntrustedOrderEntity::getEntrustedOrderNumber)
                //.leftJoin(TmsAdditionalServicesEntity.class, TmsAdditionalServicesEntity::getCustomerOrderNumber, TmsEntrustedOrderEntity::getCustomerOrderNumber)
                .leftJoin(TmsCargoInfoEntity.class, TmsCargoInfoEntity::getCustomerOrderNumber, TmsEntrustedOrderEntity::getCustomerOrderNumber)
                //.eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 1)
                //.apply("CASE WHEN t2.additional_service_type != '1' THEN t2.additional_service_time IS NOT NULL ELSE 1=1 END")
                .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber);
        wrapper.last("GROUP BY t.entrusted_order_number, t1.exception_type, t1.handling_plan");

        List<TmsEntrustedOrderDTO> dtos = entrustedOrderMapper.selectJoinList(TmsEntrustedOrderDTO.class, wrapper);

        if (dtos.isEmpty()) {
            return R.ok(Collections.emptyList());
        }

        // 2. 查询附加服务表，按 customerOrderNumber 进行分组
        List<TmsAdditionalServicesEntity> additionalServices = additionalServicesMapper.selectList(
                new LambdaQueryWrapper<TmsAdditionalServicesEntity>()
                        .eq(TmsAdditionalServicesEntity::getEntrustedOrderNumber, dtos.get(0).getEntrustedOrderNumber())
        );

        // 3. 将 additionalServices 映射到 DTO
        dtos.get(0).setAdditionalServices(additionalServices);

        return R.ok(dtos);
    }

    // 实时存储司机经纬度
    @Override
    public R driverLocation(TmsDriverLocationVo driverLocationVo) {
        TmsDriverLocationEntity location = driverLocationMapper.selectById(driverLocationVo.getDriverId());
        if (location == null) {
            // 插入新位置
            location = new TmsDriverLocationEntity();
            location.setDriverId(driverLocationVo.getDriverId());
            location.setLatitude(driverLocationVo.getLatitude());
            location.setLongitude(driverLocationVo.getLongitude());
            driverLocationMapper.insert(location);
        } else {
            // 更新位置
            location.setLatitude(driverLocationVo.getLatitude());
            location.setLongitude(driverLocationVo.getLongitude());
            driverLocationMapper.updateById(location);
        }
        return R.ok();
    }

    // 获取空闲司机实时经纬度
    @Override
    public R getDriverLocation() {
        // 查询繁忙的司机信息
        MPJLambdaWrapper<TmsDriverLocationEntity> wrapper = new MPJLambdaWrapper<TmsDriverLocationEntity>()
                .select(TmsDriverLocationEntity::getDriverId)
                .selectAs(TmsDriverLocationEntity::getLatitude, "latitude")
                .selectAs(TmsDriverLocationEntity::getLongitude, "longitude")
                .select(TmsDriverLocationEntity::getUpdateTime)
                .select(TmsDriverEntity::getDriverName)
                .selectAs(TmsEntrustedOrderEntity::getShipperAddress, "shipmentAddress")  // 选取发货地
                .selectAs(TmsEntrustedOrderEntity::getDestAddress, "deliveryAddress")    // 选取收货地
                .selectAs(TmsEntrustedOrderEntity::getEntrustedOrderNumber, "entrustedOrderNumber") // 委托单号
                .leftJoin(TmsDriverEntity.class, TmsDriverEntity::getDriverId, TmsDriverLocationEntity::getDriverId)
                .leftJoin(TmsShipmentOrderEntity.class, TmsShipmentOrderEntity::getDriverId, TmsDriverEntity::getDriverId)
                .leftJoin(TmsEntrustedOrderEntity.class, TmsEntrustedOrderEntity::getShipmentNo, TmsShipmentOrderEntity::getShipmentNo)
                .eq(TmsDriverEntity::getIsOpen, true)
                .in(TmsShipmentOrderEntity::getShipmentStatus, ShipmentOrderEnum.TO_BE_TRANSPORTED.getType(), ShipmentOrderEnum.IN_TRANSIT.getType());

        // 查询所有忙碌司机的 ID
        List<Long> busyDriverIds = driverLocationMapper.selectJoinList(Long.class, wrapper);

        // 查询空闲中的司机信息
        MPJLambdaWrapper<TmsDriverLocationEntity> wrapper2 = new MPJLambdaWrapper<TmsDriverLocationEntity>()
                .select(TmsDriverLocationEntity::getDriverId)
                .selectAs(TmsDriverLocationEntity::getLatitude, "latitude")
                .selectAs(TmsDriverLocationEntity::getLongitude, "longitude")
                .select(TmsDriverLocationEntity::getUpdateTime)
                .select(TmsDriverEntity::getDriverName)
                .selectAs(TmsEntrustedOrderEntity::getShipperAddress, "shipmentAddress")  // 选取发货地
                .selectAs(TmsEntrustedOrderEntity::getDestAddress, "deliveryAddress")    // 选取收货地
                .selectAs(TmsEntrustedOrderEntity::getEntrustedOrderNumber, "entrustedOrderNumber") // 委托单号
                .leftJoin(TmsDriverEntity.class, TmsDriverEntity::getDriverId, TmsDriverLocationEntity::getDriverId)
                .leftJoin(TmsShipmentOrderEntity.class, TmsShipmentOrderEntity::getDriverId, TmsDriverEntity::getDriverId)
                .leftJoin(TmsEntrustedOrderEntity.class, TmsEntrustedOrderEntity::getShipmentNo, TmsShipmentOrderEntity::getShipmentNo)
                .eq(TmsDriverEntity::getIsOpen, true)
                .notIn(TmsShipmentOrderEntity::getShipmentStatus, ShipmentOrderEnum.TO_BE_TRANSPORTED.getType(), ShipmentOrderEnum.IN_TRANSIT.getType());

        //**确保空闲司机不在忙碌司机列表中**;
        if (CollUtil.isNotEmpty(busyDriverIds)) {
            wrapper2.notIn(TmsDriverEntity::getDriverId, busyDriverIds);
        }

        // 查询运输中司机列表
        List<TmsDriverLocationVo> rawBusyList = driverLocationMapper.selectJoinList(TmsDriverLocationVo.class, wrapper);

        // 查询空闲中司机列表
        List<TmsDriverLocationVo> rawLeisureList = driverLocationMapper.selectJoinList(TmsDriverLocationVo.class, wrapper2);

        // 数据分组处理
        List<TmsDriverLocationVo> leisureList = processDriverData(rawLeisureList);
        List<TmsDriverLocationVo> busyList = processDriverData(rawBusyList);

        // 直接返回司机位置数据，不再进行遍历填充
        HashMap<String, List<TmsDriverLocationVo>> map = new HashMap<>();
        map.put("leisure", leisureList);
        map.put("busy", busyList);
        return R.ok(map);
    }

    /**
     * 处理查询数据，将相同司机的委托单整合到一个数组中
     */
    private List<TmsDriverLocationVo> processDriverData(List<TmsDriverLocationVo> rawList) {
        return rawList.stream()
                .collect(Collectors.groupingBy(TmsDriverLocationVo::getDriverId))  // 以司机 ID 分组
                .values().stream()
                .map(group -> {
                    TmsDriverLocationVo driverInfo = group.get(0); // 取第一条数据作为基础
                    List<TmsAppEntrustedOrderInfoVo> orderList = group.stream()
                            .map(vo -> new TmsAppEntrustedOrderInfoVo(vo.getEntrustedOrderNumber(), vo.getShipmentAddress(), vo.getDeliveryAddress()))
                            .collect(Collectors.toList());
                    driverInfo.setEntrustedOrderList(orderList); // 设置委托单数组
                    return driverInfo;
                })
                .collect(Collectors.toList());
    }


    // 客户拒签
    @Override
    public R customerRefuse(String entrustedOrderNumber, String refuseReasons) {
        // 获取当前拒收时间
        LocalDateTime now = LocalDateTime.now();
        // 更新订单状态
        boolean update = entrustedOrderService.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                .set(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.INCOMPLETE.getCode())
                .set(TmsEntrustedOrderEntity::getRefuseReasons, refuseReasons)
                .set(TmsEntrustedOrderEntity::getShipmentNo, null)    //将委托订单从当前运输单中移除
                .set(TmsEntrustedOrderEntity::getRefuseTime, now));

        // 根据委托单查询绑定的运输单
        TmsEntrustedOrderEntity tmsEntrustedOrder = entrustedOrderService.getOne(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber),false);

        if (update) {
            // 更新子单状态为失败
            entrustedOrderService.update(new LambdaUpdateWrapper<TmsEntrustedOrderEntity>()
                    .likeRight(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                    .eq(TmsEntrustedOrderEntity::getIsSubOrderNo, 1)
                    .set(TmsEntrustedOrderEntity::getShipmentNo, null)
                    .set(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.INCOMPLETE.getCode()));

            // 记录轨迹
            orderTrackService.saveTrack(entrustedOrderNumber,tmsEntrustedOrder.getCustomerOrderNumber(), EntrustedOrderStatus.INCOMPLETE.getValue(), ""
                    , "单据被拒签","", 1);

            // 判断运输单是否还有未完成的订单
            if (tmsEntrustedOrder.getShipmentNo() != null) {
                boolean hasUnfinishedOrders = entrustedOrderService.count(new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
                        .eq(TmsEntrustedOrderEntity::getShipmentNo, tmsEntrustedOrder.getShipmentNo())
                        .ne(TmsEntrustedOrderEntity::getOrderStatus, EntrustedOrderStatus.DELIVERED.getCode())) > 0;

                // 如果没有未完成的订单，则将运输单状态改为已完成
                if (!hasUnfinishedOrders) {
                    shipmentOrderService.update(new LambdaUpdateWrapper<TmsShipmentOrderEntity>()
                            .eq(TmsShipmentOrderEntity::getShipmentNo, tmsEntrustedOrder.getShipmentNo())
                            .set(TmsShipmentOrderEntity::getShipmentStatus, ShipmentOrderEnum.COMPLETED.getType()));
                }
            }
        }

        return R.ok();
    }


}