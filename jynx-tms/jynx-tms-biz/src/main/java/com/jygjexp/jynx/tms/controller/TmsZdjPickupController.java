package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.TmsBookingPickupDto;
import com.jygjexp.jynx.tms.entity.TmsWarehouseLocationEntity;
import com.jygjexp.jynx.tms.entity.TmsZdjPickupEntity;
import com.jygjexp.jynx.tms.feign.RemoteTmsAppMobileService;
import com.jygjexp.jynx.tms.service.TmsWarehouseLocationService;
import com.jygjexp.jynx.tms.service.TmsZdjPickupService;
import com.jygjexp.jynx.tms.vo.TmsZdjPickupPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 中大件自提信息记录表
 *
 * <AUTHOR>
 * @date 2025-06-06 17:03:21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsZdjPickup" )
@Tag(description = "tmsZdjPickup" , name = "中大件自提信息记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsZdjPickupController {

    private final  TmsZdjPickupService tmsZdjPickupService;
    private final TmsWarehouseLocationService  tmsWarehouseLocationService;
    private final RemoteTmsAppMobileService remoteTmsAppMobileService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsZdjPickup 中大件自提信息记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_view')" )
    public R getTmsZdjPickupPage(@ParameterObject Page page, @ParameterObject TmsZdjPickupPageVo tmsZdjPickup) {
        return R.ok(tmsZdjPickupService.search(page, tmsZdjPickup));
    }


    /**
     * 通过id查询中大件自提信息记录表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsZdjPickupService.getById(id));
    }

    /**
     * 新增中大件自提信息记录表
     * @param tmsZdjPickup 中大件自提信息记录表
     * @return R
     */
    @Operation(summary = "新增中大件自提信息记录表" , description = "新增中大件自提信息记录表" )
    @SysLog("新增中大件自提信息记录表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_add')" )
    public R add(@RequestBody TmsZdjPickupEntity tmsZdjPickup) {
        return tmsZdjPickupService.add(tmsZdjPickup);
    }

    /**
     * 修改中大件自提信息记录表
     * @param tmsZdjPickup 中大件自提信息记录表
     * @return R
     */
    @Operation(summary = "修改中大件自提信息记录表" , description = "修改中大件自提信息记录表" )
    @SysLog("修改中大件自提信息记录表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_edit')" )
    public R updateById(@RequestBody TmsZdjPickupEntity tmsZdjPickup) {
        //根据仓位id查询一下仓位信息
        TmsWarehouseLocationEntity warehouseLocation = tmsWarehouseLocationService.getById(tmsZdjPickup.getLocationId());
        if(ObjectUtil.isNotNull(warehouseLocation)){
            tmsZdjPickup.setWarehouseLocation(warehouseLocation.getLocationCode());
        }
        return R.ok(tmsZdjPickupService.updateById(tmsZdjPickup));
    }

    /**
     * 通过id删除中大件自提信息记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除中大件自提信息记录表" , description = "通过id删除中大件自提信息记录表" )
    @SysLog("通过id删除中大件自提信息记录表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsZdjPickupService.removeBatchByIds(CollUtil.toList(ids)));
    }


    // 新增时获取订单信息
    @Operation(summary = "获取订单信息" , description = "获取订单信息" )
    @GetMapping("/order/info")
    public R getOrderInfo(@RequestParam String orderNo) {
        return tmsZdjPickupService.getOrderInfo(orderNo);
    }


    /**
     * 自提出仓
     */
    @Operation(summary = "自提出仓" , description = "自提出仓" )
    @SysLog("自提出仓" )
    @PostMapping("/pickupOutWarehouse")
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_pickup')" )
    public R pickupOutWarehouse(Long id,String outWarehouseProof) {
        return tmsZdjPickupService.pickupOutWarehouse(id,outWarehouseProof);
    }

    /**
     * 销毁
     */
    @Operation(summary = "销毁" , description = "销毁" )
    @SysLog("销毁" )
    @PostMapping("/destroy/{id}")
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_destroy')" )
    public R destroy(@PathVariable Long id) {
        return tmsZdjPickupService.destroy(id);
    }

    /**
     * 预约取货时间
     */
    @Operation(summary = "预约取货时间" , description = "预约取货时间" )
    @SysLog("预约取货时间" )
    @PostMapping("/bookingPickupTime")
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_booking')" )
    public R bookingPickupTime(@RequestBody TmsBookingPickupDto tmsBookingPickupDto) {
        return tmsZdjPickupService.bookingPickupTime(tmsBookingPickupDto);
    }

    /**
     * 返仓后-重新派送
     */
    @Operation(summary = "返仓后-重新派送" , description = "返仓后-重新派送" )
    @SysLog("返仓后-重新派送" )
    @PostMapping("/rwReDelivery")
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_rd')" )
    public R rwReDelivery(@RequestBody Map<String, List<Long>> ids) {
        List<Long> idList = ids.get("ids");
        return tmsZdjPickupService.rwReDelivery(idList);
    }

    /**
     * 返仓后-发送短信
     */
    @Operation(summary = "返仓后-发送短信" , description = "返仓后-发送短信" )
    @SysLog("返仓后-发送短信" )
    @PostMapping("/rwSendMessage")
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_send')" )
    public R rwSendMessage(@RequestBody Map<String, List<Long>> ids) {
        List<Long> idList = ids.get("ids");
        return tmsZdjPickupService.rwSendMessage(idList);
    }

    /**
     * 根据id查询上架自提记录
     */
    @Operation(summary = "根据id查询上架自提记录" , description = "根据id查询上架自提记录" )
    @PostMapping("/getPickUpCodeById")
    public R getPickUpCodeById(@RequestBody Map<String, List<Long>> ids) {
        List<Long> idList = ids.get("ids");
        return tmsZdjPickupService.getPickUpCodeById(idList);
    }


    /**
     * 根据id查询上架自提记录--打印取件码远程使用
     */
    @Operation(summary = "根据id查询上架自提记录--打印取件码远程使用" , description = "根据id查询上架自提记录--打印取件码远程使用" )
    @PostMapping("/getPickUpCodeByIdForRemote")
    public List<TmsZdjPickupEntity> getPickUpCodeByIdForRomote(@RequestBody Map<String, List<Long>> ids) {
        List<Long> idList = ids.get("ids");
        return tmsZdjPickupService.getPickUpCodeByIdForRomote(idList);
    }


    /**
     * 导出excel 表格
     * @param tmsZdjPickup 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsZdjPickup_export')" )
    public List<TmsZdjPickupEntity> export(TmsZdjPickupEntity tmsZdjPickup,Long[] ids) {
        return tmsZdjPickupService.list(Wrappers.lambdaQuery(tmsZdjPickup).in(ArrayUtil.isNotEmpty(ids), TmsZdjPickupEntity::getId, ids));
    }

    /**
     * 生成取件码
     * @param warehouseCode 仓位
     * @return
     */
    public static String generatePickupCode(String warehouseCode) {
        // 5位随机字符串
        String randomStr = RandomUtil.randomString(5);
        String temp= warehouseCode + randomStr;
        // 拼接并返回
        return temp.toUpperCase() ;
    }
}