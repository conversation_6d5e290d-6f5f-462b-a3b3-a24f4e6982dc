package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.dto.UserInfo;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.mapper.TmsCarrierMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.vo.TmsCarrierPageVo;
import com.jygjexp.jynx.tms.service.TmsCarrierService;
import com.jygjexp.jynx.tms.vo.excel.TmsCarrierExportVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 承运商信息记录
 *
 * <AUTHOR>
 * @date 2025-02-21 16:13:23
 */
@RequiredArgsConstructor
@Service
public class TmsCarrierServiceImpl extends ServiceImpl<TmsCarrierMapper, TmsCarrierEntity> implements TmsCarrierService {

    private final TmsCarrierMapper carrierMapper;
    private final RemoteTmsUpmsService remoteTmsUpmsService;


    /**
     * 承运商分页查询
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<TmsCarrierPageVo> search(Page page, TmsCarrierPageVo vo) {
        LambdaQueryWrapper<TmsCarrierEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(vo.getCarrierCode()),TmsCarrierEntity::getCarrierCode,vo.getCarrierCode())
                .like(StrUtil.isNotBlank(vo.getCarrierName()),TmsCarrierEntity::getCarrierName,vo.getCarrierName())
                .eq(ObjUtil.isNotNull(vo.getIsValid()),TmsCarrierEntity::getIsValid,vo.getIsValid())
                .orderByDesc(TmsCarrierEntity::getCreateTime);
        return carrierMapper.selectPage(page,wrapper);
    }

    @Override
    public List<TmsCarrierExportVo> getExcel(TmsCarrierPageVo vo, Long[] ids) {
        LambdaQueryWrapper<TmsCarrierEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(vo.getCarrierCode()),TmsCarrierEntity::getCarrierCode,vo.getCarrierCode())
                .like(StrUtil.isNotBlank(vo.getCarrierName()),TmsCarrierEntity::getCarrierName,vo.getCarrierName())
                .eq(ObjUtil.isNotNull(vo.getIsValid()),TmsCarrierEntity::getIsValid,vo.getIsValid())
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, TmsCarrierEntity::getCarrierId, ids)
                .orderByDesc(TmsCarrierEntity::getCreateTime);
        // 根据以上条件返回导出结果
        List<TmsCarrierEntity> list = carrierMapper.selectList(wrapper);
        ArrayList<TmsCarrierExportVo> partnerExcelVos = new ArrayList<>();
        for (TmsCarrierEntity carrier : list) {
            TmsCarrierExportVo excel = new TmsCarrierExportVo();
            BeanUtils.copyProperties(carrier, excel);
            partnerExcelVos.add(excel);
        }
        return partnerExcelVos;
    }

    /**
     * 按客户/委托订单查询承运商列表
     * @param customerOrderNumber
     * @param entrustedOrderNumber
     * @return
     */
    @Override
    public List<TmsCarrierEntity> listCarriersByOrder(String customerOrderNumber, String entrustedOrderNumber) {
        MPJLambdaWrapper<TmsCarrierEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsCarrierEntity.class);

        // 优先使用客户单号查询
        if (customerOrderNumber != null && !customerOrderNumber.isEmpty()) {
            wrapper.leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getCarrierId, TmsCarrierEntity::getCarrierId)
                    .eq(TmsCustomerOrderEntity::getCustomerOrderNumber, customerOrderNumber);
        }
        // 如果客户单号为空，则使用委托单号查询
        else if (entrustedOrderNumber != null && !entrustedOrderNumber.isEmpty()) {
            wrapper.leftJoin(TmsEntrustedOrderEntity.class, TmsEntrustedOrderEntity::getCarrierId, TmsCarrierEntity::getCarrierId)
                    .eq(TmsEntrustedOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber);
        }
        // 如果两个参数都为空，返回空列表（或抛出异常）
        else {
            return Collections.emptyList();
        }

        // 按创建时间倒序排序
        wrapper.orderByDesc(TmsCarrierEntity::getCreateTime);

        return carrierMapper.selectJoinList(TmsCarrierEntity.class, wrapper);
    }


    // 启用客户
    @Override
    public Boolean enableById(Long id) {
        TmsCarrierEntity customer = getById(id);
        if (ObjectUtil.equals(customer.getIsValid(), 1)) {
            throw new RuntimeException("承运商已是启用状态!");
        }
        customer.setIsValid(1);
        //启用客户账号
        remoteTmsUpmsService.enableById(customer.getCarrierName(), SecurityConstants.FROM_IN);
        return updateById(customer);
    }

    // 停用客户
    @Override
    public Boolean disableById(Long id) {
        TmsCarrierEntity customer = getById(id);
        if (ObjectUtil.equals(customer.getIsValid(), 0)) {
            throw new RuntimeException("承运商已是停用状态!");
        }
        customer.setIsValid(0);
        //停用客户账号
        remoteTmsUpmsService.lockUser(customer.getCarrierName(), SecurityConstants.FROM_IN);
        return updateById(customer);
    }

    // 新增承运商
    @Override
    public R saveCarrier(TmsCarrierEntity tmsCarrier) {
        TmsCarrierEntity carrierName = carrierMapper.selectOne(new LambdaQueryWrapper<TmsCarrierEntity>()
                .eq(TmsCarrierEntity::getCarrierName,tmsCarrier.getCarrierName()), false);
        if (null != carrierName){
            return LocalizedR.failed("tms.carrier.add.repetition",tmsCarrier.getCarrierName());
            //R.failed("该承运商名称已存在：");
        }
        // 生成承运商编号
        String carrierNo = generateCarrierNo();
        // 设置承运商编号
        tmsCarrier.setCarrierCode(carrierNo);
        // 判断用户名是否存在于后台用户中
        R<UserInfo> info = remoteTmsUpmsService.info(tmsCarrier.getCarrierName());
        if (info.getCode() == 0) {
            return LocalizedR.failed("tms.user.name.already.exists","");
        }
        // 新增承运商的同时新增系统后台用户账号
        R<Boolean> booleanR = remoteTmsUpmsService.addAccount(tmsCarrier.getCarrierName(), "123456", tmsCarrier.getCarrierEmail(), tmsCarrier.getPhone(), true, SecurityConstants.FROM_IN);
        if (!booleanR.getData()){
            return LocalizedR.failed("tms.carrier.register.error",tmsCarrier.getCarrierName());
        }
        boolean save = carrierMapper.insert(tmsCarrier)>0;
        if (!save){
            R<UserInfo> infoDel = remoteTmsUpmsService.info(tmsCarrier.getCarrierName());
            if (infoDel.getCode() == 0) {
                remoteTmsUpmsService.userDel(new Long[]{infoDel.getData().getSysUser().getUserId()});
            }
            return LocalizedR.failed("tms.carrier.register.error",tmsCarrier.getCarrierName());
        }
        return R.ok();
    }

    // 修改承运商
    @Override
    public R updateCarrier(TmsCarrierEntity tmsCarrier) {
        // 先查出承运商信息
        TmsCarrierEntity byId = carrierMapper.selectById(tmsCarrier.getCarrierId());
        R<UserInfo> info = remoteTmsUpmsService.info(byId.getCarrierName());
        if (info.getCode() == 1) {
            return R.failed("Account does not exist");
        }
        // 修改系统后台用户账号
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(info.getData().getSysUser().getUserId());
        userDTO.setUsername(tmsCarrier.getCarrierName());
        userDTO.setName(tmsCarrier.getName());
        userDTO.setPhone(tmsCarrier.getPhone());
        remoteTmsUpmsService.updateUser(userDTO);
        return R.ok(carrierMapper.updateById(tmsCarrier));
    }


    /**
     * 生成承运商编号
     * 规则：CYS-年份-月份-001-校验位
     */
    private String generateCarrierNo() {
        // 获取当前年份和月份
        SimpleDateFormat sdfYear = new SimpleDateFormat("yyyy");
        SimpleDateFormat sdfMonth = new SimpleDateFormat("MM");
        String year = sdfYear.format(new Date());
        String month = sdfMonth.format(new Date());

        // 查询当前数据库中最大的 ID
        TmsCarrierEntity lastCarrier = carrierMapper.selectOne(new LambdaQueryWrapper<TmsCarrierEntity>()
                .orderByDesc(TmsCarrierEntity::getCarrierId)
                .last("LIMIT 1"));

        // 计算新的顺序编号（如果数据库为空，则从 1 开始）如果不为空则自增
        Long orderNo = (lastCarrier != null) ? lastCarrier.getCarrierId() + 1 : 1;
        String orderNoStr = String.format("%03d", orderNo); // 格式化为三位数字

        // 随机生成一个校验位（1位字符或数字）
        String checkDigit = RandomUtil.randomNumbers(Integer.parseInt("1"));

        // 生成最终的承运商编号
        return "CYS-" + year + "-" + month + "-" + orderNoStr + "-" + checkDigit;
    }

}