package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsDeliveryFailedProofEntity;
import com.jygjexp.jynx.tms.service.TmsDeliveryFailedProofService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 派送失败图片记录
 *
 * <AUTHOR>
 * @date 2025-06-09 19:42:15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsDeliveryFailedProof" )
@Tag(description = "tmsDeliveryFailedProof" , name = "派送失败图片记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsDeliveryFailedProofController {

    private final  TmsDeliveryFailedProofService tmsDeliveryFailedProofService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsDeliveryFailedProof 派送失败图片记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryFailedProof_view')" )
    public R getTmsDeliveryFailedProofPage(@ParameterObject Page page, @ParameterObject TmsDeliveryFailedProofEntity tmsDeliveryFailedProof) {
        LambdaQueryWrapper<TmsDeliveryFailedProofEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsDeliveryFailedProofService.page(page, wrapper));
    }


    /**
     * 通过id查询派送失败图片记录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryFailedProof_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsDeliveryFailedProofService.getById(id));
    }

    /**
     * 新增派送失败图片记录
     * @param tmsDeliveryFailedProof 派送失败图片记录
     * @return R
     */
    @Operation(summary = "新增派送失败图片记录" , description = "新增派送失败图片记录" )
    @SysLog("新增派送失败图片记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryFailedProof_add')" )
    public R save(@RequestBody TmsDeliveryFailedProofEntity tmsDeliveryFailedProof) {
        return R.ok(tmsDeliveryFailedProofService.save(tmsDeliveryFailedProof));
    }

    /**
     * 修改派送失败图片记录
     * @param tmsDeliveryFailedProof 派送失败图片记录
     * @return R
     */
    @Operation(summary = "修改派送失败图片记录" , description = "修改派送失败图片记录" )
    @SysLog("修改派送失败图片记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryFailedProof_edit')" )
    public R updateById(@RequestBody TmsDeliveryFailedProofEntity tmsDeliveryFailedProof) {
        return R.ok(tmsDeliveryFailedProofService.updateById(tmsDeliveryFailedProof));
    }

    /**
     * 通过id删除派送失败图片记录
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除派送失败图片记录" , description = "通过id删除派送失败图片记录" )
    @SysLog("通过id删除派送失败图片记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryFailedProof_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsDeliveryFailedProofService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsDeliveryFailedProof 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsDeliveryFailedProof_export')" )
    public List<TmsDeliveryFailedProofEntity> export(TmsDeliveryFailedProofEntity tmsDeliveryFailedProof,Long[] ids) {
        return tmsDeliveryFailedProofService.list(Wrappers.lambdaQuery(tmsDeliveryFailedProof).in(ArrayUtil.isNotEmpty(ids), TmsDeliveryFailedProofEntity::getId, ids));
    }
}