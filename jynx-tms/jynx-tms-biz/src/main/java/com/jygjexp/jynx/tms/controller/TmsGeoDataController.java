package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsGeoDataEntity;
import com.jygjexp.jynx.tms.service.TmsGeoDataService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 加拿大边界数据
 *
 * <AUTHOR>
 * @date 2025-03-23 18:39:11
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsGeoData" )
@Tag(description = "tmsGeoData" , name = "加拿大边界数据管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsGeoDataController {

    private final  TmsGeoDataService tmsGeoDataService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsGeoData 加拿大边界数据
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsGeoData_view')" )
    public R getTmsGeoDataPage(@ParameterObject Page page, @ParameterObject TmsGeoDataEntity tmsGeoData) {
        LambdaQueryWrapper<TmsGeoDataEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsGeoDataService.page(page, wrapper));
    }


    /**
     * 通过id查询加拿大边界数据
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsGeoData_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsGeoDataService.getById(id));
    }


    /**
     * 通过邮编查询加拿大边界数据
     */
    @Operation(summary = "通过邮编查询" , description = "通过邮编查询" )
    @GetMapping("/zip" )
    public R getByZip(String zip) {
        return R.ok(tmsGeoDataService.getByZip(zip));
    }

    /**
     * 新增加拿大边界数据
     * @param tmsGeoData 加拿大边界数据
     * @return R
     */
    @Operation(summary = "新增加拿大边界数据" , description = "新增加拿大边界数据" )
    @SysLog("新增加拿大边界数据" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsGeoData_add')" )
    public R save(@RequestBody TmsGeoDataEntity tmsGeoData) {
        return R.ok(tmsGeoDataService.save(tmsGeoData));
    }

    /**
     * 修改加拿大边界数据
     * @param tmsGeoData 加拿大边界数据
     * @return R
     */
    @Operation(summary = "修改加拿大边界数据" , description = "修改加拿大边界数据" )
    @SysLog("修改加拿大边界数据" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsGeoData_edit')" )
    public R updateById(@RequestBody TmsGeoDataEntity tmsGeoData) {
        return R.ok(tmsGeoDataService.updateById(tmsGeoData));
    }

    /**
     * 通过id删除加拿大边界数据
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除加拿大边界数据" , description = "通过id删除加拿大边界数据" )
    @SysLog("通过id删除加拿大边界数据" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsGeoData_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsGeoDataService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsGeoData 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsGeoData_export')" )
    public List<TmsGeoDataEntity> export(TmsGeoDataEntity tmsGeoData,Long[] ids) {
        return tmsGeoDataService.list(Wrappers.lambdaQuery(tmsGeoData).in(ArrayUtil.isNotEmpty(ids), TmsGeoDataEntity::getId, ids));
    }
}