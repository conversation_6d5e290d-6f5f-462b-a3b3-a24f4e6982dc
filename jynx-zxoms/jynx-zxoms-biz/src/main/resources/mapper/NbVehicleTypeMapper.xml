<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbVehicleTypeMapper">

    <resultMap id="nbVehicleTypeMap" type="com.jygjexp.jynx.zxoms.entity.NbVehicleTypeEntity">
        <id property="vehicleTypeId" column="vehicle_type_id"/>
        <result property="name" column="name"/>
        <result property="enName" column="en_name"/>
        <result property="priority" column="priority"/>
        <result property="isValid" column="is_valid"/>
        <result property="routificCapacity" column="routific_capacity"/>
        <result property="route4meKey" column="route4me_key"/>
    </resultMap>
</mapper>