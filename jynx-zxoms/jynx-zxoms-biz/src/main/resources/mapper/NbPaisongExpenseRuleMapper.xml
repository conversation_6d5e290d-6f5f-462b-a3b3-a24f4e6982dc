<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbPaisongExpenseRuleMapper">

    <resultMap id="nbPaisongExpenseRuleMap" type="com.jygjexp.jynx.zxoms.entity.NbPaisongExpenseRuleEntity">
        <id property="reId" column="re_id"/>
        <result property="billingWarehouse" column="billing_warehouse"/>
        <result property="status" column="status"/>
        <result property="postalId" column="postal_id"/>
        <result property="weightId" column="weight_id"/>
        <result property="remark" column="remark"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="revision" column="revision"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>