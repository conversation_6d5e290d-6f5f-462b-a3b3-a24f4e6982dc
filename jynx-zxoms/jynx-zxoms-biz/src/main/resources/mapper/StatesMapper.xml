<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.StatesMapper">

    <resultMap id="basicStatesMap" type="com.jygjexp.jynx.zxoms.entity.StatesEntity">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="countryId" column="country_id"/>
        <result property="countryCode" column="country_code"/>
        <result property="fipsCode" column="fips_code"/>
        <result property="iso2" column="iso2"/>
        <result property="type" column="type"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="flag" column="flag"/>
        <result property="wikiDataId" column="wiki_data_id"/>
        <result property="nameCn" column="name_cn"/>
        <result property="zoneId" column="zone_id"/>
    </resultMap>

    <select id="findByCountryIdAndNameOrIso2" resultMap="basicStatesMap">
        SELECT * FROM states
        WHERE country_id = #{countryId}
          AND (name = #{nameOrIso2} OR iso2 = #{nameOrIso2})
            LIMIT 1
    </select>

</mapper>