<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbTransferBatchMapper">

    <resultMap id="nbTransferBatchMap" type="com.jygjexp.jynx.zxoms.entity.NbTransferBatchEntity">
        <id property="batchId" column="batch_id"/>
        <result property="batchNo" column="batch_no"/>
        <result property="addTime" column="add_time"/>
        <result property="driverId" column="driver_id"/>
        <result property="orderTotal" column="order_total"/>
        <result property="estimatedHour" column="estimated_hour"/>
        <result property="getType" column="get_type"/>
        <result property="getTime" column="get_time"/>
        <result property="batchCode" column="batch_code"/>
        <result property="tranferDriverId" column="tranfer_driver_id"/>
        <result property="loadingTime" column="loading_time"/>
        <result property="unloadingTime" column="unloading_time"/>
        <result property="tcId" column="tc_id"/>
        <result property="optimizationProblemId" column="optimization_problem_id"/>
        <result property="routeId" column="route_id"/>
        <result property="tripDistance" column="trip_distance"/>
        <result property="plannedTotalRouteDuration" column="planned_total_route_duration"/>
        <result property="memberId" column="member_id"/>
        <result property="routeView" column="route_view"/>
        <result property="deliveryStatus" column="delivery_status"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="completedTotal" column="completed_total"/>
        <result property="scId" column="sc_id"/>
        <result property="isDelete" column="is_delete"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="ownerScId" column="owner_sc_id"/>
        <result property="isLoad" column="is_load"/>
        <result property="loadDriverId" column="load_driver_id"/>
        <result property="orderBatchId" column="order_batch_id"/>
        <result property="deleteUserId" column="delete_user_id"/>
        <result property="addTimestamp" column="add_timestamp"/>
        <result property="vehicleId" column="vehicle_id"/>
        <result property="isDriverLoad" column="is_driver_load"/>
        <result property="driverLoadTime" column="driver_load_time"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>