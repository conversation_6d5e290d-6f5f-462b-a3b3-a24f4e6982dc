<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbPdaOrderScanRuleMapper">

    <resultMap id="basicNbPdaOrderScanRuleMap" type="com.jygjexp.jynx.zxoms.entity.NbPdaOrderScanRuleEntity">
        <id property="ruleId" column="rule_id"/>
        <result property="title" column="title"/>
        <result property="descr" column="descr"/>
        <result property="placeholder" column="placeholder"/>
        <result property="icon" column="icon"/>
        <result property="fromOrderStatus" column="from_order_status"/>
        <result property="toOrderStatus" column="to_order_status"/>
        <result property="driverGroupId" column="driver_group_id"/>
        <result property="driverId" column="driver_id"/>
        <result property="addTime" column="add_time"/>
        <result property="isValid" column="is_valid"/>
        <result property="addressGather" column="address_gather"/>
        <result property="minVc" column="min_vc"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
</mapper>