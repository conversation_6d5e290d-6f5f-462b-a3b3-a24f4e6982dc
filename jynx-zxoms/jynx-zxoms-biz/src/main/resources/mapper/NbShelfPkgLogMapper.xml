<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbShelfPkgLogMapper">

    <resultMap id="nbShelfPkgLogMap" type="com.jygjexp.jynx.zxoms.entity.NbShelfPkgLogEntity">
        <id property="logId" column="log_id"/>
        <result property="logTime" column="log_time"/>
        <result property="shelfId" column="shelf_id"/>
        <result property="orderId" column="order_id"/>
        <result property="putawayCode" column="putaway_code"/>
        <result property="putawayTime" column="putaway_time"/>
        <result property="putawayDriverId" column="putaway_driver_id"/>
        <result property="pickupTime" column="pickup_time"/>
        <result property="pickupDriverId" column="pickup_driver_id"/>
        <result property="note" column="note"/>
        <result property="isSmsNotify" column="is_sms_notify"/>
        <result property="smsNotifyTime" column="sms_notify_time"/>
        <result property="unshelveStaffId" column="unshelve_staff_id"/>
        <result property="smsFailCount" column="sms_fail_count"/>
        <result property="putawayDatetime" column="putaway_datetime"/>
        <result property="pickupDatetime" column="pickup_datetime"/>
        <result property="syncJyStatus" column="sync_jy_status"/>
        <result property="syncJyTime" column="sync_jy_time"/>
        <result property="failureTimes" column="failure_times"/>
        <result property="onShelfDay" column="on_shelf_day"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="findShelfPkgLogFirstByScId" resultType="java.lang.Integer" parameterType="map">
        SELECT COUNT(1) AS count
        FROM nb_shelf_pkg_log spl
            JOIN nb_shelf s ON spl.shelf_id = s.shelf_id
        WHERE s.sc_id = #{scId}
          AND spl.putaway_datetime >= DATE_FORMAT(#{dateTime}, '%Y-%m-%d 00:00:00')
    </select>

    <select id="findShelfPkgLogFirstByTcId" resultType="java.lang.Integer" parameterType="map">
        SELECT COUNT(1) AS count
        from nb_shelf_pkg_log spl
            JOIN nb_shelf s ON spl.shelf_id = s.shelf_id
        WHERE s.tc_id = #{tcId}
          AND spl.putaway_datetime >= DATE_FORMAT(#{dateTime}, '%Y-%m-%d 00:00:00')
    </select>

</mapper>