<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbLtlPriceRuleMapper">

    <resultMap id="nbLtlPriceRuleMap" type="com.jygjexp.jynx.zxoms.entity.NbLtlPriceRuleEntity">
        <id property="ltlRuleId" column="ltl_rule_id"/>
        <result property="fromPdId" column="from_pd_id"/>
        <result property="targetPdId" column="target_pd_id"/>
        <result property="pallet1" column="pallet1"/>
        <result property="pallet2" column="pallet2"/>
        <result property="pallet3" column="pallet3"/>
        <result property="pallet4" column="pallet4"/>
        <result property="pallet5" column="pallet5"/>
        <result property="pallet6" column="pallet6"/>
        <result property="pallet7" column="pallet7"/>
        <result property="pallet8" column="pallet8"/>
        <result property="pallet9" column="pallet9"/>
        <result property="pallet10" column="pallet10"/>
        <result property="addTime" column="add_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
</mapper>