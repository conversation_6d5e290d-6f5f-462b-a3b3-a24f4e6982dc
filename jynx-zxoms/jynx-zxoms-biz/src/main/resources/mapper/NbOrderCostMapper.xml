<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbOrderCostMapper">

    <resultMap id="basicNbOrderCostMap" type="com.jygjexp.jynx.zxoms.entity.NbOrderCostEntity">
        <id property="orderId" column="order_id"/>
        <result property="driverId" column="driver_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="pkgNo" column="pkg_no"/>
        <result property="postalCode" column="postal_code"/>
        <result property="driverBase" column="driver_base"/>
        <result property="driverWeightSubsidy" column="driver_weight_subsidy"/>
        <result property="driverSubsidy" column="driver_subsidy"/>
        <result property="costStatus" column="cost_status"/>
        <result property="estimateTime" column="estimate_time"/>
        <result property="settleTime" column="settle_time"/>
        <result property="pkgWeight" column="pkg_weight"/>
        <result property="pkgVolumeWeight" column="pkg_volume_weight"/>
        <result property="note" column="note"/>
        <result property="costRuleItemId" column="cost_rule_item_id"/>
        <result property="estimateTimestamp" column="estimate_timestamp"/>
        <result property="settleTimestamp" column="settle_timestamp"/>
        <result property="addTime" column="add_time"/>
        <result property="scId" column="sc_id"/>
        <result property="tcId" column="tc_id"/>
        <result property="parcelScannedTime" column="parcel_scanned_time"/>
        <result property="pDriverId" column="p_driver_id"/>
        <result property="subsidyLogId" column="subsidy_log_id"/>
    </resultMap>

</mapper>