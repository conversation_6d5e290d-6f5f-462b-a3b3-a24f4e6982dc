<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbSmsTemplateMapper">

    <resultMap id="basicNbSmsTemplateMap" type="com.jygjexp.jynx.zxoms.entity.NbSmsTemplateEntity">
        <id property="templateId" column="template_id"/>
        <result property="title" column="title"/>
        <result property="triggerCondition" column="trigger_condition"/>
        <result property="content" column="content"/>
        <result property="description" column="description"/>
        <result property="isValid" column="is_valid"/>
        <result property="tplKey" column="tpl_key"/>
        <result property="tplType" column="tpl_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>