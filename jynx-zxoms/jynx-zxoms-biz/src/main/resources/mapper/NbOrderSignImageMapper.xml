<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbOrderSignImageMapper">

    <resultMap id="nbOrderSignImageMap" type="com.jygjexp.jynx.zxoms.entity.NbOrderSignImageEntity">
        <id property="imageId" column="image_id"/>
        <result property="orderId" column="order_id"/>
        <result property="pkgImage" column="pkg_image"/>
        <result property="putImage" column="put_image"/>
        <result property="addTime" column="add_time"/>
        <result property="oriPkgImage" column="ori_pkg_image"/>
        <result property="oriPutImage" column="ori_put_image"/>
        <result property="driverId" column="driver_id"/>
        <result property="staffId" column="staff_id"/>
        <result property="orderStatus" column="order_status"/>
        <result property="deliveryStatus" column="delivery_status"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="r4mUploadId" column="r4m_upload_id"/>
        <result property="fileId" column="file_id"/>
        <result property="distance" column="distance"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <resultMap id="NbOrderSignImageResultMap" type="com.jygjexp.jynx.zxoms.nbapp.vo.ApiOrderVo">
        <result property="images" column="images" />
        <result property="images2" column="images2" />
        <result property="orderId" column="order_id" />
    </resultMap>

    <select id="findImagesByOrderIds" resultMap="NbOrderSignImageResultMap">
        SELECT
        GROUP_CONCAT(pkg_image) AS images,
        GROUP_CONCAT(put_image) AS images2,
        order_id
        FROM nb_order_sign_image
        WHERE order_id IN
        <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        GROUP BY order_id
    </select>

</mapper>