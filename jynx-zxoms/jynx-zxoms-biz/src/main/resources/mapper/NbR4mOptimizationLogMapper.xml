<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbR4mOptimizationLogMapper">

    <resultMap id="nbR4mOptimizationLogMap" type="com.jygjexp.jynx.zxoms.entity.NbR4mOptimizationLogEntity">
        <id property="logId" column="log_id"/>
        <result property="orderBatchId" column="order_batch_id"/>
        <result property="tcId" column="tc_id"/>
        <result property="orderIds" column="order_ids"/>
        <result property="driverIds" column="driver_ids"/>
        <result property="algorithmType" column="algorithm_type"/>
        <result property="optimize" column="optimize"/>
        <result property="minTourSize" column="min_tour_size"/>
        <result property="isRt" column="is_rt"/>
        <result property="balance" column="balance"/>
        <result property="routeMaxDuration" column="route_max_duration"/>
        <result property="optimizationTime" column="optimization_time"/>
        <result property="isAuto" column="is_auto"/>
        <result property="adminId" column="admin_id"/>
        <result property="routedErrmsg" column="routed_errmsg"/>
        <result property="optimiationProblemId" column="optimiation_problem_id"/>
        <result property="viewUrl" column="view_url"/>
        <result property="r4mState" column="r4m_state"/>
        <result property="scId" column="sc_id"/>
        <result property="maxTourSize" column="max_tour_size"/>
    </resultMap>
</mapper>