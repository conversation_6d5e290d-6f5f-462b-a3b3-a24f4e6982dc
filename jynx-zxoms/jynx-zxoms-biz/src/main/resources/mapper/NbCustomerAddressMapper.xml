<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbCustomerAddressMapper">

    <resultMap id="nbCustomerAddressMap" type="com.jygjexp.jynx.zxoms.entity.NbCustomerAddressEntity">
        <id property="addressId" column="address_id"/>
        <result property="name" column="name"/>
        <result property="tel" column="tel"/>
        <result property="country" column="country"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="postalCode" column="postal_code"/>
        <result property="address1" column="address1"/>
        <result property="address2" column="address2"/>
        <result property="address3" column="address3"/>
        <result property="addressType" column="address_type"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
</mapper>