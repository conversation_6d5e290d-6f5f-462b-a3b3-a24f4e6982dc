<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbOrderPathBakMapper">

    <resultMap id="nbOrderPathBakMap" type="com.jygjexp.jynx.zxoms.entity.NbOrderPathBakEntity">
        <id property="pathId" column="path_id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderStatus" column="order_status"/>
        <result property="addTime" column="add_time"/>
        <result property="scId" column="sc_id"/>
        <result property="tcId" column="tc_id"/>
        <result property="driverId" column="driver_id"/>
        <result property="staffId" column="staff_id"/>
        <result property="pathAddr" column="path_addr"/>
        <result property="scanLat" column="scan_lat"/>
        <result property="scanLng" column="scan_lng"/>
        <result property="remark" column="remark"/>
        <result property="city" column="city"/>
        <result property="addTimestamp" column="add_timestamp"/>
        <result property="pathTimezone" column="path_timezone"/>
        <result property="syncJyStatus" column="sync_jy_status"/>
        <result property="syncTime" column="sync_time"/>
        <result property="smsStatus" column="sms_status"/>
        <result property="smsDate" column="sms_date"/>
        <result property="distance" column="distance"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteUid" column="delete_uid"/>
    </resultMap>
</mapper>