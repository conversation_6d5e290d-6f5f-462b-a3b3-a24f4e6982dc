<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbR4mRouteCreateLogMapper">

    <resultMap id="nbR4mRouteCreateLogMap" type="com.jygjexp.jynx.zxoms.entity.NbR4mRouteCreateLogEntity">
        <id property="id" column="id"/>
        <result property="routeId" column="route_id"/>
        <result property="addTime" column="add_time"/>
        <result property="status" column="status"/>
        <result property="synbTime" column="synb_time"/>
        <result property="transferBatchId" column="transfer_batch_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
</mapper>