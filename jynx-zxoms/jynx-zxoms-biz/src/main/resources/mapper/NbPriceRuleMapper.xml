<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbPriceRuleMapper">

    <resultMap id="nbPriceRuleMap" type="com.jygjexp.jynx.zxoms.entity.NbPriceRuleEntity">
        <id property="ruleId" column="rule_id"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="isInternational" column="is_international"/>
        <result property="weightKg" column="weight_kg"/>
        <result property="weightLb" column="weight_lb"/>
        <result property="targetPdId" column="target_pd_id"/>
        <result property="priceNornal" column="price_nornal"/>
        <result property="priceExpress" column="price_express"/>
        <result property="priceMulti" column="price_multi"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="fromPdId" column="from_pd_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
</mapper>