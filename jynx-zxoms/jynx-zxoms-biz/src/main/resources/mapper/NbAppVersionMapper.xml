<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbAppVersionMapper">

    <resultMap id="nbAppVersionMap" type="com.jygjexp.jynx.zxoms.entity.NbAppVersionEntity">
        <id property="appId" column="app_id"/>
        <result property="versionName" column="version_name"/>
        <result property="createDate" column="create_date"/>
        <result property="isFocus" column="is_focus"/>
        <result property="appDesc" column="app_desc"/>
        <result property="downloadUrl" column="download_url"/>
        <result property="isValid" column="is_valid"/>
        <result property="versionCode" column="version_code"/>
        <result property="isGaryRelease" column="is_gary_release"/>
        <result property="deviceType" column="device_type"/>
        <result property="appType" column="app_type"/>
    </resultMap>
</mapper>