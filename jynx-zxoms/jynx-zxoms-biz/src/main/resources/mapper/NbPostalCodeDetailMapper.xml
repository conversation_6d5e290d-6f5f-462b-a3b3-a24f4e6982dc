<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbPostalCodeDetailMapper">

  <resultMap id="nbPostalCodeDetailMap" type="com.jygjexp.jynx.zxoms.entity.NbPostalCodeDetailEntity">
        <id property="id" column="id"/>
        <result property="partitionCode" column="partition_code"/>
        <result property="type" column="type"/>
        <result property="startPostalCode" column="start_postal_code"/>
        <result property="endPostalCode" column="end_postal_code"/>
        <result property="groupId" column="group_id"/>
        <result property="revision" column="revision"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
  </resultMap>
</mapper>