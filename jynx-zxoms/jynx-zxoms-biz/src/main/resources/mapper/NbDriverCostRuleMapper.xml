<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbDriverCostRuleMapper">

    <resultMap id="basicNbDriverCostRuleMap" type="com.jygjexp.jynx.zxoms.entity.NbDriverCostRuleEntity">
        <id property="ruleId" column="rule_id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="isValid" column="is_valid"/>
        <result property="addTime" column="add_time"/>
        <result property="createAdminId" column="create_admin_id"/>
        <result property="coverPostalCode" column="cover_postal_code"/>
        <result property="driverId" column="driver_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="findCostRuleByIdAndBeforePostalCode" resultMap="basicNbDriverCostRuleMap">
        SELECT * FROM nb_driver_cost_rule
        WHERE driver_id = #{ruleDriverId}
          AND is_valid = true
          AND cover_postal_code LIKE CONCAT('%', #{beforePostalCode}, '%')
            LIMIT 1
    </select>

</mapper>