<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbDriverCostRuleItemMapper">

    <resultMap id="nbDriverCostRuleItemMap" type="com.jygjexp.jynx.zxoms.entity.NbDriverCostRuleItemEntity">
        <id property="itemId" column="item_id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="startKg" column="start_kg"/>
        <result property="startVw" column="start_vw"/>
        <result property="payment" column="payment"/>
        <result property="subsidy" column="subsidy"/>
        <result property="addTime" column="add_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getRuleItemByIdAndStartKg" resultMap="nbDriverCostRuleItemMap">
        select * from nb_driver_cost_rule_item
        where rule_id = #{ruleId}
        and start_kg &lt;= #{useWeight}
        order by start_kg desc
        limit 1
    </select>
</mapper>