<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbModifyRecordMapper">

    <resultMap id="nbModifyRecordMap" type="com.jygjexp.jynx.zxoms.entity.NbModifyRecordEntity">
        <id property="recordId" column="record_id"/>
        <result property="addTime" column="add_time"/>
        <result property="tableName" column="table_name"/>
        <result property="primaryId" column="primary_id"/>
        <result property="fromValue" column="from_value"/>
        <result property="toValue" column="to_value"/>
        <result property="description" column="description"/>
        <result property="modifyOperator" column="modify_operator"/>
        <result property="adminId" column="admin_id"/>
        <result property="driverId" column="driver_id"/>
    </resultMap>
</mapper>