<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbRoute4meActivityMapper">

    <resultMap id="nbRoute4meActivityMap" type="com.jygjexp.jynx.zxoms.entity.NbRoute4meActivityEntity">
        <id property="id" column="id"/>
        <result property="logTime" column="log_time"/>
        <result property="logData" column="log_data"/>
        <result property="activityType" column="activity_type"/>
        <result property="orderId" column="order_id"/>
        <result property="activityId" column="activity_id"/>
        <result property="routeId" column="route_id"/>
    </resultMap>
</mapper>