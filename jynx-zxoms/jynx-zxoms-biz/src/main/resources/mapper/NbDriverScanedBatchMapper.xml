<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbDriverScanedBatchMapper">

    <resultMap id="nbDriverScanedBatchMap" type="com.jygjexp.jynx.zxoms.entity.NbDriverScanedBatchEntity">
        <id property="batchId" column="batch_id"/>
        <result property="driverId" column="driver_id"/>
        <result property="orderTotal" column="order_total"/>
        <result property="shouldTotal" column="should_total"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
</mapper>