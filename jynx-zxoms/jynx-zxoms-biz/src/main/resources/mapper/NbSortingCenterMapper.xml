<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbSortingCenterMapper">

    <resultMap id="nbSortingCenterMap" type="com.jygjexp.jynx.zxoms.entity.NbSortingCenterEntity">
        <id property="scId" column="sc_id"/>
        <result property="centerName" column="center_name"/>
        <result property="countryId" column="country_id"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="isValid" column="is_valid"/>
        <result property="address" column="address"/>
        <result property="postalCode" column="postal_code"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="scCode" column="sc_code"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="r4mMemberId" column="r4m_member_id"/>
        <result property="scTimezone" column="sc_timezone"/>
        <result property="selfPickupMaxStorage" column="self_pickup_max_storage"/>
        <result property="businessHours" column="business_hours"/>
        <result property="serviceTel" column="service_tel"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="findFirstSc" resultMap="nbSortingCenterMap">
        select sc.*
        from nb_sorting_center sc, nb_transfer_center tc
        where sc.sc_id = tc.sc_id
        and tc.tc_id = #{tcId}
    </select>

    <select id="findScByUserId" resultMap="nbSortingCenterMap">
        select sc.*
        from nb_sorting_center sc,
             nb_user_sc_rel r
        where sc.sc_id = r.sc_id
          and r.user_id = #{userId}
    </select>

</mapper>