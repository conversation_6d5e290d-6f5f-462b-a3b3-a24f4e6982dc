<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbMerchantMapper">

    <resultMap id="basicNbMerchantMap" type="com.jygjexp.jynx.zxoms.entity.NbMerchantEntity">
        <id property="merchantId" column="merchant_id"/>
        <result property="name" column="name"/>
        <result property="apiSecret" column="api_secret"/>
        <result property="isValid" column="is_valid"/>
        <result property="isMultiDelivery" column="is_multi_delivery"/>
        <result property="merchantCode" column="merchant_code"/>
        <result property="defaultOrderStatus" column="default_order_status"/>
        <result property="discount" column="discount"/>
        <result property="chargeModel" column="charge_model"/>
        <result property="multiMinWeightKg" column="multi_min_weight_kg"/>
        <result property="multiMinWeightLb" column="multi_min_weight_lb"/>
        <result property="multiMinWeight" column="multi_min_weight"/>
        <result property="defaultTimezone" column="default_timezone"/>
        <result property="funType" column="fun_type"/>
        <result property="defaultAddress" column="default_address"/>
        <result property="pickUpService" column="pick_up_service"/>
        <result property="whCountryId" column="wh_country_id"/>
        <result property="whProvinceId" column="wh_province_id"/>
        <result property="whCityId" column="wh_city_id"/>
        <result property="whAddress" column="wh_address"/>
        <result property="whPostalCode" column="wh_postal_code"/>
        <result property="whContact" column="wh_contact"/>
        <result property="whTel" column="wh_tel"/>
        <result property="whBizHour" column="wh_biz_hour"/>
        <result property="whLat" column="wh_lat"/>
        <result property="whLng" column="wh_lng"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
</mapper>