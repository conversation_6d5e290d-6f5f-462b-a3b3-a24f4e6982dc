<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbOrderMapper">

    <resultMap id="nbOrderMap" type="com.jygjexp.jynx.zxoms.entity.NbOrderEntity">
        <id property="orderId" column="order_id"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="pDriverId" column="p_driver_id"/>
        <result property="driverId" column="driver_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="scId" column="sc_id"/>
        <result property="tcId" column="tc_id"/>
        <result property="regionId" column="region_id"/>
        <result property="regionCode" column="region_code"/>
        <result property="contactTime" column="contact_time"/>
        <result property="orderCategory" column="order_category"/>
        <result property="smsTime" column="sms_time"/>
        <result property="orderType" column="order_type"/>
        <result property="expressCategory" column="express_category"/>
        <result property="failureReason" column="failure_reason"/>
        <result property="transportWay" column="transport_way"/>
        <result property="requiredSign" column="required_sign"/>
        <result property="lastRouteTime" column="last_route_time"/>
        <result property="addTime" column="add_time"/>
        <result property="batchNo" column="batch_no"/>
        <result property="subBatchNo" column="sub_batch_no"/>
        <result property="customerOrderNo" column="customer_order_no"/>
        <result property="customerPOrderNo" column="customer_p_order_no"/>
        <result property="goodsDesc" column="goods_desc"/>
        <result property="destName" column="dest_name"/>
        <result property="destTel" column="dest_tel"/>
        <result property="destEmail" column="dest_email"/>
        <result property="destCountry" column="dest_country"/>
        <result property="destProvince" column="dest_province"/>
        <result property="destCity" column="dest_city"/>
        <result property="destAddress1" column="dest_address1"/>
        <result property="destAddress2" column="dest_address2"/>
        <result property="destAddress3" column="dest_address3"/>
        <result property="destUnitNo" column="dest_unit_no"/>
        <result property="destPostalCode" column="dest_postal_code"/>
        <result property="destLat" column="dest_lat"/>
        <result property="destLng" column="dest_lng"/>
        <result property="destAddressType" column="dest_address_type"/>
        <result property="driverRemark" column="driver_remark"/>
        <result property="orderRemark" column="order_remark"/>
        <result property="pkgWeight" column="pkg_weight"/>
        <result property="pkgLength" column="pkg_length"/>
        <result property="pkgWidth" column="pkg_width"/>
        <result property="pkgHeight" column="pkg_height"/>
        <result property="pkgValue" column="pkg_value"/>
        <result property="pkgNo" column="pkg_no"/>
        <result property="pkgType" column="pkg_type"/>
        <result property="isIntercept" column="is_intercept"/>
        <result property="interceptUserId" column="intercept_user_id"/>
        <result property="interceptBatchId" column="intercept_batch_id"/>
        <result property="interceptRemark" column="intercept_remark"/>
        <result property="boxNo" column="box_no"/>
        <result property="finishedTime" column="finished_time"/>
        <result property="deliveryStatus" column="delivery_status"/>
        <result property="orderStatus" column="order_status"/>
        <result property="packId" column="pack_id"/>
        <result property="doorAccessPwd" column="door_access_pwd"/>
        <result property="shipperName" column="shipper_name"/>
        <result property="shipperTel" column="shipper_tel"/>
        <result property="shipperCountry" column="shipper_country"/>
        <result property="shipperProvince" column="shipper_province"/>
        <result property="shipperCity" column="shipper_city"/>
        <result property="shipperAddress1" column="shipper_address1"/>
        <result property="shipperAddress2" column="shipper_address2"/>
        <result property="shipperAddress3" column="shipper_address3"/>
        <result property="shipperPostalCode" column="shipper_postal_code"/>
        <result property="startDeliveryTime" column="start_delivery_time"/>
        <result property="deliveryTry" column="delivery_try"/>
        <result property="deliveryType" column="delivery_type"/>
        <result property="latestDeliveryTime" column="latest_delivery_time"/>
        <result property="deliveryedTime" column="deliveryed_time"/>
        <result property="pickNo" column="pick_no"/>
        <result property="driverPickupTime" column="driver_pickup_time"/>
        <result property="sysStatus" column="sys_status"/>
        <result property="geoTimes" column="geo_times"/>
        <result property="tcErr" column="tc_err"/>
        <result property="isRouted" column="is_routed"/>
        <result property="expressType" column="express_type"/>
        <result property="dangerType" column="danger_type"/>
        <result property="transportType" column="transport_type"/>
        <result property="failedDeliveryAction" column="failed_delivery_action"/>
        <result property="bagNumber" column="bag_number"/>
        <result property="isMps" column="is_mps"/>
        <result property="pOrderId" column="p_order_id"/>
        <result property="hasSubOrder" column="has_sub_order"/>
        <result property="r4mOrderId" column="r4m_order_id"/>
        <result property="subChannel" column="sub_channel"/>
        <result property="eta" column="eta"/>
        <result property="etd" column="etd"/>
        <result property="ata" column="ata"/>
        <result property="atd" column="atd"/>
        <result property="jyOrderNo" column="jy_order_no"/>
        <result property="jyOrderId" column="jy_order_id"/>
        <result property="jyMarkNo" column="jy_mark_no"/>
        <result property="funType" column="fun_type"/>
        <result property="uniDelivered" column="uni_delivered"/>
        <result property="uniDeliveredTime" column="uni_delivered_time"/>
        <result property="thirdLabel" column="third_label"/>
        <result property="isPrinted" column="is_printed"/>
        <result property="inNbRange" column="in_nb_range"/>
        <result property="tag" column="tag"/>
        <result property="orderCost" column="order_cost"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="carrierId" column="carrier_id"/>
    </resultMap>

    <select id="findCustomerOrderNosByMidAndCustomerNo" resultType="java.lang.String">
        SELECT GROUP_CONCAT(customer_order_no) AS customer_order_nos
        FROM nb_order
        WHERE merchant_id = #{mid}
        AND customer_order_no IN
        <foreach item="item" index="index" collection="customerNoArr" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findInterval3DayByFunType2" resultMap="nbOrderMap">
        select o.* from nb_order o, nb_merchant m
        where o.merchant_id = m.merchant_id
          and m.fun_type = ?
          and o.add_time > CURDATE() - INTERVAL 3 DAY
          and o.jy_order_no is null
            limit 100
    </select>

    <select id="findTboByBatchIdAndStatus" resultMap="nbOrderMap">
        select o.*
        from nb_transfer_batch_order tbo, nb_order o
        where tbo.order_id = o.order_id
          and tbo.batch_id = #{batchId}
          and o.order_status > #{orderStatus200ParcelScanned}
            limit 1
    </select>
    <select id="findOrderByBatchId" resultType="com.jygjexp.jynx.zxoms.vo.OrderVo">
        SELECT IFNULL(SUM(IF(o.driver_id = 0, 0, 1)), 0) AS orderTotal
        FROM nb_order o
        JOIN nb_order_batch ob ON o.batch_no = ob.batch_no
        WHERE ob.batch_id IN
        <foreach item="batchId" collection="batchIdList" open="(" separator="," close=")">
            #{batchId}
        </foreach>
    </select>

    <select id="findOrderByTransferBatchId" resultMap="nbOrderMap">
        select o.*
        from nb_transfer_batch_order tbo, nb_order o
        where tbo.order_id = o.order_id
          and tbo.batch_id = #{batchId}
    </select>

    <select id="getPkgNoList"  resultType="com.jygjexp.jynx.zxoms.vo.NbOrderTransferBatchOrderVo">
        select c.center_name,t.pick_no,o.* from nb_transfer_center c
            left join nb_order o on c.tc_id = o.tc_id
            left join nb_transfer_batch_order t on o.order_id = t.order_id
        where  o.pkg_no=#{pkg_no}
    </select>


</mapper>