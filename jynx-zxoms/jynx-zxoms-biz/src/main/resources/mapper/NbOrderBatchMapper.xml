<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbOrderBatchMapper">

    <resultMap id="nNbOrderBatchMap" type="com.jygjexp.jynx.zxoms.entity.NbOrderBatchEntity">
        <id property="batchId" column="batch_id"/>
        <result property="batchNo" column="batch_no"/>
        <result property="orderTotal" column="order_total"/>
        <result property="note" column="note"/>
        <result property="isValid" column="is_valid"/>
        <result property="routedTime" column="routed_time"/>
        <result property="batchTotal" column="batch_total"/>
        <result property="isRouted" column="is_routed"/>
        <result property="routedErrmsg" column="routed_errmsg"/>
        <result property="optimizationProblemId" column="optimization_problem_id"/>
        <result property="batchView" column="batch_view"/>
        <result property="isMain" column="is_main"/>
        <result property="pBatchId" column="p_batch_id"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="isScaning" column="is_scaning"/>
        <result property="scId" column="sc_id"/>
        <result property="impTotal" column="imp_total"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="batchType" column="batch_type"/>
        <result property="isSyncR4mOrder" column="is_sync_r4m_order"/>
        <result property="syncR4mOrderTime" column="sync_r4m_order_time"/>
        <result property="syncR4mOrderStatus" column="sync_r4m_order_status"/>
        <result property="eta" column="eta"/>
        <result property="etd" column="etd"/>
        <result property="ata" column="ata"/>
        <result property="atd" column="atd"/>
        <result property="isLtl" column="is_ltl"/>
        <result property="syncOrderTotal" column="sync_order_total"/>
        <result property="isHeavyCargo" column="is_heavy_cargo"/>
        <result property="createTime" column="create_time"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

<!--    <select id="findNoRouted" parameterType="string" resultMap="nNbOrderBatchMap">-->
<!--        SELECT a.*-->
<!--        FROM nb_order_batch a-->
<!--        LEFT JOIN nb_order b-->
<!--        ON a.batch_no = b.sub_batch_no-->
<!--        WHERE a.is_main=0-->
<!--        AND a.batch_total = a.order_total-->
<!--        AND a.is_routed = 0-->
<!--        AND b.r4m_order_id != 0-->
<!--        AND a.batch_no = #{batchNo}-->
<!--        UNION-->
<!--        SELECT a.*-->
<!--        FROM nb_order_batch a-->
<!--        LEFT JOIN nb_order b-->
<!--        ON a.batch_no = b.batch_no-->
<!--        WHERE a.is_main !=0-->
<!--        AND a.batch_total = a.order_total-->
<!--        AND a.is_routed = 0-->
<!--        AND b.r4m_order_id != 0-->
<!--        AND a.batch_no =  #{batchNo}-->
<!--    </select>-->
</mapper>