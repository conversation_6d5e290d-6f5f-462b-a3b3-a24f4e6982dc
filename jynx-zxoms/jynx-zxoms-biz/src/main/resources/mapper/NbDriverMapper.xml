<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbDriverMapper">

    <resultMap id="nbDriverMap" type="com.jygjexp.jynx.zxoms.entity.NbDriverEntity">
        <id property="driverId" column="driver_id"/>
        <result property="country" column="country"/>
        <result property="mobile" column="mobile"/>
        <result property="password" column="password"/>
        <result property="settlePassword" column="settle_password"/>
        <result property="firstName" column="first_name"/>
        <result property="middleName" column="middle_name"/>
        <result property="lastName" column="last_name"/>
        <result property="driverName" column="driver_name"/>
        <result property="email" column="email"/>
        <result property="address" column="address"/>
        <result property="countryId" column="country_id"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="postalCode" column="postal_code"/>
        <result property="vehicleTypeId" column="vehicle_type_id"/>
        <result property="plateNumber" column="plate_number"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditTime" column="audit_time"/>
        <result property="workTypeId" column="work_type_id"/>
        <result property="isTransfer" column="is_transfer"/>
        <result property="regTime" column="reg_time"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="lastLoginIp" column="last_login_ip"/>
        <result property="sessionId" column="session_id"/>
        <result property="workHour" column="work_hour"/>
        <result property="sortingCenterScan" column="sorting_center_scan"/>
        <result property="tcId" column="tc_id"/>
        <result property="endLocLat" column="end_loc_lat"/>
        <result property="endLocLng" column="end_loc_lng"/>
        <result property="useEndLoc" column="use_end_loc"/>
        <result property="autoRoutePlanning" column="auto_route_planning"/>
        <result property="route4meVehicleId" column="route4me_vehicle_id"/>
        <result property="route4meMemberId" column="route4me_member_id"/>
        <result property="isValid" column="is_valid"/>
        <result property="birthday" column="birthday"/>
        <result property="sin" column="sin"/>
        <result property="emergencyContact" column="emergency_contact"/>
        <result property="emergencyContactTel" column="emergency_contact_tel"/>
        <result property="insurancePolicy" column="insurance_policy"/>
        <result property="vehicleRegDoc" column="vehicle_reg_doc"/>
        <result property="drivingLicenseFront" column="driving_license_front"/>
        <result property="drivingLicenseBack" column="driving_license_back"/>
        <result property="carInsurance" column="car_insurance"/>
        <result property="sinPic" column="sin_pic"/>
        <result property="vcOrDdf" column="vc_or_ddf"/>
        <result property="loadCapacity" column="load_capacity"/>
        <result property="contractType" column="contract_type"/>
        <result property="contractExpire" column="contract_expire"/>
        <result property="scId" column="sc_id"/>
        <result property="regStep" column="reg_step"/>
        <result property="licenseNumber" column="license_number"/>
        <result property="licenseNumberExpire" column="license_number_expire"/>
        <result property="socialNumber" column="social_number"/>
        <result property="refuseReason" column="refuse_reason"/>
        <result property="r4mVehicleMake" column="r4m_vehicle_make"/>
        <result property="r4mVehicleType" column="r4m_vehicle_type"/>
        <result property="r4mFuelType" column="r4m_fuel_type"/>
        <result property="r4mMaxVolume" column="r4m_max_volume"/>
        <result property="r4mMaxItems" column="r4m_max_items"/>
        <result property="r4mMaxWeight" column="r4m_max_weight"/>
        <result property="r4mVehicleCapacityProfileId" column="r4m_vehicle_capacity_profile_id"/>
        <result property="remark" column="remark"/>
        <result property="isLeader" column="is_leader"/>
        <result property="pDriverId" column="p_driver_id"/>
        <result property="addType" column="add_type"/>
        <result property="businessType" column="business_type"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

</mapper>