<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.zxoms.send.mapper.NbPreAlertBatchMapper">

    <resultMap id="nbPreAlertBatchMap" type="com.jygjexp.jynx.zxoms.entity.NbPreAlertBatchEntity">
        <id property="id" column="id"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="addTime" column="add_time"/>
        <result property="masterAirWaybill" column="master_air_waybill"/>
        <result property="realDepartureTime" column="real_departure_time"/>
        <result property="realArrivalTime" column="real_arrival_time"/>
        <result property="airportPickupTime" column="airport_pickup_time"/>
        <result property="description" column="description"/>
        <result property="originalAirport" column="original_airport"/>
        <result property="destAirport" column="dest_airport"/>
        <result property="flightNo" column="flight_no"/>
        <result property="eta" column="eta"/>
        <result property="etaDate" column="eta_date"/>
        <result property="etd" column="etd"/>
        <result property="trackingInfo" column="tracking_info"/>
        <result property="totalItems" column="total_items"/>
        <result property="weight" column="weight"/>
        <result property="weightUnit" column="weight_unit"/>
        <result property="totalBags" column="total_bags"/>
        <result property="partnerName" column="partner_name"/>
        <result property="partnerId" column="partner_id"/>
        <result property="expressType" column="express_type"/>
        <result property="isCancel" column="is_cancel"/>
        <result property="cancelTime" column="cancel_time"/>
    </resultMap>
</mapper>