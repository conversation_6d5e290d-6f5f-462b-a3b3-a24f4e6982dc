error.database.access=System access is busy, please try again later.
error.database=System access is busy, please try again later.
error.invalid.argument=Request parameter is invalid: {0}.
error.service.unavailable=Service unavailable, please try again later: {0}.
error.unauthorized=Unauthorized, please provide valid authentication information.
error.access.denied=Access denied, insufficient permissions.
error.method.not.allowed=Request method not allowed.
error.internal.server=The server is busy. Please try again later.
error.database.insert.error=Operation failed, please check：{0}.

nborder.upload.valid.file=Please upload a valid Excel file!
nborder.upload.empty.data=There is no valid data in the Excel file
nborder.no.valid.data.rows=There are no valid order data rows
nborder.transfercenter.does.not.exist=The transfer center does not exist: {0}
nborder.merchant.does.not.exist=The merchant does not exist: {0}
nborder.duplicate.customer.tracking.number=Duplicate customer tracking number: {0}
nborder.Duplicate.package.number=Duplicate package number: {0}
nborder.add.failed=Failed to add,line: {0}.
nborder.file.processing.errors=Order import processing failed, please check the error message: {0}
nborder.Duplicate.customer.tracking.number.continue.uploading=TIP: Duplicate customer tracking number, do you want to continue uploading: {0}
nborder.delivery.note.Excel.file.processing.success=The delivery note Excel file has been processed successfully, with a total of {0} orders processed
nborder.delivery.note.Excel.file.processing.exception=Delivery note Excel file processing exception: {0}
nborder.no.repeat.create=This order has already created a sub order and cannot be further created
nborder.order.successfully.delivered=The order has been successfully delivered
nborder.please.select.the.orderId=Please select the orderId
nborder.order.does.not.exist=Order does not exist: {0}
nborder.suborder.cannot.create.suborder=Sub orders cannot be created to create new sub orders

nbdriver.created.failed=Failed to create NBDriver: {0}.
nbdriver.vehicle.created.failed=Failed to create Route4me vehicle.error memberId: {0}.
nbdriver.audit.passed=Driver Audit passed.
nbdriver.business.type.is.incorrect=The business type is incorrect, please check!
nbdriver.mobile.unique.cannot.be.duplicated=The mobile number : {0} is already occupied in this system, please check!
nbdriver.msg.rejected=The message has been rejected.
nbdriver.success.under.review=Created successfully, under review
nbdriver.status.inoperable=The driver's status is inoperable
nbdriver.already.approved.for.review=The driver has already been approved for review
nbdriver.repeat.operation=Repeat operation
nbdriver.invalid.state=The driver is in an invalid state
nbdriver.valid.state=The driver is in a valid state
nbdriver.already.synchronized.no.modify.email=Already synchronized to the account of route4me, unable to modify email
nbdriver.email.no.empty=Email cannot be empty
nbdriver.mobile.no.empty=The phone number cannot be empty
nbdriver.leader.does.not.have.id=The input Leader does not have an id: {0}
nbdriver.leader.is.not.the.leader=The driver entered is not the leader: {0}
nbdriver.email.is.already.occupied=The Email is already occupied: {0}
nbdriver.mobile.is.already.occupied=The phone number is already occupied: {0}
nbdriver.error.synchronizing.to.route4me=Error synchronizing to Route4me: {0}
nbdriver.create.failed=Failed to create Driver
nbdriver.create.successfully=Driver created successfully
nbdriver.invalid.state.reason=The driver's delivery package is on the way, the delivery of failed packages, can be set as invalid

nborderBatch.successfully.updated.orders=Successfully updated orders numbers: {0}
nborderBatch.only.main.batch.creating.sub.batches=Only the main batch supports creating sub batches
nborderBatch.batch.does.not.exist=Batch does not exist
nborderBatch.planned.already=The batch has already been planned
nborderBatch.no.orders.available.for.planning=No orders available for planning
nborderBatch.an.order.cannot.be.planned=An order cannot be planned
nborderBatch.no.assigned.transfer.center.no.operation=We have found orders that have not been assigned a transfer center, and we are unable to proceed with this operation: {0}
nborderBatch.transfer.center.no.exist.at.the.beginning=The transfer center does not exist at the beginning
nborderBatch.sorting.center.no.exist.at.the.beginning=The sorting center does not exist at the beginning
nborderBatch.at.least.one.sortingcenter.or.transfercenter=Select at least one sorting center or transfer center
nborderBatch.transfercenter.no.exist=The transfer center does not exist
nborderBatch.batch.number.already.exists=Batch number already exists: {0}
nborderBatch.status.cannot.be.transferred=Orders in PARCEL_SCANNED status cannot be transferred
nborderBatch.Only.manually.created.orders.can.be.transferred=Only manually created orders can be transferred
nborderBatch.batch.consistency.no.need.to.transfer=The current batch of the order is consistent with the specified batch and does not need to be transferred
nborderBatch.no.batch.selected=No batch selected
nborderBatch.This.batch.has.already.been.synchronized=This batch has already been synchronized: {0}
nborderBatch.is.being.synchronized.not.open.it.again=This batch is being synchronized, please do not open it again: {0}
nborderBatch.only.main.batch.operation=nborderBatch.only.main.batch.operation
nborderBatch.no.modifiable.orders.within.the.batch=There are no modifiable orders within the batch
nborderBatch.five=-5,{0}

nbshelf.package.has.been.taken.off.the.shelves=The package has been taken off the shelves: {0}
nbshelf.the.generated.shelf.code.already.exists=The generated shelf code already exists: {0}

nbsortingCenter.invalid.code=无Invalid code
nbsortingCenter.the.shelf.does.not.exist=The shelf does not exist: {0}
nbsortingCenter.the.shelf.has.been.set.and.is.invalid=The shelf has been set and is invalid: {0}
nbsortingCenter.the.shelves.are.not.under.your.jurisdiction=The shelves are not under your jurisdiction: {0}
nbsortingCenter.the.package.does.not.exist=The package does not exist: {0}
nbsortingCenter.the.package.has.been.shelved=The package has been shelved: {0}
nbsortingCenter.only.orders.that.have.failed.delivery.can.be.listed=Only orders that have failed delivery can be listed

nbTransferCenter.cannot.contain.Chinese.commas=Cannot contain Chinese commas
nbTransferCenter.postal.code.is.incorrect=The length of the first three characters in the postal code is incorrect
nbTransferCenter.postal.code.not.comply.with.the.rules=The first three characters of the postal code do not comply with the rules
nbTransferCenter.postal.code.have.been.entered=The first three characters of the postal code have been entered in the transfer center, please check
nbTransferCenter.upload.successful=Upload successful
nbTransferCenter.code.already.occupied=Code already occupied: {0}

nbTransferBatch.the.road.area.does.not.exist=The road area in R4m does not exist: {0}
nbTransferBatch.there.are.no.orders.in.this.area=There are no orders in this area: {0}
nbTransferBatch.The.allocation.of.benchmark.fees.has.not.been.completed.yet=The allocation of benchmark fees has not been completed yet: {0}
nbTransferBatch.business.processing.failed=Business processing failed
nbTransferBatch.Route.does.not.exist=Route does not exist: {0}
nbTransferBatch.Delivery.routes.that.have.already.been.activated.no.delete=Delivery routes that have already been activated cannot be deleted
nbTransferBatch.the.route.has.been.deleted.and.the.time=The road area has been deleted, and the time is: {0}
nbTransferBatch.the.route.no.deleted.and.the.status=Orders within the road area cannot be deleted. Current status: {0}
nbTransferBatch.deleted.failed=Delete failed
nbTransferBatch.deleted.failed.msg=Delete failed: {0}
nbTransferBatch.not.sortingcenter.configured.cannot.perform.route.synchronization=This account does not have a sorting center configured, so it cannot perform road area synchronization
nbTransferBatch.route.not.found=route not found in R4m
nbTransferBatch.routeDestinationId.does.not.exist=R4m routeDestinationId does not exist: {0}
nbTransferBatch.transferBatchOrder.not.found=The road zone order for this batch: {0} does not exist, unable to set R4m Alias information

nbDriverScaned.report=Report cannot be generated without scanning the order.

nborderSignImage.uploaded.images.to.the.main.order=For sub orders with multiple items per order, images need to be uploaded to the main order

driverorder.package.does.not.exist=The package does not exist: {0}
driverorder.only.failed.orders.can.be.scanned=Only failed orders can be scanned and changed to Return Office status
driverorder.cannot.be.signed.for.in.the.warehouse=This order status cannot be signed for in the warehouse
driverorder.no.batch.selected.please.reselect=No batch selected, please reselect
driverorder.batch.does.not.exist.please.reselect=Batch does not exist, please reselect: {0}
driverorder.only.sub.batches.can.be.operated=Only sub batches can be operated
driverorder.Truck.delivery.batches.cannot.be.directly.scanned=Truck delivery batches cannot be directly scanned
driverorder.batch.not.enabled.scanning=This batch has not enabled scanning
driverorder.already.started.not.performed=This batch has already started synchronization or has been synchronized before, blind scanning cannot be performed
driverorder.order.has.been.intercepted=The order has been intercepted
driverorder.self.pickup.items=Self pickup items
driverorder.has.been.sent.to.the.transfer.center.no.create.sub.batch=The order has been sent to the transit center and cannot be created as a sub batch
driverorder.repeat.scanning=Repeat scanning: {0}
driverorder.this.package.is.in.a.sub.batch=This package is in a sub batch: {0}

routeGroup.route.does.not.exist=Route in R4m does not exist: {0}
routeGroup.transferred.by.other.drivers=Transferred by other drivers: {0}
routeGroup.unboarded.status.no.need.to.cancel=Unboarded status, no need to cancel
routeGroup.scanned.unloading.cannot.be.revoked=Scanned unloading, cannot be revoked
routeGroup.no.corresponding.information.found=No corresponding information found: {0}
routeGroup.has.been.scanned.in.this.time=Scanned, scan time: {0}
routeGroup.does.not.match.the.registered.transfer.center=The scanned road area does not match the registered transfer center. Please check

user.account.not.empty=Account cannot be empty
user.password.not.empty=Password cannot be empty
user.account.or.password.error=Account or password error

managerorder.no.batch.selected=No batch selected
managerorder.batchId.format.error=batchId format error

shelf.the.shelf.does.not.exist=The shelf does not exist: {0}
shelf.the.shelf.has.been.set.as.invalid=The shelf has been set as invalid: {0}
shelf.only.orders.in.status.can.be.listed=Only orders in status 200,280,286 can be listed
shelf.package.has.been.picked.up=The package has been picked up: {0}

nborderbatchrouteno.selection.batch=No batch selected.
nborderbatchrouteno.batch.has.been.synchronized=The batch has already been synchronized：{0}
nborderbatchrouteno.do.not.open.it.again=The batch is being synchronized, please do not open it again：{0}
nborderbatchrouteno.there.are.no.orders.to.plan=There are no orders for this batch to plan：{0}
nborderbatchrouteno.the.batch.planning.fails.contact.the.administrator=The batch planning fails. Contact the administrator：{0}
nborderbatchrouteno.orders.unassigned.transfer.centers=Some orders are found unassigned to transfer centers, this operation cannot be performed：{0}
nborderbatchrouteno.batch.not.exist=The batch does not exist.
nborderbatchrouteno.have.planned=Already planned.
nborderbatchrouteno.start.transit.center.does.not.exist=The starting transfer center does not exist.
nborderbatchrouteno.sorting.center.or.transshipment.center.choose.at.least.one=At least one sorting center or transfer center must be selected.
nborderbatchrouteno.transit.center.does.not.exist=The transfer center does not exist.
nborderbatchrouteno.region.code.not.set=The transit center {0} has not set the region number and cannot complete the grouping for the order, please check

verification.code.has.expired.please.obtain.the.verification.code.again=The graphic verification code has expired. Please obtain the verification code again
verification.code.is.incorrect=The graphic verification code is incorrect

nbMerchant.customer.unique.code.cannot.be.duplicated=The unique customer code {0} already exists and cannot be added again!
nbMerchant.weight.format.error=Weight format error: {0}

route4me.batch.create.empty=The quantity of the batch in storage is empty{0}
route4me.batch.create.error=The batch route failed to be generated. Procedure{0}
appshelf.at.least.one.parameter=At least one parameter needs to be passed in: mobile/pkgNo/putawayCode

nbPostalCodeDetail.Excel.file.processing.exception=The Excel file processing exception: {0}
nbPostalCodeDetail.Excel.file.processing.success= The Excel file has been processed successfully, with a total of {0} record(s) processed
nbPostalCodeDetail.file.processing.errors=Import processing failed, please check the error message: {0}

weight.delivery.note.Excel.file.processing.success=The Excel file of weight details was successfully processed, and a total of {0} items were processed
weight.delivery.note.Excel.file.processing.exception= Weight details Excel file processing exception: {0}
weight.file.processing.errors=Failed to import weight details. Please check information: {0}

tms.package.does.not.exist=No package information found
tms.missing.parameter=You need to pass in a parameter: package number/pick-up code
tms.shelf.code.exists=The shelf number already exists：{0}



