error.database.access=系统访问繁忙，请稍后再试。
error.database=系统访问繁忙，请稍后再试。
error.invalid.argument=请求参数不合法：{0}。
error.service.unavailable=服务不可用，请稍后再试：{0}。
error.unauthorized=未授权，请提供有效的认证信息。
error.access.denied=禁止访问，权限不足。
error.method.not.allowed=请求方法不允许。
error.internal.server=服务器内部繁忙，请稍后再试。
error.database.insert.error=操作失败，请检查：{0}.

nborder.upload.valid.file=请上传有效的 Excel 文件！
nborder.upload.empty.data=Excel文件中数据为空
nborder.no.valid.data.rows=没有有效的订单数据行
nborder.transfercenter.does.not.exist=转运中心不存在：{0}
nborder.merchant.does.not.exist=商家不存在：{0}
nborder.duplicate.customer.tracking.number=客户单号重复：{0}
nborder.Duplicate.package.number=包裹号重复：{0}
nborder.add.failed=添加失败，行：{0}
nborder.file.processing.errors=订单导入处理失败，请检查错误信息：{0}
nborder.Duplicate.customer.tracking.number.continue.uploading=TIP:客户单号重复，是否继续上传：{0}
nborder.delivery.note.Excel.file.processing.success=派送单Excel文件处理成功，共处理了 {0} 条订单
nborder.delivery.note.Excel.file.processing.exception=派送单Excel文件处理异常：{0}
nborder.order.status.not.selected.for.modification=没有选择修改的订单状态
nborder.no.repeat.create=该订单已经创建过子订单，不能再继续创建
nborder.order.successfully.delivered=订单已经配送成功
nborder.please.select.the.orderId=请选择订单ID
nborder.order.does.not.exist=订单不存在：{0}
nborder.suborder.cannot.create.suborder=子订单不能再创建子订单

nbdriver.created.failed=创建Route4me用户失败：{0}
nbdriver.vehicle.created.failed=创建Route4me车辆失败。失败R4M用户ID：{0}
nbdriver.driver.audit.passed=司机审核通过
nbdriver.business.type.is.incorrect=业务类型不正确，请检查！
nbdriver.mobile.unique.cannot.be.duplicated=该手机号：{0} 重复，请检查！
nbdriver.audit.passed=审核通过
nbdriver.msg.rejected=信息已驳回
nbdriver.success.under.review=创建成功，审核中
nbdriver.status.inoperable=司机状态不可操作
nbdriver.already.approved.for.review=已经审核通过
nbdriver.repeat.operation=重复操作
nbdriver.invalid.state=司机已为无效状态
nbdriver.valid.state=司机已为有效状态
nbdriver.already.synchronized.no.modify.email=已经同步到route4me的账号，无法修改邮箱
nbdriver.email.no.empty=邮箱不能为空
nbdriver.mobile.no.empty=手机号不能为空
nbdriver.leader.does.not.have.id=输入的Leader不存在id：{0}
nbdriver.leader.is.not.the.leader=输入的司机不是leader：{0}
nbdriver.email.is.already.occupied=邮箱已经被占用email：{0}
nbdriver.mobile.is.already.occupied=手机号已经被占用mobile：{0}
nbdriver.error.synchronizing.to.route4me=同步到Route4me时出错：{0}
nbdriver.create.failed=司机创建失败
nbdriver.create.successfully=司机创建成功
nbdriver.invalid.state.reason=司机所在的派送包裹是在途、派送失败的包裹，才可以置为无效

nborderBatch.successfully.updated.orders=成功更新订单数：{0}
nborderBatch.only.main.batch.creating.sub.batches=只有主批次才支持创建子批次
nborderBatch.batch.does.not.exist=批次不存在
nborderBatch.planned.already=该批次已规划过
nborderBatch.no.orders.available.for.planning=没有订单可供规划
nborderBatch.an.order.cannot.be.planned=一个订单无法规划
nborderBatch.no.assigned.transfer.center.no.operation=发现有未分配转运中心的订单，无法进行该操作：{0}
nborderBatch.transfer.center.no.exist.at.the.beginning=开始转运中心不存在
nborderBatch.sorting.center.no.exist.at.the.beginning=开始分拣中心不存在
nborderBatch.at.least.one.sortingcenter.or.transfercenter=分拣中心或转运中心，至少选择一个
nborderBatch.transfercenter.no.exist=转运中心不存在
nborderBatch.batch.number.already.exists=批次号已存在：{0}
nborderBatch.status.cannot.be.transferred=PARCEL_SCANNED 状态后的订单不能转移
nborderBatch.Only.manually.created.orders.can.be.transferred=只有手动创建的订单才能转移
nborderBatch.batch.consistency.no.need.to.transfer=订单当前批次和指定批次一致，无需转移
nborderBatch.no.batch.selected=没有选择批次
nborderBatch.This.batch.has.already.been.synchronized=该批次已同步过：{0}
nborderBatch.is.being.synchronized.not.open.it.again=该批次正在同步,请勿重复开启：{0}
nborderBatch.only.main.batch.operation=只有主批次才能进行此操作
nborderBatch.no.modifiable.orders.within.the.batch=批次内没有可修改的订单
nborderBatch.five=-5，{0}

nbshelf.package.has.been.taken.off.the.shelves=包裹已下架：{0}
nbshelf.the.generated.shelf.code.already.exists=生成的货架编码已存在：{0}

nbsortingCenter.invalid.code=无效码
nbsortingCenter.the.shelf.does.not.exist=货架不存在：{0}
nbsortingCenter.the.shelf.has.been.set.and.is.invalid=货架已置位无效：{0}
nbsortingCenter.the.shelves.are.not.under.your.jurisdiction=不是你管辖的货架：{0}
nbsortingCenter.the.package.does.not.exist=包裹不存在：{0}
nbsortingCenter.the.package.has.been.shelved=该包裹已上架：{0}
nbsortingCenter.only.orders.that.have.failed.delivery.can.be.listed=只有配送失败的订单才能上架

nbTransferCenter.cannot.contain.Chinese.commas=不能包含中文逗号
nbTransferCenter.postal.code.is.incorrect=前三字邮编长度不正确
nbTransferCenter.postal.code.not.comply.with.the.rules=前三字邮编规则不符合
nbTransferCenter.postal.code.have.been.entered=前三字邮编已经在转运中心中录入，请检查
nbTransferCenter.upload.successful=上传成功
nbTransferCenter.code.already.occupied=编码已被占用：{0}

nbTransferBatch.the.road.area.does.not.exist=R4m路区不存在：{0}
nbTransferBatch.there.are.no.orders.in.this.area=该路区没有订单：{0}
nbTransferBatch.The.allocation.of.benchmark.fees.has.not.been.completed.yet=路区订单尚未完成基准费分配：{0}
nbTransferBatch.business.processing.failed=业务处理失败
nbTransferBatch.Route.does.not.exist=Route不存在：{0}
nbTransferBatch.Delivery.routes.that.have.already.been.activated.no.delete=已开启派送的路区无法删除
nbTransferBatch.the.route.has.been.deleted.and.the.time=该路区已删除，时间在：{0}
nbTransferBatch.the.route.no.deleted.and.the.status=路区内订单无法对路区删除，当前状态：{0}
nbTransferBatch.deleted.failed=删除失败
nbTransferBatch.deleted.failed.msg=删除失败：{0}
nbTransferBatch.not.sortingcenter.configured.cannot.perform.route.synchronization=该账户没有配置分拣中心，无法进行路区同步
nbTransferBatch.route.not.found=路区没有在R4m中找到
nbTransferBatch.routeDestinationId.does.not.exist= = R4m路区目的地ID不存在：{0}
nbTransferBatch.transferBatchOrder.not.found=该批次的路区订单：{0} 不存在，无法设置R4m Alias信息

nbDriverScaned.report=Report cannot be generated without scanning the order.

nborderSignImage.uploaded.images.to.the.main.order=一票多件的子订单，需要将图片上传到主订单上

driverorder.package.does.not.exist=包裹不存在：{0}
driverorder.only.failed.orders.can.be.scanned=只有失败订单才能扫描改到 Return Office状态
driverorder.cannot.be.signed.for.in.the.warehouse=该订单状态无法在仓库签收
driverorder.no.batch.selected.please.reselect=没有选择批次，请重新选择
driverorder.batch.does.not.exist.please.reselect=选择的批次不存在，请从新选择：{0}
driverorder.only.sub.batches.can.be.operated=只能操作子批次
driverorder.Truck.delivery.batches.cannot.be.directly.scanned=卡派批次不能直接扫描
driverorder.batch.not.enabled.scanning=该批次未开启扫描
driverorder.already.started.not.performed=该批次已经开始同步或同步过，不能进行盲扫了
driverorder.order.has.been.intercepted=订单已被拦截
driverorder.self.pickup.items=自提件
driverorder.has.been.sent.to.the.transfer.center.no.create.sub.batch=订单已经派往转运中心，无法再建立子批次
driverorder.repeat.scanning=重复扫描：{0}
driverorder.this.package.is.in.a.sub.batch=该包裹在子批次：{0}

routeGroup.route.does.not.exist=R4m路区不存在：{0}
routeGroup.transferred.by.other.drivers=已被其他司机转运：{0}
routeGroup.unboarded.status.no.need.to.cancel=未上车状态，无需撤销
routeGroup.scanned.unloading.cannot.be.revoked=已扫描卸货，无法撤销
routeGroup.no.corresponding.information.found=没有找到对应信息：{0}
routeGroup.has.been.scanned.in.this.time=已扫描，扫描时间：{0}
routeGroup.does.not.match.the.registered.transfer.center=扫描的路区与登记的转运中心不符，请检查

user.account.not.empty=账号不能为空
user.password.not.empty=密码不能为空
user.account.or.password.error=账号或密码错误

managerorder.no.batch.selected=没有选择批次
managerorder.batchId.format.error=批次ID格式错误

shelf.the.shelf.does.not.exist=货架不存在：{0}
shelf.the.shelf.has.been.set.as.invalid=货架已置为无效：{0}
shelf.only.orders.in.status.can.be.listed=只有200,280,286状态的订单才能上架
shelf.package.has.been.picked.up=快件已经取走：{0}

nborderbatchrouteno.selection.batch=没有选择批次
nborderbatchrouteno.batch.has.been.synchronized=该批次已同步过:：{0}
nborderbatchrouteno.do.not.open.it.again=该批次正在同步,请勿重复开启：{0}
nborderbatchrouteno.there.are.no.orders.to.plan=该批次没有订单可供规划：{0}
nborderbatchrouteno.the.batch.planning.fails.contact.the.administrator=该批次已规划失败，请联系管理员处理：{0}
nborderbatchrouteno.orders.unassigned.transfer.centers=发现有未分配转运中心的订单，无法进行该操作：{0}
nborderbatchrouteno.batch.not.exist=批次不存在
nborderbatchrouteno.have.planned=已规划过
nborderbatchrouteno.start.transit.center.does.not.exist=开始转运中心不存在
nborderbatchrouteno.sorting.center.or.transshipment.center.choose.at.least.one=分拣中心或转运中心，至少选择一个
nborderbatchrouteno.transit.center.does.not.exist=转运中心不存在
nborderbatchrouteno.region.code.not.set=转运中心{0}没有设置大区号,无法为订单完成分组,请检查

verification.code.has.expired.please.obtain.the.verification.code.again=图形验证码已过期，请重新获取验证码
verification.code.is.incorrect=图形验证码不正确

nbMerchant.customer.unique.code.cannot.be.duplicated=客户唯一编码：{0} 已存在，不能重复添加！
nbMerchant.weight.format.error=重量格式错误：{0}

route4me.batch.create.empty=该批次入库数量为空{0}
route4me.batch.create.error=该批次路线生成失败{0}
appshelf.at.least.one.parameter=至少需要传入一个参数：手机号/包裹号/上架编码

nbPostalCodeDetail.Excel.file.processing.exception=Excel文件处理异常：{0}
nbPostalCodeDetail.Excel.file.processing.success=Excel文件处理成功，共处理了 {0} 条记录
nbPostalCodeDetail.file.processing.errors=导入处理失败，请检查错误信息：{0}
weight.delivery.note.Excel.file.processing.success=重量明细Excel文件处理成功，共处理了 {0} 条
weight.delivery.note.Excel.file.processing.exception=重量明细Excel文件处理异常：{0}
weight.file.processing.errors=重量明细导入处理失败，请检查信息：{0}

tms.package.does.not.exist=未查询到包裹信息
tms.missing.parameter=需要传入一个参数：包裹号/取件码
tms.shelf.code.exists=货架编码已存在：{0}

