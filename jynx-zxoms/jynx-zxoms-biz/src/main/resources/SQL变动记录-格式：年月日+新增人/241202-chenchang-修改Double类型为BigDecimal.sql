
ALTER TABLE nb_merchant MODIFY COLUMN multi_min_weight_kg DECIMAL(10, 3);
ALTER TABLE nb_merchant MODIFY COLUMN multi_min_weight_lb DECIMAL(10, 3);

ALTER TABLE nb_price_rule MODIFY COLUMN weight_kg DECIMAL(15, 2);
ALTER TABLE nb_price_rule MODIFY COLUMN weight_lb DECIMAL(15, 2);
ALTER TABLE nb_price_rule MODIFY COLUMN price_nornal DECIMAL(10, 3);
ALTER TABLE nb_price_rule MODIFY COLUMN price_express DECIMAL(10, 3);
ALTER TABLE nb_price_rule MODIFY COLUMN price_multi DECIMAL(10, 3);

ALTER TABLE nb_ltl_surcharge MODIFY COLUMN charges DECIMAL(8, 2);

ALTER TABLE nb_driver_income MODIFY COLUMN amount DECIMAL(8, 2);

ALTER TABLE nb_order_cost MODIFY COLUMN driver_base DECIMAL(8, 2);
ALTER TABLE nb_order_cost MODIFY COLUMN driver_weight_subsidy DECIMAL(8, 2);
ALTER TABLE nb_order_cost MODIFY COLUMN driver_subsidy DECIMAL(8, 2);
ALTER TABLE nb_order_cost MODIFY COLUMN pkg_weight DECIMAL(8, 2);
ALTER TABLE nb_order_cost MODIFY COLUMN pkg_volume_weight DECIMAL(8, 2);

ALTER TABLE nb_transfer_batch_cost_modify_log MODIFY COLUMN subsidy_amount DECIMAL(8, 2);
ALTER TABLE nb_transfer_batch_cost_modify_log MODIFY COLUMN avg_amount DECIMAL(8, 2);

ALTER TABLE nb_driver_cost_rule_item MODIFY COLUMN start_kg DECIMAL(8, 2);
ALTER TABLE nb_driver_cost_rule_item MODIFY COLUMN start_vw DECIMAL(8, 2);
ALTER TABLE nb_driver_cost_rule_item MODIFY COLUMN payment DECIMAL(8, 2);
ALTER TABLE nb_driver_cost_rule_item MODIFY COLUMN subsidy DECIMAL(8, 2);

ALTER TABLE nb_order MODIFY COLUMN pkg_weight DECIMAL(8, 2);    -- 包裹重量
ALTER TABLE nb_order MODIFY COLUMN pkg_length DECIMAL(8, 2);    -- 包裹长
ALTER TABLE nb_order MODIFY COLUMN pkg_width DECIMAL(8, 2);     -- 包裹宽
ALTER TABLE nb_order MODIFY COLUMN pkg_height DECIMAL(8, 2);    -- 包裹高
ALTER TABLE nb_order MODIFY COLUMN pkg_value DECIMAL(8, 2);     -- 包裹体积

ALTER TABLE nb_transfer_batch MODIFY COLUMN estimated_hour DECIMAL(8, 2);     -- 预估时间

