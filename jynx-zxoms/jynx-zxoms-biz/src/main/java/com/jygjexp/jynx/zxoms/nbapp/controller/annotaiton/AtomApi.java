package com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton;

import java.lang.annotation.*;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/7 15:46
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface AtomApi {

    /**
     * 自定义key，否则按类+方法名
     */
    public String key() default "";

    /**
     * 拼接的参数，不配置为全部参数
     */
    public String[] params() default {};

    /**
     * 超时时间，单位秒
     */
    public int expire() default 10;

}
