package com.jygjexp.jynx.zxoms.nbapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/12 9:29
 */
@Data
public class APPDriverUploadVo {
    @Schema(description = "上传文件")
    private MultipartFile file;
    @Schema(description = "文件类型")
    private String type;
    @Schema(description = "文件名")
    private String fileName;
    @Schema(description = "订单号")
    private String orderId;


}
