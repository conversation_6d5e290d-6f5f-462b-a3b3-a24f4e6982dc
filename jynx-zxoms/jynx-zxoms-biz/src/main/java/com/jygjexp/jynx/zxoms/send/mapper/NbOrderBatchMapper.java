package com.jygjexp.jynx.zxoms.send.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.zxoms.entity.NbOrderBatchEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;


@Mapper
public interface NbOrderBatchMapper extends JynxBaseMapper<NbOrderBatchEntity> {


    //@Tenant(false) // 禁用租户过滤
    //@InterceptorIgnore(tenantLine = "true") // 禁用租户过滤
    //List<NbOrderBatchEntity> findNoRouted(String batchNo);
}