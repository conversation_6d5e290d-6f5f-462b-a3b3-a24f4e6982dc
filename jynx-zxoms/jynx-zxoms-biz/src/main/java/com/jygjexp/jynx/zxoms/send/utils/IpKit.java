package com.jygjexp.jynx.zxoms.send.utils;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/11 13:51
 */
public class IpKit {
    public IpKit() {
    }

    public static String getRealIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        return ip;
    }

    public static String getRealIpV2(HttpServletRequest request) {
        String accessIP = request.getHeader("x-forwarded-for");
        return null == accessIP ? request.getRemoteAddr() : accessIP;
    }
}
