package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbPostalCodeDetailEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbPostalCodeDetailMapper;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.NbPostalCodeDetailService;
import com.jygjexp.jynx.zxoms.vo.NbPostalCodeDetailPageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 邮编分区-明细
 *
 * <AUTHOR>
 * @date 2025-01-09 15:15:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NbPostalCodeDetailServiceImpl extends ServiceImpl<NbPostalCodeDetailMapper, NbPostalCodeDetailEntity> implements NbPostalCodeDetailService {
    private final NbPostalCodeDetailMapper nbPostalCodeDetailMapper;

    /**
     * 导入邮编分区明细Excel
     * @param file
     * @return
     */
    @Override
    public R importNbPostalCodeDetail(MultipartFile file, Long groupId) {
        // 验证文件类型和空文件
        if (file == null || file.isEmpty() || !isExcelFile(file.getOriginalFilename())) {
            return LocalizedR.failed("nborder.upload.valid.file", Optional.ofNullable(null));
        }
        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<List<Object>> lines = reader.read(1, reader.getRowCount());

            // 验证Excel数据是否为空
            if (lines == null || lines.isEmpty()) {
                return LocalizedR.failed("nborder.upload.empty.data", Optional.ofNullable(null));
            }
            // 创建集合来记录所有问题的字段
            List<String> errorMessages = new ArrayList<>();
            List<NbPostalCodeDetailEntity> detailList = new ArrayList<>();

            // 批量获取分区组的所有现有的邮编区间
            List<NbPostalCodeDetailEntity> existingRanges = nbPostalCodeDetailMapper.selectList(new QueryWrapper<NbPostalCodeDetailEntity>().eq("group_id", groupId));

            for (int i = 0; i < lines.size(); i++) {
                List<Object> line = lines.get(i);
                log.info("Processing line {}: {}", i, line);

                // 每一行的错误集合
                List<String> lineErrors = new ArrayList<>();
                // 验证字段
                Integer partitionCode = validateIntegerField(line, 0, "The partitionCode cannot be empty/分区代码不能为空", i + 1, lineErrors);
                String typeStr = validateStringField(line, 1, "Type cannot be empty/类型不能为空", i + 1, lineErrors);
                Integer type = null;

                if (StrUtil.isNotBlank(typeStr)) {
                    if (typeStr.equals("范围")) {
                        type = 1;
                    } else if (typeStr.equals("精准")) {
                        type = 2;
                    } else {
                        lineErrors.add("Invalid type value/类型值无效");
                    }
                }
                String startPostalCode = validateStringField(line, 2, "The startPostalCode cannot be empty/开始邮编不能为空", i + 1, lineErrors);
                String endPostalCode = validateStringField(line, 3, "The endPostalCode cannot be empty/截止邮编不能为空", i + 1, lineErrors);

                String postalCodeRegex = null;
                // 邮编格式验证的正则表达式 如果类型为精准 三字邮编格式为：A1A 1A1
                if (null != type && type == 1) {
                    postalCodeRegex = "^[A-Z]\\d[A-Z]$";
                }else if (null != type && type == 2) {
                    postalCodeRegex = "^[A-Z]\\d[A-Z] \\d[A-Z]\\d$";
                } else {
                    lineErrors.add("Invalid type value/类型值无效");
                }

                // 邮编格式验证
                if (null != type && !startPostalCode.matches(postalCodeRegex)) {
                    lineErrors.add("Invalid start postal code format/开始邮编格式无效");
                }
                if (null != type && !endPostalCode.matches(postalCodeRegex)) {
                    lineErrors.add("Invalid end postal code format/截止邮编格式无效");
                }

                // 校验分区代码是否重复
                validatePartitionCode(line, 0, existingRanges, lineErrors);

                // 校验邮编区间的重复或重叠
                validatePostalCodeRange(line, 2, 3, "The postal code range overlaps with an existing range/邮编区间与现有区间重叠", i + 1, lineErrors, groupId);

                // 如果当前行有错误，跳过该行
                if (!lineErrors.isEmpty()) {
                    errorMessages.addAll(lineErrors);  // 将当前行的所有错误添加到全局错误信息列表中
                } else {
                    NbPostalCodeDetailEntity detail = new NbPostalCodeDetailEntity();
                    detail.setPartitionCode(partitionCode);
                    detail.setType(type);  // 根据判断设置类型
                    // 如果是范围类型，则将起始和结束邮编后三位填充为0A0和9Z9
                    if (type == 1){
                        startPostalCode = String.join(" ", startPostalCode, "0A0");
                        endPostalCode = String.join(" ", endPostalCode, "9Z9");
                    }
                    detail.setStartPostalCode(startPostalCode);
                    detail.setEndPostalCode(endPostalCode);
                    detail.setGroupId(groupId);
                    try {
                        detailList.add(detail);
                    } catch (Exception e) {
                        log.error("Failed to save postal code detail for line {}: {}", i, e.getMessage());
                        errorMessages.add("Failed to save postal code detail for line " + (i + 1) + ": " + e.getMessage());
                    }
                }
            }

            if (!detailList.isEmpty()) {
                for (NbPostalCodeDetailEntity detail : detailList) {
                    this.save(detail);
                }
            }

            //记录成功条数信息
            String successMessage = "Successfully processed: " + detailList.size() + " rows.";
            if (errorMessages.isEmpty()) {
                return LocalizedR.ok("nbPostalCodeDetail.Excel.file.processing.success", successMessage);
            } else {
                return LocalizedR.failed("nbPostalCodeDetail.file.processing.errors", successMessage + "<br>Errors:<br>" + errorMessages);
            }
        } catch (IOException e) {
            log.error("邮编分区Excel文件处理异常", e);
            return LocalizedR.failed("nbPostalCodeDetail.Excel.file.processing.exception", e.getMessage());
        }
    }

    private void validatePartitionCode(List<Object> line, int i, List<NbPostalCodeDetailEntity> existingRanges, List<String> lineErrors) {
        Object value = line.get(i);
        for (NbPostalCodeDetailEntity existingRange : existingRanges) {
            if (existingRange.getPartitionCode().equals(value)) {
                lineErrors.add("row" + i + "：分区代码重复");
            }
        }
    }

    /**
     * 邮编分区明细-分页查询
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<NbPostalCodeDetailPageVo> search(Page page, NbPostalCodeDetailPageVo vo) {
        LambdaQueryWrapper<NbPostalCodeDetailEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(ObjectUtil.isNotNull(vo.getPartitionCode()), NbPostalCodeDetailEntity::getPartitionCode, vo.getPartitionCode())   // 条件查询-分区代码
                .like(StrUtil.isNotBlank(vo.getStartPostalCode()), NbPostalCodeDetailEntity::getStartPostalCode, vo.getStartPostalCode()) // 条件查询-开始邮编
                .like(StrUtil.isNotBlank(vo.getEndPostalCode()), NbPostalCodeDetailEntity::getEndPostalCode, vo.getEndPostalCode())      // 条件查询-截止邮编
                .eq(NbPostalCodeDetailEntity::getGroupId, vo.getGroupId());
        String creatTime = vo.getCreateTimeVo();   // 创建时间 2024/03/25-2024/12/31
        if (creatTime != null) {
            String[] dates = creatTime.split("-");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
            LocalDate startDate = LocalDate.parse(dates[0], formatter);
            LocalDate endDate = LocalDate.parse(dates[1], formatter);
            LocalDateTime startDateTime = startDate.atStartOfDay(); // 默认时间为 00:00:00
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59); // 默认时间为 23:59:59
            wrapper.between(NbPostalCodeDetailEntity::getCreateTime, startDateTime, endDateTime);
        }
        wrapper.orderByDesc(NbPostalCodeDetailEntity::getCreateTime);
        return nbPostalCodeDetailMapper.selectPage(page, wrapper);
    }

    // 校验邮编区间重复
    private String validatePostalCodeRange(List<Object> line, int index, int index2, String errorMessage, int row, List<String> lineErrors, Long groupId) {
        Object value = line.get(index);
        Object value2 = line.get(index2);

        List<NbPostalCodeDetailEntity> existingRanges = nbPostalCodeDetailMapper.selectList(new LambdaQueryWrapper<NbPostalCodeDetailEntity>()
                .like(NbPostalCodeDetailEntity::getStartPostalCode, value + "%")
                .like(NbPostalCodeDetailEntity::getEndPostalCode, value2 + "%")
                .eq(NbPostalCodeDetailEntity::getGroupId, groupId).last("limit 1"));
        if (CollUtil.isNotEmpty(existingRanges)) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        return value.toString().trim();
    }

    private String validateStringField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null || value.toString().trim().isEmpty()) {
            lineErrors.add(errorMessage + ", line: " + row);
            return "";
        }
        return value.toString().trim();
    }

    private Integer validateIntegerField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null) {
            lineErrors.add(errorMessage + ", line: " + row);
            return null;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            // 处理格式错误
            lineErrors.add(errorMessage + " (format error), line: " + row);
            return null;
        }
    }

    private boolean isExcelFile(String fileName) {
        return fileName != null && (fileName.endsWith(".xls") || fileName.endsWith(".xlsx"));
    }

}