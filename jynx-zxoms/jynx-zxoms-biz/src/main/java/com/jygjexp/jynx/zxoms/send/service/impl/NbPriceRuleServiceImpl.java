package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbMerchantEntity;
import com.jygjexp.jynx.zxoms.entity.NbPriceDistrictEntity;
import com.jygjexp.jynx.zxoms.entity.NbPriceRuleEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbPriceRuleMapper;
import com.jygjexp.jynx.zxoms.send.service.NbPriceRuleService;
import com.jygjexp.jynx.zxoms.vo.NbPriceRulePageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
/**
 * 报价表
 *
 * <AUTHOR>
 * @date 2024-11-08 10:35:26
 */
@Service
@RequiredArgsConstructor
public class NbPriceRuleServiceImpl extends ServiceImpl<NbPriceRuleMapper, NbPriceRuleEntity> implements NbPriceRuleService {
    private final NbPriceRuleMapper nbPriceRuleMapper;

    /**
     * 报价表分页查询
     * @param page
     * @param nbPriceRule
     * @return
     */
    @Override
    public Page<NbPriceRulePageVo> search(Page page, NbPriceRuleEntity nbPriceRule) {
        //select pd_id ID,district_code code from nb_price_district;ds=nbd;
        //select id, login_id from eova_user; ds=eova;
        //select merchant_id ID, name CN from nb_merchant; ds=nbd;
        MPJLambdaWrapper<NbPriceRuleEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbPriceRuleEntity.class)
                .select(NbPriceDistrictEntity::getPdId)
                .selectAs(NbPriceDistrictEntity::getDistrictCode, NbPriceRulePageVo.Fields.districtCode)
                .select(NbMerchantEntity::getMerchantId)
                .selectAs(NbMerchantEntity::getName, NbPriceRulePageVo.Fields.merchantName)
                .leftJoin(NbPriceDistrictEntity.class, NbPriceDistrictEntity::getPdId, NbPriceRuleEntity::getTargetPdId)
                .leftJoin(NbMerchantEntity.class, NbMerchantEntity::getMerchantId, NbPriceRuleEntity::getMerchantId);
        return nbPriceRuleMapper.selectJoinPage(page, NbPriceRulePageVo.class, wrapper);
    }

}