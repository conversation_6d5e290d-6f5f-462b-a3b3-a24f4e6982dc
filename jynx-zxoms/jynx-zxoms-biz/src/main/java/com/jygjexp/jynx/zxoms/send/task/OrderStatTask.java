package com.jygjexp.jynx.zxoms.send.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderStatEntity;
import com.jygjexp.jynx.zxoms.entity.NbSortingCenterEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderStatService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: chenchang
 * @Description: 【统计】订单状态
 * @Date: 2024/10/9 23:35
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OrderStatTask {
    private final NbSortingCenterService nbSortingCenterService;
    private final NbOrderService nbOrderService;
    private final NbOrderStatService nbOrderStatService;

    @SneakyThrows
    @XxlJob("orderStatHandler")
    public void orderStatHandler() {
//        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务：【统计订单状态】开始时间: {}, 入参:{}", LocalDateTime.now());

        // "select * from nb_sorting_center where is_valid = true");
        LambdaQueryWrapper<NbSortingCenterEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NbSortingCenterEntity::getIsValid, true);
        List<NbSortingCenterEntity> scList = nbSortingCenterService.list(wrapper);

        LocalDateTime ldt = LocalDateTime.now();
        for (int i=0; i<30; i++) {
            String dateNow = ldt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            for (NbSortingCenterEntity sc : scList) {
                stat(sc.getScId(), dateNow);
            }
            ldt = ldt.minusDays(1);
        }

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【统计订单状态】执行结束，时间: {}", LocalDateTime.now());
    }

    private void stat(Integer scId, String dateNow) {
        // "select count(1) cont, order_status from nb_order where sc_id = ? and add_time >= '" + date + " 00:00:00' and add_time <= '" + date + " 23:59:59' group by order_status", scId);
        QueryWrapper<NbOrderEntity> orderWrapper = new QueryWrapper<>();
        orderWrapper.select("order_status", "count(1) AS cont")
                .eq("sc_id", scId)
                .ge("add_time", dateNow + " 00:00:00")
                .le("add_time", dateNow + " 23:59:59")
                .groupBy("order_status");
        List<Map<String, Object>> orders = nbOrderService.listMaps(orderWrapper);

        // "select * from nb_order_stat where sc_id = ? and stat_date = ? limit 1", scId, dateNow);
        LambdaQueryWrapper<NbOrderStatEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NbOrderStatEntity::getScId, scId);
        wrapper.eq(NbOrderStatEntity::getStatDate, dateNow);
        NbOrderStatEntity stat = nbOrderStatService.getOne(wrapper);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date statDate = null;
        try {
            statDate = sdf.parse(dateNow);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        if (stat == null) {
            stat = new NbOrderStatEntity();
            stat.setScId(scId);
            stat.setStatDate(statDate);
            stat.setUpdateTime(LocalDateTime.now());
            nbOrderStatService.save(stat);
        }
        List<Integer> failos = Arrays.asList(OrderDto.ORDER_STATUS_210_FAILED_DELIVERY,
                OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1, OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2,
                OrderDto.ORDER_STATUS_280_FAILURE, OrderDto.ORDER_STATUS_331_FAILED_DELIVERY_RETRY1);
        Map<Integer, Integer> osCountMap = new HashMap<>();

        int total = 0;
        int failCount = 0;
        for (Map<String, Object> row : orders) {
            Integer os = (Integer) row.get("order_status");
            Integer count = ((Long) row.get("cont")).intValue();
            total += count;

            osCountMap.put(os, count);

            if (failos.contains(os)) {
                failCount += count;
            }

        }
        stat.setS190(osCountMap.getOrDefault(OrderDto.ORDER_STATUS_190_ORDER_RECEIVED, 0));
        stat.setS200(osCountMap.getOrDefault(OrderDto.ORDER_STATUS_200_PARCEL_SCANNED, 0));
        stat.setS201(osCountMap.getOrDefault(OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER, 0));
        stat.setS204(osCountMap.getOrDefault(OrderDto.ORDER_STATUS_204_IN_TRANSIT, 0));
        stat.setS205(osCountMap.getOrDefault(OrderDto.ORDER_STATUS_205_DELIVERED, 0));
        stat.setS225(osCountMap.getOrDefault(OrderDto.ORDER_STATUS_225_THIRD_PARTY_LOGISTICS, 0));
        stat.setS236(osCountMap.getOrDefault(OrderDto.ORDER_STATUS_236_SHIPPING_LABEL_PRINTED, 0));
        stat.setS237(osCountMap.getOrDefault(OrderDto.ORDER_STATUS_237_THIRD_PARTY_LOGISTICS_RECEIVED, 0));
        stat.setS290(osCountMap.getOrDefault(OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE, 0));
        stat.setS340(osCountMap.getOrDefault(OrderDto.ORDER_STATUS_340_PACKAGE_INTERCEPTED, 0));

        stat.setSFail(failCount);
        stat.setAddOrderTotal(total);
        nbOrderStatService.updateById(stat);

    }

}
