package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbOrderStatusEntity;
import com.jygjexp.jynx.zxoms.entity.NbPdaOrderScanRuleEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbPdaOrderScanRuleMapper;
import com.jygjexp.jynx.zxoms.send.service.NbPdaOrderScanRuleService;
import com.jygjexp.jynx.zxoms.vo.NbPdaOrderScanRulePageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
/**
 * PDA订单扫描规则
 *
 * <AUTHOR>
 * @date 2024-10-14 16:05:31
 */
@Service
@RequiredArgsConstructor
public class NbPdaOrderScanRuleServiceImpl extends ServiceImpl<NbPdaOrderScanRuleMapper, NbPdaOrderScanRuleEntity> implements NbPdaOrderScanRuleService {
    private final NbPdaOrderScanRuleMapper nbPdaOrderScanRuleMapper;

    @Override
    public Page<NbPdaOrderScanRulePageVo> search(Page page, NbPdaOrderScanRuleEntity entity) {
        //select id ID,title CN from nb_order_status; ds=nbd;
        //select id ID,title CN from nb_order_status; ds=nbd;
        MPJLambdaWrapper<NbPdaOrderScanRuleEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbPdaOrderScanRuleEntity.class)
                .select(NbOrderStatusEntity::getId)
                .selectAs(NbOrderStatusEntity::getTitle, NbPdaOrderScanRulePageVo.Fields.orderStatusTitle)
                .leftJoin(NbOrderStatusEntity.class, NbOrderStatusEntity::getId, NbPdaOrderScanRuleEntity::getToOrderStatus);
        return nbPdaOrderScanRuleMapper.selectJoinPage(page, NbPdaOrderScanRulePageVo.class, wrapper);
    }

}