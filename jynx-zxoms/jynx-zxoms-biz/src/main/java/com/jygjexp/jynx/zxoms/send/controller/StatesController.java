package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.zxoms.send.service.StatesService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.StatesEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 国家所在地区
 *
 * <AUTHOR>
 * @date 2024-09-30 17:48:13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/states" )
//@Tag(description = "states" , name = "国家所在地区管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class StatesController {

    private final StatesService basicStatesService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param basicStates 国家所在地区
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('jynx_states_view')" )
    public R getBasicStatesPage(@ParameterObject Page page, @ParameterObject StatesEntity basicStates) {
        LambdaQueryWrapper<StatesEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(basicStatesService.page(page, wrapper));
    }


    /**
     * 通过id查询国家所在地区
     * @param id id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('jynx_states_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(basicStatesService.getById(id));
    }

    /**
     * 新增国家所在地区
     * @param basicStates 国家所在地区
     * @return R
     */
//    @Operation(summary = "新增国家所在地区" , description = "新增国家所在地区" )
    @SysLog("新增国家所在地区" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('jynx_states_add')" )
    public R save(@RequestBody StatesEntity basicStates) {
        return R.ok(basicStatesService.save(basicStates));
    }

    /**
     * 修改国家所在地区
     * @param basicStates 国家所在地区
     * @return R
     */
//    @Operation(summary = "修改国家所在地区" , description = "修改国家所在地区" )
    @SysLog("修改国家所在地区" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('jynx_states_edit')" )
    public R updateById(@RequestBody StatesEntity basicStates) {
        return R.ok(basicStatesService.updateById(basicStates));
    }

    /**
     * 通过id删除国家所在地区
     * @param ids id列表
     * @return R
     */
//    @Operation(summary = "通过id删除国家所在地区" , description = "通过id删除国家所在地区" )
    @SysLog("通过id删除国家所在地区" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('jynx_states_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(basicStatesService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param basicStates 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('jynx_states_export')" )
    public List<StatesEntity> export(StatesEntity basicStates, Integer[] ids) {
        return basicStatesService.list(Wrappers.lambdaQuery(basicStates).in(ArrayUtil.isNotEmpty(ids), StatesEntity::getId, ids));
    }
}