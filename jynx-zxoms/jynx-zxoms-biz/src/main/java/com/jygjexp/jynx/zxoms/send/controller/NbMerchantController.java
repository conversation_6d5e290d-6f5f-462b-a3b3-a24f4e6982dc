package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.zxoms.entity.NbMerchantEntity;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.NbMerchantService;
import com.jygjexp.jynx.zxoms.vo.MerchantPageVo;
import com.jygjexp.jynx.zxoms.send.vo.NbMerchantExcelVo;
import com.jygjexp.jynx.zxoms.send.vo.NbMerchantQuoteExcelVo;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 客户
 *
 * <AUTHOR>
 * @date 2024-09-30 22:34:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/merchant" )
@Tag(description = "merchant" , name = "客户列表" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbMerchantController {

    private final NbMerchantService nbMerchantService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 客户
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('basic_merchant_view')" )
    public R getNbMerchantPage(@ParameterObject Page page, @ParameterObject MerchantPageVo vo) {
        return R.ok(nbMerchantService.search(page, vo));
    }

    @Operation(summary = "客户报价分页查询" , description = "客户报价分页查询" )
    @GetMapping("/quotePage" )
    @PreAuthorize("@pms.hasPermission('basic_merchant_quote_view')" )
    public R getNbMerchantQuotePage(@ParameterObject Page page, @ParameterObject MerchantPageVo vo) {
        return R.ok(nbMerchantService.searchQuote(page, vo));
    }

    /**
     * 通过id查询客户
     * @param merchantId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{merchantId}" )
    @PreAuthorize("@pms.hasPermission('basic_merchant_view')" )
    public R getById(@PathVariable("merchantId" ) Integer merchantId) {
        return R.ok(nbMerchantService.getById(merchantId));
    }

    /**
     * 新增客户
     * @param basicNbMerchant 客户
     * @return R
     */
    @Operation(summary = "新增客户" , description = "新增客户" )
    @SysLog("新增客户" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('basic_merchant_add')" )
    public R save(@RequestBody NbMerchantEntity basicNbMerchant) {
        // 设置客户ApiSecret
        String secret = RandomStringUtils.randomAlphanumeric(32);
        basicNbMerchant.setApiSecret(secret);
        basicNbMerchant.setCreateUserName(SecurityUtils.getUser().getUsername());   // 设置创建人名称

        Boolean isCodeDuplicated = nbMerchantService.checkMerchantCode(basicNbMerchant);    // 校验唯一编码在系统中是否存在，不能重复添加
        if (isCodeDuplicated) {
            return LocalizedR.failed("nbMerchant.customer.unique.code.cannot.be.duplicated", basicNbMerchant.getMerchantCode());
        }
        try {
            processMultiMinWeight(basicNbMerchant); // 处理一票多件重量  mutiMinWeight = 55.99LB/55.99KG
        } catch (IllegalArgumentException e) {
            return LocalizedR.failed("nbMerchant.weight.format.error", e.getMessage());
        }
        return R.ok(nbMerchantService.save(basicNbMerchant));
    }

    /**
     * 修改客户
     * @param basicNbMerchant 客户
     * @return R
     */
    @Operation(summary = "修改客户" , description = "修改客户" )
    @SysLog("修改客户" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('basic_merchant_edit')" )
    public R updateById(@RequestBody NbMerchantEntity basicNbMerchant) {
        basicNbMerchant.setUpdateTime(new Date());
        basicNbMerchant.setMultiMinWeight(basicNbMerchant.getMultiMinWeight());
        try {
            processMultiMinWeight(basicNbMerchant); // 处理一票多件重量  mutiMinWeight = 55.99LB/55.99KG
        } catch (IllegalArgumentException e) {
            return LocalizedR.failed("nbMerchant.weight.format.error", e.getMessage());
        }
        return R.ok(nbMerchantService.updateById(basicNbMerchant));
    }

    /**
     * 通过id删除客户
     * @param ids merchantId列表
     * @return R
     */
    @Operation(summary = "通过id删除客户" , description = "通过id删除客户" )
    @SysLog("通过id删除客户" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('basic_merchant_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbMerchantService.removeBatchByIds(CollUtil.toList(ids)));
    }

    @Operation(summary = "查询全部客户名称" , description = "查询全部客户名称" )
    @GetMapping("/merchantNameList")
    public R merchantNameList() {
        LambdaQueryWrapper<NbMerchantEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(NbMerchantEntity::getMerchantId, NbMerchantEntity::getName ,NbMerchantEntity::getIsValid).groupBy(NbMerchantEntity::getMerchantId);
        return R.ok(nbMerchantService.list(wrapper));
    }

    /**
     * 导出excel 表格
     * @param vo 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @Operation(summary = "客户列表导出Excel" , description = "客户列表导出Excel" )
    @SysLog("客户列表导出Excel" )
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('basic_merchant_export')" )
    public List<NbMerchantExcelVo> export(MerchantPageVo vo, Integer[] ids) {
        return nbMerchantService.getExcel(vo, ids);
    }

    @ResponseExcel
    @Operation(summary = "客户报价导出Excel" , description = "客户报价导出Excel" )
    @SysLog("客户报价导出Excel" )
    @GetMapping("/exportQuote")
    @PreAuthorize("@pms.hasPermission('merchant_quote_export')" )
    public List<NbMerchantQuoteExcelVo> exportQuote(MerchantPageVo vo, Integer[] ids) {
        return nbMerchantService.getQuoteExcel(vo, ids);
    }

    /**
     * 处理一票多件重量
     * @param basicNbMerchant
     */
    private void processMultiMinWeight(NbMerchantEntity basicNbMerchant) {
        if (StrUtil.isNotBlank(basicNbMerchant.getMultiMinWeight())) {
            String mutiMinWeight = basicNbMerchant.getMultiMinWeight();
            try {
                if (mutiMinWeight.endsWith("KG")) {
                    // 截取数字部分并设置为公斤（KG）
                    String weightKg = mutiMinWeight.substring(0, mutiMinWeight.length() - 2); // 去掉 "KG"
                    basicNbMerchant.setMultiMinWeightKg(new BigDecimal(weightKg.trim())); // 设置为公斤
                    basicNbMerchant.setMultiMinWeightLb(null);  // 清空磅的字段
                } else if (mutiMinWeight.endsWith("LB")) {
                    // 截取数字部分并设置为磅（LB）
                    String weightLb = mutiMinWeight.substring(0, mutiMinWeight.length() - 2); // 去掉 "LB"
                    basicNbMerchant.setMultiMinWeightLb(new BigDecimal(weightLb.trim())); // 设置为磅
                    basicNbMerchant.setMultiMinWeightKg(null);  // 清空公斤的字段
                }
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid weight format: " + mutiMinWeight);
            }
        }
    }

}