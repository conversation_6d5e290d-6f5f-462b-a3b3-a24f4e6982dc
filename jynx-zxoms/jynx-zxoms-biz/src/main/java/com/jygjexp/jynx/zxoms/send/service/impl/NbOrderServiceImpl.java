package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Maps;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.common.sentinel.handle.GlobalBizExceptionHandler;
import com.jygjexp.jynx.zxoms.dto.*;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.entity.NbSortingCenterEntity;

import com.jygjexp.jynx.zxoms.nbapp.utils.RedisUtil;
import com.jygjexp.jynx.zxoms.nbapp.vo.*;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.dto.BarCodeDto;
import com.jygjexp.jynx.zxoms.send.dto.OrderLabelDto;
import com.jygjexp.jynx.zxoms.send.dto.TransferBatchOrderDto;
import com.jygjexp.jynx.zxoms.send.mapper.*;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.AliYunOSS;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderExcel2Vo;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderExcelVo;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderLate190ExcelVo;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderSignPOD2ExcelVo;

import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.utils.NBDUtils;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.send.utils.PdfUtils;
import com.jygjexp.jynx.zxoms.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.*;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单
 *
 * <AUTHOR>
 * @date 2024-10-11 20:44:09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NbOrderServiceImpl extends MPJBaseServiceImpl<NbOrderMapper, NbOrderEntity> implements NbOrderService {
    private final NbMerchantService nbMerchantService;
    private final NbSortingCenterService nbSortingCenterService;
    private final NbOrderBatchService nbOrderBatchService;
    private final NbOrderPathService nbOrderPathService;
    private final NbOrderStatusModifyRelService nbOrderStatusModifyRelService;
    private final NbOrderStatusService nbOrderStatusService;
    private final NbOrderUpdateLogService nbOrderUpdateLogService;
    private final NbOrderPathBakService nbOrderPathBakService;
    private final NbOrderCostService nbOrderCostService;
    private final NbShelfPkgLogMapper shelfPkgLogMapper;
    private final NbShelfMapper shelfMapper;
    private final NbDriverMapper nbDriverMapper;
    private final NbOrderSignImageMapper nbOrderSignImageMapper;
    private final RemoteUserService remoteUserService;
    private final NbOrderMpsMapper nbOrderMpsMapper;
    private final NbTransferBatchMapper nbTransferBatchMapper;
    private final NbOrderMapper nbOrderMapper;
    private final NbOrderPathMapper nbOrderPathMapper;
    private final NbTransferCenterMapper nbTransferCenterMapper;
    private final CommonDataUtil commonDataUtil;
    private final RedisUtil redisUtil;
    private final  NbPostalCodeGroupMapper nbPostalCodeGroupMapper;
    private final NbPostalCodeDetailMapper nbPostalCodeDetailMapper;
    private final NbWeightGroupMapper nbWeightGroupMapper;
    private final NbWeightDetailMapper nbWeightDetailMapper;
    private final NbPaisongExpenseRuleMapper nbPaisongExpenseRuleMapper;

    private final RedisTemplate<String, Object> redisTemplate;

    // 调用 MongoDB 处理操作日志
//    private final OperationService operationService;

    @Value("${pdf.templatePath}")
    private String templatePdfPath;   // 绝对路径：/home/<USER>/backend/tpl/20250327_label_normal.pdf

    // 佳邮查询轨迹接口
    private final String apiUrl = "http://api.jygjexp.com/v1/api/tracking/query/trackNB";
    // 佳邮查询轨迹密钥
    private final String apiKey = "675bfe2fd67105e9a88e564bf0f0344c";

//    @Override
//    public R processFileTest(MultipartFile file, Boolean checkCustomerNoRepeat) {
//        if (file == null || file.isEmpty() || !isExcelFile(file.getOriginalFilename())) {
////            String message = getMessage("error.invalid.file", null);
//            return LocalizedR.failed("error.invalid.file", Optional.ofNullable(null));
//        }
//        // 其他逻辑
//        return LocalizedR.ok("file.upload.success", Optional.ofNullable(null));
//    }

    // 导入订单
    @Override
    public R processFile(MultipartFile file, Boolean checkCustomerNoRepeat) {
        // 验证文件类型和空文件
        if (file == null || file.isEmpty() || !isExcelFile(file.getOriginalFilename())) {
            return LocalizedR.failed("nborder.upload.valid.file", Optional.ofNullable(null));
        }
        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<List<Object>> lines = reader.read(1, reader.getRowCount());
            // 验证Excel数据是否为空
            if (lines == null || lines.isEmpty()) {
                return LocalizedR.failed("nborder.upload.empty.data", Optional.ofNullable(null));
            }

            // 创建集合来记录所有问题的字段
            List<String> errorMessages = new ArrayList<>();
            Map<Integer, NbMerchantEntity> merchantMap = new HashMap<>();
            Map<Integer, NbSortingCenterEntity> sortingMap = new HashMap<>();
            List<String> customerNoUnique = new ArrayList<>();
            List<String> pkgNoUnique = new ArrayList<>();
            List<NbOrderEntity> orderList = new ArrayList<>();
            Map<Integer, List<String>> customerNoMap = new HashMap<>();

            for (int i = 0; i < lines.size(); i++) {
                List<Object> line = lines.get(i);
                log.info("Processing line {}: {}", i, line);

                // 每一行的错误集合
                List<String> lineErrors = new ArrayList<>();
                // 验证字段
                Integer merchantId = validateIntegerField(line, 0, "Merchant ID cannot be empty/商家ID不能为空", i + 1, lineErrors);
                String scCode = validateStringField(line, 1, "The sorting center code cannot be empty/分拣中心代码不能为空", i + 1, lineErrors);
                NbSortingCenterEntity sc = nbSortingCenterService.getOne(new LambdaQueryWrapper<NbSortingCenterEntity>().eq(NbSortingCenterEntity::getScCode, scCode), false);
                Integer scId = sc.getScId()!=null?sc.getScId():0;
                String customerPNo = null;
                if (line.get(2) != null) {
                    customerPNo = line.get(2).toString();
                }

                String customerNo = validateStringField(line, 3, "Customer order number cannot be empty/客户订单号不能为空", i + 1, lineErrors);
                List<String> customerNoList = customerNoMap.get(merchantId);
                if (customerNoList == null) {
                    customerNoList = Lists.newArrayList();
                }
                customerNoList.add(customerNo);
                customerNoMap.put(merchantId, customerNoList);
                // 收件人
                String pkgNo = validateStringField(line, 4, "The package number cannot be empty/包裹号不能为空", i + 1, lineErrors);
                String destName = validateStringField(line, 5, "The recipient's name cannot be empty/收件人姓名不能为空", i + 1, lineErrors);
                String destTel = validateStringField(line, 6, "The recipient's phone number cannot be empty/收件人电话不能为空", i + 1, lineErrors);
                String destProvince = validateStringField(line, 7, "The recipient's province cannot be empty/收件人省不能为空", i + 1, lineErrors);
                String destCity = validateStringField(line, 8, "The recipient city cannot be empty/收件人市不能为空", i + 1, lineErrors);
                String destAddress = validateStringField(line, 9, "The recipient's address cannot be empty/收件人地址不能为空", i + 1, lineErrors);
                String destPostalCode = validateStringField(line, 10, "The recipient's postal code cannot be empty/收件人邮编不能为空", i + 1, lineErrors);
                String destEmail = validateStringField(line, 11, "The recipient's email cannot be empty/收件人邮箱不能为空", i + 1, lineErrors);
                // 发件人
                String senderName = validateStringField(line, 12, "The sender cannot be empty/发件人不能为空", i + 1, lineErrors);
                String senderTel = validateStringField(line, 13, "The sender's phone number cannot be empty/发件人电话不能为空", i + 1, lineErrors);
                String senderCountry = validateStringField(line, 14, "The sender's country cannot be empty/发件人国不能为空", i + 1, lineErrors);
                String senderProvince = validateStringField(line, 15, "The sender's province cannot be empty/发件人省不能为空", i + 1, lineErrors);
                String senderCity = validateStringField(line, 16, "The sender's city cannot be empty/发件人市不能为空", i + 1, lineErrors);
                String senderAddress = validateStringField(line, 17, "The sender's address cannot be empty/发件人地址不能为空", i + 1, lineErrors);
                String senderPostalCode = validateStringField(line, 18, "The sender's postal code cannot be empty/发件人邮编不能为空", i + 1, lineErrors);
                // 包裹
                BigDecimal weight = validateBigDecimalField(line, 19, "The weight of the package cannot be empty/包裹重量不能为空", i + 1, lineErrors);
                BigDecimal length = validateBigDecimalField(line, 20, "The package length cannot be empty/包裹长不能为空", i + 1, lineErrors);
                BigDecimal width = validateBigDecimalField(line, 21, "The package width cannot be empty/包裹宽不能为空", i + 1, lineErrors);
                BigDecimal height = validateBigDecimalField(line, 22, "The package height cannot be empty/包裹高不能为空", i + 1, lineErrors);
                BigDecimal pkgValue = validateBigDecimalField(line, 23, "The declared price of the package cannot be empty/包裹申报价格不能为空", i + 1, lineErrors);
                String goodsDesc = validateStringField(line, 24, "The product description cannot be empty/货品描述不能为空", i + 1, lineErrors);
                if (goodsDesc.length() > 500) {
                    lineErrors.add("The product description cannot exceed 500 characters/货品描述不能超过500个字符");
                }
                // 配送类型
                Integer selfPickup = validateSelfPickup(line, 25, i + 1, lineErrors);

                NbSortingCenterEntity soring;
                if (sortingMap.containsKey(scId)) {
                    soring = sortingMap.get(scId);
                } else {
                    soring = nbSortingCenterService.getById(scId);
                    if (soring == null) {
                        lineErrors.add("sortingcenter does not exist/分拣中心不存在");
                    }
                    sortingMap.put(scId, soring);
                }

                NbOrderBatchEntity useBatch = null;
                String batchNo = line.size() == 27 ? line.get(26).toString() : null;
                if (StringUtils.isNotBlank(batchNo)) {
                    batchNo = batchNo.trim();
                    // 存在批次号时，需要检查批次号是否可用
                    useBatch = nbOrderBatchService.getOne(new LambdaQueryWrapper<NbOrderBatchEntity>().eq(NbOrderBatchEntity::getBatchNo, batchNo));
                    if (useBatch == null) {
//					renderJson(Easy.fail("指定了批次号，但是批次号不存在：" + batchNo + ",行：" + i));
//					return;
                        useBatch = new NbOrderBatchEntity();
                        useBatch.setBatchNo(batchNo);
                        useBatch.setBatchTotal(0);
                        useBatch.setImpTotal(0);
                        useBatch.setCreateTime(NBDUtils.getLocalDate(soring.getScTimezone()));
                        useBatch.setIsValid(true);
                        useBatch.setIsRouted(false);
                        nbOrderBatchService.save(useBatch);
                    }
                }

                NbMerchantEntity merchant;
                if (merchantMap.containsKey(merchantId)) {
                    merchant = merchantMap.get(merchantId);
                } else {
                    merchant = nbMerchantService.getById(merchantId);
                    merchantMap.put(merchantId, merchant);
                    if (merchant == null) {
                        lineErrors.add("merchant does not exist/商家不存在, line: " + i + 1);
                    }
                }

                if (customerNoUnique.contains(customerNo)) {
                    lineErrors.add("Duplicate customer tracking number/客户单号重复, line: " + i + 1);
                } else {
                    customerNoUnique.add(customerNo);
                }
                if (pkgNoUnique.contains(pkgNo)) {
                    lineErrors.add("Duplicate package number/包裹号重复, line: " + i + 1);
                } else {
                    pkgNoUnique.add(pkgNo);
                }

                NbOrderEntity checkExist = getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getMerchantId, merchantId).eq(NbOrderEntity::getPkgNo, pkgNo), false);
                if (checkExist != null) {
                    lineErrors.add("Duplicate package number/包裹号重复, line: " + i + 1);
                }

                // 如果当前行有错误，跳过该行
                if (!lineErrors.isEmpty()) {
                    errorMessages.addAll(lineErrors);  // 将当前行的所有错误添加到全局错误信息列表中
                } else {
                    NbOrderEntity order = new NbOrderEntity();
                    if (useBatch != null) {
                        order.setBatchNo(useBatch.getBatchNo());
                    }
                    order.setMerchantId(merchantId);
                    order.setScId(scId);
                    order.setCustomerOrderNo(customerNo);
                    order.setCustomerPOrderNo(customerPNo);

                    if (customerPNo == null || customerPNo.trim().equals("")) {
                        order.setIsMps(false);  //是否为一票多件
                    } else {
                        order.setIsMps(true);
                    }
                    order.setPkgNo(pkgNo);
                    order.setDestName(destName);
                    order.setDestTel(destTel);
                    order.setDestCountry("CA");
                    order.setDestProvince(destProvince);
                    order.setDestCity(destCity);
                    order.setDestAddress1(destAddress);
                    order.setDestPostalCode(destPostalCode);
                    order.setDestEmail(destEmail);

                    order.setPkgWeight(weight);
                    order.setPkgLength(length);
                    order.setPkgWidth(width);
                    order.setPkgHeight(height);
                    order.setPkgValue(pkgValue);
                    order.setGoodsDesc(goodsDesc);

                    order.setOrderNo("IMP" + DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss") + "" + i);
                    order.setAddTime(NBDUtils.getLocalDate(soring.getScTimezone()));
                    order.setOrderStatus(OrderDto.ORDER_STATUS_190_ORDER_RECEIVED); // 2024-05-19 导入后默认状态为190
                    order.setDriverId(0);
                    order.setRegionId(0);
                    order.setRegionCode("");
                    order.setOrderCategory("");
                    order.setExpressCategory("normal");
                    order.setTransportWay("");
                    order.setDestLat(0d);
                    order.setDestLng(0d);
                    order.setPkgType(1);
                    order.setSysStatus(1);
                    order.setShipperName(senderName);
                    order.setShipperTel(senderTel);
                    order.setShipperCountry(senderCountry);
                    order.setShipperProvince(senderProvince);
                    order.setShipperCity(senderCity);
                    order.setShipperAddress1(senderAddress);
                    order.setShipperPostalCode(senderPostalCode);
                    order.setPickNo("0");
                    order.setExpressType(selfPickup);
                    order.setCarrierId(1);  // 默认承运商为NB本身
                    try {
                        orderList.add(order);
                    } catch (Exception e) {
                        e.printStackTrace();
                        lineErrors.add("add failed/添加失败, line: " + i);
                    }
                }
            }
            // Boolean checkCustomerNoRepeat = getBoolean("checkCustomerNoRepeat", true); // 检查客户单号重复
            if (checkCustomerNoRepeat) {
                Iterator<Map.Entry<Integer, List<String>>> iter = customerNoMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry<Integer, List<String>> entry = iter.next();
                    Integer mid = entry.getKey();
                    String customerNoArr = "'" + StringUtils.join(entry.getValue(), "','") + "'";

                    // select group_concat(customer_order_no) customer_order_no from nb_order where merchant_id = ? and customer_order_no in (" + customerNoArr + ")", mid);
                    String customerOrderNo = findCustomerOrderNoByMidAndCustomerNo(mid, customerNoArr);
                    if (StringUtils.isNotBlank(customerOrderNo)) {
                        return LocalizedR.failed("nborder.Duplicate.customer.tracking.number.continue.uploading", customerOrderNo);
                    }
                }
            }

            // 处理订单信息
            processOrders(orderList, merchantMap);
            //导入成功后，计算订单费用
            computeOrderCost2(orderList);
            //记录成功条数信息
            String successMessage = "Successfully processed: " + orderList.size() + " rows.";
            if (errorMessages.isEmpty()) {
                return LocalizedR.ok("nborder.delivery.note.Excel.file.processing.success", successMessage);
            } else {
                return LocalizedR.failed("nborder.file.processing.errors", successMessage + "<br>Errors:<br>" + errorMessages);
            }
        } catch (IOException e) {
            log.error("派送单Excel文件处理异常", e);
            return LocalizedR.failed("nborder.delivery.note.Excel.file.processing.exception", e.getMessage());
        }
    }

    //派送订单导入成功后，进行订单费用计算
    public Boolean computeOrderCost2(List<NbOrderEntity> orderList) {

        if (null != orderList){
            return Boolean.FALSE;
        }

        //查询派送费用规则，获取相应的邮编分区和重量段
        NbPaisongExpenseRuleEntity nbPaisongExpenseRule = nbPaisongExpenseRuleMapper.selectOne(new LambdaQueryWrapper<NbPaisongExpenseRuleEntity>()
                .eq(NbPaisongExpenseRuleEntity::getStatus, 1), false);

        //获取邮编分区组
        NbPostalCodeGroupEntity postalCodeGroup = nbPostalCodeGroupMapper.selectOne(new LambdaQueryWrapper<NbPostalCodeGroupEntity>()
                .eq(NbPostalCodeGroupEntity::getIsValid, 1)
                .eq(NbPostalCodeGroupEntity::getId,nbPaisongExpenseRule.getPostalId()),false);

        List<NbPostalCodeDetailEntity> postalCodeDetail = new ArrayList<>();
        if (null != postalCodeGroup.getId()){
            //获取邮编分区明细
            postalCodeDetail = nbPostalCodeDetailMapper.selectList(new LambdaQueryWrapper<NbPostalCodeDetailEntity>()
                    .eq(NbPostalCodeDetailEntity::getGroupId, postalCodeGroup.getId()));
        }
        //获取重量段
        NbWeightGroupEntity weightGroup = nbWeightGroupMapper.selectOne(new LambdaQueryWrapper<NbWeightGroupEntity>()
                .eq(NbWeightGroupEntity::getStatus, 1)
                .eq(NbWeightGroupEntity::getId,nbPaisongExpenseRule.getWeightId()),false);

        List<NbWeightDetailEntity> weightDetail = new ArrayList<>();
        if (null != weightGroup.getId()) {
            //获取重量段明细
            weightDetail = nbWeightDetailMapper.selectList(new LambdaQueryWrapper<NbWeightDetailEntity>()
                    .eq(NbWeightDetailEntity::getGroupId, weightGroup.getId()));
        }


        //遍历获取邮编分区和价格
        for (NbOrderEntity order : orderList) {
            //获取当前订单的邮编
            String destPostalCode = order.getDestPostalCode();
            //判断该订单的邮编，是否在邮编分区中
            for (NbPostalCodeDetailEntity detail : postalCodeDetail) {
                // 获取起始邮编和结束邮编
                String startPostalCode = detail.getStartPostalCode().replaceAll("\\s", "").toUpperCase();
                String endPostalCode = detail.getEndPostalCode().replaceAll("\\s", "").toUpperCase();
                String orderPostalCode = destPostalCode.replaceAll("\\s", "").toUpperCase();
                //比较字符串的字典顺序
                if (orderPostalCode.compareTo(startPostalCode) >= 0 && orderPostalCode.compareTo(endPostalCode) <= 0) {
                    //获取该订单的重量
                    BigDecimal weight = order.getPkgWeight();
                    //先判断重量是否合理，重泡比，体积/6000<重量
                    BigDecimal isMultiply = order.getPkgLength().multiply(order.getPkgWidth()).multiply(order.getPkgHeight());
                    if(isMultiply.compareTo(weight)>0){
                        order.setOrderRemark("(The weight ratio cannot be greater than the weight)重泡比不能大于重量");
                        nbOrderMapper.updateById(order);
                        continue;
                    }
                    //判断该订单的重量，是否在重量段中
                    for (NbWeightDetailEntity weightDetailEntity : weightDetail) {
                        if (weight.compareTo(weightDetailEntity.getStartWeight()) >= 0 && weight.compareTo(weightDetailEntity.getEndWeight()) <= 0) {
                            //计算价格
                            BigDecimal orderCost = weightDetailEntity.getPieceWeight();
                            //System.out.println("重量明细："+weightDetailEntity.getStartWeight()+"-"+weightDetailEntity.getEndWeight()+"--匹配成功");
                            //将计算后的价格更新到订单中
                            order.setOrderCost(orderCost);
                            nbOrderMapper.updateById(order);
                            break;
                        }
                    }
                    //System.out.println("邮编分区："+detail.getStartPostalCode()+"-"+detail.getEndPostalCode()+"--匹配成功");
                    break;
                }else{       //如果未匹配到邮编，则添加订单备注告知客户原因
                    order.setOrderRemark("(Order cost calculation: This postcode is not covered)订单费用计算：该邮编未被覆盖");
                    nbOrderMapper.updateById(order);
                }

            }
        }
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public void processOrders(List<NbOrderEntity> orderList, Map<Integer, NbMerchantEntity> merchantMap) {
        String ymdStr = DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss");
        NbOrderBatchEntity newBatch = null;
        Set<String> batchNoSet = new HashSet<>();
        String newBatchNo = "IMP" + ymdStr;
        batchNoSet.add(newBatchNo);

        for (NbOrderEntity order : orderList) {
            if (order.getBatchNo() == null) {
                if (newBatch == null) {
                    newBatch = new NbOrderBatchEntity();
                    newBatch.setBatchNo(newBatchNo);
                    newBatch.setBatchTotal(orderList.size());
                    newBatch.setImpTotal(orderList.size());
                    newBatch.setCreateTime(new Date());
                    newBatch.setIsValid(true);
                    newBatch.setIsRouted(false);
                    nbOrderBatchService.save(newBatch);
                }
                order.setBatchNo(newBatch.getBatchNo());
                nbOrderMapper.insert(order);
            } else {
                batchNoSet.add(order.getBatchNo());
                nbOrderMapper.insert(order);
            }
            try {
                NbMerchantEntity merchant = merchantMap.get(order.getMerchantId());
                String address = null;
                String timezone = ZoneId.systemDefault().getId();
                if (merchant.getDefaultTimezone() != null) {
                    timezone = merchant.getDefaultTimezone();
                }

                NbOrderPathEntity op190 = new OrderPathDto().build(order.getOrderId(), OrderDto.ORDER_STATUS_190_ORDER_RECEIVED, 0, 0d, 0d, address, timezone);
                nbOrderPathService.save(op190);
//						OrderPath op199 = new OrderPath().build(order.getOrderId(), Order.ORDER_STATUS_199_GATEWAY_TRANSIT, 0, 0d, 0d, address, ZoneId.systemDefault().getId());
//						op199.save();
                // 2024-05-16 sophie 上传时取消自动生成199状态
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        Iterator<String> iter = batchNoSet.iterator();
        while (iter.hasNext()) {
            String batchNo = iter.next();
            // "select count(1) cont from nb_order where batch_no = ? limit 1", batchNo).getInt("cont");
            long countLong = count(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getBatchNo, batchNo));
            Integer count = Math.toIntExact(countLong);
            NbOrderBatchEntity ob = nbOrderBatchService.getOne(new LambdaQueryWrapper<NbOrderBatchEntity>().eq(NbOrderBatchEntity::getBatchNo, batchNo));
            if (ob != null) {
                ob.setImpTotal(count);
                ob.setBatchTotal(count);
                nbOrderBatchService.updateById(ob);
            }
        }
    }

    @Override
    public String findCustomerOrderNoByMidAndCustomerNo(Integer mid, String customerNoArr) {
        List<String> customerNoList = Arrays.asList(customerNoArr.split(","));
        return nbOrderMapper.findCustomerOrderNosByMidAndCustomerNo(mid, customerNoList);
    }

    @Override
    public List<NbOrderEntity> list(String batchNo) {
        LambdaQueryWrapper<NbOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtils.isNotEmpty(batchNo), NbOrderEntity::getBatchNo, batchNo);
        return list(queryWrapper);
    }

    @Override
    public List<NbOrderEntity> findOrderByUniDeliveredAndStatus190(String before5Days, String last30Days) {
        LambdaQueryWrapper<NbOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.gt(NbOrderEntity::getOrderId, 1365632)
                .eq(NbOrderEntity::getMerchantId, 1001)
                .eq(NbOrderEntity::getUniDelivered, false)
                .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_190_ORDER_RECEIVED)
                .lt(NbOrderEntity::getAddTime, before5Days)
                .ge(NbOrderEntity::getAddTime, last30Days)
                .last("limit 5000");
        return list(queryWrapper);
    }

    /**
     * 分页查询滞留订单 当前时间的6天之前   元数据编码nb_order_late190
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<OrderLate190PageVo> pageOrderLate190(Page page, OrderLate190PageVo vo) {
        // select * from nb_order where order_status = 190 and add_time < date_sub(curDate(), interval 6 day) order by order_id desc
        // select  merchant_id ID, name CN from nb_merchant; ds=nbd;
        // select driver_id, first_name, last_name, email, mobile from nb_driver; ds=nbd;
        // select sc_id id, center_name cn from nb_sorting_center; ds=nbd;
        // select tc_id id, center_name cn from nb_transfer_center; ds=nbd;
        // select id ID,title CN from nb_order_status; ds=nbd;
        MPJLambdaWrapper orderLate190Wrapper = getOrderLate190Wrapper(vo, null);
        return nbOrderMapper.selectJoinPage(page, OrderLate190PageVo.class, orderLate190Wrapper);
    }

    // 导出滞留订单Excel
    @Override
    public List<NbOrderLate190ExcelVo> getOrderLate190Excel(OrderLate190PageVo vo, Integer[] ids) {
        MPJLambdaWrapper wrapper = getOrderLate190Wrapper(vo, ids);
        return nbOrderMapper.selectJoinList(NbOrderLate190ExcelVo.class, wrapper);
    }

    private MPJLambdaWrapper getOrderLate190Wrapper(OrderLate190PageVo vo, Integer[] ids) {
        LocalDateTime sixDaysAgo = LocalDateTime.now().minusDays(6);
        MPJLambdaWrapper<NbOrderEntity> wrapper = new MPJLambdaWrapper<NbOrderEntity>();
        wrapper.selectAll(NbOrderEntity.class)
                .select(NbMerchantEntity::getMerchantId).selectAs(NbMerchantEntity::getName, OrderLate190PageVo.Fields.merchantName)
                .select(NbDriverEntity::getDriverId, NbDriverEntity::getFirstName, NbDriverEntity::getLastName, NbDriverEntity::getEmail, NbDriverEntity::getMobile)
                .selectAs(NbDriverEntity::getDriverName, OrderLate190PageVo.Fields.driverName)
                .select(NbSortingCenterEntity::getScId).selectAs(NbSortingCenterEntity::getCenterName, OrderLate190PageVo.Fields.sortingCenterName)
                .select(NbTransferCenterEntity::getTcId).selectAs(NbTransferCenterEntity::getCenterName, OrderLate190PageVo.Fields.transferCenterName)
                .select(NbOrderStatusEntity::getId).selectAs(NbOrderStatusEntity::getTitle, OrderLate190PageVo.Fields.orderStatusTitle)
                .leftJoin(NbMerchantEntity.class, NbMerchantEntity::getMerchantId, NbOrderEntity::getMerchantId)
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbOrderEntity::getDriverId)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbOrderEntity::getScId)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbOrderEntity::getTcId)
                .leftJoin(NbOrderStatusEntity.class, NbOrderStatusEntity::getId, NbOrderEntity::getOrderStatus)
                .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_190_ORDER_RECEIVED)
                .lt(NbOrderEntity::getAddTime, sixDaysAgo)
                .orderByDesc(NbOrderEntity::getOrderId);

        wrapper.eq(ObjectUtil.isNotNull(vo.getOrderId()), NbOrderEntity::getOrderId, vo.getOrderId())       // 条件查询-订单ID
                .eq(ObjectUtil.isNotNull(vo.getMerchantId()), NbOrderEntity::getMerchantId, vo.getMerchantId()) // 条件查询-商户ID
                .eq(ObjectUtil.isNotNull(vo.getScId()), NbOrderEntity::getScId, vo.getScId())       // 条件查询-分拣中心
                .eq(ObjectUtil.isNotNull(vo.getTcId()), NbOrderEntity::getTcId, vo.getTcId())       // 条件查询-转运中心
                .eq(ObjectUtil.isNotNull(vo.getDriverId()), NbOrderEntity::getDriverId, vo.getDriverId())   // 条件查询-司机
                .like(StrUtil.isNotBlank(vo.getOrderNo()), NbOrderEntity::getOrderNo, vo.getOrderNo())      // 条件查询-订单编号
                .like(StrUtil.isNotBlank(vo.getPkgNo()), NbOrderEntity::getPkgNo, vo.getPkgNo())            // 条件查询-包裹编号
                .like(StrUtil.isNotBlank(vo.getBatchNo()), NbOrderEntity::getBatchNo, vo.getBatchNo())  // 条件查询-批次号
                .like(StrUtil.isNotBlank(vo.getSubBatchNo()), NbOrderEntity::getSubBatchNo, vo.getSubBatchNo()) // 条件查询-子批次号
                .like(StrUtil.isNotBlank(vo.getCustomerOrderNo()), NbOrderEntity::getCustomerOrderNo, vo.getCustomerOrderNo()) // 条件查询-客户单号
                .like(StrUtil.isNotBlank(vo.getCustomerPOrderNo()), NbOrderEntity::getCustomerPOrderNo, vo.getCustomerPOrderNo()) // 条件查询-客户主订单号
                .eq(ObjectUtil.isNotNull(vo.getExpressType()), NbOrderEntity::getExpressType, vo.getExpressType())  // 条件查询-快递类型
                .eq(ObjectUtil.isNotNull(vo.getBagNumber()), NbOrderEntity::getBagNumber, vo.getBagNumber())        // 条件查询-bagNumber
                .eq(ObjectUtil.isNotNull(vo.getSubChannel()), NbOrderEntity::getSubChannel, vo.getSubChannel())     // 条件查询-子渠道
                .like(StrUtil.isNotBlank(vo.getDestTel()), NbOrderEntity::getDestTel, vo.getDestTel());             // 条件查询-收件人电话

        // ETA时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        String etaDatetime = vo.getEtaTime();
        if (etaDatetime != null) {
            int splitIndex = etaDatetime.indexOf(":", etaDatetime.indexOf(":") + 1) + 3;
            String startPutawayTime = etaDatetime.substring(0, splitIndex);
            String endPutAwayTime = etaDatetime.substring(splitIndex + 1);
            wrapper.ge(NbOrderEntity::getEta, startPutawayTime).le(NbOrderEntity::getEta, endPutAwayTime);          // 条件查询-ETA时间
            wrapper.in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbOrderEntity::getOrderId, ids);
        }
        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbOrderEntity::getScId, idList);
        return wrapper;
    }

    /**
     * 待打印订单分页查询
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<UnprintedOrderPageVo> pageUnprintedOrder(Page page, UnprintedOrderPageVo vo) {
        // select * from nb_order where is_printed = false and in_nb_range in (1,2) order by order_id desc
        //select  merchant_id ID, name CN from nb_merchant; ds=nbd;
        //select driver_id, first_name, last_name, email, mobile from nb_driver; ds=nbd;
        //select sc_id id, center_name cn from nb_sorting_center; ds=nbd;
        //select tc_id id, center_name cn from nb_transfer_center; ds=nbd;
        //select id ID,title CN from nb_order_status; ds=nbd;
        MPJLambdaWrapper<NbOrderEntity> wrapper = new MPJLambdaWrapper<NbOrderEntity>();
//        List<Integer> inNbRangeList = Arrays.asList(1, 2);
        Integer inNbRange = 1;  // 2024-11-27 需求要求，只打印在配送范围内的订单
        wrapper.eq(ObjectUtil.isNotNull(vo.getOrderId()), NbOrderEntity::getOrderId, vo.getOrderId())               // 查询条件-订单ID
                .eq(ObjectUtil.isNotNull(vo.getInNbRange()), NbOrderEntity::getInNbRange, vo.getInNbRange())        // 查询条件-配送范围
                .eq(ObjectUtil.isNotNull(vo.getMerchantId()), NbOrderEntity::getMerchantId, vo.getMerchantId())     // 查询条件-商家
                .eq(ObjectUtil.isNotNull(vo.getScId()), NbOrderEntity::getScId, vo.getScId())                       // 查询条件-分拣中心
                .eq(ObjectUtil.isNotNull(vo.getTcId()), NbOrderEntity::getTcId, vo.getTcId())                       // 查询条件-转运中心
                .like(StrUtil.isNotBlank(vo.getOrderNo()), NbOrderEntity::getOrderNo, vo.getOrderNo())              // 查询条件-订单号
                .like(StrUtil.isNotBlank(vo.getPkgNo()), NbOrderEntity::getPkgNo, vo.getPkgNo())                    // 查询条件-包裹编号
                .like(StrUtil.isNotBlank(vo.getCustomerOrderNo()), NbOrderEntity::getCustomerOrderNo, vo.getCustomerOrderNo())  // 查询条件-客户订单号
                .like(StrUtil.isNotBlank(vo.getCustomerPOrderNo()), NbOrderEntity::getCustomerPOrderNo, vo.getCustomerPOrderNo())   // 查询条件-客户主订单号
                .eq(ObjectUtil.isNotNull(vo.getExpressType()), NbOrderEntity::getExpressType, vo.getExpressType())  // 查询条件-快递类型
                .like(StrUtil.isNotBlank(vo.getBagNumber()), NbOrderEntity::getBagNumber, vo.getBagNumber())        // 查询条件-bagNumber
                .like(StrUtil.isNotBlank(vo.getSubChannel()), NbOrderEntity::getSubChannel, vo.getSubChannel());    // 查询条件-子渠道
        String etaDatetime = vo.getEtaTime();       //  查询条件 ETA时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        if (etaDatetime != null){
            int splitIndex = etaDatetime.indexOf(":", etaDatetime.indexOf(":") + 1) + 3;
            String startPutawayTime = etaDatetime.substring(0, splitIndex);
            String endPutAwayTime = etaDatetime.substring(splitIndex + 1);
            wrapper.ge(NbOrderEntity::getEta, startPutawayTime).le(NbOrderEntity::getEta, endPutAwayTime);
        }
        wrapper.like(StrUtil.isNotBlank(vo.getDestTel()), NbOrderEntity::getDestTel, vo.getDestTel())       // 查询条件-收件人电话

                .selectAll(NbOrderEntity.class)
                .select(NbMerchantEntity::getMerchantId).selectAs(NbMerchantEntity::getName, UnprintedOrderPageVo.Fields.merchantName)
                .select(NbDriverEntity::getDriverId, NbDriverEntity::getFirstName, NbDriverEntity::getLastName, NbDriverEntity::getEmail, NbDriverEntity::getMobile)
                .selectAs(NbDriverEntity::getDriverName, UnprintedOrderPageVo.Fields.driverName)
                .select(NbSortingCenterEntity::getScId).selectAs(NbSortingCenterEntity::getCenterName, UnprintedOrderPageVo.Fields.sortingCenterName)
                .select(NbTransferCenterEntity::getTcId).selectAs(NbTransferCenterEntity::getCenterName, UnprintedOrderPageVo.Fields.transferCenterName)
                .select(NbOrderStatusEntity::getId).selectAs(NbOrderStatusEntity::getTitle, UnprintedOrderPageVo.Fields.orderStatusTitle)

                .leftJoin(NbMerchantEntity.class, NbMerchantEntity::getMerchantId, NbOrderEntity::getMerchantId)
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbOrderEntity::getDriverId)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbOrderEntity::getScId)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbOrderEntity::getTcId)
                .leftJoin(NbOrderStatusEntity.class, NbOrderStatusEntity::getId, NbOrderEntity::getOrderStatus)
                .eq(NbOrderEntity::getIsPrinted, false)
                .eq(NbOrderEntity::getInNbRange, inNbRange) // 2024-11-27 需求要求，只打印在配送范围内的订单
                .orderByDesc(NbOrderEntity::getOrderId);
        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbOrderEntity::getScId, idList);
        return nbOrderMapper.selectJoinPage(page, UnprintedOrderPageVo.class, wrapper);
    }

    /**
     * POD2分页查询
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<OrderSignPOD2PageVo> pageOrderSignPOD2(Page page, OrderSignPOD2PageVo vo) {
        MPJLambdaWrapper pod2Wrapper = getPOD2Wrapper(vo, null);

        // 分页查询主表数据
        Page<OrderSignPOD2PageVo> dtoPage = nbOrderMapper.selectJoinPage(page, OrderSignPOD2PageVo.class, pod2Wrapper);
        List<OrderSignPOD2PageVo> records = dtoPage.getRecords();

        // 获取所有 orderId，并一次性查询对应的签收图片数据
        Set<Integer> orderIds = records.stream().map(OrderSignPOD2PageVo::getOrderId).collect(Collectors.toSet());
        Map<Integer, List<NbOrderSignImageEntity>> orderSignImageMap = orderIds.isEmpty() ? Collections.emptyMap()
                : nbOrderSignImageMapper.selectList(new LambdaQueryWrapper<NbOrderSignImageEntity>()
                        .in(NbOrderSignImageEntity::getOrderId, orderIds)
                        .orderByDesc(NbOrderSignImageEntity::getImageId))
                .stream().collect(Collectors.groupingBy(NbOrderSignImageEntity::getOrderId));

        // 收集 DriverId 和 StaffId
        Set<Integer> driverIds = new HashSet<>();
        Set<Long> staffIds = new HashSet<>();
        orderSignImageMap.values().forEach(images -> {
            for (NbOrderSignImageEntity image : images) {
                if (image.getDriverId() > 0) driverIds.add(image.getDriverId());
                if (image.getStaffId() > 0) staffIds.add(image.getStaffId().longValue());
            }
        });

        // 一次性查询 Driver 和 Staff 数据
        Map<Integer, NbDriverEntity> driverMap = driverIds.isEmpty() ? Collections.emptyMap() :
                nbDriverMapper.selectBatchIds(driverIds).stream().collect(Collectors.toMap(NbDriverEntity::getDriverId, d -> d));
        Map<Long, SysUser> staffMap = staffIds.isEmpty() ? Collections.emptyMap() :
                remoteUserService.getUserById(new ArrayList<>(staffIds)).getData().stream()
                        .collect(Collectors.toMap(SysUser::getUserId, u -> u));

        // 组装数据
        for (OrderSignPOD2PageVo pageVo : records) {
            List<NbOrderSignImageEntity> images = orderSignImageMap.getOrDefault(pageVo.getOrderId(), Collections.emptyList());
            // 限制到最多5条签收图片表查询记录，一般情况下司机操作5次，故此处限制最多5条
            List<NbOrderSignImageEntity> limitedImages = images.size() > 5 ? images.subList(0, 5) : images;

            List<OrderSignPOD2Dto> pod2DtoList = new ArrayList<>();
            for (int i = 0; i < limitedImages.size(); i++) {
                NbOrderSignImageEntity image = limitedImages.get(i);
                OrderSignPOD2Dto dto = new OrderSignPOD2Dto();
                dto.setVPkgurl(image.getPkgImage());
                dto.setVPuturl(image.getPutImage());
                dto.setVAddtime(DateFormatUtils.format(image.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
                dto.setVLatlng(image.getLng() + "," + image.getLat());
                dto.setVDistance(image.getDistance());
                dto.setVDriver(driverMap.containsKey(image.getDriverId()) ?
                        driverMap.get(image.getDriverId()).getFirstName() : "未知司机");
                dto.setVStaff(staffMap.containsKey(image.getStaffId().longValue()) ?
                        staffMap.get(image.getStaffId().longValue()).getUserId().intValue() : 0);
                pod2DtoList.add(dto);
            }
            pageVo.setSignPOD2Dtos(pod2DtoList);
        }

        return dtoPage;
    }

    // 获取POD2导出数据
    @Override
    public List<NbOrderSignPOD2ExcelVo> getPOD2Excel(OrderSignPOD2PageVo vo, Integer[] ids) {
        MPJLambdaWrapper pod2Wrapper = getPOD2Wrapper(vo, ids);
        return nbOrderMapper.selectJoinList(NbOrderSignPOD2ExcelVo.class, pod2Wrapper);
    }

    private MPJLambdaWrapper getPOD2Wrapper(OrderSignPOD2PageVo vo, Integer[] ids) {
        MPJLambdaWrapper<NbOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(NbOrderEntity::getOrderId, NbOrderEntity::getOrderNo, NbOrderEntity::getPkgNo, NbOrderEntity::getOrderStatus)
                .select(NbOrderSignImageEntity::getPkgImage, NbOrderSignImageEntity::getPutImage, NbOrderSignImageEntity::getAddTime,
                        NbOrderSignImageEntity::getDistance, NbOrderSignImageEntity::getDriverId, NbOrderSignImageEntity::getStaffId,
                        NbOrderSignImageEntity::getLng, NbOrderSignImageEntity::getLat)
                .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_205_DELIVERED)
                .leftJoin(NbOrderSignImageEntity.class, NbOrderSignImageEntity::getOrderId, NbOrderEntity::getOrderId)
                .like(StrUtil.isNotBlank(vo.getPkgNo()), NbOrderEntity::getPkgNo, vo.getPkgNo())            // 条件查询-包裹号
                .eq(ObjectUtil.isNotNull(vo.getOrderId()), NbOrderEntity::getOrderId, vo.getOrderId())      // 条件查询-订单ID
                .eq(ObjectUtil.isNotNull(vo.getDriverId()), NbOrderSignImageEntity::getDriverId, vo.getDriverId());   // 条件查询-司机ID

        String signTime = vo.getSignTime();   //签收时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        if (signTime != null) {
            int splitIndex = signTime.indexOf(":", signTime.indexOf(":") + 1) + 3;
            String startSignTime = signTime.substring(0, splitIndex);
            String endSignTime = signTime.substring(splitIndex + 1);
            wrapper.ge(NbOrderSignImageEntity::getAddTime, startSignTime).le(NbOrderSignImageEntity::getAddTime, endSignTime);    // 条件查询-签收时间
        }
        wrapper.orderByDesc(NbOrderEntity::getOrderId)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbOrderEntity::getOrderId, ids);

        SysUser user = remoteUserService.getOneUserById(SecurityUtils.getUser().getId()).getData();
        String nbdScId = user.getNbdScId();     //获取用户登录下的分拣中心
        if (StringUtils.isNotBlank(nbdScId)) {
            // 如果该用户配置了分拣中心，则取该用户下的分拣中心关联
            List<Integer> idList = Arrays.stream(nbdScId.split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
            wrapper.in(NbOrderEntity::getScId, idList);
        } else {
            // 如果该用户没有配置分拣中心，则取所有有订单数据的分拣中心关联，避免索引失效走全表扫描
            MPJLambdaWrapper<NbSortingCenterEntity> wrapper1 = new MPJLambdaWrapper<>();
            // 去重，避免重复的分拣中心
            wrapper1.leftJoin(NbOrderEntity.class, NbOrderEntity::getScId, NbSortingCenterEntity::getScId).select(NbSortingCenterEntity::getScId).distinct();
            List<NbSortingCenterEntity> scIdList = nbSortingCenterService.list(wrapper1);
            if (scIdList.size() == 0) {
                return wrapper;
            }
            List<Integer> collectScId = scIdList.stream().map(NbSortingCenterEntity::getScId).collect(Collectors.toList());
            wrapper.in(NbOrderEntity::getScId, collectScId);
        }

        return wrapper;
    }

    private Double getDouble(List<Object> line, int index) {
        Object obj = line.get(index);
        if (obj == null) {
            return null;
        }
        try {
            return Double.valueOf(obj.toString());
        } catch (Exception e) {
            e.printStackTrace();

            return null;
        }
    }

    // 展示批次内所有条码
    @Override
    public R batchBarList(Integer batchId) {
        NbOrderBatchEntity batch = nbOrderBatchService.getById(batchId);

        List<NbOrderEntity> orders;
        if (batch.getIsMain()) {
            orders = list(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getBatchNo, batch.getBatchNo()));
        } else {
            orders = list(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getSubBatchNo, batch.getBatchNo()));
        }
        // setAttr("batch", batch);
        // setAttr("orders", orders);
        Map<String,Object> map = new HashMap<>();
        map.put("batch",batch);
        map.put("orders",orders);
        return R.ok(map);
    }

    private boolean check(List<Object> line, int lineIndex, int rowIndex, String errmsg) {
        Object obj = line.get(lineIndex);
        if (obj == null) {
            R.failed(errmsg + obj + ",行：" + rowIndex);
            return false;
        }
        return true;
    }

    private Integer getInt(List<Object> line, int index) {
        Object obj = line.get(index);
        if (obj == null) {
            return null;
        }
        try {
            return Integer.valueOf(obj.toString());
        } catch (Exception e) {
            e.printStackTrace();

            return null;
        }
    }

    /**
     * 状态变更列表
     * @param orderId
     * @return
     */
    @Override
    public R info(Integer orderId) {
        NbOrderEntity order = getById(orderId);
        // "select * from nb_order_status_modify_rel where from_status = ? limit 1", orderStatus;
//        NbOrderStatusModifyRelEntity rel = nbOrderStatusModifyRelService.findOneByOrderStatus(order.getOrderStatus());
        NbOrderStatusModifyRelEntity rel = nbOrderStatusModifyRelService.getOne(new LambdaQueryWrapper<NbOrderStatusModifyRelEntity>().eq(NbOrderStatusModifyRelEntity::getFromStatus, order.getOrderStatus()), false);
        JSONArray ja = new JSONArray();

        if (rel != null) {
            String toStatus = rel.getToStatus();
            if (StrUtil.isNotBlank(toStatus)) {
                //   select * from nb_order_status where id in (" + toStatus + ")"
//                List<NbOrderStatusEntity> osList = nbOrderStatusService.findByToStatus(toStatus);
                List<NbOrderStatusEntity> osList = nbOrderStatusService.list(new LambdaQueryWrapper<NbOrderStatusEntity>()
                        .in(NbOrderStatusEntity::getId, toStatus).orderByAsc(NbOrderStatusEntity::getId));
                for (NbOrderStatusEntity item : osList) {
                    Integer value = item.getId();
                    String name = item.getTitle();
                    JSONObject jo = new JSONObject();
                    jo.put("name", name);
                    jo.put("value", value);
                    ja.add(jo);
                }
            }
        }
        return R.ok(ja);
    }

    /**
     * 修改订单状态
     * @param orderId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R changeStatus(Integer orderId, Integer targetOrderStatus, String changeRemark) {
        if (targetOrderStatus == null) {
            return LocalizedR.failed("nborder.order.status.not.selected.for.modification", Optional.ofNullable(null));
        }
        NbOrderEntity order = getById(orderId);
        int beforeStatus = order.getOrderStatus();

        // 判断修改的目标状态是否等于当前状态
        if (beforeStatus == targetOrderStatus){
            return R.failed("The current status is:"+targetOrderStatus+"; cannot modify");
        }

        order.setOrderStatus(targetOrderStatus);
        nbOrderMapper.updateById(order);

        NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), 0, 0d, 0d, null, ZoneId.systemDefault().getId());
        R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
        SysUser user = sysUserR.getData();
        op.setStaffId(user.getUserId());
        op.setRemark(changeRemark);

        // 2024-02-22 改到派送成功时，发送短信
        if (targetOrderStatus == OrderDto.ORDER_STATUS_205_DELIVERED) {
            op.setSmsStatus(OrderPathDto.SMS_STATUS_10_UNSEND);
        }
        if (targetOrderStatus == OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1) {
            op.setSmsStatus(OrderPathDto.SMS_STATUS_10_UNSEND);
        }
        nbOrderPathService.save(op);


        NbOrderUpdateLogEntity updateLog = new NbOrderUpdateLogEntity();
        updateLog.setOrderId(order.getOrderId());
        updateLog.setLogTime(new Date());
        updateLog.setAction("订单状态(" + beforeStatus + "->" + targetOrderStatus + ")," + user.getUserId());
        updateLog.setLogType(1);
        updateLog.setColnumName("order_status");
        updateLog.setAdminId(SecurityUtils.getUser().getId());
        updateLog.setRemark(changeRemark);
        nbOrderUpdateLogService.save(updateLog);

        return R.ok();
    }


    /**
     * 批量修改订单状态
     * @param orderIds
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R updateStatusByIds(List<Integer> orderIds, Integer targetOrderStatus) {
        if (targetOrderStatus == null) {
            return LocalizedR.failed("nborder.order.status.not.selected.for.modification", Optional.ofNullable(null));
        }

        // 获取当前用户信息
        R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
        SysUser user = sysUserR.getData();

        // 用于批量保存路径信息和日志
        List<NbOrderPathEntity> orderPaths = new ArrayList<>();
        List<NbOrderUpdateLogEntity> updateLogs = new ArrayList<>();

        // 用于批量保存200路径信息和日志
//        List<NbOrderPathEntity> orderPaths200 = new ArrayList<>();
//        List<NbOrderUpdateLogEntity> updateLogs200 = new ArrayList<>();

        for (Integer orderId : orderIds) {
            NbOrderEntity order = getById(orderId);
            int beforeStatus = order.getOrderStatus();

            // 更新订单状态
            order.setOrderStatus(targetOrderStatus);
            nbOrderMapper.updateById(order);

            // 自动构建目标状态200路径信息
//            NbOrderPathEntity op200 = new OrderPathDto().build(order.getOrderId(), OrderDto.ORDER_STATUS_200_PARCEL_SCANNED, 0, 0d, 0d, null, ZoneId.systemDefault().getId());
//            op200.setStaffId(user.getUserId());
//            orderPaths200.add(op200);

            // 构建目标状态路径信息
            NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), 0, 0d, 0d, null, ZoneId.systemDefault().getId());
            op.setStaffId(user.getUserId());
            op.setScId(order.getScId());
            op.setTcId(order.getTcId());

            // 2024-02-22 改到派送成功时，发送短信
            if (targetOrderStatus == OrderDto.ORDER_STATUS_205_DELIVERED) {
                op.setSmsStatus(OrderPathDto.SMS_STATUS_10_UNSEND);
            }
            if (targetOrderStatus == OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1) {
                op.setSmsStatus(OrderPathDto.SMS_STATUS_10_UNSEND);
            }
            orderPaths.add(op);

            // 构建200更新日志
//            NbOrderUpdateLogEntity updateLog200 = new NbOrderUpdateLogEntity();
//            updateLog200.setOrderId(order.getOrderId());
//            updateLog200.setLogTime(new Date());
//            updateLog200.setAction("订单状态(" + 190 + "->" + 200 + ")," + user.getUserId());
//            updateLog200.setLogType(1);
//            updateLog200.setColnumName("order_status");
//            updateLog200.setAdminId(SecurityUtils.getUser().getId());
//            updateLogs200.add(updateLog200);

            // 构建更新日志
            NbOrderUpdateLogEntity updateLog = new NbOrderUpdateLogEntity();
            updateLog.setOrderId(order.getOrderId());
            updateLog.setLogTime(new Date());
            updateLog.setAction("订单状态(" + beforeStatus + "->" + targetOrderStatus + ")," + user.getUserId());
            updateLog.setLogType(1);
            updateLog.setColnumName("order_status");
            updateLog.setAdminId(SecurityUtils.getUser().getId());
            updateLogs.add(updateLog);
        }

        // 批量保存200路径信息和日志
//        if (!orderPaths200.isEmpty()) {
//            nbOrderPathService.saveBatch(orderPaths200);
//        }
//        if (!updateLogs200.isEmpty()) {
//            nbOrderUpdateLogService.saveBatch(updateLogs200);
//        }

        // 批量保存路径信息和日志
        if (!orderPaths.isEmpty()) {
            nbOrderPathService.saveBatch(orderPaths);
        }
        if (!updateLogs.isEmpty()) {
            nbOrderUpdateLogService.saveBatch(updateLogs);
        }

        return R.ok();
    }




    /**
     * 可回退状态列表
     * @param orderId
     * @return
     */
    @Override
    public R statusReturnEnableList(Integer orderId) {
        //  select op.*, os.title title from nb_order_path op, nb_order_status os where op.order_status = os.id and op.order_id = ? order by op.path_id asc
        List<OrderPathDto> paths = nbOrderPathMapper.findStatusReturnEnableList(orderId);
        JSONArray ja = new JSONArray();
        for (int i=0; i<paths.size() - 1; i++) {
            OrderPathDto path = paths.get(i);
            JSONObject jo = new JSONObject();
            jo.put("id", path.getOrderStatus());
            jo.put("title", path.getTitle());

            ja.add(jo);
        }
        return R.ok(ja);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R statusReturn(Integer orderId, Integer targetOrderStatus, String changeRemark) {
        if (targetOrderStatus == null) {
            return LocalizedR.failed("nborder.order.status.not.selected.for.modification", Optional.ofNullable(null));
        }
        NbOrderEntity order = getById(orderId);
        List<NbOrderPathEntity> paths = nbOrderPathService.findByOrderIdAsc(orderId);

        R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
        SysUser user = sysUserR.getData();

        boolean startDel = false;
        for (NbOrderPathEntity path : paths) {
            if (Objects.equals(path.getOrderStatus(), targetOrderStatus)) {
                // 下一个开始删除
                startDel = true;
                continue;
            }
            if (startDel) {
                NbOrderPathBakEntity opb = new NbOrderPathBakEntity();
                opb.setPathId(path.getPathId());
                opb.setOrderId(path.getOrderId());
                opb.setOrderStatus(path.getOrderStatus());
                opb.setAddTime(path.getAddTime());
                opb.setScId(path.getScId());
                opb.setTcId(path.getTcId());
                opb.setDriverId(path.getDriverId());
                opb.setStaffId(path.getStaffId());
                opb.setPathAddr(path.getPathAddr());
                opb.setScanLat(path.getScanLat());
                opb.setScanLng(path.getScanLng());
                opb.setRemark(path.getRemark());
                opb.setCity(path.getCity());
                opb.setAddTimestamp(path.getAddTimestamp());
                opb.setPathTimezone(path.getPathTimezone());
                opb.setSyncJyStatus(path.getSyncJyStatus());
                opb.setSyncTime(path.getSyncTime());
                opb.setSmsStatus(path.getSmsStatus());
                opb.setSmsDate(path.getSmsDate());
                opb.setDistance(path.getDistance());
                opb.setDeleteTime(new Date());
                opb.setDeleteUid(user.getUserId());
                nbOrderPathBakService.save(opb);

                nbOrderPathService.removeById(orderId);
            }
        }

        int beforeStatus = order.getOrderStatus();
        order.setOrderStatus(targetOrderStatus);
        nbOrderMapper.updateById(order);

        NbOrderUpdateLogEntity updateLog = new NbOrderUpdateLogEntity();
        updateLog.setOrderId(order.getOrderId());
        updateLog.setLogTime(new Date());
        updateLog.setAction("订单状态回退(" + beforeStatus + "->" + targetOrderStatus + ")," + user.getUserId());
        updateLog.setLogType(1);
        updateLog.setColnumName("order_status");
        updateLog.setAdminId(user.getUserId());
        updateLog.setRemark(changeRemark);
        nbOrderUpdateLogService.save(updateLog);
        return R.ok();
    }

    /**
     * 拷贝订单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R copy(Integer orderId) {
        String batchNo = "nb_virtual_" + DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMdd");

        NbOrderEntity order = getById(orderId);
        if (order.getHasSubOrder()) {
            return LocalizedR.failed("nborder.no.repeat.create", Optional.ofNullable(null));
        }

        if (order.getOrderStatus() == OrderDto.ORDER_STATUS_205_DELIVERED) {
            return LocalizedR.failed("nborder.order.successfully.delivered", Optional.ofNullable(null));
        }

        if (order.getPOrderId() > 0) {
            // 20240207 二派订单将不再允许再次拷贝订单。
            return LocalizedR.failed("nborder.suborder.cannot.create.suborder", Optional.ofNullable(null));
        }

        NbOrderBatchEntity ob = nbOrderBatchService.getOne(new LambdaQueryWrapper<NbOrderBatchEntity>().eq(NbOrderBatchEntity::getBatchNo, batchNo));
        if (ob == null) {
            ob = new NbOrderBatchEntity();
            ob.setBatchNo(batchNo);
            ob.setCreateTime(new Date());
            ob.setOrderTotal(0);
            ob.setIsValid(true);
            ob.setIsRouted(false);
            ob.setIsMain(true);
            ob.setBatchType(OrderBatchDto.BATCH_TYPE_4_VIRTUAL);
            nbOrderBatchService.save(ob);
        }

        // "select count(1) cont from nb_order where p_order_id = ?", orderId).getInt("cont");
        LambdaQueryWrapper<NbOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NbOrderEntity::getPOrderId, orderId);
        int subOrderTotal = Math.toIntExact(count(queryWrapper));
        String suffix = "_v" + (subOrderTotal + 1);

        order.setOrderId(null);
        order.setPOrderId(orderId);
        order.setOrderNo(order.getOrderNo() + suffix);
        order.setSubBatchNo(null);
        order.setBatchNo(batchNo);
        order.setPkgNo(order.getPkgNo() + suffix);
        order.setCustomerOrderNo(order.getCustomerOrderNo() + suffix);
        order.setOrderStatus(OrderDto.ORDER_STATUS_190_ORDER_RECEIVED);
        order.setAddTime(new Date());
        order.setDriverId(0);
        order.setIsRouted(false);
        order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_0_UNSTART);
        order.setDeliveryTry(0);
        order.setPickNo("0");
        save(order);

        // "select count(1) cont from nb_order where batch_no = ?", batchNo).getInt("cont");
        long countLong = count(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getBatchNo, batchNo));
        Integer orderTotal = Math.toIntExact(countLong);
        ob.setOrderTotal(orderTotal);
        ob.setImpTotal(orderTotal);
        nbOrderBatchService.updateById(ob);

        String timezone = ZoneId.systemDefault().getId();
        NbSortingCenterEntity sc = null;
        if (order.getScId() != null && order.getScId() > 0) {
            sc = nbSortingCenterService.getById(order.getScId());
            if (sc != null) {
                timezone = sc.getScTimezone();
            }
        }

        NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), 0, 0d, 0d, null, timezone);
        nbOrderPathService.save(op);

        NbOrderEntity oldOrder = getById(orderId);
        oldOrder.setOrderStatus(OrderDto.ORDER_STATUS_223_SECOND_DELIVERY_CREATED);
        oldOrder.setHasSubOrder(true);
        nbOrderMapper.updateById(oldOrder);

        String address = null;
        Integer scId = oldOrder.getScId();
        if (sc != null) {
            address = commonDataUtil.getAddress(sc.getProvinceId(), sc.getCityId());
        }

        NbOrderPathEntity op223 = new OrderPathDto().build(oldOrder.getOrderId(), oldOrder.getOrderStatus(), 0, 0d, 0d, address, timezone);
        op223.setScId(oldOrder.getScId());
        nbOrderPathService.save(op223);

        NbOrderCostEntity oc = nbOrderCostService.getById(oldOrder.getOrderId());
        if (oc != null) {
            oc.setNote("创建子订单[" + order.getOrderId() + "]清除补贴");
            oc.setDriverSubsidy(BigDecimal.ZERO);
            oc.setDriverWeightSubsidy(BigDecimal.ZERO);
            nbOrderCostService.updateById(oc);
        }
        return R.ok();
    }

    // 打印电子面单
    @Override
    public void orderLabel(String orderIds, HttpServletResponse response) {
        if (StrUtil.isBlank(orderIds)) {
            LocalizedR.failed("nborder.please.select.the.orderId", Optional.ofNullable(null));
            return;
        }
        String[] orderIdArr = orderIds.split(",");  //  传多个订单ID用逗号隔开
        List<OrderLabelDto> labelDataList = new ArrayList<>();
        Boolean showPdf = null;
        List<String> labels = Lists.newArrayList();
        Map<Integer, NbSortingCenterEntity> scMap = new HashMap<>();
        Map<Integer, NbTransferCenterEntity> tcMap = new HashMap<>();

//        select o.order_id, o.pkg_no from nb_order o left join nb_transfer_center tc on o.tc_id = tc.tc_id where o.order_id in (" + orderIds + ") order by tc.region_code asc
        MPJLambdaWrapper<NbOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(NbOrderEntity::getOrderId, NbOrderEntity::getPkgNo)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbOrderEntity::getTcId)
                .in(NbOrderEntity::getOrderId, orderIdArr)
                .orderByAsc(NbTransferCenterEntity::getRegionCode);
        List<OrderDto> orders = selectJoinList(OrderDto.class, wrapper);
        orderIdArr = orders.stream().map(NbOrderEntity::getOrderId).map(v -> v.toString()).toArray(value -> new String[value]);
        String packageNumber = orders.stream().map(NbOrderEntity::getPkgNo).limit(3).collect(Collectors.joining("-"));
        if (orders.size() > 3) {
            packageNumber += "-etc";
        }
        for (String orderId : orderIdArr) {
            NbOrderEntity order = getById(Integer.valueOf(orderId));
            if (showPdf == null && order.getInNbRange() == 2) {
                showPdf = false; // inNbRange为2： 配送范围之外的 显示网页图片面单
            } else {
                showPdf = true; // 按pdf显示
            }
            if (showPdf) {
                int scId = order.getScId();
                NbSortingCenterEntity sc = nbSortingCenterService.getById(scId);
                if (packageNumber == null) {
                    packageNumber = order.getPkgNo(); // 提取第一个订单的包裹号
                }
                Map<String, String> params = Maps.newHashMap();
                List<BarCodeDto> barCodes = Lists.newArrayList();
                String yyzPostfix = "";
                int tcId = order.getTcId();
                if (tcId > 0) {
                    NbTransferCenterEntity tc = null;
                    if (tcMap.containsKey(tcId)) {
                        tc = tcMap.get(tcId);
                    } else {
                        tc = nbTransferCenterMapper.selectById(tcId);
                        if (tc != null) {
                            tcMap.put(tcId, tc);
                        }
                    }
                    if (tc != null) {
                        yyzPostfix = "-" + tc.getRegionCode();
                    }
                }
                String expresType = OrderDto.expresTypeMap.getOrDefault(order.getExpressType(), "");
                params.put("YYZ", sc.getScCode() + yyzPostfix);
                params.put("to", order.getDestName());
                params.put("toAddress", order.getDestAddress1() + " " + order.getDestCity() + " " + order.getDestProvince() + " " + order.getDestPostalCode() + " " + order.getDestCountry());
                params.put("toPostalCode", order.getDestPostalCode());
                params.put("toMobile", order.getDestTel());
                params.put("expressType", expresType);
                params.put("weight", "Weight:" + order.getPkgWeight());

                NbMerchantEntity merchant = nbMerchantService.getById(order.getMerchantId());
                params.put("from", Optional.ofNullable(merchant.getWhContact()).orElse(""));
                String address = "";
                if (merchant.getWhProvinceId() != null && merchant.getWhCityId() != null && merchant.getWhProvinceId() > 0 && merchant.getWhCityId() > 0) {
                    address = commonDataUtil.getAddress(merchant.getWhProvinceId(), merchant.getWhCityId());
                }
                params.put("fromAddress", merchant.getWhAddress() + " " + address);
                String refNoPosifix = "";
                if (StrUtil.isNotBlank(order.getCustomerPOrderNo())) {
                    //      select order_id, order_no, customer_order_no from nb_order where merchant_id = ? and customer_p_order_no = ? order by order_id asc
                    LambdaQueryWrapper<NbOrderEntity> wrapper1 = Wrappers.lambdaQuery(NbOrderEntity.class);
                    wrapper1.select(NbOrderEntity::getOrderId, NbOrderEntity::getOrderNo, NbOrderEntity::getCustomerOrderNo)
                            .eq(NbOrderEntity::getMerchantId, order.getMerchantId())
                            .eq(NbOrderEntity::getCustomerPOrderNo, order.getCustomerPOrderNo())
                            .orderByAsc(NbOrderEntity::getOrderId);
                    List<NbOrderEntity> pOrders = list(wrapper1);
                    for (int i = 0; i < pOrders.size(); i++) {
                        NbOrderEntity innerOrder = pOrders.get(i);
                        if (order.getCustomerOrderNo().equals(innerOrder.getCustomerOrderNo())) {
                            refNoPosifix = "(" + (i + 1) + "/" + pOrders.size() + ")";
                        }
                    }
                }
                params.put("oriOrderNo", order.getCustomerOrderNo() + refNoPosifix);
                params.put("orderTime", order.getAddTime().toInstant().atZone(ZoneId.of(sc.getScTimezone())).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                params.put("goodsDesc", order.getGoodsDesc());
                params.put("pickNo", order.getPickNo());

                BarCodeDto bar1 = new BarCodeDto(order.getPkgNo(), false, 20f, 50, 20, 295);
                bar1.setFontSize(12f);
                bar1.setBaseLine(16f);
                barCodes.add(bar1);
                BarCodeDto bar2 = new BarCodeDto(order.getPkgNo(), false, 10f, 35, 10, 35);
                bar2.setFontSize(12f);
                bar2.setBaseLine(16f);
                barCodes.add(bar2);
                BarCodeDto bar3 = new BarCodeDto(order.getPkgNo(), false, 0.6f, 110, 160, 20);
                bar3.setType("qr");
                bar3.setFontSize(12f);
                bar3.setBaseLine(16f);
                barCodes.add(bar3);

                labelDataList.add(new OrderLabelDto(params, barCodes));
                order.setIsPrinted(true);
                nbOrderMapper.updateById(order);

            } else {
                String label = order.getThirdLabel();
                if (StrUtil.isNotBlank(label)) {
                    labels.add(label);
                    if (order.getIsPrinted() == false) {
                        order.setIsPrinted(true);
                        order.setOrderStatus(OrderDto.ORDER_STATUS_236_SHIPPING_LABEL_PRINTED);
                        nbOrderMapper.updateById(order);
                        String address = null;
                        String timezone = ZoneId.systemDefault().getId();
                        if (order.getScId() > 0) {
                            NbSortingCenterEntity sc;
                            if (scMap.containsKey(order.getScId())) {
                                sc = scMap.get(order.getScId());
                            } else {
                                sc = nbSortingCenterService.getById(order.getScId());
                                if (sc != null) {
                                    scMap.put(order.getScId(), sc);
                                }
                            }
                            if (sc != null) {
                                address = sc.getAddress();
                                timezone = sc.getScTimezone();
                            }
                        }
                        NbOrderPathEntity op190 = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), 0, 0d, 0d, address, timezone);
                        nbOrderPathService.save(op190);
                    }
                }
            }
        }
        if (showPdf) {
            try {
                // String template = PathUtil.getRootClassPath() + File.separator + "tpl" + File.separator + "20250327_label_normal.pdf";
                //判断当前环境，测试用
                String osName = System.getProperty("os.name").toLowerCase();
                if (osName.contains("win")) {
                    // user.dir 获取项目根目录路径
                    templatePdfPath = System.getProperty("user.dir") + File.separator + "jynx-zxoms" + File.separator + "jynx-zxoms-biz" + File.separator + "tpl" +
                            File.separator + "20250327_label_normal.pdf";
                }
                // 生产面单地址-阿里云
                String URL = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/20250327_label_normal.pdf";
                log.info("电子面单路径: " + templatePdfPath);    // 测试服绝对路径：/home/<USER>/backend/tpl/20250327_label_normal.pdf
                PdfUtils.toResponseStream(URL, labelDataList, response);
//                PdfUtils.toResponseStreamNew(templatePdfPath, labelDataList, response, packageNumber);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
//        else {
//            setAttr("labels", labels);
//            setAttr("test", "test");
//            return R.ok(labels);
//        }
    }

    /**
     * 获取面单信息
     */
    public R orderInfoByCustomNoPkgNo(String code) {
        // select * from nb_order where customer_order_no = ? or pkg_no = ?
        LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
        wrapper.eq(StrUtil.isNotBlank(code), NbOrderEntity::getOrderNo, code)
                .or()
                .eq(StrUtil.isNotBlank(code), NbOrderEntity::getPkgNo, code);
        NbOrderEntity order = getOne(wrapper);
        if (ObjectUtil.isNull(order)) {
            return LocalizedR.failed("nborder.order.does.not.exist", code);
        }

        JSONObject jo = new JSONObject();
        jo.put("orderId", order.getOrderId());
        jo.put("pkgNo", order.getPkgNo());
        jo.put("customOrderNo", order.getCustomerOrderNo());
        jo.put("isPrinted", order.getIsPrinted());
        jo.put("destName", order.getDestName());
        jo.put("destTel", order.getDestTel());
        jo.put("destAddress", order.getDestPostalCode() + " " + order.getDestCity() + " " + order.getDestProvince());

        return R.ok(jo);
    }

    // 派送单分页查询
    @Override
    public Page<OrderPageVo> search(Page page, OrderPageVo vo) {
        MPJLambdaWrapper wrapper = getWrapper(vo, null);
        return nbOrderMapper.selectJoinPage(page, OrderPageVo.class, wrapper);
    }

    @Override
    public List<NbOrderEntity> findOrderByTransferBatchId(Integer batchId) {
        return nbOrderMapper.findOrderByTransferBatchId(batchId);
    }

    /**
     * 通过批次号查询条形码列表
     * @param batchNo
     * @return
     */
    @Override
    public List<NbOrderEntity> showBarListByBatchNo(String batchNo) {
        MPJLambdaWrapper<NbOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(NbOrderEntity::getPkgNo)
                .select(NbOrderEntity::getOrderStatus)
                .eq(NbOrderEntity::getBatchNo, batchNo)
                .leftJoin(NbOrderBatchEntity.class, NbOrderBatchEntity::getBatchNo, NbOrderEntity::getBatchNo);
        List<NbOrderEntity> list = nbOrderMapper.selectList(wrapper);
        return list;
    }

    @Override
    public OrderVo findOrderByBatchId(List<Integer> batchIdList) {
        return nbOrderMapper.findOrderByBatchId(batchIdList);
    }

    // 辅助方法
    private boolean isExcelFile(String fileName) {
        return fileName != null && (fileName.endsWith(".xls") || fileName.endsWith(".xlsx"));
    }

    private Integer validateIntegerField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null) {
            lineErrors.add(errorMessage + ", line: " + row);
            return null;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            // 处理格式错误
            lineErrors.add(errorMessage + " (format error), line: " + row);
            return null;
        }
    }

    // 验证配送类型字段
    private Integer validateSelfPickup(List<Object> line, int index, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null || "".equals(value.toString().trim())) {
            lineErrors.add("The delivery method cannot be empty, line: " + row);
            return null;
        }
        try {
            Integer selfPickup = Integer.valueOf(value.toString().trim());

            if (selfPickup != 0 && selfPickup != 1 && selfPickup != 2 && selfPickup != 3) {
                lineErrors.add("The delivery type is: 0/1/2/3, line：" + row);
                return null;
            }
            return selfPickup;
        } catch (NumberFormatException e) {
            lineErrors.add(value + "Delivery method format error, line: " + row);
            return null;
        }
    }

    private String validateStringField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null || value.toString().trim().isEmpty()) {
            lineErrors.add(errorMessage + ", line: " + row);
            return null;
        }
        return value.toString().trim();
    }

//    private Double validateDoubleField(List<Object> line, int index, String errorMessage, int row) {
//        Object value = line.get(index);
//        if (value == null) {
//            throw new IllegalArgumentException(errorMessage + ", 行：" + row);
//        }
//        try {
//            return Double.valueOf(value.toString());
//        } catch (NumberFormatException e) {
//            throw new IllegalArgumentException(errorMessage + " (格式错误), 行：" + row);
//        }
//    }
    private BigDecimal validateBigDecimalField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null) {
            lineErrors.add(errorMessage + ", line: " + row);
            return null;
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            lineErrors.add(value + errorMessage + ", line: " + row);
            return null;
        }
    }

    /**
     * 派送单模板导出
     * @param vo
     * @param ids
     * @return
     */
    @Override
    public List<NbOrderExcelVo> getExcelNew(OrderPageVo vo, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids);
        List<NbOrderExcelVo> list = nbOrderMapper.selectJoinList(NbOrderExcelVo.class, wrapper);
        if (list.isEmpty()) {
            return list;
        }

        // 提取所有订单ID
        List<Integer> orderIds = list.stream().map(NbOrderExcelVo::getOrderId).collect(Collectors.toList());

        // 批量查询路径信息（200、204、205状态）
        Map<Integer, Map<Integer, NbOrderPathEntity>> orderPathMap = getOrderPathMap(orderIds);

        // 批量查询分拣中心时区
        Map<Integer, String> scTimezoneMap = getScTimezoneMap(list);

        // 处理每个订单
        for (NbOrderExcelVo excelVo : list) {
            processExcelVo(excelVo, orderPathMap.get(excelVo.getOrderId()), scTimezoneMap);
        }

        return list;
    }

    private Map<Integer, Map<Integer, NbOrderPathEntity>> getOrderPathMap(List<Integer> orderIds) {
        LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(NbOrderPathEntity::getOrderId, orderIds)
                .in(NbOrderPathEntity::getOrderStatus,
                        OrderDto.ORDER_STATUS_200_PARCEL_SCANNED,
                        OrderDto.ORDER_STATUS_204_IN_TRANSIT,
                        OrderDto.ORDER_STATUS_205_DELIVERED);

        return nbOrderPathMapper.selectList(wrapper)
                .stream()
                .collect(Collectors.groupingBy(NbOrderPathEntity::getOrderId, Collectors.toMap(NbOrderPathEntity::getOrderStatus,
                                Function.identity(),
                                (existing, replacement) -> existing // 处理重复记录，保留已有
                        )
                ));
    }

    private Map<Integer, String> getScTimezoneMap(List<NbOrderExcelVo> list) {
        Set<Integer> scIds = list.stream()
                .map(NbOrderExcelVo::getScId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (scIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return nbSortingCenterService.listByIds(scIds)
                .stream()
                .collect(Collectors.toMap(
                        NbSortingCenterEntity::getScId,
                        NbSortingCenterEntity::getScTimezone
                ));
    }

    private void processExcelVo(NbOrderExcelVo excelVo, Map<Integer, NbOrderPathEntity> statusMap, Map<Integer, String> scTimezoneMap) {
        // 获取各状态路径记录
        NbOrderPathEntity op200 = statusMap != null ? statusMap.get(OrderDto.ORDER_STATUS_200_PARCEL_SCANNED) : null;
        NbOrderPathEntity op204 = statusMap != null ? statusMap.get(OrderDto.ORDER_STATUS_204_IN_TRANSIT) : null;
        NbOrderPathEntity op205 = statusMap != null ? statusMap.get(OrderDto.ORDER_STATUS_205_DELIVERED) : null;

        // 设置状态标题
        excelVo.setOrderStatusTitle(OrderDto.orderStatusMap.getOrDefault(excelVo.getOrderStatus(), ""));

        // 获取时区
        String timezone = scTimezoneMap.getOrDefault(excelVo.getScId(),
                ZoneOffset.systemDefault().getRules().getOffset(Instant.now()).toString());

        // 处理时区转换
        ZoneId zoneId = parseTimezone(timezone);

        // 处理时间字段
        processTimeFields(excelVo, op200, op204, op205, zoneId);

        // 设置重量单位
        excelVo.setWeightUnit("");
    }

    private ZoneId parseTimezone(String timezone) {
        try {
            return timezone.contains(":") ?
                    ZoneOffset.of(timezone) :
                    ZoneId.of(timezone);
        } catch (Exception e) {
            return ZoneOffset.UTC; // 默认时区
        }
    }

    private void processTimeFields(NbOrderExcelVo excelVo, NbOrderPathEntity op200, NbOrderPathEntity op204, NbOrderPathEntity op205, ZoneId zoneId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
        // 包裹扫描时间（200状态）
        excelVo.setParcelScanTime(getFormattedTime(op200, zoneId, formatter));

        // 运输中时间（204状态）
        excelVo.setInTransitTime(getFormattedTime(op204, zoneId, formatter));

        // 配送成功时间（205状态）
        excelVo.setWith205Time(getFormattedTime(op205, zoneId, formatter));

        // 190-200时效（订单创建到200状态）
        excelVo.setWith190To200Time(calculateDurationHours(excelVo.getAddTime(), op200));

        // 200-205时效（200到205状态）
        excelVo.setWith200To205Time(calculateDurationHours(op200, op205));
    }

    private String getFormattedTime(NbOrderPathEntity entity, ZoneId zoneId, DateTimeFormatter formatter) {
        if (entity == null || entity.getAddTime() == null) {
            return "";
        }
        return Instant.ofEpochMilli(entity.getAddTime().getTime()).atZone(zoneId).format(formatter);
    }

    private String calculateDurationHours(Date start, NbOrderPathEntity endEntity) {
        if (endEntity == null || endEntity.getAddTime() == null || start == null) {
            return "";
        }
        long diff = endEntity.getAddTime().getTime() - start.getTime();
        return String.valueOf((int) (diff / 3_600_000)); // 转换为小时
    }

    private String calculateDurationHours(NbOrderPathEntity startEntity, NbOrderPathEntity endEntity) {
        if (startEntity == null || endEntity == null ||
                startEntity.getAddTime() == null || endEntity.getAddTime() == null) {
            return "";
        }
        long diff = endEntity.getAddTime().getTime() - startEntity.getAddTime().getTime();
        return String.valueOf((int) (diff / 3_600_000));
    }

    /**
     * 派送单模板导出2
     * @param vo
     * @param ids
     * @return
     */
    @Override
    public List<NbOrderExcel2Vo> getExcelNew2(OrderPageVo vo, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids);
        List<NbOrderExcel2Vo> list = nbOrderMapper.selectJoinList(NbOrderExcel2Vo.class, wrapper);

        if (list.size() > 100000) {
            String errorMessage = "导出失败：单次导出不能超过10万条记录，请分批次导出！";
            log.warn(errorMessage);
            throw new IllegalArgumentException(errorMessage);
        }

        // 1. 收集所有需要预加载的ID
        Set<Integer> orderIds = list.stream().map(NbOrderExcel2Vo::getOrderId).collect(Collectors.toSet());
        Set<Integer> driverIds = list.stream().map(NbOrderExcel2Vo::getDriverId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Integer> scIds = list.stream().map(NbOrderExcel2Vo::getScId).filter(Objects::nonNull).collect(Collectors.toSet());

        // 2. 批量查询驾司机信息
        Map<Integer, NbDriverEntity> driverMap = driverIds.isEmpty() ? Maps.newHashMap() :
                nbDriverMapper.selectBatchIds(driverIds).stream()
                        .collect(Collectors.toMap(NbDriverEntity::getDriverId, Function.identity()));

        // 3. 批量查询分拣中心时区
        Map<Integer, String> scTimezoneMap = scIds.isEmpty() ? Maps.newHashMap() :
                nbSortingCenterService.listByIds(scIds).stream()
                        .collect(Collectors.toMap(NbSortingCenterEntity::getScId, NbSortingCenterEntity::getScTimezone));

        // 4. 预加载MPS关联信息
        Map<Integer, Integer> mpsPOrderMap = Maps.newHashMap();
        if (!orderIds.isEmpty()) {
            List<NbOrderMpsEntity> mpsList = nbOrderMpsMapper.selectList(
                    new LambdaQueryWrapper<NbOrderMpsEntity>().in(NbOrderMpsEntity::getOrderId, orderIds));
            mpsList.forEach(mps -> mpsPOrderMap.put(mps.getOrderId(), mps.getPOrderId()));
        }

        // 5. 预加载父订单信息
        Set<Integer> pOrderIds = mpsPOrderMap.values().stream().filter(id -> id > 0).collect(Collectors.toSet());
        Map<Integer, NbOrderEntity> pOrderMap = pOrderIds.isEmpty() ? Maps.newHashMap() :
                nbOrderMapper.selectBatchIds(pOrderIds).stream()
                        .collect(Collectors.toMap(NbOrderEntity::getOrderId, Function.identity()));

        // 6. 预加载路径信息
        Set<Integer> allOrderIds = new HashSet<>(orderIds);
        allOrderIds.addAll(pOrderIds);
        Map<Integer, Map<Integer, NbOrderPathEntity>> pathMap = Maps.newHashMap();
        if (!allOrderIds.isEmpty()) {
            List<NbOrderPathEntity> paths = nbOrderPathMapper.selectList(
                    new LambdaQueryWrapper<NbOrderPathEntity>().in(NbOrderPathEntity::getOrderId, allOrderIds));
            paths.forEach(path ->
                    pathMap.computeIfAbsent(path.getOrderId(), k -> Maps.newHashMap())
                            .put(path.getOrderStatus(), path));
        }

        // 7. 预加载转运批次信息
        Map<Integer, TransferBatchOrderDto> batchMap = Maps.newHashMap();
        if (!orderIds.isEmpty()) {
            MPJLambdaWrapper<NbTransferBatchEntity> batchWrapper = new MPJLambdaWrapper<>();
            batchWrapper.select(NbTransferBatchEntity::getBatchNo)
                    .select(NbTransferBatchOrderEntity::getPickNo, NbTransferBatchOrderEntity::getOrderId)
                    .leftJoin(NbTransferBatchOrderEntity.class,
                            NbTransferBatchOrderEntity::getBatchId, NbTransferBatchEntity::getOrderBatchId)
                    .in(NbTransferBatchOrderEntity::getOrderId, orderIds);
            batchMap = nbTransferBatchMapper.selectJoinList(TransferBatchOrderDto.class, batchWrapper).stream()
                    .collect(Collectors.toMap(TransferBatchOrderDto::getOrderId,
                            Function.identity(), (existing, replacement) -> existing));
        }

        // 8. 处理数据转换
        for (NbOrderExcel2Vo excelVo2 : list) {
            // 处理司机信息
            processDriverInfo(excelVo2, driverMap);

            // 处理父订单信息
            Integer useOrderId = processParentOrder(excelVo2, mpsPOrderMap, pOrderMap);

            // 处理时区信息
            ZoneId zoneId = processTimezone(excelVo2, scTimezoneMap);

            // 处理时间字段转换
            processTimeFields(excelVo2, zoneId, pathMap.getOrDefault(useOrderId, Maps.newHashMap()));

            // 处理路径时效计算
            processPathDurations(excelVo2, pathMap.getOrDefault(useOrderId, Maps.newHashMap()));

            // 处理运输批次信息
            processBatchInfo(excelVo2, batchMap);

            // 处理运输类型转换
            processTransportType(excelVo2);
        }
        return list;
    }

    // 处理司机信息
    private void processDriverInfo(NbOrderExcel2Vo vo, Map<Integer, NbDriverEntity> driverMap) {
        NbDriverEntity driver = driverMap.get(vo.getDriverId());
        vo.setLastName(driver != null ? driver.getLastName() : "");
        vo.setMobile(driver != null ? driver.getMobile() : "");
    }

    // 处理父订单信息
    private Integer processParentOrder(NbOrderExcel2Vo vo, Map<Integer, Integer> mpsMap, Map<Integer, NbOrderEntity> pOrderMap) {
        Integer useOrderId = vo.getOrderId();
        if (vo.getIsMps()) {
            Integer pOrderId = mpsMap.get(vo.getOrderId());
            if (pOrderId != null && pOrderId > 0) {
                NbOrderEntity pOrder = pOrderMap.get(pOrderId);
                if (pOrder != null) {
                    vo.setUseOrderStatus(pOrder.getOrderStatus());
                    useOrderId = pOrderId;
                }
            }
        }
        return useOrderId;
    }

    // 处理时区
    private ZoneId processTimezone(NbOrderExcel2Vo vo, Map<Integer, String> scTimezoneMap) {
        String timezone = scTimezoneMap.getOrDefault(vo.getScId(),
                ZoneOffset.systemDefault().getId());
        try {
            return timezone.contains(":") ? ZoneOffset.of(timezone) : ZoneId.of(timezone);
        } catch (Exception e) {
            log.warn("Invalid timezone: {}, using default", timezone);
            return ZoneOffset.UTC;
        }
    }

    // 处理时间字段
    private void processTimeFields(NbOrderExcel2Vo vo, ZoneId zoneId, Map<Integer, NbOrderPathEntity> statusMap) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss").withZone(zoneId);

        // LabelingTime
        vo.setLablingTime(formatTime(vo.getAddTime(), formatter));

        // 各状态时间字段处理
        vo.setGatewayInTime(getPathTime(statusMap, OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT, formatter));
        vo.setScanTime(getPathTime(statusMap, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED, formatter));
        vo.setInTransitTime(getPathTime(statusMap, OrderDto.ORDER_STATUS_204_IN_TRANSIT, formatter));
        vo.setDeliveredTime(getPathTime(statusMap, OrderDto.ORDER_STATUS_205_DELIVERED, formatter));

        // 处理211/210时间逻辑
        NbOrderPathEntity op211 = statusMap.get(OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1);
        if (op211 != null) {
            vo.setWith211Time(formatTime(op211.getAddTime(), formatter));
        } else {
            NbOrderPathEntity op210 = statusMap.get(OrderDto.ORDER_STATUS_210_FAILED_DELIVERY);
            vo.setWith211Time(op210 != null ? formatTime(op210.getAddTime(), formatter) : "");
        }
    }

    // 处理路径时效计算
    private void processPathDurations(NbOrderExcel2Vo vo, Map<Integer, NbOrderPathEntity> statusMap) {
        NbOrderPathEntity op199 = statusMap.get(OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT);
        NbOrderPathEntity op200 = statusMap.get(OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
        NbOrderPathEntity op204 = statusMap.get(OrderDto.ORDER_STATUS_204_IN_TRANSIT);
        NbOrderPathEntity op205 = statusMap.get(OrderDto.ORDER_STATUS_205_DELIVERED);
        NbOrderPathEntity op211 = statusMap.get(OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1);

        // Labeling-Gatewayin时效
        setDuration(vo::setLabelingGatewayinPrescription, vo.getAddTime(), op199);

        // Gatewayin-parcel scanned时效
        setDuration(vo::setGatewayinParcelScannedPrescription, op199, op200);

        // Parcel scanned-in transit时效
        setDuration(vo::setParcelScannedInTransitPrescription, op200, op204);

        // parcel scanned-delivered时效
        NbOrderPathEntity startPath = op211 != null ? op211 : op200;
        setDuration(vo::setParcelScannedDeliveredPrescription, startPath, op205);
    }

    // 处理运输批次信息
    private void processBatchInfo(NbOrderExcel2Vo vo, Map<Integer, TransferBatchOrderDto> batchMap) {
        TransferBatchOrderDto tbo = batchMap.get(vo.getOrderId());
        if (tbo != null) {
            vo.setRouteNo(tbo.getBatchNo());
            vo.setPickNo(tbo.getPickNo());
        } else {
            vo.setRouteNo("");
            vo.setPickNo("");
        }
    }

    // 运输类型转换
    private void processTransportType(NbOrderExcel2Vo vo) {
        String type = Optional.ofNullable(vo.getTransportType()).orElse("");
        vo.setExpressType(OrderDto.expresTypeMap.getOrDefault(type, ""));

        String transportType;
        switch (type) {
            case "1":
                transportType = "By Sea";
                break;
            case "2":
                transportType = "By Air";
                break;
            case "3":
                transportType = "By Ground";
                break;
            default:
                transportType = type;  // 保持原值
        }
        vo.setTransportType(transportType);
    }

    // 格式化时间
    private String formatTime(Date date, DateTimeFormatter formatter) {
        return date != null ? formatter.format(date.toInstant()) : "";
    }

    // 获取路径时间
    private String getPathTime(Map<Integer, NbOrderPathEntity> statusMap, int status, DateTimeFormatter formatter) {
        NbOrderPathEntity path = statusMap.get(status);
        return path != null ? formatTime(path.getAddTime(), formatter) : "";
    }

    // 设置时效字段
    private void setDuration(Consumer<String> setter, Date start, NbOrderPathEntity endPath) {
        if (start != null && endPath != null && endPath.getAddTime() != null) {
            long diff = endPath.getAddTime().getTime() - start.getTime();
            setter.accept(convertMillisToHours(diff));
        } else {
            setter.accept("");
        }
    }

    private void setDuration(Consumer<String> setter, NbOrderPathEntity startPath, NbOrderPathEntity endPath) {
        if (startPath != null && endPath != null) {
            setDuration(setter, startPath.getAddTime(), endPath);
        } else {
            setter.accept("");
        }
    }

    private String convertMillisToHours(long millis) {
        return String.valueOf((int) (millis / 1000 / 60 / 60));
    }

    private MPJLambdaWrapper getWrapper(OrderPageVo vo, Integer[] ids) {
        MPJLambdaWrapper<NbOrderEntity> qw = new MPJLambdaWrapper<NbOrderEntity>();
        qw.eq(ObjectUtil.isNotNull(vo.getOrderId()), NbOrderEntity::getOrderId, vo.getOrderId())
                .eq(ObjectUtil.isNotNull(vo.getMerchantId()), NbOrderEntity::getMerchantId, vo.getMerchantId())
                .eq(ObjectUtil.isNotNull(vo.getPDriverId()), NbOrderEntity::getPDriverId, vo.getPDriverId())
                .eq(ObjectUtil.isNotNull(vo.getDriverId()), NbOrderEntity::getDriverId, vo.getDriverId())
                .eq(ObjectUtil.isNotNull(vo.getScId()), NbOrderEntity::getScId, vo.getScId())
                .eq(ObjectUtil.isNotNull(vo.getTcId()), NbOrderEntity::getTcId, vo.getTcId())
                .like(StrUtil.isNotBlank(vo.getOrderNo()), NbOrderEntity::getOrderNo, vo.getOrderNo())
                .like(StrUtil.isNotBlank(vo.getPkgNo()), NbOrderEntity::getPkgNo, vo.getPkgNo())
                .eq(ObjectUtil.isNotNull(vo.getOrderStatus()), NbOrderEntity::getOrderStatus, vo.getOrderStatus())
                .like(StrUtil.isNotBlank(vo.getBatchNo()), NbOrderEntity::getBatchNo, vo.getBatchNo())  // 条件查询-批次号
                .like(StrUtil.isNotBlank(vo.getSubBatchNo()), NbOrderEntity::getSubBatchNo, vo.getSubBatchNo())   // 条件查询-子批次号
                .like(StrUtil.isNotBlank(vo.getSubBatchNo()), NbOrderEntity::getSubBatchNo, vo.getSubBatchNo())
                .like(StrUtil.isNotBlank(vo.getCustomerOrderNo()), NbOrderEntity::getCustomerOrderNo, vo.getCustomerOrderNo())
                .like(StrUtil.isNotBlank(vo.getCustomerPOrderNo()), NbOrderEntity::getCustomerPOrderNo, vo.getCustomerPOrderNo())   // 条件查询-客户主订单号
                .like(StrUtil.isNotBlank(vo.getPickNo()), NbOrderEntity::getPickNo, vo.getPickNo())     // 条件查询-线路号
                .like(StrUtil.isNotBlank(vo.getVTransferBatchNo()), NbTransferBatchEntity::getBatchNo, vo.getVTransferBatchNo())    // 条件查询-路区号
                .eq(ObjectUtil.isNotNull(vo.getExpressType()), NbOrderEntity::getExpressType, vo.getExpressType())
                .like(StrUtil.isNotBlank(vo.getBagNumber()), NbOrderEntity::getBagNumber, vo.getBagNumber())
                .like(StrUtil.isNotBlank(vo.getSubChannel()), NbOrderEntity::getSubChannel, vo.getSubChannel())
                .like(StrUtil.isNotBlank(vo.getRegionCode()), NbOrderEntity::getRegionCode, vo.getRegionCode())
                .like(StrUtil.isNotBlank(vo.getDestTel()), NbOrderEntity::getDestTel, vo.getDestTel())
                .eq(ObjectUtil.isNotNull(vo.getInNbRange()), NbOrderEntity::getInNbRange, vo.getInNbRange());   // 条件查询-是否在配送范围

        String orderIds = vo.getOrderIds();
        if (StrUtil.isNotBlank(orderIds)) {
            // 逗号 空格 分号 转成数组
            String[] orderIdArray = null;
            if (orderIds.contains(",")) {
                orderIdArray = StrUtil.splitToArray(orderIds, ',');
            } else if (orderIds.contains(";")) {
                orderIdArray = StrUtil.splitToArray(orderIds, ';');
            } else {
                orderIdArray = StrUtil.splitToArray(orderIds, ' ');
            }
            // 将 String[] 转换为 Integer[] 类型
            Integer[] orderIdIntegers = Arrays.stream(orderIdArray).map(String::trim)  // 去除每个元素的空格
                    .map(Integer::parseInt).toArray(Integer[]::new);
            qw.in(NbOrderEntity::getOrderId, orderIdIntegers);  // 条件查询-多个订单ID
        }

        String etaDatetime = vo.getEtaTime();   // ETA时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        if (etaDatetime != null) {
            int splitIndex = etaDatetime.indexOf(":", etaDatetime.indexOf(":") + 1) + 3;
            String startPutawayTime = etaDatetime.substring(0, splitIndex);
            String endPutAwayTime = etaDatetime.substring(splitIndex + 1);
            qw.ge(NbOrderEntity::getEta, startPutawayTime).le(NbOrderEntity::getEta, endPutAwayTime);
        }
        String vParcelScannedTime = vo.getVParcelScannedTime();     // 盲扫时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        if (vParcelScannedTime != null) {
            int splitIndex = vParcelScannedTime.indexOf(":", vParcelScannedTime.indexOf(":") + 1) + 3;
            String startScannedTime = vParcelScannedTime.substring(0, splitIndex);
            String endScannedTime = vParcelScannedTime.substring(splitIndex + 1);

            LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.ge(NbOrderPathEntity::getAddTime, startScannedTime) // 盲扫时间 >= 开始时间
                    .le(NbOrderPathEntity::getAddTime, endScannedTime)   // 盲扫时间 <= 结束时间
                    .eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED) // 状态匹配
                    .select(NbOrderPathEntity::getOrderId); // 只需要提取 orderId 列
            List<NbOrderPathEntity> orderPathEntities = nbOrderPathMapper.selectList(wrapper);
            List<Integer> orderIdList = orderPathEntities.stream().map(NbOrderPathEntity::getOrderId).distinct().collect(Collectors.toList());
            if (orderIdList.isEmpty()) {
                // 如果没有匹配的订单，则添加无效条件
                qw.eq("1", "2");
            } else {
                // 添加 order_id 的条件
                qw.in(NbOrderEntity::getOrderId, orderIdList);
            }
        }
        qw.selectAll(NbOrderEntity.class)
                .select(NbMerchantEntity::getMerchantId).selectAs(NbMerchantEntity::getName, OrderPageVo.Fields.merchantName)
                .select(NbDriverEntity::getDriverId, NbDriverEntity::getDriverName, NbDriverEntity::getFirstName, NbDriverEntity::getLastName, NbDriverEntity::getEmail, NbDriverEntity::getMobile)
                .select(NbOrderStatusEntity::getId).selectAs(NbOrderStatusEntity::getTitle, OrderPageVo.Fields.orderStatusTitle)
                .leftJoin(NbMerchantEntity.class, NbMerchantEntity::getMerchantId, NbOrderEntity::getMerchantId)
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbOrderEntity::getDriverId)
                .leftJoin(NbOrderStatusEntity.class, NbOrderStatusEntity::getId, NbOrderPathEntity::getOrderStatus)
                .selectAs(NbTransferCenterEntity::getCenterName, OrderPageVo.Fields.transferCenterName)
                .selectAs(NbTransferCenterEntity::getTransferCenterCode, OrderPageVo.Fields.transferCenterCode)
                .select(NbSortingCenterEntity::getScId)
                .selectAs(NbSortingCenterEntity::getCenterName, OrderPageVo.Fields.sortingCenterName)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbOrderEntity::getTcId)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbOrderEntity::getScId)
                .selectAs(NbTransferBatchEntity::getBatchNo, OrderPageVo.Fields.vTransferBatchNo)
                .leftJoin(NbTransferBatchOrderEntity.class, NbTransferBatchOrderEntity::getOrderId, NbOrderEntity::getOrderId)
                .leftJoin(NbTransferBatchEntity.class, NbTransferBatchEntity::getBatchId, NbTransferBatchOrderEntity::getBatchId)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, OrderPageVo::getOrderId, ids)
                .orderByDesc(NbOrderEntity::getOrderId);

        SysUser user = remoteUserService.getOneUserById(SecurityUtils.getUser().getId()).getData();
        String nbdScId = user.getNbdScId();     //获取用户登录下的分拣中心
        if (StringUtils.isNotBlank(nbdScId)) {
            // 如果该用户配置了分拣中心，则取该用户下的分拣中心关联
            List<Integer> idList = Arrays.stream(nbdScId.split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
            qw.in(NbOrderEntity::getScId, idList);
        } else {
            // 如果该用户没有配置分拣中心，则取所有有订单数据的分拣中心关联，避免索引失效走全表扫描
            MPJLambdaWrapper<NbSortingCenterEntity> wrapper = new MPJLambdaWrapper<>();
            // 去重，避免重复的分拣中心
            wrapper.leftJoin(NbOrderEntity.class, NbOrderEntity::getScId, NbSortingCenterEntity::getScId).select(NbSortingCenterEntity::getScId).distinct();
            List<NbSortingCenterEntity> scIdList = nbSortingCenterService.list(wrapper);
            if (scIdList.size() == 0) {
                return qw;
            }
            List<Integer> collectScId = scIdList.stream().map(NbSortingCenterEntity::getScId).collect(Collectors.toList());
            qw.in(NbOrderEntity::getScId, collectScId);
        }
        return qw;
    }

    /**
     * 官网获取轨迹
     * @param pkgNo
     * @return
     */
    @Override
    public OldResult getTrack(String pkgNo) {
        if (StrUtil.isBlank(pkgNo)) {
            return OldResult.fail("500300", "pkgNo can not be empty");
        }
        NbOrderEntity order = null;
        if (StrUtil.isNotBlank(pkgNo)) {
            // "select order_id, customer_order_no, pkg_no, sc_id, order_status, add_time, is_mps from nb_order where pkg_no = ? limit 1", pkgNo);
            order = nbOrderMapper.selectOne(new LambdaQueryWrapper<>(NbOrderEntity.class).eq(NbOrderEntity::getPkgNo, pkgNo));
        }

        if (order == null) {
            return OldResult.fail("500301", "Order does not exist");
        }

        JSONObject orderJo = new JSONObject();
        orderJo.set("pkgNo", order.getPkgNo());


        // 大于5天，从接口读数据
//		if (order.getOrderStatus() == Order.ORDER_STATUS_190_ORDER_RECEIVED && DateUtil.betweenDay(order.getAddTime(), new Date(), false) >= 5) {
        // 20231215 黎佳丽：取消5天限制
        if (order.getOrderStatus() <= OrderDto.ORDER_STATUS_200_PARCEL_SCANNED) {
            try {
                JSONArray trcknos = new JSONArray();
                trcknos.add(order.getPkgNo());

                HttpRequest request = HttpRequest.post(apiUrl);
                request.header("apiKey", apiKey);
                request.body(JSONUtil.toJsonStr(trcknos));
                HttpResponse response = request.execute();
                String result = response.body();
                log.info("JL::" + result);

                JSONObject retJo = JSONUtil.parseObj(result);
                if (retJo.getStr("message").equals("success") && retJo.containsKey("data")) {
                    JSONObject tracks = retJo.getJSONArray("data").getJSONObject(0);
                    JSONArray fromDetails = tracks.getJSONArray("fromDetail");

                    if (fromDetails.size() > 0) {
                        JSONArray localJa = new JSONArray();

                        for (int i = 0; i < fromDetails.size(); i++) {
                            JSONObject item = fromDetails.getJSONObject(i);

                            JSONObject localJo = new JSONObject();
                            localJo.set("time", item.getStr("pathTime"));
                            localJo.set("timestamp", item.getStr("pathTime"));
                            localJo.set("status", item.getStr("pathInfo"));
                            localJo.set("address", item.getStr("pathLocation"));

                            localJa.add(localJo);
                        }

                        // 获取pods图片,并将url上传至我们的oss
                        JSONArray podsArray = tracks.getJSONArray("pods");
                        List<String> ossUrls = new ArrayList<>();

                        if (null != podsArray && !podsArray.isEmpty()) {
                            for (Object pod : podsArray) {
                                if (pod instanceof String) {
                                    String remoteUrl = (String) pod;
                                    try {
                                        String ossUrl = AliYunOSS.sendToOss(remoteUrl);
                                        if (ossUrl != null) {
                                            ossUrls.add(ossUrl);
                                        }
                                    } catch (Exception e) {
                                        log.error("上传图片到OSS失败，url: {}", remoteUrl, e);
                                    }
                                }
                            }
                        }

                        orderJo.set("track", localJa);
                        orderJo.set("images", ossUrls.isEmpty() ? null : ossUrls);
                        return OldResult.ok("0", orderJo);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                // ExceptionKit.handler(e, "/track/uni/" + order.getPkgNo(), this);
                new GlobalBizExceptionHandler().handleGlobalException(e);
            }
        }

        int userOrderId = order.getOrderId();
        if (order.getIsMps()) {
            // 2024-02-17 为一票多件时，直接查主订单
            NbOrderMpsEntity om = nbOrderMpsMapper.selectOne(new LambdaQueryWrapper<NbOrderMpsEntity>().eq(NbOrderMpsEntity::getOrderId, order.getOrderId()));
            if (om != null && om.getPOrderId() > 0) {
                userOrderId = om.getPOrderId();
            }
        }

        // "select * from nb_order_path where order_id = ? order by add_timestamp desc", userOrderId);
        List<NbOrderPathEntity> paths = nbOrderPathService.list(new LambdaQueryWrapper<NbOrderPathEntity>().eq(NbOrderPathEntity::getOrderId, userOrderId)
                .orderByDesc(NbOrderPathEntity::getAddTimestamp));
        JSONArray ja = paths.stream().map(op -> {
            // JSONObject jo = op.toAppJson();
            JSONObject jo = new OrderPathDto().toAppJson(op);
            return jo;
        }).filter(p -> p != null).collect(Collectors.toCollection(JSONArray::new));

        orderJo.set("track", ja);

        // "select * from nb_order_sign_image where order_id = ?", userOrderId);
        List<NbOrderSignImageEntity> signImages = nbOrderSignImageMapper.selectList(new LambdaQueryWrapper<NbOrderSignImageEntity>().eq(NbOrderSignImageEntity::getOrderId, userOrderId));

        JSONArray images = NBDUtils.assemble(signImages);

//		JSONArray images = signImages.stream().map(OrderSignImage::toAppJson).collect(Collectors.toCollection(JSONArray::new));
        orderJo.set("images", images);
        return OldResult.ok("0", orderJo);
    }

    /**
     * 官网获取批量轨迹
     * @param pkgNos
     * @return
     */
    @Override
    public OldResult getTracks(String pkgNos, String zipInput) {
        if (StrUtil.isBlank(pkgNos)) {
            return OldResult.fail("500300", "pkgNo can not be empty");
        }
        int maxSize = 50;
        List<NbOrderEntity> orders = null;

        JSONArray jySearchNos = new JSONArray(); // 需要去佳邮查询的单号
        JSONArray ja = new JSONArray();     // 最终轨迹结果集合
        Set<String> handledPkgNos = new HashSet<>();    // 防止重复添加

        String[] pkgNoArr = pkgNos.split(",");
        if (pkgNoArr.length > maxSize) {
            return OldResult.fail("500301", "Query up to " + maxSize + " packages at a time");
        }

        // String in = Arrays.asList(pkgNoArr).stream().map(s -> "'" + s.trim() + "'").collect(Collectors.joining(","));
        List<String> pkgNoList = Arrays.stream(pkgNoArr).map(String::trim).collect(Collectors.toList());

        // "select order_id, customer_order_no, pkg_no, sc_id, order_status, add_time, is_mps from nb_order where pkg_no in (" + in + ") ") or jy_order_no in (" + in + ") ");
        LambdaQueryWrapper<NbOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(NbOrderEntity::getPkgNo, pkgNoList);
        orders = nbOrderMapper.selectList(wrapper);

        // 250604-处理收件人邮编
        List<String> zipList = Arrays.stream(StrUtil.nullToEmpty(zipInput).split(","))
                .map(String::trim)                     // 去掉首尾空格
                .filter(StrUtil::isNotBlank)           // 去除空值
                .map(String::toUpperCase)              // 转大写，与数据库统一格式
                .collect(Collectors.toList());

        // 250604-检查是否有订单含有目标邮编
        boolean hasZipMatch = CollUtil.isNotEmpty(zipList) && orders.stream().anyMatch(order -> {
            String destZip = order.getDestPostalCode();
            return StrUtil.isNotBlank(destZip) && zipList.contains(destZip.toUpperCase());
        });

        // 250604-如果有匹配的邮编，则加入邮编条件
        if (hasZipMatch) {
            wrapper.and(qw -> {
                for (String zip : zipList) {
                    qw.or().eq(NbOrderEntity::getDestPostalCode, zip);
                }
            });
            orders = nbOrderMapper.selectList(wrapper); // 重新查询
        }

        if (null != orders && !orders.isEmpty()) {
            for (NbOrderEntity order : orders) {
//                if (order.getOrderStatus() <= OrderDto.ORDER_STATUS_200_PARCEL_SCANNED) {
//                    jySearchNos.add(order.getJyOrderNo());
//                    continue;
//                }

                // 250604-如果传了邮编，且当前订单邮编不匹配，跳过
                if (hasZipMatch) {
                    String destZip = StrUtil.blankToDefault(order.getDestPostalCode(), "").toUpperCase();
                    if (!zipList.contains(destZip)) {
                        continue;
                    }
                }

                // 承运商ID：默认1为NB本身，2=Canada Post，3=Fedex，4=UPS，5=DHL，6=intelcom，除了1外都需要调佳邮小包接口去查询轨迹，需要排除旧数据为null的值，防止空指针
                if (null == order.getCarrierId() || order.getCarrierId() > 1) {
                    jySearchNos.add(order.getPkgNo());
                    continue;
                }

                Integer userOrderId = order.getOrderId();
                if (order.getIsMps()) {
                    // 2024-02-17 为一票多件时，直接查主订单
                    NbOrderMpsEntity om = nbOrderMpsMapper.selectOne(new LambdaQueryWrapper<NbOrderMpsEntity>().eq(NbOrderMpsEntity::getOrderId, order.getOrderId()));
                    if (om != null && om.getPOrderId() > 0) {
                        userOrderId = om.getPOrderId();
                    }
                }
                JSONObject orderJo = new JSONObject();
                String matchNo = pkgNoList.contains(order.getJyOrderNo()) ? order.getJyOrderNo() : order.getPkgNo();
                orderJo.set("pkgNo", matchNo);

                // "select * from nb_order_path where order_id = ? order by add_time desc", userOrderId);
                List<NbOrderPathEntity> paths = nbOrderPathService.list(new LambdaQueryWrapper<NbOrderPathEntity>()
                        .eq(NbOrderPathEntity::getOrderId, userOrderId).orderByDesc(NbOrderPathEntity::getAddTime));
                JSONArray trackJa = paths.stream().map(op -> new OrderPathDto().toAppJson(op))
                        .filter(p -> p != null).collect(Collectors.toCollection(JSONArray::new));
                orderJo.set("track", trackJa);

                // "select * from nb_order_sign_image where order_id = ?", userOrderId);
                List<NbOrderSignImageEntity> signImages = nbOrderSignImageMapper.selectList(new LambdaQueryWrapper<NbOrderSignImageEntity>()
                        .eq(NbOrderSignImageEntity::getOrderId, userOrderId));

                List<String> imageUrls = new ArrayList<>();
                for (NbOrderSignImageEntity image : signImages) {
                    if (StringUtils.isNotBlank(image.getPkgImage())) {
                        imageUrls.add(image.getPkgImage());
                    }
                    if (StringUtils.isNotBlank(image.getPutImage())) {
                        imageUrls.add(image.getPutImage());
                    }
                }
//			JSONArray images = signImages.stream().map(OrderSignImage::toAppJson).collect(Collectors.toCollection(JSONArray::new));
                //JSONArray images = NBDUtils.assemble(signImages);
                if (hasZipMatch) {
                    orderJo.set("images", imageUrls);
                }
                ja.add(orderJo);
                if (pkgNoList.contains(matchNo)) {
                    handledPkgNos.add(matchNo);
                }
            }
        }

        // 查找未被数据库处理的 pkgNo，加到 jySearchNos 中
        if (!hasZipMatch) {
            for (String pkgNo : pkgNoList) {
                if (!handledPkgNos.contains(pkgNo)) {
                    jySearchNos.add(pkgNo);
                }
            }
        }

        // 大于5天，从接口读数据
//		if (order.getOrderStatus() == Order.ORDER_STATUS_190_ORDER_RECEIVED && DateUtil.betweenDay(order.getAddTime(), new Date(), false) >= 5) {
        // 20231215 黎佳丽：取消5天限制
        if (!jySearchNos.isEmpty()) {
            try {
                HttpRequest request = HttpRequest.post(apiUrl);
                request.header("apiKey", apiKey);
                request.body(JSONUtil.toJsonStr(jySearchNos));
                HttpResponse response = request.execute();
                String result = response.body();
                log.info("JL::" + result);

                JSONObject retJo = JSONUtil.parseObj(result);
                if (retJo.getStr("message").equals("success") && retJo.containsKey("data")) {
                    JSONArray data = retJo.getJSONArray("data");
                    for (int orderIndex = 0; orderIndex < data.size(); orderIndex++) {
                        JSONObject tracks = data.getJSONObject(orderIndex);
                        String pkgNo = tracks.getStr("trackingNo");
                        // 防止重复加入
                        if (handledPkgNos.contains(pkgNo)) {
                            continue;
                        }
                        JSONObject orderJo = new JSONObject();
                        orderJo.set("pkgNo", pkgNo);

                        JSONArray fromDetails = tracks.getJSONArray("fromDetail");

                        if (fromDetails.size() > 0) {
                            JSONArray localJa = new JSONArray();

                            for (int i = 0; i < fromDetails.size(); i++) {
                                JSONObject item = fromDetails.getJSONObject(i);

                                JSONObject localJo = new JSONObject();
                                localJo.set("time", item.getStr("pathTime"));
                                localJo.set("timestamp", item.getStr("pathTime"));
                                localJo.set("status", item.getStr("pathInfo"));
                                localJo.set("address", item.getStr("pathLocation"));

                                localJa.add(localJo);
                            }

                            // 获取pods图片,并将url上传至我们的oss
                            JSONArray podsArray = tracks.getJSONArray("pods");
                            List<String> ossUrls = new ArrayList<>();

                            if (null != podsArray && !podsArray.isEmpty()) {
                                for (Object pod : podsArray) {
                                    if (pod instanceof String) {
                                        String remoteUrl = (String) pod;
                                        try {
                                            String ossUrl = AliYunOSS.sendToOss(remoteUrl);
                                            if (ossUrl != null) {
                                                ossUrls.add(ossUrl);
                                            }
                                        } catch (Exception e) {
                                            log.error("上传图片到OSS失败，url: {}", remoteUrl, e);
                                        }
                                    }
                                }
                            }

                            orderJo.set("track", localJa);
                            orderJo.set("images", ossUrls.isEmpty() ? null : ossUrls);

//                            return OldResult.ok("0", orderJo);
                            ja.add(orderJo);
                            if (pkgNoList.contains(pkgNo)) {
                                handledPkgNos.add(pkgNo); // 标记处理过
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("JL::" + e);
                new GlobalBizExceptionHandler().handleGlobalException(e);
            }
        }

        if (CollUtil.isEmpty(ja)) {
            return OldResult.fail("500301", "Order does not exist");
        }
        return OldResult.ok("0", ja);
    }

    @Override
    public OldResult get_17Track(String trackingNumber) {

        if ("JY257890N690221868".equals(trackingNumber)){
            System.out.println("------------------------------------经过了正向旧系统接口---------------------------------------------");
        }
        JSONObject retJo = new JSONObject();
        if (StrUtil.isBlank(trackingNumber)) {
            return OldResult.fail(retJo);
        }

        // "select * from nb_order where pkg_no = ? limit 1", trackingNumber.trim());
        NbOrderEntity order = nbOrderMapper.selectOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo, trackingNumber.trim()), false);
        if (order == null) {
            return OldResult.fail(retJo);
        }

        List<Integer> useScCityStatus = new ArrayList<>();
        useScCityStatus.add(OrderDto.ORDER_STATUS_190_ORDER_RECEIVED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_191_ORDER_CANCELED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_192_CUSTOM_HOLD);
        useScCityStatus.add(OrderDto.ORDER_STATUS_198_CUSTOM_RELEASE_DIRECT);
        useScCityStatus.add(OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT);
        useScCityStatus.add(OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER);
        useScCityStatus.add(OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE);

        List<Integer> useTcCityStatus = new ArrayList<>();
        useTcCityStatus.add(OrderDto.ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER);
        useTcCityStatus.add(OrderDto.ORDER_STATUS_203_LOAD_SCANNED);
        useTcCityStatus.add(OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT);

        List<Integer> useUserCityStatus = new ArrayList<>();
        useUserCityStatus.add(OrderDto.ORDER_STATUS_204_IN_TRANSIT);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_205_DELIVERED);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_210_FAILED_DELIVERY);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_280_FAILURE);

        NbSortingCenterEntity sc = nbSortingCenterService.getById(order.getScId());

        JSONObject cityJo = null;
        if (sc != null) {
            cityJo = commonDataUtil.getCityById(sc.getCityId());
        }

        Map<Integer, NbTransferCenterEntity> tcMap = new HashMap<>();
        Integer tcId = order.getTcId();
        NbTransferCenterEntity tc = tcMap.getOrDefault(tcId, null);
        if (tc == null) {
            tc = nbTransferCenterMapper.selectById(tcId);
            if (tc != null) {
                tcMap.put(tcId, tc);
            }
        }

        NbSortingCenterEntity finalSc = sc;
        NbTransferCenterEntity finalTc = tc;
        JSONObject finalJo = cityJo;

        JSONObject tcCityJo = null;
        if (tc != null) {
            tcCityJo = commonDataUtil.getCityById(tc.getCityId());
        }
        JSONObject finalTcCityJo = tcCityJo;
        NbOrderEntity finalOrder = order;
        
        retJo.set("number", order.getPkgNo());
        NbMerchantEntity merchant = nbMerchantService.getById(order.getMerchantId());
        retJo.set("oriChannel", merchant.getMerchantCode());
        retJo.set("oriNumber", order.getCustomerOrderNo());
        retJo.set("oriCountry", order.getShipperCountry());
        retJo.set("destCountry", order.getDestCountry());
        retJo.set("status", "Shipment");

        int orderStatus = order.getOrderStatus();
        switch (orderStatus) {
            case OrderDto.ORDER_STATUS_205_DELIVERED:
                retJo.set("status", "Delivered");
                break;
            case OrderDto.ORDER_STATUS_206_FAILED_DELIVERY_WRONG_ADDRESS:
            case OrderDto.ORDER_STATUS_210_FAILED_DELIVERY:
            case OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1:
            case OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2:
            case OrderDto.ORDER_STATUS_280_FAILURE:
                retJo.set("status", "DeliveryFailure");
                break;
        }

        JSONArray events = new JSONArray();
        int userOrderId = order.getOrderId();
        if (order.getIsMps()) {
            NbOrderMpsEntity om = nbOrderMpsMapper.selectOne(new LambdaQueryWrapper<>(NbOrderMpsEntity.class).eq(NbOrderMpsEntity::getOrderId, userOrderId));
            if (om != null && om.getPOrderId() > 0) {
                // 2024-02-17 一票多件，且为子订单时，切换为查询主订单
                userOrderId = om.getPOrderId();
            }
        }

        // "select * from nb_order_path where order_id = ?", userOrderId);
        List<NbOrderPathEntity> paths = nbOrderPathService.list(new LambdaQueryWrapper<NbOrderPathEntity>().eq(NbOrderPathEntity::getOrderId, userOrderId));
        for (NbOrderPathEntity path : paths) {
            JSONObject jo = new JSONObject();
            jo.set("time", path.getAddTime().toInstant().atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
            jo.set("location", "");
            jo.set("content", "");
            jo.set("status", OrderDto.orderStatusMap.getOrDefault(path.getOrderStatus(), ""));
            jo.set("code", path.getOrderStatus());

            NbOrderStatusEntity os = nbOrderStatusService.getById(path.getOrderStatus());
            if (os != null) {
                jo.set("content", os.getContent());
            }

            if (useScCityStatus.contains(path.getOrderStatus())) {
                // 用分拣中心
                if (finalSc != null) {
                    if (finalJo != null) {
                        jo.set("location", finalJo.getStr("enName") + " CA");
                    }
                }
            }
            if (useTcCityStatus.contains(path.getOrderStatus())) {
                // 用转运中心
                if (finalTc != null) {
                    if (finalTcCityJo != null) {
                        jo.set("location", finalTcCityJo.getStr("enName") + " CA");
                    }
                }
            }
            if (useUserCityStatus.contains(path.getOrderStatus())) {
                // 使用用户地址
                jo.set("location", order.getDestCity());
            }
            events.add(jo);
        }
        retJo.set("events", events);
        return OldResult.ok("0", retJo);
    }

    /**
     * API-创建订单
     * @param paramsVo
     * @param vo
     * @return
     */
    @Override
    public OldResult add(ApiRequestParamsVo paramsVo, ApiOrderAddVo vo) {
        // 添加日志查看实际传递的参数
        log.info("Received paramsVo: " + paramsVo);
        log.info("Received vo: " + vo);
        NbMerchantEntity merchant = nbMerchantService.getById(paramsVo.getAppId());

        String timezone = merchant.getDefaultTimezone();
        String warehouseCode = vo.getWarehouseCode();
        String regionCode = vo.getRegionCode();
        String batchNo = vo.getBatchNo();
        Integer batchTotal = vo.getBatchTotal();
        if (ObjectUtil.isNull(batchTotal)) {
            batchTotal = 0;
        }

        if (StrUtil.isBlank(batchNo)) {
            batchNo = merchant.getMerchantCode() + DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMdd");
        } else {
            if (batchTotal == 0) {
                return OldResult.fail("200001", "batchTotal required");
            }
        }

        // 2023-12-17 增加ETA、ETD、ATA、ATD
        String eta = vo.getEta();
        String etd = vo.getEtd();
        Date etaDate = null, etdDate = null;
        String dateFormat = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        if (StrUtil.isNotBlank(eta)) {
            try {
                etaDate = sdf.parse(eta);
            } catch (ParseException e) {
                e.printStackTrace();
                return OldResult.fail("500201", "eta time format invalid. act:" + eta + ",expect:" + dateFormat);
            }
        }
        if (StrUtil.isNotBlank(etd)) {
            try {
                etdDate = sdf.parse(etd);
            } catch (ParseException e) {
                e.printStackTrace();
                return OldResult.fail("500202", "etd time format invalid. act:" + etd + ",expect:" + dateFormat);
            }
        }
        // "select * from nb_sorting_center where sc_code = ? limit 1", warehouseCode);
        NbSortingCenterEntity sc = nbSortingCenterService.getOne(new LambdaQueryWrapper<NbSortingCenterEntity>().eq(NbSortingCenterEntity::getScCode, warehouseCode));
        if (sc == null) {
            return OldResult.fail("500101", "The warehouse code does not exist");
        }

        // "select * from nb_order_batch where merchant_id = ? and batch_no = ? limit 1", merchant.getMerchantId(), batchNo);
        NbOrderBatchEntity ob = nbOrderBatchService.getOne(new LambdaQueryWrapper<NbOrderBatchEntity>()
                .eq(NbOrderBatchEntity::getMerchantId, merchant.getMerchantId()).eq(NbOrderBatchEntity::getBatchNo, batchNo));
        if (ob == null) {
            String requestId = String.valueOf(System.currentTimeMillis()) + RandomUtils.nextInt(1000, 99999);
            String keyToString = "nb_driver_lock_api_order_add_batchno_" + batchNo;
            try {
                if (redisUtil.tryLockAwait(keyToString, requestId, 10, 5)) {
                    // "select * from nb_order_batch where merchant_id = ? and batch_no = ? limit 1", merchant.getMerchantId(), batchNo);
                    ob = nbOrderBatchService.getOne(new LambdaQueryWrapper<NbOrderBatchEntity>()
                            .eq(NbOrderBatchEntity::getMerchantId, merchant.getMerchantId()).eq(NbOrderBatchEntity::getBatchNo, batchNo));

                    if (ob == null) {
                        ob = new NbOrderBatchEntity();
                        ob.setBatchNo(batchNo);
                        LocalDateTime.ofInstant(Instant.now(), ZoneId.of(timezone));
                        ob.setCreateTime(NBDUtils.getLocalDate(sc.getScTimezone()));
                        ob.setBatchTotal(batchTotal);
                        ob.setMerchantId(merchant.getMerchantId());
                        ob.setEta(etaDate);
                        ob.setEtd(etdDate);
                        nbOrderBatchService.save(ob);
                    }
                } else {
                    return OldResult.fail("-99", "Operations are too frequent.");
                }
            } finally {
                redisUtil.releaseLock(keyToString, requestId);
            }
        } else {
            try {
                boolean update = false;
                if (ob.getEta() == null && etaDate != null) {
                    ob.setEta(etaDate);
                    update = true;
                }
                if (ob.getEtd() == null && etdDate != null) {
                    ob.setEtd(etdDate);
                    update = true;
                }
                if (update) {
                    nbOrderBatchService.updateById(ob);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        String customerOrderNo = vo.getCustomerOrderNo(); // 客户单号，此值唯一
        String customerPOrderNo = vo.getCustomerPOrderNo(); // 20230808新增，父单号，一票多件时用
        String goodsDesc = vo.getGoodsDesc();
        String orderCategory = vo.getOrderCategory(); // 订单种类GV B2C
        String expressCategory = vo.getExpressCategory(); // 快件种类Normal
        String transportWay = vo.getTransportWay(); // 运输方式 By Air

        // 收件人
        String destName = vo.getDestName();
        String destTel = vo.getDestTel();
        String destEmail = vo.getDestEmail();
        String destCountry = vo.getDestCountry();
        String destProvince = vo.getDestProvince();
        String destCity = vo.getDestCity();
        String destAddress1 = vo.getDestAddress1();
        String destAddress2 = vo.getDestAddress2();
        String destAddress3 = vo.getDestAddress3();
        String destUnitNo = vo.getDestUnitNo();
        String destPostalCode = vo.getDestPostalCode();
        Double destLng = vo.getDestLng();
        Double destLat = vo.getDestLat();
        Integer destAddressType = vo.getDestAddressType();

        // 包裹信息
        BigDecimal pkgWeight = vo.getPkgWeight();
        BigDecimal pkgLength = vo.getPkgLength();
        BigDecimal pkgWidth = vo.getPkgWidth();
        BigDecimal pkgHeight = vo.getPkgHeight();
        String pkgNo = vo.getPkgNo(); // 包裹编号
        Integer pkgType = vo.getPkgType();  //包裹类型：这里拿到的是：普通

        // 发件人
        String shipperName = vo.getShipperName();
        String shipperTel = vo.getShipperTel();
        String shipperCountry = vo.getShipperCountry();
        String shipperProvince = vo.getShipperProvince();
        String shipperCity = vo.getShipperCity();
        String shipperAddress1 = vo.getShipperAddress1();
        String shipperAddress2 = vo.getShipperAddress2();
        String shipperAddress3 = vo.getShipperAddress3();
        String shipperPostalCode = vo.getShipperPostalCode();

        Boolean requiredSign = vo.getRequiredSign();    // 要求
        // 20230716新增
        Integer expressType = vo.getExpressType();  // 20230808，新增类型2=自提，2024-02-15新增类型3=卡派LTL
        if (expressType < 0 || expressType > 3) {
            return OldResult.fail("20000", "expressType range expect (0,1,2,3), actual=[" + expressType + "]");
        }
        Integer dangerType = vo.getDangerType();
        Integer transportType = vo.getTransportType();
        Integer failedDeliveryAction = vo.getFailedDeliveryAction();
        String bagNumber = vo.getBagNumber();
        BigDecimal pkgValue = vo.getPkgValue();
        String subChannel = vo.getSubChannel(); // 子渠道      2023-10-25 新增，区分jiayou的temu和shein

        // 2023-12-03 yes: 推送运单信息中的这一列"customerOrderNo"可以改为不唯一吗，因为一些原因客户发货然后我们进行推送数据，但是因为一些原因进行了退回然后重新进行发货
        // "select * from nb_order where merchant_id = ? and customer_order_no = ? limit 1", merchant.getMerchantId(), customerOrderNo);
//		if (order != null) {
//			return R.failed("500100", "Order duplication, customerOrderNo= [" + customerOrderNo + "]");
//			return;
//		}

        // select 1 from nb_order where pkg_no = ? limit 1", pkgNo);
        NbOrderEntity order = getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo, pkgNo));
        if (order != null) {
            return OldResult.fail("500101", "Order duplication, pkgNo= [" + pkgNo + "]");
        }

        order = new NbOrderEntity();
        order.setMerchantId(merchant.getMerchantId());
        order.setEta(etaDate);
        order.setEtd(etdDate);

        String randomStr = RandomStringUtils.randomNumeric(4);

        if (customerPOrderNo == null || customerPOrderNo.trim().equals("") || customerPOrderNo.equals(customerOrderNo)) {
            order.setIsMps(false);
        } else {
            order.setIsMps(true); // 为一票多件
            order.setCustomerPOrderNo(customerPOrderNo);
        }

        order.setOrderNo(DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss") + randomStr + merchant.getMerchantId());
        order.setScId(sc.getScId());
        order.setRegionCode(regionCode);
        order.setOrderCategory(orderCategory);
        order.setExpressCategory(expressCategory);
        order.setTransportWay(transportWay);
        order.setRequiredSign(requiredSign);
        order.setLastRouteTime(NBDUtils.getLocalDate(sc.getScTimezone()));
        order.setAddTime(NBDUtils.getLocalDate(sc.getScTimezone()));
        order.setBatchNo(batchNo);
        order.setCustomerOrderNo(customerOrderNo);
        order.setGoodsDesc(goodsDesc);
        order.setDestName(destName);
        order.setDestTel(destTel);
        order.setDestCountry(destCountry);
        order.setDestProvince(destProvince);
        order.setDestCity(destCity);
        order.setDestAddress1(destAddress1);
        order.setDestAddress2(destAddress2);
        order.setDestAddress3(destAddress3);
        order.setDestUnitNo(destUnitNo);
        order.setDestPostalCode(destPostalCode);
        order.setDestLat(destLat);
        order.setDestLng(destLng);
        order.setDestAddressType(destAddressType);
        order.setDestEmail(destEmail);

        order.setPkgWeight(pkgWeight);
        order.setPkgHeight(pkgHeight);
        order.setPkgLength(pkgLength);
        order.setPkgWidth(pkgWidth);
        order.setPkgValue(pkgValue);
        order.setPkgNo(pkgNo);
        order.setPkgType(pkgType);
        order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_0_UNSTART);
        order.setOrderStatus(merchant.getDefaultOrderStatus());

        order.setShipperName(shipperName);
        order.setShipperTel(shipperTel);
        order.setShipperCountry(shipperCountry);
        order.setShipperProvince(shipperProvince);
        order.setShipperCity(shipperCity);
        order.setShipperAddress1(shipperAddress1);
        order.setShipperAddress2(shipperAddress2);
        order.setShipperAddress3(shipperAddress3);
        order.setShipperPostalCode(shipperPostalCode);
        order.setPickNo("0");

        order.setExpressType(expressType);
        order.setDangerType(dangerType);
        order.setTransportType(transportType);
        order.setFailedDeliveryAction(failedDeliveryAction);
        order.setBagNumber(bagNumber);
        order.setSubChannel(subChannel);

        save(order);

        order.setOrderNo(DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss") + order.getOrderId());
        updateById(order);

        NbOrderPathEntity path = new OrderPathDto().createPath(order.getOrderId(), order.getOrderStatus(), 0, null, null, null, merchant.getDefaultTimezone());
        path.setScId(sc.getScId());
        path.setTcId(order.getTcId());
        nbOrderPathService.save(path);

        // int orderTotal = Db.findFirst("select count(1) cont from nb_order where batch_no = ?", batchNo).getInt("cont");
        Long count = count(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getBatchNo, batchNo));
        int orderTotal = Math.toIntExact(count);
        ob.setImpTotal(orderTotal);
        nbOrderBatchService.updateById(ob); // 数量直接传过来

        JSONObject retJo = new JSONObject();
        retJo.set("orderNo", order.getOrderNo());
        retJo.set("orderId", order.getOrderId());
        retJo.set("customerOrderNo", order.getCustomerOrderNo());
        retJo.set("pkgNo", order.getPkgNo());

        return OldResult.ok("0", retJo);
    }

    /**
     * API-更新订单状态
     * @param paramsVo
     * @param pkgNo
     * @param state
     * @return
     */
    @Override
    public OldResult updateState(ApiRequestParamsVo paramsVo, String pkgNo, String state) {
        NbMerchantEntity merchant = nbMerchantService.getById(paramsVo.getAppId());
//		String customerOrderNo = get("customerOrderNo"); // 客户单号，此值唯一
//        String state = vo.getState(); // 198CUSTOM_RELEASE_DIRECT, 199GATEWAY_TRANSIT
        if (!"CUSTOM_RELEASE_DIRECT".equals(state) && !"GATEWAY_TRANSIT".equals(state)) {
            return OldResult.fail("500200", "state invalid");
        }
        // select * from nb_order where merchant_id = ? and pkg_no = ? limit 1", merchant.getMerchantId(), pkgNo);
        NbOrderEntity order = getOne(new LambdaQueryWrapper<NbOrderEntity>()
                .eq(NbOrderEntity::getMerchantId, merchant.getMerchantId()).eq(NbOrderEntity::getPkgNo, pkgNo));
        if (order == null) {
            return OldResult.fail("500201", "Order not exist[" + pkgNo + "]");
        }

        if ("CUSTOM_RELEASE_DIRECT".equals(state)) {
            if (order.getOrderStatus() == OrderDto.ORDER_STATUS_190_ORDER_RECEIVED) {
                order.setOrderStatus(OrderDto.ORDER_STATUS_198_CUSTOM_RELEASE_DIRECT);
            } else {
                return OldResult.fail("500202", "The current status is " + OrderDto.orderStatusMap.getOrDefault(order.getOrderStatus(), "") + " and cannot be modified to CUSTOM_RELEASE_DIRECT");
            }

        } else if ("GATEWAY_TRANSIT".equals(state)) {
            if (order.getOrderStatus() == OrderDto.ORDER_STATUS_198_CUSTOM_RELEASE_DIRECT) {
                order.setOrderStatus(OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT);
            } else {
                return OldResult.fail("500203", "The current status is " + OrderDto.orderStatusMap.getOrDefault(order.getOrderStatus(), "") + " and cannot be modified to GATEWAY_TRANSIT");
            }
        }
        nbOrderMapper.updateById(order);
        NbOrderPathEntity path = new OrderPathDto().createPath(order.getOrderId(), order.getOrderStatus(), 0, null, null, null, merchant.getDefaultTimezone());
        path.setScId(order.getScId());
        path.setTcId(order.getTcId());
        nbOrderPathService.save(path);

        return OldResult.ok("0", order);
    }

    /**
     * API-获取轨迹
     * @param paramsVo
     * @param pkgNo
     * @return
     */
    @Override
    public OldResult track(ApiRequestParamsVo paramsVo, String pkgNo) {
        NbMerchantEntity merchant = nbMerchantService.getById(paramsVo.getAppId());
//		String customerOrderNo = get("customerOrderNo"); // 客户单号，此值唯一
        if (StrUtil.isBlank(pkgNo)) {
            return OldResult.fail("500300", "pkgNo can not be empty");
        }

        NbOrderEntity order = null;
        if (order == null && StrUtil.isNotBlank(pkgNo)) {
            // 2024-01-18 佳邮可以查所有订单
            if (merchant.getMerchantId() == 1001) {
                // "select order_id, customer_order_no, pkg_no, sc_id, tc_id, dest_city, dest_postal_code, is_mps from nb_order where pkg_no = ? limit 1", pkgNo);
                order = getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo, pkgNo));
            } else {
                // "select order_id, customer_order_no, pkg_no, sc_id, tc_id, dest_city, dest_postal_code, is_mps from nb_order where merchant_id = ? and pkg_no = ? limit 1", merchant.getMerchantId(), pkgNo);
                order = getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getMerchantId, merchant.getMerchantId()).eq(NbOrderEntity::getPkgNo, pkgNo));
            }
        }
        if (order == null) {
            //	return R.failed("500301", "订单不存在");
            JSONObject orderJo = new JSONObject();
            orderJo.set("customerOrderNo", pkgNo);
            orderJo.set("pkgNo", pkgNo);
            orderJo.set("track", new JSONArray());
            return OldResult.ok(orderJo);
        }

        List<Integer> useScCityStatus = new ArrayList<>();
        useScCityStatus.add(OrderDto.ORDER_STATUS_190_ORDER_RECEIVED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_191_ORDER_CANCELED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_192_CUSTOM_HOLD);
        useScCityStatus.add(OrderDto.ORDER_STATUS_198_CUSTOM_RELEASE_DIRECT);
        useScCityStatus.add(OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT);
        useScCityStatus.add(OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER);
        useScCityStatus.add(OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE);

        List<Integer> useTcCityStatus = new ArrayList<>();
        useTcCityStatus.add(OrderDto.ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER);
        useTcCityStatus.add(OrderDto.ORDER_STATUS_203_LOAD_SCANNED);
        useTcCityStatus.add(OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT);

        List<Integer> useUserCityStatus = new ArrayList<>();
        useUserCityStatus.add(OrderDto.ORDER_STATUS_204_IN_TRANSIT);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_205_DELIVERED);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_210_FAILED_DELIVERY);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_280_FAILURE);

        JSONObject orderJo = new JSONObject();
        orderJo.set("customerOrderNo", order.getCustomerOrderNo());
        orderJo.set("pkgNo", order.getPkgNo());

        Integer scId = order.getScId();
        NbSortingCenterEntity sc = nbSortingCenterService.getById(scId);
        JSONObject cityJo = null;
        if (sc != null) {
            cityJo = commonDataUtil.getCityById(sc.getCityId());
        }

        Map<Integer, NbTransferCenterEntity> tcMap = new HashMap<>();
        Integer tcId = order.getTcId();
        NbTransferCenterEntity tc = tcMap.getOrDefault(tcId, null);
        if (tc == null) {
            tc = nbTransferCenterMapper.selectById(tcId);
            if (tc != null) {
                tcMap.put(tcId, tc);
            }
        }

        NbSortingCenterEntity finalSc = sc;
        NbTransferCenterEntity finalTc = tc;
        JSONObject finalJo = cityJo;
        JSONObject tcCityJo = null;
        if (tc != null) {
            tcCityJo = commonDataUtil.getCityById(tc.getCityId());
        }
        JSONObject finalTcCityJo = tcCityJo;
        NbOrderEntity finalOrder = order;

        // 2024-02-17 为一票多件时，直接查主订单
        int useOrderId = order.getOrderId();
        if (order.getIsMps()) {
            NbOrderMpsEntity om = nbOrderMpsMapper.selectById(order.getOrderId());
            if (om != null && om.getPOrderId() > 0) {
                useOrderId = om.getPOrderId();
            }
        }

        // "select * from nb_order_path where order_id = ? order by add_timestamp desc", useOrderId);
        List<NbOrderPathEntity> paths = nbOrderPathService.list(new LambdaQueryWrapper<NbOrderPathEntity>()
                .eq(NbOrderPathEntity::getOrderId, useOrderId).orderByDesc(NbOrderPathEntity::getAddTimestamp));
        JSONArray ja = paths.stream().map(op -> {
            // JSONObject jo = op.toAppJson();
            JSONObject jo = new OrderPathDto().toAppJson(op);
            if (jo == null) {
                return null;
            }
            try {

                if (useScCityStatus.contains(op.getOrderStatus())) {
                    // 用分拣中心
                    if (finalSc != null) {
                        if (finalJo != null) {
                            jo.set("city", finalJo.getStr("enName"));
                        }
                        jo.set("postalCode", finalSc.getPostalCode());
                    }
                }
                if (useTcCityStatus.contains(op.getOrderStatus())) {
                    // 用转运中心
                    if (finalTc != null) {
                        if (finalTcCityJo != null) {
                            jo.set("city", finalTcCityJo.getStr("enName"));
                        }
                        jo.set("postalCode", finalTc.getPostalCode());
                    }
                }
                if (useUserCityStatus.contains(op.getOrderStatus())) {
                    // 使用用户地址
                    jo.set("city", finalOrder.getDestCity());
                    jo.set("postalCode", finalOrder.getDestPostalCode());
                }

            } catch (Exception e) {
                e.printStackTrace();
//                ExceptionKit.handler(e, "/api/order/tracks", this);
                log.error("/api/order/tracks", e);
                new GlobalBizExceptionHandler().handleGlobalException(e);
            }

            // 2024-04-18 自提件返回自提信息
            if (op.getOrderStatus() == OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE) {
                // "select * from nb_shelf_pkg_log where order_id = ? order by log_id desc limit 1", op.getOrderId());
                NbShelfPkgLogEntity shelfPkgLog = shelfPkgLogMapper.selectOne(new LambdaQueryWrapper<NbShelfPkgLogEntity>().eq(NbShelfPkgLogEntity::getOrderId, op.getOrderId()), false);
                if (shelfPkgLog != null) {
                    JSONObject pickupInfo = new JSONObject();

                    pickupInfo.set("pickupCode", shelfPkgLog.getPutawayCode());
                    pickupInfo.set("putawayTimestamp", shelfPkgLog.getPutawayTime() / 1000);
                    NbShelfEntity shelf = shelfMapper.selectById(shelfPkgLog.getShelfId());
                    if (shelf.getScId() > 0) {
                        pickupInfo.set("serviceTel", finalSc.getServiceTel());
                        pickupInfo.set("openingHours", finalSc.getBusinessHours());

                        // String cp = CommonDataKit.getAddress(finalSc.getProvinceId(), finalSc.getCityId());
                        pickupInfo.set("address", finalSc.getAddress() + " " + finalSc.getPostalCode());
                    } else if (shelf.getTcId() > 0) {
                        pickupInfo.set("serviceTel", finalTc.getServiceTel());
                        pickupInfo.set("openingHours", finalTc.getBusinessHours());

                        // String cp = CommonDataKit.getAddress(finalTc.getProvinceId(), finalTc.getCityId());
                        pickupInfo.set("address", finalTc.getAddress() + " " + finalTc.getPostalCode());
                    }

                    jo.set("pickupInfo", pickupInfo);
                }
            }

            return jo;
        }).filter(p -> p != null).collect(Collectors.toCollection(JSONArray::new));

        orderJo.set("track", ja);
        return OldResult.ok("0", orderJo);
    }

    /**
     * API-小包批量获取轨迹
     * @param paramsVo
     * @param pkgNos
     * @return
     */
    @Override
    public OldResult tracks(ApiRequestParamsVo paramsVo, String pkgNos) {
        NbMerchantEntity merchant = nbMerchantService.getById(paramsVo.getAppId());
//		String customerOrderNos = get("customerOrderNos"); // 客户单号，此值唯一
        int maxSize = 100;
        if (StrUtil.isBlank(pkgNos)) {
            return OldResult.fail("500300", "pkgNo can not be empty");
        }
        List<NbOrderEntity> orders = null;
        List<String> allPkgNos = new ArrayList<>();
        if (orders == null && StrUtil.isNotBlank(pkgNos)) {
            String[] pkgNoArr = pkgNos.split(",");
            if (pkgNoArr.length > maxSize) {
                return OldResult.fail("500301", "Query up to " + maxSize + " packages at a time");
            }

            List<String> pkgNoList = Arrays.stream(pkgNoArr).map(String::trim).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(pkgNoList)) {
                if (merchant.getMerchantId() == 1001) {
                    // "select order_id, customer_order_no, pkg_no, sc_id, tc_id, dest_city, dest_postal_code, is_mps, jy_mark_no, jy_order_id, jy_order_no "
                    // + "from nb_order where pkg_no in (" + in + ") or jy_order_no in (" + in + ") ");
                    LambdaQueryWrapper<NbOrderEntity> wrapper = new LambdaQueryWrapper<>();
                    wrapper.select(NbOrderEntity::getOrderId, NbOrderEntity::getCustomerOrderNo, NbOrderEntity::getPkgNo, NbOrderEntity::getScId, NbOrderEntity::getTcId,
                            NbOrderEntity::getDestCity, NbOrderEntity::getDestPostalCode, NbOrderEntity::getIsMps, NbOrderEntity::getJyMarkNo, NbOrderEntity::getJyOrderId, NbOrderEntity::getJyOrderNo);
                    wrapper.and(w -> w.in(NbOrderEntity::getPkgNo, pkgNoList)
                            .or(q -> q.in(NbOrderEntity::getJyOrderNo, pkgNoList)));
                    orders = list(wrapper);
                } else {
                    // "select order_id, customer_order_no, pkg_no, sc_id, tc_id, dest_city, dest_postal_code, is_mps from nb_order where merchant_id = ? and pkg_no in (" + in + ") ", merchant.getMerchantId());
                    orders = list(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getMerchantId, merchant.getMerchantId()).in(NbOrderEntity::getPkgNo, pkgNoList));
                }
            }
            allPkgNos = new ArrayList<String>(pkgNoArr.length);
            Collections.addAll(allPkgNos, pkgNoArr);
        }
        Map<Integer, NbSortingCenterEntity> scMap = new HashMap<>();
        Map<Integer, NbTransferCenterEntity> tcMap = new HashMap<>();

        List<Integer> useScCityStatus = new ArrayList<>();
        useScCityStatus.add(OrderDto.ORDER_STATUS_190_ORDER_RECEIVED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_191_ORDER_CANCELED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_192_CUSTOM_HOLD);
        useScCityStatus.add(OrderDto.ORDER_STATUS_198_CUSTOM_RELEASE_DIRECT);
        useScCityStatus.add(OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT);
        useScCityStatus.add(OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER);
        useScCityStatus.add(OrderDto.ORDER_STATUS_226_READY_FOR_PICK_UP);
        useScCityStatus.add(OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE);

        List<Integer> useTcCityStatus = new ArrayList<>();
        useTcCityStatus.add(OrderDto.ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER);
        useTcCityStatus.add(OrderDto.ORDER_STATUS_203_LOAD_SCANNED);
        useTcCityStatus.add(OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT);

        List<Integer> useUserCityStatus = new ArrayList<>();
        useUserCityStatus.add(OrderDto.ORDER_STATUS_204_IN_TRANSIT);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_205_DELIVERED);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_210_FAILED_DELIVERY);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_280_FAILURE);

        Map<Integer, NbOrderStatusEntity> osMap = null;
        if (!orders.isEmpty()) {
            String key = "nb_order_status_static_map";
            List<NbOrderStatusEntity> oslist = (List<NbOrderStatusEntity>) redisTemplate.opsForValue().get(key);

            // 如果缓存中没有数据，则查询并存入 Redis
            if (oslist == null) {
                oslist = nbOrderStatusService.list();
                if (oslist != null && !oslist.isEmpty()) {
                    // 将 List 存入 Redis，过期时间为 RedisUtil.MINUTE_10 * 6 秒
                    redisTemplate.opsForValue().set(key, oslist, RedisUtil.MINUTE_10 * 6, TimeUnit.SECONDS);
                }
            }

            // 如果 oslist 不为空，则转换为 Map
            if (oslist != null && !oslist.isEmpty()) {
                osMap = oslist.stream().collect(Collectors.toMap(NbOrderStatusEntity::getId, Function.identity()));
            }
        }

        Map<Integer, NbOrderStatusEntity> finalOsMap = osMap;
        JSONArray retJa = new JSONArray();
        for (NbOrderEntity order : orders) {
            JSONObject orderJo = new JSONObject();
            orderJo.set("customerOrderNo", order.getCustomerOrderNo());
            orderJo.set("pkgNo", order.getPkgNo());

            String jyTrackingNo = order.getJyOrderNo();
            if (StrUtil.isNotBlank(jyTrackingNo)) {
                if (allPkgNos.contains(jyTrackingNo)) {
                    allPkgNos.remove(jyTrackingNo);

                    orderJo.set("pkgNo", jyTrackingNo); // 2024-05-11 如果有佳邮轨迹，则将查询的轨迹返回
                }
            }
            allPkgNos.remove(order.getPkgNo());
            Integer scId = order.getScId();

            NbSortingCenterEntity sc = scMap.getOrDefault(scId, null);
            if (sc == null) {
                sc = nbSortingCenterService.getById(scId);
                if (sc != null) {
                    scMap.put(scId, sc);
                }
            }

            Integer tcId = order.getTcId();
            NbTransferCenterEntity tc = tcMap.getOrDefault(tcId, null);
            if (tc == null) {
                tc = nbTransferCenterMapper.selectById(tcId);
                if (tc != null) {
                    tcMap.put(tcId, tc);
                }
            }

            JSONObject cityJo = null;
            if (sc != null) {
                cityJo = commonDataUtil.getCityById(sc.getCityId());
            }

            NbSortingCenterEntity finalSc = sc;
            NbTransferCenterEntity finalTc = tc;
            JSONObject finalJo = cityJo;

            JSONObject tcCityJo = null;
            if (tc != null) {
                tcCityJo = commonDataUtil.getCityById(tc.getCityId());
            }
            JSONObject finalTcCityJo = tcCityJo;

            // 2024-02-17 为一票多件时，直接查主订单
            int useOrderId = order.getOrderId();
            if (order.getIsMps()) {
                NbOrderMpsEntity om = nbOrderMpsMapper.selectById(order.getOrderId());
                if (om != null && om.getPOrderId() > 0) {
                    useOrderId = om.getPOrderId();
                }
            }

            // "select * from nb_order_path where order_id = ? order by add_timestamp desc", useOrderId);
            List<NbOrderPathEntity> paths = nbOrderPathService.list(new LambdaQueryWrapper<NbOrderPathEntity>().eq(NbOrderPathEntity::getOrderId, useOrderId)
                    .orderByDesc(NbOrderPathEntity::getAddTimestamp));
            JSONArray ja = paths.stream().map(op -> {
                // JSONObject jo = op.toAppJson();
                JSONObject jo = new OrderPathDto().toAppJson(op);
                if (jo == null) {
                    return null;
                }

                jo.set("city", "");
                try {

                    if (useScCityStatus.contains(op.getOrderStatus())) {
                        // 用分拣中心
                        if (finalSc != null) {
                            if (finalJo != null) {
                                jo.set("city", finalJo.getStr("enName"));
                            }
                            jo.set("postalCode", finalSc.getPostalCode());
                        }
                    }
                    if (useTcCityStatus.contains(op.getOrderStatus())) {
                        // 用转运中心
                        if (finalTc != null) {
                            if (finalTcCityJo != null) {
                                jo.set("city", finalTcCityJo.getStr("enName"));
                            }
                            jo.set("postalCode", finalTc.getPostalCode());
                        }
                    }
                    if (useUserCityStatus.contains(op.getOrderStatus())) {
                        // 使用用户地址
                        jo.set("city", order.getDestCity());
                        jo.set("postalCode", order.getDestPostalCode());
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    // ExceptionKit.handler(e, "/api/order/tracks", this);
                    log.error("/api/order/tracks", e);
                    new GlobalBizExceptionHandler().handleGlobalException(e);
                }
                // log.info(op.getOrderStatus() + "->" + jo);
                if (finalOsMap != null) {
                    NbOrderStatusEntity os = finalOsMap.get(op.getOrderStatus());
                    if (os != null) {
                        jo.set("content", os.getContent());
                    }
                }

                // 2024-04-18 自提件返回自提信息
                if (op.getOrderStatus() == OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE) {
                    // select * from nb_shelf_pkg_log where order_id = ? order by log_id desc limit 1", op.getOrderId());
                    NbShelfPkgLogEntity shelfPkgLog = shelfPkgLogMapper.selectOne(new LambdaQueryWrapper<NbShelfPkgLogEntity>()
                            .eq(NbShelfPkgLogEntity::getOrderId, op.getOrderId()).orderByDesc(NbShelfPkgLogEntity::getLogId), false);
                    if (shelfPkgLog != null) {
                        JSONObject pickupInfo = new JSONObject();

                        pickupInfo.set("pickupCode", shelfPkgLog.getPutawayCode());
                        pickupInfo.set("putawayTimestamp", shelfPkgLog.getPutawayTime() / 1000);

                        NbShelfEntity shelf = shelfMapper.selectById(shelfPkgLog.getShelfId());
                        if (shelf.getScId() > 0) {
                            pickupInfo.set("serviceTel", finalSc.getServiceTel());
                            pickupInfo.set("openingHours", finalSc.getBusinessHours());
                            // String cp = CommonDataKit.getAddress(finalSc.getProvinceId(), finalSc.getCityId());
                            pickupInfo.set("address", finalSc.getAddress() + " " + finalSc.getPostalCode());
                        } else if (shelf.getTcId() > 0) {
                            pickupInfo.set("serviceTel", finalTc.getServiceTel());
                            pickupInfo.set("openingHours", finalTc.getBusinessHours());

                            // String cp = CommonDataKit.getAddress(finalTc.getProvinceId(), finalTc.getCityId());
                            pickupInfo.set("address", finalTc.getAddress() + " " + finalTc.getPostalCode());
                        }

                        jo.set("pickupInfo", pickupInfo);
                    }
                }


                return jo;
            }).filter(p -> p != null).collect(Collectors.toCollection(JSONArray::new));

            orderJo.set("track", ja);
            retJa.add(orderJo);
        }

        for (String existPkgNo : allPkgNos) {
            JSONObject orderJo = new JSONObject();
            orderJo.set("customerOrderNo", "");
            orderJo.set("pkgNo", existPkgNo);
            orderJo.set("track", new JSONArray());

            retJa.add(orderJo);
        }
        return OldResult.ok("0", retJa);
    }

    /**
     * API-获取面单
     * @param paramsVo
     * @param pkgNo
     * @param response
     * @return
     */
    @Override
    public OldResult label(ApiRequestParamsVo paramsVo, String pkgNo, HttpServletResponse response) {
        NbMerchantEntity merchant = nbMerchantService.getById(paramsVo.getAppId());
        // select * from nb_order where merchant_id = ? and pkg_no = ? limit 1", merchant.getMerchantId(), pkgNo);
        NbOrderEntity order = getOne(new LambdaQueryWrapper<NbOrderEntity>()
                .eq(NbOrderEntity::getMerchantId, merchant.getMerchantId()).eq(NbOrderEntity::getPkgNo, pkgNo));
        if (order == null) {
            return OldResult.fail("490", "Order does not exist[" + pkgNo + "]");
        }

        int scId = order.getScId();
        NbSortingCenterEntity sc = nbSortingCenterService.getById(scId);
        Map<String, String> params = new HashMap<>();
        List<BarCodeDto> barCodes = new ArrayList<>();

        params.put("YYZ", sc.getScCode());
        params.put("to", order.getDestName());
        params.put("toAddress", order.getDestAddress1() + " " + order.getDestCity() + " " + order.getDestProvince() + " " + order.getDestPostalCode() + " " + order.getDestCountry());
        params.put("toPostalCode", order.getDestPostalCode());
        params.put("toMobile", order.getDestTel());

        params.put("from", order.getShipperName());
        params.put("fromAddress", order.getShipperAddress1());
        params.put("oriOrderNo", order.getCustomerOrderNo());
        params.put("orderTime", order.getAddTime().toInstant().atZone(ZoneId.of(sc.getScTimezone())).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        BarCodeDto bar1 = new BarCodeDto(order.getPkgNo(), false, 20f, 50, 20, 295);
        bar1.setFontSize(12f);
        bar1.setBaseLine(16f);
        barCodes.add(bar1);

        BarCodeDto bar2 = new BarCodeDto(order.getPkgNo(), false, 10f, 35, 10, 35);
        bar2.setFontSize(12f);
        bar2.setBaseLine(16f);
        barCodes.add(bar2);

        BarCodeDto bar3 = new BarCodeDto(order.getPkgNo(), false, 0.6f, 110, 160, 20);
        bar3.setType("qr");
        bar3.setFontSize(12f);
        bar3.setBaseLine(16f);
        barCodes.add(bar3);

        List<OrderLabelDto> labelDataList = new ArrayList<>();
        labelDataList.add(new OrderLabelDto(params, barCodes));
//        String template = PathUtil.getWebRootPath() + "/WEB-INF/tpl/20250327_label_normal.pdf";
        //判断当前环境，测试用
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("win")) {
            // user.dir 获取项目根目录路径
            templatePdfPath = System.getProperty("user.dir") + File.separator + "jynx-zxoms" + File.separator + "jynx-zxoms-biz" + File.separator + "tpl" +
                    File.separator + "20250327_label_normal.pdf";
        }
        String template = templatePdfPath;

        response.setContentType("application/pdf");
        String pdfBase64 = PdfUtils.toResponseStream(template, labelDataList);

        JSONObject jo = new JSONObject();
        jo.set("pdf", pdfBase64);
        return OldResult.ok("0", jo);
    }

    /**
     * API-批量拉取POD
     * @param paramsVo
     * @param pkgNos
     * @return
     */
    @Override
    public OldResult pods(ApiRequestParamsVo paramsVo, String pkgNos) {
        NbMerchantEntity merchant = nbMerchantService.getById(paramsVo.getAppId());
//		String customerOrderNos = get("customerOrderNos"); // 客户单号，此值唯一
        int maxSize = 100;
        if (StrUtil.isBlank(pkgNos)) {
            return OldResult.fail("500300", "pkgNo can not be empty");
        }
        List<NbOrderEntity> orders = null;
        List<String> allPkgNos = new ArrayList<>();
        if (orders == null && StrUtil.isNotBlank(pkgNos)) {
            String[] pkgNoArr = pkgNos.split(",");
            if (pkgNoArr.length > maxSize) {
                return OldResult.fail("500301", "Query up to " + maxSize + " packages at a time");
            }

            // String in = Arrays.asList(pkgNoArr).stream().map(s -> "'" + s.trim() + "'").collect(Collectors.joining(","));
            List<String> pkgNoList = Arrays.stream(pkgNoArr).map(String::trim).collect(Collectors.toList());
            if (merchant.getMerchantId() == 1001) {
                // "select order_id, customer_order_no, pkg_no, sc_id, tc_id, dest_city, dest_postal_code, is_mps, order_status from nb_order where pkg_no in (" + in + ") ");
                orders = list(new LambdaQueryWrapper<NbOrderEntity>().in(NbOrderEntity::getPkgNo, pkgNoList));
            } else {
                // "select order_id, customer_order_no, pkg_no, sc_id, tc_id, dest_city, dest_postal_code, is_mps, order_status from nb_order where merchant_id = ? and pkg_no in (" + in + ") ", merchant.getMerchantId());
                orders = list(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getMerchantId, merchant.getMerchantId())
                        .in(NbOrderEntity::getPkgNo, pkgNoList));
            }
            allPkgNos = new ArrayList<String>(pkgNoArr.length);
            Collections.addAll(allPkgNos, pkgNoArr);
        }
        List<Integer> orderIds = orders.stream().map(NbOrderEntity::getOrderId).collect(Collectors.toList());
        Map<Integer, NbOrderEntity> orderMap = orders.stream().collect(Collectors.toMap(NbOrderEntity::getOrderId, o -> o));

        // "select group_concat(pkg_image) images, group_concat(put_image) images2, order_id from nb_order_sign_image where order_id in (" + orderIdArrStr + ") group by order_id");
        if (orderIds == null || orderIds.isEmpty()) {
            throw new IllegalArgumentException("orderIds cannot be empty");
        }
        List<ApiOrderVo> images = nbOrderSignImageMapper.findImagesByOrderIds(orderIds);
        // "select * from nb_order_path where order_status = ? and order_id in (" + orderIdArrStr + ")", OrderDto.ORDER_STATUS_205_DELIVERED);
        List<NbOrderPathEntity> paths = nbOrderPathService.list(new LambdaQueryWrapper<NbOrderPathEntity>().eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_205_DELIVERED)
                .in(NbOrderPathEntity::getOrderId, orderIds));

        Map<Integer, NbOrderPathEntity> pathMap = paths.stream().collect(Collectors.toMap(NbOrderPathEntity::getOrderId, op -> op, (a, b) -> {
            NbOrderPathEntity op1 = (NbOrderPathEntity) a;
            NbOrderPathEntity op2 = (NbOrderPathEntity) b;
            if (op1.getPathId() > op2.getPathId()) {
                return op1;
            }
            return op2;
        }));

        JSONArray ja = new JSONArray();
        for (ApiOrderVo image : images) {
            Integer orderId = image.getOrderId();
            String path = image.getImages();
            String path2 = image.getImages2();

            NbOrderEntity order = orderMap.get(orderId);

            JSONObject jo = new JSONObject();
            if (order.getOrderStatus() == OrderDto.ORDER_STATUS_205_DELIVERED) {
                NbOrderPathEntity op = pathMap.get(order.getOrderId());
                if (op != null) {
                    jo.set("location", op.getPathAddr());
                    jo.set("lat", op.getScanLat());
                    jo.set("lng", op.getScanLng());
                }
            }

            String[] imageArr1 = path.split(",");
            if (StrUtil.isNotBlank(path2)) {
                String[] imageArr2 = path2.split(",");
                imageArr1 = ArrayUtils.addAll(imageArr1, imageArr2);
            }

            jo.set("pkgNo", order.getPkgNo());
            jo.set("pods", imageArr1);
            jo.set("serviceTel", "**************");
            ja.add(jo);

            allPkgNos.remove(order.getPkgNo());
        }
        for (String pkgNo : allPkgNos) {
            JSONObject jo = new JSONObject();
            jo.set("pkgNo", pkgNo);
            jo.set("pods", new JSONArray());
            ja.add(jo);
        }

        return OldResult.ok("0", ja);
    }

    /**
     * API-获取JY订单号
     * @param paramsVo
     * @param orderNo
     * @return
     */
    @Override
    public OldResult getJyTrackingNumber(ApiRequestParamsVo paramsVo, String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return OldResult.fail("900000", "orderNo can not be empty");
        }
        NbMerchantEntity merchant = nbMerchantService.getById(paramsVo.getAppId());
        orderNo = orderNo.trim();

        // "select * from nb_order where merchant_id = ? and order_no = ? limit 1", merchant.getMerchantId(), orderNo);
        NbOrderEntity order = getOne(new LambdaQueryWrapper<NbOrderEntity>()
                .eq(NbOrderEntity::getMerchantId, merchant.getMerchantId()).eq(NbOrderEntity::getOrderNo, orderNo));
        if (order == null) {
            return OldResult.fail("900001", "orderNo not found[" + orderNo + "]");
        }

        String jyOrder = order.getJyOrderNo();
        JSONObject ret = new JSONObject();
        ret.set("orderNo", orderNo);
        ret.set("orderId", order.getOrderId());
        ret.set("jyOrder", jyOrder);
        ret.set("jyMarkNo", order.getJyMarkNo());
        ret.set("customerOrderNo", order.getCustomerOrderNo());

        return OldResult.ok("0", ret);
    }

    /**
     * API-添加订单轨迹
     * @param paramsVo
     * @param vo
     * @return
     */
    @Override
    public OldResult addPath(ApiRequestParamsVo paramsVo, ApiOrderAddPathVo vo) {
        String pkgNo = vo.getPkgNo();
        if (StrUtil.isBlank(pkgNo)) {
            return OldResult.fail("900000", "pkgNo can not be empty");
        }
        NbMerchantEntity merchant = nbMerchantService.getById(paramsVo.getAppId());

        // "select * from nb_order where merchant_id = ? and pkg_no = ? limit 1", merchant.getMerchantId(), pkgNo);
        NbOrderEntity order = getOne(new LambdaQueryWrapper<NbOrderEntity>()
                .eq(NbOrderEntity::getMerchantId, merchant.getMerchantId()).eq(NbOrderEntity::getPkgNo, pkgNo));
        if (order == null) {
            return OldResult.fail("900001", "order not found[" + pkgNo + "]");
        }
        Integer statusCode = vo.getStatusCode();
        NbOrderStatusEntity os = nbOrderStatusService.getById(statusCode);
        if (os == null) {
            return OldResult.fail("900002", "order status invalid [" + statusCode + "]");
        }

        // "select 1 from nb_order_path where order_id = ? and order_status = ? limit 1", order.getOrderId(), statusCode);
        NbOrderPathEntity checkOp = nbOrderPathService.getOne(new LambdaQueryWrapper<NbOrderPathEntity>()
                .eq(NbOrderPathEntity::getOrderId, order.getOrderId()).eq(NbOrderPathEntity::getOrderStatus, statusCode));
        if (checkOp != null) {
            return OldResult.fail("900004", "path exist [" + pkgNo + "," + statusCode + "]");
        }

        String address = vo.getAddress();
        String time = vo.getTime(); // yyyy-MM-dd'T'HH:mm:ssZZ   2024-11-07T11:32:53+0000
        Double lat = vo.getLat();
        Double lng = vo.getLng();
        Date date;
        try {
            date = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.parse(time);
        } catch (Exception e) {
            e.printStackTrace();
            return OldResult.fail("900003", "path time format invalid. expect yyyy-MM-dd'T'HH:mm:ssZZ, actual[" + time + "]");
        }
        ZonedDateTime zdt = ZonedDateTime.parse(time, DateTimeFormatter.ISO_DATE_TIME);
        String timezone = zdt.getOffset().getId();
        long milliTimestamp = zdt.toEpochSecond() * 1000;

        int orderId = order.getOrderId();
        NbOrderPathEntity path = new OrderPathDto().createPath(orderId, statusCode, 0, lat, lng, address, timezone, milliTimestamp);
        path.setScId(order.getScId());
        path.setTcId(order.getTcId());
        nbOrderPathService.save(path);

        order.setOrderStatus(statusCode);
        updateById(order);

        return OldResult.ok("0");
    }

    /*
    *
    *   根据邮编和重量区间计算订单价格
    *
    * */
    @Override
    public Boolean computeOrderCost(String orderIds) {
        //使用逗号分割
        List<String> orderIdsList = Arrays.stream(orderIds.trim().split(","))
                .map(String::trim)
                .collect(Collectors.toList());

        //查询需要计算的订单信息
        LambdaQueryWrapper<NbOrderEntity> queryWrapper = Wrappers.lambdaQuery(NbOrderEntity.class)
                .in(NbOrderEntity::getOrderId, orderIdsList);
        List<NbOrderEntity> orderList = nbOrderMapper.selectList(queryWrapper);

        //查询派送费用规则，获取相应的邮编分区和重量段
        NbPaisongExpenseRuleEntity nbPaisongExpenseRule = nbPaisongExpenseRuleMapper.selectOne(new LambdaQueryWrapper<NbPaisongExpenseRuleEntity>()
                .eq(NbPaisongExpenseRuleEntity::getStatus, 1), false);

        //获取邮编分区组
        NbPostalCodeGroupEntity postalCodeGroup = nbPostalCodeGroupMapper.selectOne(new LambdaQueryWrapper<NbPostalCodeGroupEntity>()
                .eq(NbPostalCodeGroupEntity::getIsValid, 1)
                .eq(NbPostalCodeGroupEntity::getId,nbPaisongExpenseRule.getPostalId()),false);

        List<NbPostalCodeDetailEntity> postalCodeDetail = new ArrayList<>();
        if (null != postalCodeGroup.getId()){
            //获取邮编分区明细
            postalCodeDetail = nbPostalCodeDetailMapper.selectList(new LambdaQueryWrapper<NbPostalCodeDetailEntity>()
                    .eq(NbPostalCodeDetailEntity::getGroupId, postalCodeGroup.getId()));
        }
        //获取重量段
        NbWeightGroupEntity weightGroup = nbWeightGroupMapper.selectOne(new LambdaQueryWrapper<NbWeightGroupEntity>()
                .eq(NbWeightGroupEntity::getStatus, 1)
                .eq(NbWeightGroupEntity::getId,nbPaisongExpenseRule.getWeightId()),false);

        List<NbWeightDetailEntity> weightDetail = new ArrayList<>();
        if (null != weightGroup.getId()) {
            //获取重量段明细
            weightDetail = nbWeightDetailMapper.selectList(new LambdaQueryWrapper<NbWeightDetailEntity>()
                    .eq(NbWeightDetailEntity::getGroupId, weightGroup.getId()));
        }

        //遍历获取邮编分区和价格
        for (NbOrderEntity order : orderList) {
            //获取当前订单的邮编
            String destPostalCode = order.getDestPostalCode();
            //判断该订单的邮编，是否在邮编分区中
            for (NbPostalCodeDetailEntity detail : postalCodeDetail) {
                // 获取起始邮编和结束邮编
                String startPostalCode = detail.getStartPostalCode().replaceAll("\\s", "").toUpperCase();
                String endPostalCode = detail.getEndPostalCode().replaceAll("\\s", "").toUpperCase();
                String orderPostalCode = destPostalCode.replaceAll("\\s", "").toUpperCase();
                //比较字符串的字典顺序
                if (orderPostalCode.compareTo(startPostalCode) >= 0 && orderPostalCode.compareTo(endPostalCode) <= 0) {
                    //获取该订单的重量
                    BigDecimal weight = order.getPkgWeight();
                    //先判断重量是否合理，重泡比，体积/6000<重量
                    BigDecimal isMultiply = order.getPkgLength().multiply(order.getPkgWidth()).multiply(order.getPkgHeight());
                    if(isMultiply.compareTo(weight)>0){
                        order.setOrderRemark("(The weight ratio cannot be greater than the weight)重泡比不能大于重量");
                        nbOrderMapper.updateById(order);
                        continue;
                    }
                    //判断该订单的重量，是否在重量段中
                    for (NbWeightDetailEntity weightDetailEntity : weightDetail) {
                        if (weight.compareTo(weightDetailEntity.getStartWeight()) >= 0 && weight.compareTo(weightDetailEntity.getEndWeight()) <= 0) {
                            //计算价格
                            BigDecimal orderCost = weightDetailEntity.getPieceWeight();
                            //System.out.println("重量明细："+weightDetailEntity.getStartWeight()+"-"+weightDetailEntity.getEndWeight()+"--匹配成功");
                            //将计算后的价格更新到订单中
                            order.setOrderCost(orderCost);
                            nbOrderMapper.updateById(order);
                            break;
                        }
                    }
                    //System.out.println("邮编分区："+detail.getStartPostalCode()+"-"+detail.getEndPostalCode()+"--匹配成功");
                    break;
                }else{       //如果未匹配到邮编，则添加订单备注告知客户原因
                    order.setOrderRemark("(Order cost calculation: This postcode is not covered)订单费用计算：该邮编未被覆盖");
                    nbOrderMapper.updateById(order);
                }
            }
        }
        return Boolean.TRUE;
    }



    public static void main(String[] args) {
        log.info(DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(System.currentTimeMillis()));
    }

}