package com.jygjexp.jynx.zxoms.send.task;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderPathEntity;
import com.jygjexp.jynx.zxoms.entity.NbPatchPathOrderEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderPathService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbPatchPathOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenchang
 * @Description: 【同步】同步佳邮轨迹
 * @Date: 2024/10/9 23:47
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class PatchOrderPathTask {
    private final NbPatchPathOrderService nbPatchPathOrderService;
    private final NbOrderService nbOrderService;
    private final NbOrderPathService nbOrderPathService;

    @SneakyThrows
    @XxlJob("patchOrderPathHandler")
    public void patchOrderPathHandler() {
//        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务：【同步佳邮轨迹】于:{}，输入参数{}", LocalDateTime.now(), "运行中");

        // "select * from nb_patch_path_order where finished = false");
        LambdaQueryWrapper<NbPatchPathOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NbPatchPathOrderEntity::getFinished,false);
        List<NbPatchPathOrderEntity> orders = nbPatchPathOrderService.list(wrapper);

        for (NbPatchPathOrderEntity pporder : orders) {
            if (pporder.getSourceType() == 1) { // uni
                uniSync(pporder);
            } else if (pporder.getSourceType() == 2) { // jy
                jySync(pporder);
            }
        }

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【同步佳邮轨迹】执行结束，时间: {}", LocalDateTime.now());
    }

    private void jySync(NbPatchPathOrderEntity pporder) {
        NbOrderEntity order = nbOrderService.getById(pporder.getOrderId());

        JSONArray trcknos = new JSONArray();
        trcknos.add(order.getPkgNo());

        HttpRequest request = HttpRequest.post("http://api.jygjexp.com/v1/api/tracking/query/trackNB");
        request.header("apiKey", "675bfe2fd67105e9a88e564bf0f0344c");
        request.body(trcknos.toJSONString());
        HttpResponse response = request.execute();
        String result = response.body();
        log.info("JL::" + result);
        XxlJobHelper.log("JL::" + result);

        JSONObject retJo = JSON.parseObject(result);
        if (retJo.getString("message").equals("success") && retJo.containsKey("data")) {
            JSONObject tracks = retJo.getJSONArray("data").getJSONObject(0);
            JSONArray fromDetails = tracks.getJSONArray("fromDetail");

            if (fromDetails.size() > 0) {
                JSONArray localJa = new JSONArray();

                for (int i = 0; i < fromDetails.size(); i++) {
                    JSONObject item = fromDetails.getJSONObject(i);

                    int pathCode = item.getIntValue("pathCode");
                    String pathTime = item.getString("pathTime");
                    String timezone = "-07:00";
                    String pathLocation = item.getString("pathLocation");
                    String pathInfo = item.getString("pathInfo");

                    int checkOrderStatus = 0;
                    switch (pathCode) {
                        case 315:
                            // 190
                            break;
                        case 500:
                            checkOrderStatus = OrderDto.ORDER_STATUS_200_PARCEL_SCANNED;
                            break;
                        case 510:
                            checkOrderStatus = OrderDto.ORDER_STATUS_204_IN_TRANSIT;
                            break;
                        case 520:
                            checkOrderStatus = OrderDto.ORDER_STATUS_205_DELIVERED;
                            break;
                        case 501:
                            checkOrderStatus = OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER;
                            break;
                        default:
                            XxlJobHelper.log("orderId=" + pporder.getOrderId() + ",pkgNo=" + order.getPkgNo() + ",pathCode=" + pathCode + "未处理");
                            break;
                    }

                    double lat = 0;
                    double lng = 0;

                    if (checkOrderStatus > 0) {
                        LocalDateTime ldt = LocalDateTime.parse(pathTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        ZonedDateTime zdt = ldt.atZone(ZoneId.of(timezone));
                        long milliTimestamp = zdt.toEpochSecond() * 1000;

                        // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", order.getOrderId(), checkOrderStatus);
                        LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(NbOrderPathEntity::getOrderId, order.getOrderId()).eq(NbOrderPathEntity::getOrderStatus, checkOrderStatus);
                        NbOrderPathEntity op = nbOrderPathService.getOne(wrapper, false);

                        if (op == null) {
                            XxlJobHelper.log("创建轨迹orderId=" + order.getOrderId() + ",pkgNo=" + order.getPkgNo() + ",orderStatus=" + checkOrderStatus + ",time=" + pathTime + ",addr=" + pathLocation);

                            op = new OrderPathDto().createPath(order.getOrderId(), checkOrderStatus, 0, lat, lng, pathLocation, timezone, milliTimestamp);
                            nbOrderPathService.save(op);
                            order.setOrderStatus(op.getOrderStatus());
                            nbOrderService.updateById(order);

                            if (checkOrderStatus == OrderDto.ORDER_STATUS_205_DELIVERED) {
                                pporder.setFinished(true);
                                pporder.setFinishedTime(new Date());

                                order.setDeliveryedTime(op.getAddTime());
                                order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_4_FINISHED);
                                order.setUniDelivered(true); // 标记为UNI派送的
                                order.setUniDeliveredTime(op.getAddTime());
                                nbOrderService.updateById(order);
                            }
                        }

                    }
                }

            }
        }

        pporder.setLastSyncTime(new Date());
        pporder.setLastData(result);
        nbPatchPathOrderService.updateById(pporder);
    }

    private void uniSync(NbPatchPathOrderEntity pporder) {
        NbOrderEntity order = nbOrderService.getById(pporder.getOrderId());

        String uniApi = "https://delivery-api.uniuni.ca/cargo/trackinguniuninew?id=" + order.getPkgNo() + "&key=SMq45nJhQuNR3WHsJA6N";
        log.info(uniApi);
        XxlJobHelper.log(uniApi);

        String result = HttpUtil.get(uniApi);
        log.info(result);
        XxlJobHelper.log(result);

        if (1 == 1) {
            return;
        }

        JSONObject uniJo = JSON.parseObject(result);
        if ("SUCCESS".equals(uniJo.getString("status")) && uniJo.containsKey("data")) {
            JSONArray valid_tno = uniJo.getJSONObject("data").getJSONArray("valid_tno");

            JSONArray spath_list = valid_tno.getJSONObject(0).getJSONArray("spath_list");

            for (int i=0; i<spath_list.size(); i++) {
                JSONObject jo = spath_list.getJSONObject(i);
                log.info("jo",jo);
                XxlJobHelper.log("jo",jo);

                Long pathTime = jo.getLong("pathTime");
                Integer state = jo.getInteger("state");
                String pathAddr = jo.getString("pathAddr");
                Double lat = jo.getDouble("lat");
                Double lng = jo.getDouble("lng");

                String localTime = jo.getJSONObject("dateTime").getString("localTime");
                String timezone = jo.getJSONObject("dateTime").getString("timezone");

                Integer checkOrderStatus = 0;
                switch (state) {
                    case 190:
                        break;
                    case 199:
                        checkOrderStatus = OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT;
                        break;
                    case 200:
                        checkOrderStatus = OrderDto.ORDER_STATUS_200_PARCEL_SCANNED;
                        break;
                    case 202:
                        checkOrderStatus = OrderDto.ORDER_STATUS_204_IN_TRANSIT;
                        break;
                    case 231:
                        checkOrderStatus = OrderDto.ORDER_STATUS_210_FAILED_DELIVERY;
                        break;
                    case 203:
                        checkOrderStatus = OrderDto.ORDER_STATUS_205_DELIVERED;
                        break;
                    default :
                        log.info("未处理的state:" + state);
                        XxlJobHelper.log("未处理的state:" + state);
                        break;
                }

                if (checkOrderStatus > 0) {
                    // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", order.getOrderId(), checkOrderStatus);
                    LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(NbOrderPathEntity::getOrderId, order.getOrderId()).eq(NbOrderPathEntity::getOrderStatus, checkOrderStatus);
                    NbOrderPathEntity op202 = nbOrderPathService.getOne(wrapper, false);

                    if (op202 == null) {
                        // createPath(order.getOrderId(), checkOrderStatus, 0, lat, lng, pathAddr, timezone, pathTime);
                        op202 = new OrderPathDto().createPath(order.getOrderId(), checkOrderStatus, 0, lat, lng, pathAddr, timezone, pathTime);
                        nbOrderPathService.save(op202);
                        order.setOrderStatus(op202.getOrderStatus());
                        nbOrderService.updateById(order);
                    }

                    if (checkOrderStatus == OrderDto.ORDER_STATUS_205_DELIVERED) {
                        pporder.setFinished(true);
                        pporder.setFinishedTime(new Date());
                    }
                }

            }
        }

        pporder.setLastSyncTime(new Date());
        pporder.setLastData(result);
        nbPatchPathOrderService.updateById(pporder);
    }

}
