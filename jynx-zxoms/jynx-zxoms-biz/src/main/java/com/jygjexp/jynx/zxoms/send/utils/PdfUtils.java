package com.jygjexp.jynx.zxoms.send.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.*;
import com.itextpdf.text.pdf.qrcode.EncodeHintType;
import com.itextpdf.text.pdf.qrcode.ErrorCorrectionLevel;
import com.jygjexp.jynx.zxoms.send.dto.BarCodeDto;
import com.jygjexp.jynx.zxoms.send.dto.OrderLabelDto;
import org.apache.commons.codec.binary.Base64;

import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

public class PdfUtils {

    public static void main(String[] args) {
//		fromPDFTempletToPdfWithValue();
    }

    public static void tplExport(String tplFile, String tarFile, JSONArray data) {
        String fileName = tplFile; //
        try {
            int pages = data.size();
            ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pages];//用于存储每页生成PDF流

            /** 向PDF模板中插入数据 */
            for (int item = 0; item < pages; item++) {
                baos[item] = new ByteArrayOutputStream();
                PdfReader reader = new PdfReader(fileName);
                PdfStamper ps = new PdfStamper(reader, baos[item]);

                AcroFields form = ps.getAcroFields();

                JSONObject jo = data.getJSONObject(item);

                form.setField("consignee", jo.getString("consignee"));
                form.setField("consignee_tel", jo.getString("consignee_tel"));
                form.setField("consignee_address", jo.getString("consignee_address"));
                form.setField("consignee_zip", jo.getString("consignee_zip"));
                form.setField("country", "中国");
                form.setField("goods_item", jo.getString("goods_item"));
                form.setField("sender", jo.getString("sender"));
                form.setField("sender_addr", jo.getString("sender_addr"));
                form.setField("sender_mobile", jo.getString("sender_mobile"));
                form.setField("senderAddress", jo.getString("senderAddress"));

                PdfContentByte over = ps.getOverContent(1);
                Barcode39 code39 = new Barcode39();
                code39.setCode(jo.getString("logi_no").toUpperCase());
                code39.setStartStopText(false);
                try {
                    Image img = code39.createImageWithBarcode(over, null, null);
                    img.setAlignment(1);
                    img.setAbsolutePosition(340, 740);
                    over.addImage(img);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                ps.setFormFlattening(true);
                ps.close();
                reader.close();
            }

            FileOutputStream fos = new FileOutputStream(tarFile);
            Document doc = new Document();


            PdfCopy pdfCopy = new PdfCopy(doc, fos);
            doc.open();
            PdfImportedPage impPage = null;
            /**取出之前保存的每页内容*/
            for (int i = 0; i < pages; i++) {
                impPage = pdfCopy.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);

                pdfCopy.addPage(impPage);

                doc.add(new Paragraph("Barcode 3 of 9"));
                Barcode39 code39 = new Barcode39();
                code39.setCode("ITEXT IN ACTION");
                doc.add(code39.createImageWithBarcode(pdfCopy.getDirectContent(), null, null));
            }
            doc.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // doc.close();
        }
    }


    public static void wecomExcelToPDFExport(String tplFile, String tarFile, JSONArray data, int barCodeType) {
        String fileName = tplFile; //
        try {
            int pages = data.size();
            ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pages];//用于存储每页生成PDF流

            /** 向PDF模板中插入数据 */
            for (int item = 0; item < pages; item++) {
                baos[item] = new ByteArrayOutputStream();
                PdfReader reader = new PdfReader(fileName);
                PdfStamper ps = new PdfStamper(reader, baos[item]);

                AcroFields form = ps.getAcroFields();

                JSONObject jo = data.getJSONObject(item);

                form.setField("consignee", jo.getString("consignee"));
                form.setField("consignee2", jo.getString("consignee"));
                form.setField("consignee_tel", jo.getString("consignee_tel"));
                form.setField("consignee_tel2", jo.getString("consignee_tel"));
                form.setField("consignee_mobile", jo.getString("consignee_mobile"));
                form.setField("consignee_mobile2", jo.getString("consignee_mobile"));
                form.setField("consignee_addr", jo.getString("consignee_addr"));
                form.setField("consignee_addr2", jo.getString("consignee_addr"));
                form.setField("consignee_zip", jo.getString("consignee_zip"));
                form.setField("goods_desc", jo.getString("goods_desc"));
                form.setField("logi_no", jo.getString("logi_no"));
                form.setField("sender", jo.getString("sender"));
                form.setField("sender2", jo.getString("sender"));
                form.setField("sender_mobile", jo.getString("sender_mobile"));
                form.setField("sender_mobile2", jo.getString("sender_mobile"));
                form.setField("sender_addr", jo.getString("sender_addr"));
                form.setField("sender_addr2", jo.getString("sender_addr"));
                form.setField("volume", jo.getString("volume"));
                form.setField("weight", jo.getString("weight"));
                form.setField("customs", jo.getString("customs"));
                form.setField("oriSenderCountry", jo.getString("oriSenderCountry"));
                form.setField("oriProductCountry", jo.getString("oriProductCountry"));
                form.setField("price", jo.getString("price"));
                form.setField("length", jo.getString("length"));
                form.setField("wide", jo.getString("wide"));
                form.setField("height", jo.getString("height"));
                form.setField("clientCode", jo.getString("clientCode"));
                form.setField("mainCode", jo.getString("mainCode"));

                String code = jo.getString("code");

                PdfContentByte over = ps.getOverContent(1);
                Barcode barcode = new Barcode39();

                if (barCodeType == 39) {
                    barcode = new Barcode39();
                } else if (barCodeType == 128) {
                    barcode = new Barcode128();
                }

                barcode.setCode(code);
                barcode.setStartStopText(false);
                try {
                    Image img = barcode.createImageWithBarcode(over, null, null);
                    img.setAlignment(5);
                    img.setAbsolutePosition(115, 380);
                    if (code.length() > 13) {
                        img.scalePercent(90, 100);
                    }
                    if (code.length() <= 8) {
                        img.scalePercent(180, 100);
                    }
                    img.setCompressionLevel(0);
                    over.addImage(img);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                PdfContentByte over_1 = ps.getOverContent(1);
                Barcode code1 = new Barcode39();
                if (barCodeType == 39) {
                    code1 = new Barcode39();
                } else if (barCodeType == 128) {
                    code1 = new Barcode128();
                }

                code1.setCode(code);
                code1.setStartStopText(false);
                code1.setCodeType(1);
                try {
                    Image img = code1.createImageWithBarcode(over, null, null);
                    img.setAlignment(5);
                    img.setAbsolutePosition(20, 30);
                    if (code.length() > 13) {
                        img.scalePercent(90, 100);
                    }
                    if (code.length() <= 8) {
                        img.scalePercent(180, 100);
                    }
                    img.setCompressionLevel(0);
                    over_1.addImage(img);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                ps.setFormFlattening(true);
                ps.close();
                reader.close();
            }

            FileOutputStream fos = new FileOutputStream(tarFile);
            Document doc = new Document();


            PdfCopy pdfCopy = new PdfCopy(doc, fos);
            doc.open();
            PdfImportedPage impPage = null;
            /**取出之前保存的每页内容*/
            for (int i = 0; i < pages; i++) {
                impPage = pdfCopy.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);

                pdfCopy.addPage(impPage);

                doc.add(new Paragraph("Barcode 3 of 9"));
                Barcode39 code39 = new Barcode39();
                code39.setCode("ITEXT IN ACTION");
                doc.add(code39.createImageWithBarcode(pdfCopy.getDirectContent(), null, null));
            }
            doc.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // doc.close();
        }
    }

    public static void saveExport(String tplFile, String tarFile, JSONArray data) {
        String fileName = tplFile; //
        try {
            int pages = Double.valueOf(Math.ceil(data.size() / 4d)).intValue();
            ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pages];//用于存储每页生成PDF流

            /** 向PDF模板中插入数据 */
            int cursor = 0;
            for (int item = 0; item < pages; item++) {
                baos[item] = new ByteArrayOutputStream();
                PdfReader reader = new PdfReader(fileName);
                PdfStamper ps = new PdfStamper(reader, baos[item]);

                AcroFields form = ps.getAcroFields();

                for (int index = 1; index < 5; index++) {
                    String attach = "";
                    if (index > 1) {
                        attach = index - 1 + "";
                    }

                    if (cursor >= data.size()) {
                        break;
                    }

                    JSONObject jo = data.getJSONObject(cursor);
                    form.setField("consignee" + attach, jo.getString("consignee" + attach));
                    form.setField("consignee_tel" + attach, jo.getString("consignee_tel" + attach));
                    form.setField("consignee_address" + attach, jo.getString("consignee_address" + attach));
                    form.setField("consignee_zip" + attach, jo.getString("consignee_zip" + attach));
                    form.setField("country" + attach, "中国");

                    form.setField("items" + attach, jo.getString("items" + attach));
                    form.setField("weight" + attach, jo.getDouble("weight" + attach) + "");
//					form.setField("sender", jo.getString("sender"));
//					form.setField("sender_addr", jo.getString("sender_addr"));
//					form.setField("sender_mobile", jo.getString("sender_mobile"));

                    PdfContentByte over = ps.getOverContent(1);
                    Barcode39 code39 = new Barcode39();
                    code39.setCode(jo.getString("logi_no" + attach).toUpperCase());
                    code39.setStartStopText(false);

                    try {
                        int offset = (index - 1) * -200;
                        Image img = code39.createImageWithBarcode(over, null, null);
                        img.setAlignment(1);
                        img.setAbsolutePosition(25, 765 + offset);
                        over.addImage(img);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }


                    cursor++;
                }
                ps.setFormFlattening(true);
                ps.close();
                reader.close();

            }

            FileOutputStream fos = new FileOutputStream(tarFile);
            Document doc = new Document();

            PdfCopy pdfCopy = new PdfCopy(doc, fos);
            doc.open();
            PdfImportedPage impPage = null;
            /**取出之前保存的每页内容*/
            for (int i = 0; i < pages; i++) {
                impPage = pdfCopy.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);

                pdfCopy.addPage(impPage);

                doc.add(new Paragraph("Barcode 3 of 9"));
                Barcode39 code39 = new Barcode39();
                code39.setCode("ITEXT IN ACTION");
                doc.add(code39.createImageWithBarcode(pdfCopy.getDirectContent(), null, null));
            }

            doc.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // doc.close();
        }
    }

    public static void wtdExport(String targetPath, JSONArray data) {
        String srcPath = PathUtil.getWebRootPath() + "/WEB-INF/tpl/WTD_order_fmt.pdf";

        String fileName = srcPath; //
        try {
            int pages = data.size();
            ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pages];//用于存储每页生成PDF流

            /** 向PDF模板中插入数据 */
            for (int item = 0; item < pages; item++) {
                baos[item] = new ByteArrayOutputStream();
                PdfReader reader = new PdfReader(fileName);
                PdfStamper ps = new PdfStamper(reader, baos[item]);

                AcroFields form = ps.getAcroFields();

                JSONObject jo = data.getJSONObject(item);

                form.setField("consignee", jo.getString("consignee"));
                form.setField("consignee_tel", jo.getString("consigneeMobile"));
                form.setField("consignee_addr", jo.getString("consigneeAddr"));

                form.setField("sender", jo.getString("sender"));
                form.setField("sender_tel", jo.getString("senderMobile"));
                form.setField("sender_addr", jo.getString("senderAddr"));

                String code = jo.getString("logiNo");

                PdfContentByte over = ps.getOverContent(1);
                Barcode barcode = new Barcode39();

                barcode.setCode(code);
                barcode.setStartStopText(false);
                try {
                    Image img = barcode.createImageWithBarcode(over, null, null);
                    img.setAlignment(5);
                    img.setAbsolutePosition(185, 410);
                    if (code.length() > 13) {
                        img.scalePercent(90, 100);
                    }
                    over.addImage(img);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                ps.setFormFlattening(true);
                ps.close();
                reader.close();
            }

            FileOutputStream fos = new FileOutputStream(targetPath);
            Document doc = new Document();

            PdfCopy pdfCopy = new PdfCopy(doc, fos);
            doc.open();
            PdfImportedPage impPage = null;
            /**取出之前保存的每页内容*/
            for (int i = 0; i < pages; i++) {
                impPage = pdfCopy.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);

                pdfCopy.addPage(impPage);

            }

            doc.close();

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // doc.close();
        }

    }

    public static void sfToPDFExport(String tplFile, String tarFile, JSONArray data, int barCodeType) {
        String fileName = tplFile; //
        try {
            int pages = data.size();
            ByteArrayOutputStream baos[] = new ByteArrayOutputStream[pages];//用于存储每页生成PDF流

            /** 向PDF模板中插入数据 */
            for (int item = 0; item < pages; item++) {
                baos[item] = new ByteArrayOutputStream();
                PdfReader reader = new PdfReader(fileName);
                PdfStamper ps = new PdfStamper(reader, baos[item]);

                AcroFields form = ps.getAcroFields();

                JSONObject jo = data.getJSONObject(item);

                form.setField("yuanjidi", jo.getString("yuanjidi"));
                form.setField("cardNo", jo.getString("cardNo"));

                form.setField("consignee", jo.getString("consignee"));
                form.setField("consignee_mobile", jo.getString("consignee_mobile"));
                form.setField("consignee_addr", jo.getString("consignee_addr"));
                form.setField("goods_details", jo.getString("goods_desc"));
                form.setField("logi_no", jo.getString("logi_no"));
                form.setField("sender", jo.getString("sender"));
                form.setField("sender_mobile", jo.getString("sender_mobile"));
                form.setField("sender_addr", jo.getString("sender_addr"));


                String code = jo.getString("sfCode");

                PdfContentByte over = ps.getOverContent(1);
                Barcode barcode = new Barcode39();

                if (barCodeType == 39) {
                    barcode = new Barcode39();
                } else if (barCodeType == 128) {
                    barcode = new Barcode128();
                }

                barcode.setCode(code);
                barcode.setStartStopText(false);
                try {
                    Image img = barcode.createImageWithBarcode(over, null, null);
                    img.setAlignment(5);
                    img.setAbsolutePosition(50, 757);
                    if (code.length() > 13) {
                        img.scalePercent(130, 100);
                    }
                    img.setCompressionLevel(0);
                    over.addImage(img);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                PdfContentByte over_1 = ps.getOverContent(1);
                Barcode code1 = new Barcode39();
                if (barCodeType == 39) {
                    code1 = new Barcode39();
                } else if (barCodeType == 128) {
                    code1 = new Barcode128();
                }

                code1.setCode(code);
                code1.setStartStopText(false);
                code1.setCodeType(1);
                try {
                    Image img = code1.createImageWithBarcode(over, null, null);
                    img.setAlignment(5);
                    img.setAbsolutePosition(40, 575);
                    if (code.length() > 13) {
                        img.scalePercent(130, 100);
                    }
                    img.setCompressionLevel(0);
                    over_1.addImage(img);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                ps.setFormFlattening(true);
                ps.close();
                reader.close();
            }

            FileOutputStream fos = new FileOutputStream(tarFile);
            Document doc = new Document();


            PdfCopy pdfCopy = new PdfCopy(doc, fos);
            doc.open();
            PdfImportedPage impPage = null;
            /**取出之前保存的每页内容*/
            for (int i = 0; i < pages; i++) {
                impPage = pdfCopy.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);

                pdfCopy.addPage(impPage);

                doc.add(new Paragraph("Barcode 3 of 9"));
                Barcode39 code39 = new Barcode39();
                code39.setCode("ITEXT IN ACTION");
                doc.add(code39.createImageWithBarcode(pdfCopy.getDirectContent(), null, null));
            }
            doc.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // doc.close();
        }
    }

    public static void renderPdf(HttpServletResponse response, String filePath) {
        PdfReader reader = null;
        PdfStamper ps = null;
        try {
            response.setContentType("application/pdf");
            reader = new PdfReader(filePath);
            ps = new PdfStamper(reader, response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (DocumentException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }

            if (reader != null) {
                reader.close();
            }
        }
    }

    public static void toResponseStream(String tpl, Map<String, String> params, List<BarCodeDto> barCodes, ServletResponse response) {
        //填充创建pdf
        PdfReader reader = null;
        PdfStamper ps = null;
        try {
            reader = new PdfReader(tpl);

            ps = new PdfStamper(reader, response.getOutputStream());
            //取出报表模板中的所有字段
            AcroFields form = ps.getAcroFields();
            // 填充数据

            Iterator<Entry<String, String>> iter = params.entrySet().iterator();
            while (iter.hasNext()) {
                Entry<String, String> entry = iter.next();

                form.setField(entry.getKey(), entry.getValue());
            }

            PdfContentByte over = ps.getOverContent(1);

            if (barCodes != null) {
                for (BarCodeDto dto : barCodes) {
                    Barcode128 code39 = new Barcode128();
                    code39.setCode(dto.getCode().toUpperCase());
                    code39.setStartStopText(false);
                    code39.setX(dto.getX());
                    if (dto.getFontHide()) {
                        code39.setFont(null);
                    }
                    code39.setSize(dto.getFontSize());
                    code39.setBarHeight(dto.getBarHeight());
                    if (dto.getBaseLine() != null) {
                        code39.setBaseline(dto.getBaseLine());
                    }
                    try {
                        Image img = code39.createImageWithBarcode(over, null, null);
                        img.setAlignment(1);
                        img.setAbsolutePosition(dto.getPositionX(), dto.getPositionY());
                        over.addImage(img);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
            String generationdate = dateformat.format(new Date());
            form.setField("generationdate", generationdate);
            ps.setFormFlattening(true);


        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch (DocumentException | IOException e) {
                    e.printStackTrace();
                }
            }
            if (reader != null) {
                reader.close();
            }
        }
    }

    public static String toResponseStream(String template, List<OrderLabelDto> labelDataList) {

        try {

            ByteArrayOutputStream baos[] = new ByteArrayOutputStream[labelDataList.size()];//用于存储每页生成PDF流

            for (int i=0; i<labelDataList.size(); i++) {
                OrderLabelDto labelData = labelDataList.get(i);

                baos[i] = new ByteArrayOutputStream();
                PdfReader reader = new PdfReader(template);
                PdfStamper ps = new PdfStamper(reader, baos[i]);

                AcroFields form = ps.getAcroFields();
                // 填充数据

                PdfContentByte over = ps.getOverContent(1);

                Map<String, String> params = labelData.getParams();
                List<BarCodeDto> barCodes = labelData.getBarCodes();

                Iterator<Entry<String, String>> iter = params.entrySet().iterator();
                while (iter.hasNext()) {
                    Entry<String, String> entry = iter.next();

                    form.setField(entry.getKey(), entry.getValue());
                }

                if (barCodes != null) {
                    for (BarCodeDto dto : barCodes) {
                        if ("qr".equals(dto.getType())) {
                            Map<EncodeHintType,Object> hints = Maps.newHashMap();
                            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
                            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");

                            BarcodeQRCode barcodeQRCode = new BarcodeQRCode(dto.getCode().toUpperCase(), dto.getBarHeight(), dto.getBarHeight(), hints);
                            Image image = barcodeQRCode.getImage();
                            image.setAbsolutePosition(Float.valueOf(dto.getPositionX() + ""), Float.valueOf(dto.getPositionY() + ""));
                            image.setBorder(0);
                            image.setBorderWidth(0f);
                            image.setAlignment(Image.TEXTWRAP);
                            over.addImage(image);

                        } else {
                            Barcode128 code39 = new Barcode128();
                            code39.setCode(dto.getCode().toUpperCase());
                            code39.setStartStopText(false);
                            code39.setX(dto.getX() / dto.getCode().length());
                            if (dto.getFontHide()) {
                                code39.setFont(null);
                            }
                            code39.setSize(dto.getFontSize());
                            code39.setBarHeight(dto.getBarHeight());
                            if (dto.getBaseLine() != null) {
                                code39.setBaseline(dto.getBaseLine());
                            }
                            try {
                                Image img = code39.createImageWithBarcode(over, null, null);
                                img.setAlignment(1);
                                img.setAbsolutePosition(dto.getPositionX(), dto.getPositionY());
                                over.addImage(img);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }

                ps.setFormFlattening(true);
                ps.close();
                reader.close();
            }

            Document doc = new Document();

            ByteArrayOutputStream baos1 = new ByteArrayOutputStream();

            PdfCopy pdfCopy = new PdfCopy(doc, baos1);
            doc.open();
            PdfImportedPage impPage = null;
            /**取出之前保存的每页内容*/
            for (int i = 0; i < labelDataList.size(); i++) {
                impPage = pdfCopy.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);

                pdfCopy.addPage(impPage);

                doc.add(new Paragraph("Barcode 3 of 9"));
                Barcode39 code39 = new Barcode39();
                code39.setCode("ITEXT IN ACTION");
                doc.add(code39.createImageWithBarcode(pdfCopy.getDirectContent(), null, null));
            }

            pdfCopy.close();

            byte[] pdf = baos1.toByteArray();
            byte[] base64 = Base64.encodeBase64(pdf);

            return new String(base64);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void toResponseStream(String template, List<OrderLabelDto> labelDataList, HttpServletResponse response) {
        try {
            ByteArrayOutputStream baos[] = new ByteArrayOutputStream[labelDataList.size()];//用于存储每页生成PDF流
            for (int i = 0; i < labelDataList.size(); i++) {
                OrderLabelDto labelData = labelDataList.get(i);
                baos[i] = new ByteArrayOutputStream();
                PdfReader reader = new PdfReader(template);
                PdfStamper ps = new PdfStamper(reader, baos[i]);

//            	ps = new PdfStamper(reader, response.getOutputStream());
                //取出报表模板中的所有字段
                AcroFields form = ps.getAcroFields();
                // 填充数据
                PdfContentByte over = ps.getOverContent(1);
                Map<String, String> params = labelData.getParams();
                List<BarCodeDto> barCodes = labelData.getBarCodes();
                Iterator<Entry<String, String>> iter = params.entrySet().iterator();
                while (iter.hasNext()) {
                    Entry<String, String> entry = iter.next();
                    form.setField(entry.getKey(), entry.getValue());
                }
                if (barCodes != null) {
                    for (BarCodeDto dto : barCodes) {
                        if ("qr".equals(dto.getType())) {
                            Map<EncodeHintType, Object> hints = Maps.newHashMap();
                            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
                            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");

                            BarcodeQRCode barcodeQRCode = new BarcodeQRCode(dto.getCode().toUpperCase(), dto.getBarHeight(), dto.getBarHeight(), hints);
                            Image image = barcodeQRCode.getImage();
                            image.setAbsolutePosition(Float.valueOf(dto.getPositionX() + ""), Float.valueOf(dto.getPositionY() + ""));
                            image.setBorder(0);
                            image.setBorderWidth(0f);
                            image.setAlignment(Image.TEXTWRAP);
                            over.addImage(image);
                        } else {
                            Barcode128 code39 = new Barcode128();
                            code39.setCode(dto.getCode().toUpperCase());
                            code39.setStartStopText(false);
                            code39.setX(dto.getX() / dto.getCode().length());
                            if (dto.getFontHide()) {
                                code39.setFont(null);
                            }
                            code39.setSize(dto.getFontSize());
                            code39.setBarHeight(dto.getBarHeight());
                            if (dto.getBaseLine() != null) {
                                code39.setBaseline(dto.getBaseLine());
                            }
                            try {
                                Image img = code39.createImageWithBarcode(over, null, null);
                                img.setAlignment(1);
                                img.setAbsolutePosition(dto.getPositionX(), dto.getPositionY());
                                over.addImage(img);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
                ps.setFormFlattening(true);
                ps.close();
                reader.close();
            }
//			FileOutputStream fos = new FileOutputStream(tarFile);
            Document doc = new Document();
//            FileOutputStream fos = new FileOutputStream(new File("a.pdf"));
            PdfCopy pdfCopy = new PdfCopy(doc, response.getOutputStream());
            doc.open();
            PdfImportedPage impPage = null;
            /**取出之前保存的每页内容*/
            for (int i = 0; i < labelDataList.size(); i++) {
                impPage = pdfCopy.getImportedPage(new PdfReader(baos[i].toByteArray()), 1);
                pdfCopy.addPage(impPage);
                doc.add(new Paragraph("Barcode 3 of 9"));
                Barcode39 code39 = new Barcode39();
                code39.setCode("ITEXT IN ACTION");
                doc.add(code39.createImageWithBarcode(pdfCopy.getDirectContent(), null, null));
            }
            pdfCopy.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void toResponseStreamNew(String template, List<OrderLabelDto> labelDataList, HttpServletResponse response, String packageNumber) {
        try {
            // 用于存储每页生成PDF的输出流
            ByteArrayOutputStream[] baos = new ByteArrayOutputStream[labelDataList.size()];
            // 生成每一页PDF
            for (int i = 0; i < labelDataList.size(); i++) {
                OrderLabelDto labelData = labelDataList.get(i);
                baos[i] = new ByteArrayOutputStream();
                PdfReader reader = new PdfReader(template);
                PdfStamper ps = new PdfStamper(reader, baos[i]);
                // 获取模板中的字段
                AcroFields form = ps.getAcroFields();
                PdfContentByte over = ps.getOverContent(1);

                // 填充表单字段
                Map<String, String> params = labelData.getParams();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    form.setField(entry.getKey(), entry.getValue());
                }
                // 处理条形码
                List<BarCodeDto> barCodes = labelData.getBarCodes();
                if (barCodes != null) {
                    for (BarCodeDto dto : barCodes) {
                        if ("qr".equals(dto.getType())) {
                            BarcodeQRCode barcodeQRCode = new BarcodeQRCode(dto.getCode().toUpperCase(), dto.getBarHeight(), dto.getBarHeight(), null);
                            Image image = barcodeQRCode.getImage();
                            image.setAbsolutePosition(dto.getPositionX(), dto.getPositionY());
                            over.addImage(image);
                        } else {
                            Barcode128 code39 = new Barcode128();
                            code39.setCode(dto.getCode().toUpperCase());
                            Image img = code39.createImageWithBarcode(over, null, null);
                            img.setAbsolutePosition(dto.getPositionX(), dto.getPositionY());
                            over.addImage(img);
                        }
                    }
                }
                ps.setFormFlattening(true); // 设置表单字段不再可编辑
                ps.close();
                reader.close();
            }
            String fileName = String.format("%s_%s.pdf", LocalDate.now(), packageNumber);
            response.setHeader("Content-Disposition", "inline; filename=\"" + fileName + "\""); // 让浏览器弹出保存对话框，或直接展示
            Document doc = new Document();  // 创建文档并将每页PDF写入到响应输出流
            response.setContentType("application/pdf");  // 设置Content-Type为PDF
            PdfCopy pdfCopy = new PdfCopy(doc, response.getOutputStream());
            doc.open();

            for (int i = 0; i < labelDataList.size(); i++) {
                PdfReader reader = new PdfReader(baos[i].toByteArray());
                PdfImportedPage page = pdfCopy.getImportedPage(reader, 1);
                pdfCopy.addPage(page);
            }
            doc.close();
            response.getOutputStream().flush(); // 不返回任何对象，直接写入响应流
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            try {
                response.getWriter().write("Error generating PDF: " + e.getMessage());
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }

}
