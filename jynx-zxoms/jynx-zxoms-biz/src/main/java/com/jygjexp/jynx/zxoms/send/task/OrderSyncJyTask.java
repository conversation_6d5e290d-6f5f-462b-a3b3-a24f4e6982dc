package com.jygjexp.jynx.zxoms.send.task;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.mapper.NbMerchantMapper;
import com.jygjexp.jynx.zxoms.send.service.NbJySyncLogService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderPathService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.send.utils.Md5Util;

import com.jygjexp.jynx.zxoms.dto.MerchantDto;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: chenchang
 * @Description: 【订单】亚马逊同步有你
 * @Date: 2024/10/9 22:56
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OrderSyncJyTask {
    private final NbOrderService nbOrderService;
    private final NbSortingCenterService nbSortingCenterService;
    private final NbJySyncLogService nbJySyncLogService;
    private final NbOrderPathService nbOrderPathService;
    private final NbMerchantMapper nbMerchantMapper;
    private final CommonDataUtil commonDataUtil;

    @SneakyThrows
    @XxlJob(value = "orderSyncJyHandler")
    public void orderSyncJyHandler() {
//        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务：【订单亚马逊同步有你】于:{}，输入参数{}", LocalDateTime.now(), "运行中");
        Map<Integer, JSONObject> provinceMap = new HashMap<>();

        // "select o.* from nb_order o, nb_merchant m where o.merchant_id = m.merchant_id and m.fun_type = ? and o.add_time > CURDATE() - INTERVAL 3 DAY and o.jy_order_no is null limit 100", Merchant.FUN_TYPE_2_SYNC_JY);
        MPJLambdaWrapper<NbMerchantEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbOrderEntity.class)
                .eq(NbMerchantEntity::getFunType, MerchantDto.FUN_TYPE_2_SYNC_JY)
                .gt(NbOrderEntity::getAddTime, LocalDateTime.now().minusDays(3))
                .isNull(NbOrderEntity::getJyOrderNo)
                .orderByDesc(NbOrderEntity::getAddTime)
                .last("limit 100")
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getMerchantId, NbMerchantEntity::getMerchantId);
        List<NbOrderEntity> orders = nbMerchantMapper.selectJoinList(NbOrderEntity.class, wrapper);

        for (NbOrderEntity order : orders) {
            JSONObject provinceJo = null;
            if (order.getScId() != null && order.getScId() > 0) {
                if (provinceMap.containsKey(order.getScId())) {
                    provinceJo = provinceMap.get(order.getScId());
                } else {
                    NbSortingCenterEntity sc = nbSortingCenterService.getById(order.getScId());
                    if (sc == null){
                        // 如果 sc 为 null，打印日志或跳过该订单
                        XxlJobHelper.log("分拣中心信息未找到，订单ID: {}", order.getOrderId());
                        continue;   // 跳过该订单
                    }
                    Integer sysProvinceid = sc.getProvinceId();
                    provinceJo = commonDataUtil.getProvinceById(sysProvinceid);

                    provinceMap.put(order.getScId(), provinceJo);
                }
            }

            syncOrder(order, provinceJo);
        }

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【订单亚马逊同步有你】执行结束，时间: {}", LocalDateTime.now());
    }

    private void syncOrder(NbOrderEntity order, JSONObject provinceJo) {
        HttpRequest request = null;
        boolean exchangeOrder = false;
        String channelCode = null;
        if (order.getTcId() > 0) {
            // 已匹配
//			String code = "219999";
//			String apiKey = "1b2dd7a8a79d471ca976492a168a0052";
            request = getCommonRequest("220999", "198e900a5a66403b86f6c916d05b43ae");
            channelCode = "JY118";

            // 2024-01-10  根据发件人地址省份判断，如果发件人地址省份（例如ON）和分拣中心（例如YYZ）匹配，同时三字邮编属于转运中心覆盖范围。此类订单属于NB派送范围内，其他非NB派送范围需要转第三方派送。
            String senderProvince = order.getShipperProvince();

            String sysProName = provinceJo.getStr("enName");
            String sysProIso2 = provinceJo.getStr("iso2");

            senderProvince = senderProvince.toUpperCase();
            if (senderProvince.equals(sysProName.toUpperCase()) || senderProvince.equals(sysProIso2.toUpperCase())) {
            } else {
                channelCode = "LT051";
                exchangeOrder = true;
            }
        } else {
            if (order.getSysStatus() == 1) {
                // 等待处理
                return;
            } else if (order.getSysStatus() == 10) {
                // 超出配送范围
                request = getCommonRequest("220999", "198e900a5a66403b86f6c916d05b43ae");
                channelCode = "LT051";
                exchangeOrder = true;
            }
        }

        if (request == null) {
            return;
        }

        JSONObject reqData = new JSONObject();
        reqData.put("channelCode", channelCode); // 渠道编码
        reqData.put("referenceNo", order.getOrderNo()); // 客户参考号
//		reqData.put("trackingNo", order.getPkgNo()); // 跟踪号
        reqData.put("productType", 1); // 1,包裹:2,PAK 袋:3 文件,不填默认 1
        reqData.put("pweight", order.getPkgWeight()); // (0,10000],最多保留3位小数(单位为 kg)
        reqData.put("pieces", "1"); // 件数
        reqData.put("insured", 0); // 是否购买保险
//		reqData.put("batteryType", ""); // 带电类型
//		reqData.put("shipMode", ""); // 发货模式
        reqData.put("consigneeName", order.getDestName()); // 收件人姓名
//		reqData.put("consigneeCompany", ""); // 收件人公司
        reqData.put("consigneeCountryCode", order.getDestCountry()); // 收件人国家
        reqData.put("consigneeProvince", order.getDestProvince()); // 收件人州/省
        reqData.put("consigneeCity", order.getDestCity()); // 收件人城市
//		reqData.put("consigneeDistrict", ""); // 收件人区/县
//		reqData.put("consigneeStreet", ""); // 收件人街道
//		reqData.put("consigneeHouseNumber", ""); // 收件人门牌号
        reqData.put("consigneeAddress", order.getDestAddress1() + Optional.ofNullable(order.getDestAddress2()).orElse("") + Optional.ofNullable(order.getDestAddress3()).orElse("")); // 收件人地址
        reqData.put("consigneePostcode", order.getDestPostalCode()); // 收件人邮编
//		reqData.put("consigneeMobile", order.getDestTel()); // 收件人手机
        reqData.put("consigneePhone", order.getDestTel()); // 收件人电话
        reqData.put("consigneeEmail", order.getDestEmail()); // 收件人邮箱
//		reqData.put("consigneePassport", ""); // 收件人护照号
//		reqData.put("consigneeWarehouse", ""); // 收件人仓库
        reqData.put("consigneeTariff", ""); // 收件人税号
        reqData.put("shipperName", order.getShipperName()); // 发件人姓名
//		reqData.put("shipperCompany", ""); // 发件人公司
        reqData.put("shipperCountryCode", order.getShipperCountry()); // 发件人国家
        reqData.put("shipperProvince", order.getShipperProvince()); // 发件人省/州
        reqData.put("shipperCity", order.getShipperCity()); // 发件人城市
//		reqData.put("shipperDistrict", ""); // 发件人区/县
//		reqData.put("shipperStreet", ""); //  发件人街道
//		reqData.put("shipperHouseNumber", ""); // 发件人门牌号
        reqData.put("shipperAddress", order.getShipperAddress1()); // 发件人地址
        reqData.put("shipperPostcode", order.getShipperPostalCode()); // 发件人邮编
//		reqData.put("shipperMobile", ""); // 发件人手机
        reqData.put("shipperPhone", order.getShipperTel()); // 发件人电话
//		reqData.put("shipperEmail", ""); // 发件人邮箱


        JSONArray apiOrderItemList = new JSONArray();

        JSONObject item = new JSONObject();
        item.put("ename", "jeans");
        item.put("cname", "衣服");
        item.put("sku", "0000001234");
        item.put("price", "1");
        item.put("quantity", "1");
        item.put("weight", "1");
        item.put("unitCode", "PCE");

        apiOrderItemList.add(item);
        reqData.put("apiOrderItemList", apiOrderItemList); // 申报明细列表

        request.body(reqData.toString());
        HttpResponse response = request.execute();
        String result = response.body();
        XxlJobHelper.log("订单创建结果orderId=" + order.getOrderId() + "->" + result);

        JSONObject jo = JSONUtil.parseObj(result);

        // "select * from nb_jy_sync_log where order_id = ? limit 1", order.getOrderId());
        NbJySyncLogEntity jySyncLogEntity = nbJySyncLogService.getById(order.getOrderId());

        if (jySyncLogEntity == null) {
            jySyncLogEntity = new NbJySyncLogEntity();
            jySyncLogEntity.setOrderId(order.getOrderId());
            jySyncLogEntity.setLogTime(new Date());
            jySyncLogEntity.setRetData(result);
            jySyncLogEntity.setRetCode(jo.getInt("code"));
            nbJySyncLogService.save(jySyncLogEntity);
        } else {
            jySyncLogEntity.setLogTime(new Date());
            jySyncLogEntity.setRetData(result);
            jySyncLogEntity.setRetCode(jo.getInt("code"));
            nbJySyncLogService.updateById(jySyncLogEntity);
        }

        Integer code = jo.getInt("code");
        if (code == 1 && jo.containsKey("data")) {
            JSONObject data = jo.getJSONObject("data");

            // 2024-01-02
//			trackingNo是尾程派送单号，markNo是用于平台标记的单号，两者都可以查到轨迹
//			如果尾程是NB派则 markNo = trackingNo，如果非NB派送则 markNo != trackingNo

            String markNo = data.getStr("markNo");
            String referenceNo = data.getStr("referenceNo");
            String orderId = data.getStr("orderId");
            String trackingNo = data.getStr("trackingNo");
            String labelPath = data.getStr("labelPath");

            order.setInNbRange(1);
            if (exchangeOrder) {
                order.setOrderStatus(OrderDto.ORDER_STATUS_225_THIRD_PARTY_LOGISTICS);
                order.setInNbRange(2);
            }
            order.setJyOrderId(orderId);
            order.setJyMarkNo(markNo);
            order.setJyOrderNo(trackingNo);
            order.setThirdLabel(labelPath);
            order.setPkgNo(markNo); // 2024-05-29 包裹编号改回markNo
            nbOrderService.updateById(order);

            if (exchangeOrder) {
                String timezone = ZoneId.systemDefault().getId();
                String address = null;
                if (order.getScId() > 0) {
                    NbSortingCenterEntity sc = nbSortingCenterService.getById(order.getScId());

                    timezone = sc.getScTimezone();
                    address = commonDataUtil.getAddress(sc.getProvinceId(), sc.getCityId());
                }
//                OrderPath op = new OrderPath().build(order.getOrderId(), order.getOrderStatus(), 0, null, null, address, timezone);
                NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), 0, null, null, address, timezone);
                op.setScId(order.getScId());
                nbOrderPathService.save(op);
            }
        }

    }

    private HttpRequest getCommonRequest(String code, String apiKey) {
        HttpRequest request = HttpRequest.post("https://api.jygjexp.com/v1/api/orderNew/createOrder");
//
//		String code = "219999";
//		String apiKey = "1b2dd7a8a79d471ca976492a168a0052";

        ZonedDateTime zdt = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        String time = zdt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        request.header("code", code);
        request.header("apiKey", apiKey);
        request.header("timestamp", time);

        String sign = Md5Util.getMD5(code + apiKey);   //用的basic服务的MD5的加密工具类

        request.header("sign", sign);

        return request;
    }

}
