package com.jygjexp.jynx.zxoms.send.utils;

import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;

/**
 * @Author: chenchang
 * @Description: 阿里云OSS文件上传
 * @Date: 2024/9/4 11:01
 * @Version: 1.0
 */
public class AliYunOSS {
    protected static final Logger log = LoggerFactory.getLogger(AliYunOSS.class);
    private static String endpoint = "oss-us-east-1.aliyuncs.com";    //本地、测试环境
    //oss.neighbourexpress.ca
//    private static String endpoint = "oss-us-east-1-internal.aliyuncs.com";  //生产环境
    //private static String endpoint = "nbexpress-1804309143481531.oss-us-east-1-internal.oss-accesspoint.aliyuncs.com";  //生产环境
    private static final String accessKeyId = "LTAI4GHBMPW2sZ7AiSVQNfMg";
    private static final String accessKeySecret = "******************************";
    private static final String bucketName = "nbexpress";


    //本地文件上传OSS
    public static String sendToOssTwo(MultipartFile file, String dir, String fileName) throws IOException {
        OSS ossClient = null;
        InputStream inputStream = null;
        try {
            log.info("开始处理图片上传：{}", file.getOriginalFilename());

            // 检查文件大小（比如限制为10MB）
            long fileSize = file.getSize();
            if (fileSize > 10 * 1024 * 1024) {  // 10MB
                throw new RuntimeException("图片大小超过限制, 最大允许上传 10MB");
            }

            // 获取文件输入流
            inputStream = file.getInputStream();

            // 设置上传的路径
            String objectName=null;  // 定义上传到 OSS 的路径
            //如果传入文件夹为空，则使用默认文件夹
            if (StrUtil.isBlank(dir)){
                objectName = "imageInformation/" + fileName;  // 定义上传到 OSS 的路径
            }else{
                // 去除多余的斜杠
                objectName = dir.replaceAll("/$", "")+"/" + fileName;
            }

            // 上传到 OSS
            log.info("开始上传文件到 OSS...");

            //判断当前环境，测试用
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("win")) {
                endpoint = "oss-us-east-1.aliyuncs.com";
            }

            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            ossClient.putObject(new PutObjectRequest(bucketName, objectName, inputStream));
            ossClient.shutdown();

            // 返回文件的访问 URL
            String url = "https://" + bucketName + "." + endpoint + "/" + objectName;
            log.info("文件上传成功，访问地址：{}", url);
            return url;

        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("上传失败"+e.getMessage(),e);
        } finally {
            // 关闭流
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输入流时发生异常", e);
                }
            }
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }


    //远程图片上传OSS
    public static String sendToOss(String imagePath) {
        OSS ossClient = null;
        InputStream inputStream = null;
        try {
            log.info("开始处理图片上传：{}", imagePath);

            // 创建 URL 连接
            URL imgUrl = new URL(imagePath);
            URLConnection connection = imgUrl.openConnection();
            connection.setConnectTimeout(50000); // 连接超时 50 秒
            connection.setReadTimeout(50000);   // 读取超时 50 秒

            // 打开输入流
            log.info("开始打开远程图片流...");
            inputStream = connection.getInputStream();
            log.info("图片流打开成功");

            // 提取文件名
            String path = imgUrl.getPath();
            String fileName = path.substring(path.lastIndexOf('/') + 1);
            String objectName = "NB-push-jiayouexp/" + fileName;

            // 上传到 OSS
            log.info("开始上传文件到 OSS...");
            ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            ossClient.putObject(new PutObjectRequest(bucketName, objectName, inputStream));
            ossClient.shutdown();

            String url = "https://" + bucketName + "." + endpoint + "/" + objectName;
            log.info("文件上传成功，访问地址：{}", url);
            return url;

        } catch (SocketTimeoutException e) {
            log.error("网络请求超时：{}", imagePath, e);
            throw new RuntimeException("网络请求超时", e);
        } catch (Exception e) {
            log.error("图片上传失败", e);
            throw new RuntimeException("上传失败", e);
        } finally {
            // 关闭流
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输入流时发生异常", e);
                }
            }
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }


    public static void main(String[] args) {
        String imagePath = "https://storage.googleapis.com/r4m-uploaded-content/3220bba058d46923463f95599a528071.jpeg";
        String s = sendToOss(imagePath);
        System.out.println(s);
    }

}
