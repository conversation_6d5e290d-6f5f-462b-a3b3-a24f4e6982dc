package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbPatchPathOrderEntity;
import com.jygjexp.jynx.zxoms.send.service.NbPatchPathOrderService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 需要修复轨迹的订单
 *
 * <AUTHOR>
 * @date 2024-10-18 00:33:11
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbPatchPathOrder" )
@Tag(description = "nbPatchPathOrder" , name = "需要修复轨迹的订单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbPatchPathOrderController {

    private final  NbPatchPathOrderService nbPatchPathOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbPatchPathOrder 需要修复轨迹的订单
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbPatchPathOrder_view')" )
    public R getNbPatchPathOrderPage(@ParameterObject Page page, @ParameterObject NbPatchPathOrderEntity nbPatchPathOrder) {
        LambdaQueryWrapper<NbPatchPathOrderEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbPatchPathOrderService.page(page, wrapper));
    }


    /**
     * 通过id查询需要修复轨迹的订单
     * @param orderId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{orderId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbPatchPathOrder_view')" )
    public R getById(@PathVariable("orderId" ) Integer orderId) {
        return R.ok(nbPatchPathOrderService.getById(orderId));
    }

    /**
     * 新增需要修复轨迹的订单
     * @param nbPatchPathOrder 需要修复轨迹的订单
     * @return R
     */
    @Operation(summary = "新增需要修复轨迹的订单" , description = "新增需要修复轨迹的订单" )
    @SysLog("新增需要修复轨迹的订单" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbPatchPathOrder_add')" )
    public R save(@RequestBody NbPatchPathOrderEntity nbPatchPathOrder) {
        return R.ok(nbPatchPathOrderService.save(nbPatchPathOrder));
    }

    /**
     * 修改需要修复轨迹的订单
     * @param nbPatchPathOrder 需要修复轨迹的订单
     * @return R
     */
    @Operation(summary = "修改需要修复轨迹的订单" , description = "修改需要修复轨迹的订单" )
    @SysLog("修改需要修复轨迹的订单" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbPatchPathOrder_edit')" )
    public R updateById(@RequestBody NbPatchPathOrderEntity nbPatchPathOrder) {
        return R.ok(nbPatchPathOrderService.updateById(nbPatchPathOrder));
    }

    /**
     * 通过id删除需要修复轨迹的订单
     * @param ids orderId列表
     * @return R
     */
    @Operation(summary = "通过id删除需要修复轨迹的订单" , description = "通过id删除需要修复轨迹的订单" )
    @SysLog("通过id删除需要修复轨迹的订单" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbPatchPathOrder_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbPatchPathOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbPatchPathOrder 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbPatchPathOrder_export')" )
    public List<NbPatchPathOrderEntity> export(NbPatchPathOrderEntity nbPatchPathOrder,Integer[] ids) {
        return nbPatchPathOrderService.list(Wrappers.lambdaQuery(nbPatchPathOrder).in(ArrayUtil.isNotEmpty(ids), NbPatchPathOrderEntity::getOrderId, ids));
    }
}