package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbOrderMpsEntity;
import com.jygjexp.jynx.zxoms.vo.OrderMpsVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

public interface NbOrderMpsService extends IService<NbOrderMpsEntity> {

    NbOrderMpsEntity findOrderMpsByOrderId(Integer orderId);

}