package com.jygjexp.jynx.zxoms.nbapp.controller.api;

import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiRequestParamsVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/10/31 23:11
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/info" )
@Tag(description = "appinfo" , name = "APP-获取仓库列表" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class InfoController extends BaseController {
    private final NbSortingCenterService sortingCenterService;

    /**
     * 获取仓库列表
     */
    @Operation(summary = "获取仓库列表" , description = "获取仓库列表" )
    @GetMapping("/warehouseList" )
    @Inner(value = false)
    public OldResult warehouseList(@Validated @ModelAttribute ApiRequestParamsVo paramsVo) {
        return sortingCenterService.getWarehouseList(paramsVo);
    }

}

