package com.jygjexp.jynx.zxoms.nbapp.controller.api;

import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.zxoms.nbapp.vo.*;
import com.jygjexp.jynx.zxoms.send.service.NbLtlPriceRuleService;
import com.jygjexp.jynx.zxoms.send.service.NbLtlSurchargeService;
import com.jygjexp.jynx.zxoms.send.service.NbPriceDistrictService;

import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: chenchang
 * @Description: 询价
 * @Date: 2024/11/8 9:43
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/quote")
@Tag(description = "appquote", name = "APP-询价")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class QuoteController extends BaseController {
    private final NbPriceDistrictService priceDistrictService;
    private final NbLtlPriceRuleService ltlPriceRuleService;
    private final NbLtlSurchargeService ltlSurchargeService;

    /**
     * 国标件询价
     */
    @Operation(summary = "APP-国标件询价", description = "APP-国标件询价")
    @PostMapping("/international")
    @Inner(value = false)
    public OldResult international(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @Validated @ModelAttribute ApiQuoteInternationalVo vo) {
        return priceDistrictService.getInternational(vo, paramsVo);
    }

    /**
     * 查询国内价格
     */
    @Operation(summary = "APP-查询国内价格", description = "APP-查询国内价格")
    @PostMapping("/domestic")
    @Inner(value = false)
    public OldResult domestic(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @Validated @ModelAttribute ApiQuoteDomesticVo vo) {
        return priceDistrictService.getDomestic(vo, paramsVo);
    }

    /**
     * LTL询价
     */
    @Operation(summary = "APP-LTL询价", description = "APP-LTL询价")
    @PostMapping("/ltl")
    @Inner(value = false)
    public OldResult ltl(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @RequestParam("destPostalCode") @NotBlank(message = "收件邮编不能为空") String destPostalCode,
                         @RequestParam("senderPostalCode") @NotBlank(message = "发件邮编不能为空") String senderPostalCode,
                         @RequestParam("items") @NotBlank(message = "items不能为空") String items,
                         @RequestParam("surchargeIds") @Parameter(description = "附加费用id-多个ID用逗号分割") @NotNull(message = "附加费用id不能为空") Integer[] surchargeIds) {
        return ltlPriceRuleService.ltl(paramsVo, destPostalCode, senderPostalCode, items, surchargeIds);
    }

    /**
     * LTL附加费
     */
    @Operation(summary = "APP-LTL附加费", description = "APP-LTL附加费")
    @PostMapping("/ltlSurcharge")
    @Inner(value = false)
    public OldResult ltlSurcharge(@Validated @ModelAttribute ApiRequestParamsVo paramsVo) {
        return ltlSurchargeService.getLtlSurcharge();
    }

    @Operation(summary = "APP-测试International", description = "APP-测试International")
    @PostMapping("/testInternational")
    @Inner(value = false)
    public OldResult testInternational(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @Valid @ModelAttribute ApiQuoteInternationalVo vo,
                                       @RequestParam("destAddress1") @NotBlank(message = "目的地址1不能为空") String destAddress1,
                                       @RequestParam("destAddress2") @NotBlank(message = "目的地址2不能为空") String destAddress2) {
        return priceDistrictService.testInternational(vo, paramsVo, destAddress1, destAddress2);
    }

    @Operation(summary = "APP-测试Domestic", description = "APP-测试Domestic")
    @PostMapping("/testDomestic")
    @Inner(value = false)
    public OldResult testDomestic(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @Valid @ModelAttribute ApiQuoteDomesticVo vo, @RequestParam("destAddress1") @NotBlank(message = "收件地址1不能为空") String destAddress1,
                                  @RequestParam("destAddress2") @NotBlank(message = "收件地址2不能为空") String destAddress2) {
        return priceDistrictService.testDomestic(vo, paramsVo, destAddress1, destAddress2);
    }

}
