package com.jygjexp.jynx.zxoms.send.task;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.send.mapper.NbMerchantMapper;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderMpsMapper;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.send.utils.ConfigUtil;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.entity.*;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: chenchang
 * @Description: 向佳邮同步亚马逊订单轨迹
 * @Date: 2024/10/9 23:53
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class PostPathToJyTask {
    private final NbOrderService nbOrderService;
    private final NbOrderStatusService nbOrderStatusService;
    private final NbSortingCenterService nbSortingCenterService;
    private final NbTransferCenterService nbTransferCenterService;
    private final NbOrderPathService nbOrderPathService;
    private final NbShelfPkgLogService nbShelfPkgLogService;
    private final NbShelfService nbShelfService;
    private final CommonDataUtil commonDataUtil;
    private final ConfigUtil configUtil;
    private final NbOrderMpsMapper nbOrderMpsMapper;
    private final NbMerchantMapper nbMerchantMapper;

    @SneakyThrows
    @XxlJob("postPathToJyHandler")
    public void postPathToJyHandler() {
//        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务：【向佳邮同步亚马逊订单轨迹】于:{}，输入参数{}", LocalDateTime.now(), "运行中");
        String api = configUtil.findStrByKey("nb_sync_jy_order_path");

        // "select distinct o.order_id order_id from nb_order_path op , nb_order o, nb_merchant m "
        //  + "where op.order_id = o.order_id and o.merchant_id = m.merchant_id and m.fun_type = 2 and op.sync_jy_status = 0 and o.jy_order_no is not null and o.order_id > 1687116 limit 1");
        MPJLambdaWrapper<NbMerchantEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbOrderEntity.class)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getMerchantId, NbMerchantEntity::getMerchantId)
                .leftJoin(NbOrderPathEntity.class, NbOrderPathEntity::getOrderId, NbOrderEntity::getOrderId)
                .eq(NbMerchantEntity::getFunType, 2)
                .eq(NbOrderPathEntity::getSyncJyStatus, 0)
                .isNotNull(NbOrderEntity::getJyOrderNo)
                .gt(NbOrderEntity::getOrderId, 1687116)
                .last("limit 1");
        List<NbOrderEntity> orders = nbMerchantMapper.selectJoinList(NbOrderEntity.class, wrapper);

        Map<Integer, NbSortingCenterEntity> scMap = new HashMap<>();
        Map<Integer, NbTransferCenterEntity> tcMap = new HashMap<>();

        List<NbOrderStatusEntity> oslist = nbOrderStatusService.findAll();
        Map<Integer, NbOrderStatusEntity> osMap = oslist.stream().collect(Collectors.toMap(NbOrderStatusEntity::getId, Function.identity()));

        List<Integer> useScCityStatus = Lists.newArrayList();
        useScCityStatus.add(OrderDto.ORDER_STATUS_190_ORDER_RECEIVED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_191_ORDER_CANCELED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_192_CUSTOM_HOLD);
        useScCityStatus.add(OrderDto.ORDER_STATUS_198_CUSTOM_RELEASE_DIRECT);
        useScCityStatus.add(OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT);
        useScCityStatus.add(OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
        useScCityStatus.add(OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER);
        useScCityStatus.add(OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE);

        List<Integer> useTcCityStatus = Lists.newArrayList();
        useTcCityStatus.add(OrderDto.ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER);
        useTcCityStatus.add(OrderDto.ORDER_STATUS_203_LOAD_SCANNED);
        useTcCityStatus.add(OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT);

        List<Integer> useUserCityStatus = Lists.newArrayList();
        useUserCityStatus.add(OrderDto.ORDER_STATUS_204_IN_TRANSIT);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_205_DELIVERED);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_210_FAILED_DELIVERY);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2);
        useUserCityStatus.add(OrderDto.ORDER_STATUS_280_FAILURE);

        for (NbOrderEntity orderObj : orders) {
            JSONArray retJa = new JSONArray();
            NbOrderEntity order = nbOrderService.getById(orderObj.getOrderId());

            JSONObject orderJo = new JSONObject();
            orderJo.set("customerOrderNo", order.getCustomerOrderNo());
            orderJo.set("pkgNo", order.getPkgNo());

            Integer scId = order.getScId();

            NbSortingCenterEntity sc = scMap.getOrDefault(scId, null);
            if (sc == null) {
                sc = nbSortingCenterService.getById(scId);
                if (sc != null) {
                    scMap.put(scId, sc);
                }
            }

            Integer tcId = order.getTcId();
            NbTransferCenterEntity tc = tcMap.getOrDefault(tcId, null);
            if (tc == null) {
                tc = nbTransferCenterService.getById(tcId);
                if (tc != null) {
                    tcMap.put(tcId, tc);
                }
            }

            JSONObject cityJo = null;
            if (sc != null) {
                cityJo = commonDataUtil.getCityById(sc.getCityId());
            }

            NbSortingCenterEntity finalSc = sc;
            NbTransferCenterEntity finalTc = tc;
            JSONObject finalJo = cityJo;

            JSONObject tcCityJo = null;
            if (tc != null) {
                tcCityJo = commonDataUtil.getCityById(tc.getCityId());
            }
            JSONObject finalTcCityJo = tcCityJo;

            int useOrderId = order.getOrderId();
            if (order.getIsMps()) {
                NbOrderMpsEntity om = nbOrderMpsMapper.selectOne(new LambdaQueryWrapper<NbOrderMpsEntity>().eq(NbOrderMpsEntity::getOrderId, order.getOrderId()));
                if (om != null && om.getPOrderId() > 0) {
                    useOrderId = om.getPOrderId();
                }
            }

            // "select * from nb_order_path where order_id = ? order by add_timestamp desc", useOrderId);
            LambdaQueryWrapper<NbOrderPathEntity> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(NbOrderPathEntity::getOrderId, useOrderId)
                    .orderByDesc(NbOrderPathEntity::getAddTimestamp);
            List<NbOrderPathEntity> paths = nbOrderPathService.list(wrapper1);
            JSONArray ja = paths.stream().map(op -> {

                JSONObject jo = new OrderPathDto().toAppJson(op);
                if (jo == null) {
                    return null;
                }

                jo.put("city", "");
                try {

                    if (useScCityStatus.contains(op.getOrderStatus())) {
                        if (finalSc != null) {
                            if (finalJo != null) {
                                jo.put("city", finalJo.getStr("enName"));
                            }
                            jo.put("postalCode", finalSc.getPostalCode());
                        }
                    }
                    if (useTcCityStatus.contains(op.getOrderStatus())) {
                        if (finalTc != null) {
                            if (finalTcCityJo != null) {
                                jo.put("city", finalTcCityJo.getStr("enName"));
                            }
                            jo.put("postalCode", finalTc.getPostalCode());
                        }
                    }
                    if (useUserCityStatus.contains(op.getOrderStatus())) {
                        jo.put("city", order.getDestCity());
                        jo.put("postalCode", order.getDestPostalCode());
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }

                NbOrderStatusEntity orderStatus = osMap.get(op.getOrderStatus());
                if (orderStatus != null) {
                    jo.set("content", orderStatus.getContent());
                }

                // 2024-04-18 自提件返回自提信息
                if (op.getOrderStatus() == OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE) {
                    // "select * from nb_shelf_pkg_log where order_id = ? order by log_id desc limit 1", op.getOrderId());
                    LambdaQueryWrapper<NbShelfPkgLogEntity> wrapper2 = new LambdaQueryWrapper<>();
                    wrapper2.eq(NbShelfPkgLogEntity::getOrderId, op.getOrderId())
                            .orderByDesc(NbShelfPkgLogEntity::getLogId).last("limit 1");
                    NbShelfPkgLogEntity shelfPkgLog = nbShelfPkgLogService.getOne(wrapper2, false);
                    if (shelfPkgLog != null) {
                        JSONObject pickupInfo = new JSONObject();

                        pickupInfo.put("pickupCode", shelfPkgLog.getPutawayCode());
                        pickupInfo.put("putawayTimestamp", shelfPkgLog.getPutawayTime() / 1000);

                        NbShelfEntity shelf = nbShelfService.getById(shelfPkgLog.getShelfId());
                        if (shelf.getScId() > 0) {
                            pickupInfo.put("serviceTel", finalSc.getServiceTel());
                            pickupInfo.put("openingHours", finalSc.getBusinessHours());

                            pickupInfo.put("address", finalSc.getAddress() + " " + finalSc.getPostalCode());
                        } else if (shelf.getTcId() > 0) {
                            pickupInfo.put("serviceTel", finalTc.getServiceTel());
                            pickupInfo.put("openingHours", finalTc.getBusinessHours());

                            pickupInfo.put("address", finalTc.getAddress() + " " + finalTc.getPostalCode());
                        }

                        jo.set("pickupInfo", pickupInfo);
                    }
                }

                return jo;
            }).filter(p -> p != null).collect(Collectors.toCollection(JSONArray::new));

            orderJo.set("track", ja);
            retJa.add(orderJo);

            HttpResponse response = HttpRequest.post(api).body(JSONUtil.toJsonStr(retJa)).execute();
            String result = response.body();

            XxlJobHelper.log("同步轨迹返回数据：orderId=" + order.getOrderId() + ",ret->" + result);

            JSONObject retJo = JSONUtil.parseObj(result);
            String status = retJo.getStr("status");
            if ("success".equals(status)) {
//                Db.use("nbd").update("update nb_order_path set sync_jy_status = 1, sync_time = now() where order_id = ?", order.getOrderId());
                nbOrderPathService.updateByOrderId(order.getOrderId());
            }

        }

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【向佳邮同步亚马逊订单轨迹】执行结束，时间: {}", LocalDateTime.now());
    }
}
