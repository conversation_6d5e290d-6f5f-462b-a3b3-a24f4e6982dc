package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.common.websocket.config.WebSocketMessageSender;
import com.jygjexp.jynx.zxoms.dto.*;
import com.jygjexp.jynx.zxoms.entity.*;

import com.jygjexp.jynx.zxoms.nbapp.vo.ApiOrderUpdateVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiRequestParamsVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.dto.OrderSyncR4mData;
import com.jygjexp.jynx.zxoms.send.dto.R4mTobeUpdateRouter;
import com.jygjexp.jynx.zxoms.send.mapper.*;
import com.jygjexp.jynx.zxoms.send.service.NbOrderBatchService;

import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.send.utils.ConfigUtil;
import com.jygjexp.jynx.zxoms.utils.NBDUtils;
import com.jygjexp.jynx.zxoms.send.utils.Route4MeUtil;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderBatchExcelVo;
import com.jygjexp.jynx.zxoms.vo.NbOrderBatchPageVo;
import com.jygjexp.jynx.zxoms.vo.OrderBatchVo;
import com.route4me.sdk.services.orders.OrderStatus;
import com.route4me.sdk.services.routing.*;
import com.route4me.sdk.services.routing.balance.Balance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 派送批次
 *
 * <AUTHOR>
 * @date 2024-10-12 16:48:25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class NbOrderBatchServiceImpl extends ServiceImpl<NbOrderBatchMapper, NbOrderBatchEntity> implements NbOrderBatchService {
    private final NbSortingCenterMapper nbSortingCenterMapper;
    private final NbDriverMapper nbDriverMapper;
    private final NbTransferBatchMapper nbTransferBatchMapper;
    private final NbTransferBatchOrderMapper nbTransferBatchOrderMapper;
    private final NbOrderMapper nbOrderMapper;
    private final NbTransferCenterMapper nbTransferCenterMapper;
    private final NbOrderMpsMapper nbOrderMpsMapper;
    private final NbOrderPathMapper nbOrderPathMapper;
    private final NbMerchantMapper nbMerchantMapper;
    private final NbOrderUpdateLogMapper orderUpdateLogMapper;
    private final RemoteUserService remoteUserService;

    private final NbOrderBatchMapper nbOrderBatchMapper;
    private final CommonDataUtil commonDataUtil;
    private final Route4MeUtil route4MeUtil;
    private final ConfigUtil configUtil;

    @Override
    public NbOrderBatchEntity findNoRouted() {
        return null;
    }

    /**
     * 派送批次分页查询
     * @param page
     * @param vo
     * @return
     */
    @Override
    public Page<NbOrderBatchPageVo> search(Page page, NbOrderBatchPageVo vo) {
        MPJLambdaWrapper wrapper = getWrapper(vo, null);
        return nbOrderBatchMapper.selectJoinPage(page, NbOrderBatchPageVo.class, wrapper);
    }

    /**
     * 添加子批次
     */
    @Override
    public R addSub(Integer batchId) {
        NbOrderBatchEntity ob = getById(batchId);
        if (!ob.getIsMain()) {
            return LocalizedR.failed("nborderBatch.only.main.batch.creating.sub.batches", Optional.ofNullable(null));
        }

        //  select count(1) cont from nb_order_batch where is_main = false and p_batch_id = ?", batchId).getInt("cont");
        LambdaQueryWrapper<NbOrderBatchEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(NbOrderBatchEntity::getIsMain, false)
                .eq(NbOrderBatchEntity::getPBatchId, batchId);
        Long count = count(wrapper);
        int subBatchTotal = Math.toIntExact(count);
        String batchNo = "SUB." + ob.getBatchNo() + "." + (subBatchTotal + 1);

        NbOrderBatchEntity sub = new NbOrderBatchEntity();
        sub.setBatchNo(batchNo);
        sub.setOrderTotal(0);
        sub.setCreateTime(new Date());
        sub.setIsValid(true);
        sub.setBatchTotal(0);
        sub.setIsRouted(false);
        sub.setIsMain(false);
        sub.setPBatchId(ob.getBatchId());
        sub.setBatchType(OrderBatchDto.BATCH_TYPE_2_SUB);
        save(sub);

        return R.ok();
    }

    /**
     * 把派送批次按转运中心进行分组
     */
    @Override
    public R tcGroup(Integer batchId) {
        NbOrderBatchEntity batch = getById(batchId);
        if (batchId == null) {
            return LocalizedR.failed("nborderBatch.batch.does.not.exist", Optional.ofNullable(null));
        }
        if (batch.getIsRouted()) {
            return LocalizedR.failed("nborderBatch.planned.already", Optional.ofNullable(null));
        }

        List<NbOrderEntity> orders;
        if (batch.getIsMain()) {
            //  select * from nb_order where batch_no = ? and order_status = ? and is_routed = false and express_type in (0,1)", batch.getBatchNo(), OrderDto.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的主批次
            LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
            wrapper.eq(NbOrderEntity::getBatchNo, batch.getBatchNo())
                    .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                    .eq(NbOrderEntity::getIsRouted, false)
                    .in(NbOrderEntity::getExpressType, 0, 1);
            orders = nbOrderMapper.selectList(wrapper);
        } else {
            // "select * from nb_order where sub_batch_no = ? and order_status = ? and is_routed = false and express_type in (0,1)", batch.getBatchNo(), OrderDto.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的子批次
            LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
            wrapper.eq(NbOrderEntity::getSubBatchNo, batch.getBatchNo())
                    .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                    .eq(NbOrderEntity::getIsRouted, false)
                    .in(NbOrderEntity::getExpressType, 0, 1);
            orders = nbOrderMapper.selectList(wrapper);
        }

        if (orders.size() == 0) {
            return LocalizedR.failed("nborderBatch.no.orders.available.for.planning", Optional.ofNullable(null));
        }

        if (orders.size() == 1) {
            return LocalizedR.failed("nborderBatch.an.order.cannot.be.planned", Optional.ofNullable(null));
        }

        Map<Integer, List<NbOrderEntity>> orderMap = new HashMap<>();
        for (NbOrderEntity order : orders) {
            Integer tcId = order.getTcId();
            if (tcId == null || tcId == 0) {
                log.info("发现有未分配转运中心的订单，暂时不规划： " + order.getOrderId());

                batch.setRoutedErrmsg("发现有未分配转运中心的订单，暂时不规划： " + order.getOrderId());
                nbOrderBatchMapper.updateById(batch);

                return LocalizedR.failed("nborderBatch.no.assigned.transfer.center.no.operation", order.getOrderId());
            }

            List<NbOrderEntity> tcOrders;
            if (orderMap.containsKey(tcId)) {
                tcOrders = orderMap.get(tcId);
            } else {
                tcOrders = Lists.newArrayList();
            }

            tcOrders.add(order);
            orderMap.put(tcId, tcOrders);
        }
        log.info("共" + orderMap.size() + "个转运中心参与");
        JSONArray ja = new JSONArray();

        Iterator<Map.Entry<Integer, List<NbOrderEntity>>> iter = orderMap.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<Integer, List<NbOrderEntity>> entry = iter.next();
            int tcId = entry.getKey();
            List<NbOrderEntity> tcOrders = entry.getValue();

            //  select * from nb_driver where audit_status = 3 and is_valid = true and tc_id = ? and auto_route_planning = true and route4me_member_id > 0", tcId);
            LambdaQueryWrapper<NbDriverEntity> wrapper = Wrappers.lambdaQuery(NbDriverEntity.class);
            wrapper.eq(NbDriverEntity::getAuditStatus, DriverDto.AUDIT_STATUS_3_PASS)
                    .eq(NbDriverEntity::getIsValid, true)
                    .eq(NbDriverEntity::getTcId, tcId)
                    .eq(NbDriverEntity::getAutoRoutePlanning, true)
                    .gt(NbDriverEntity::getRoute4meMemberId, 0);
            List<NbDriverEntity> drivers = nbDriverMapper.selectList(wrapper);

            NbTransferCenterEntity tc = nbTransferCenterMapper.selectById(tcId);
            JSONObject item = new JSONObject();
            item.set("tc", tc);
            item.set("drivers", drivers);
            item.set("orders", tcOrders);

            ja.add(item);
        }

        //  select * from nb_sorting_center where is_valid = true
        LambdaQueryWrapper<NbSortingCenterEntity> wrapper = Wrappers.lambdaQuery(NbSortingCenterEntity.class);
        wrapper.eq(NbSortingCenterEntity::getIsValid, true);
        List<NbSortingCenterEntity> scList = nbSortingCenterMapper.selectList(wrapper);

        JSONArray scJa = new JSONArray();
        for (NbSortingCenterEntity entity : scList) {
            JSONObject jo = new JSONObject();
            jo.set("scId", entity.getScId());
            jo.set("centerName", entity.getCenterName());
            jo.set("address", entity.getAddress());
            jo.set("postalCode", entity.getPostalCode());
            jo.set("country", commonDataUtil.getCountryById(entity.getCountryId()));
            jo.set("province", commonDataUtil.getProvinceById(entity.getProvinceId()));
            jo.set("city", commonDataUtil.getCityById(entity.getCityId()));
            jo.set("scCode", entity.getScCode());
            scJa.add(jo);
        }

        JSONObject ret = new JSONObject();
        ret.set("batch", batch);
        ret.set("tcGroup", ja);
        ret.set("sortingCenters", scJa);

        return R.ok(ret);
    }

    /**
     * 按一个转运中心进行路径规划
     */
    @Override
    public R optimizationByTc(OrderBatchVo vo) {
        String timeOrSize = vo.getTimeOrSize();
        Integer batchId = vo.getBatchId();
        Integer startTcId = vo.getTcId();
        Integer startScId = vo.getScId();
        String tcIdsStr = vo.getTcIds();
        String[] tcIdsStrArr = tcIdsStr.split(",");
        List<Integer> tcIds = new ArrayList<>();
        for (String tcIdStr : tcIdsStrArr) {
            tcIds.add(Integer.valueOf(tcIdStr));
        }

        NbOrderBatchEntity ob = getById(batchId);
        if (ob == null) {
            return LocalizedR.failed("nborderBatch.batch.does.not.exist", Optional.ofNullable(null));
        }
        if (ob.getIsRouted()) {
            return LocalizedR.failed("nborderBatch.planned.already", Optional.ofNullable(null));
        }

        String routeName = null;
        NbTransferCenterEntity startTc = null;

        String timezone = null;
        Integer scId = null;
        if (startTcId != null && startTcId > 0) {
            startTc = nbTransferCenterMapper.selectById(startTcId);
            if (startTc == null) {
                return LocalizedR.failed("nborderBatch.transfer.center.no.exist.at.the.beginning", Optional.ofNullable(null));
            }

            NbSortingCenterEntity sc = nbSortingCenterMapper.selectById(startTc.getScId());
            routeName = sc.getScCode();

            timezone = sc.getScTimezone();
            scId = sc.getScId();
        }

        NbSortingCenterEntity startSc = null;
        if (startScId != null && startScId > 0) {
            startSc = nbSortingCenterMapper.selectById(startScId);
            if (startSc == null) {
                return LocalizedR.failed("nborderBatch.sorting.center.no.exist.at.the.beginning", Optional.ofNullable(null));
            }

            routeName = startSc.getScCode();
            timezone = startSc.getScTimezone();
            scId = startSc.getScId();
        }

        if (startTc == null && startSc == null) {
            return LocalizedR.failed("nborderBatch.at.least.one.sortingcenter.or.transfercenter", Optional.ofNullable(null));
        }

        List<NbOrderEntity> allOrders = new ArrayList<>();
        for (Integer tcId : tcIds) {
            NbTransferCenterEntity tc = nbTransferCenterMapper.selectById(tcId);
            if (tc == null) {
                return LocalizedR.failed("nborderBatch.transfercenter.no.exist", Optional.ofNullable(null));
            }

            List<NbOrderEntity> orders;
            if (ob.getIsMain()) {
                //  select * from nb_order where batch_no = ? and tc_id = ? and order_status = ? and is_routed = false and express_type in (0,1)", ob.getBatchNo(), tcId, Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的主批次
                LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
                wrapper.eq(NbOrderEntity::getBatchNo, ob.getBatchNo())
                        .eq(NbOrderEntity::getTcId, tcId)
                        .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                        .eq(NbOrderEntity::getIsRouted, false)
                        .in(NbOrderEntity::getExpressType, 0, 1);
                orders = nbOrderMapper.selectList(wrapper);
            } else {
                //  select * from nb_order where sub_batch_no = ? and tc_id = ? and order_status = ? and is_routed = false and express_type in (0,1)", ob.getBatchNo(), tcId, Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的子批次
                LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
                wrapper.eq(NbOrderEntity::getSubBatchNo, ob.getBatchNo())
                        .eq(NbOrderEntity::getTcId, tcId)
                        .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                        .eq(NbOrderEntity::getIsRouted, false)
                        .in(NbOrderEntity::getExpressType, 0, 1);
                orders = nbOrderMapper.selectList(wrapper);
            }

            allOrders.addAll(orders);
        }

        String route4meSelectParams = vo.getRoute4meSelectParams();
        JSONObject params = JSONUtil.parseObj(route4meSelectParams);
        Integer routeMaxDuration = params.getInt("routeMaxDuration");
        if (timeOrSize.equals("size")) {
            routeMaxDuration = null; // 20230826 时间和数量只能二选一
        }

        if (routeMaxDuration == null) {
            routeMaxDuration = configUtil.findIntegerByKey(ConfigUtil.r4m_route_max_duration);
        } else {
            routeMaxDuration = routeMaxDuration * 60;
        }
        String balance = "distance";

        String algorithmType = "3";

        String optimize = "Distance";

        Integer maxTourSize = params.getInt("maxTourSize");
        if (timeOrSize.equals("time")) {
            maxTourSize = null;
        }

        Boolean isRt = params.getBool("isRt");
        Boolean isDynamicStartTime = params.getBool("isDynamicStartTime");
        JSONArray driverIds = params.getJSONArray("driverIds");

        Parameters innerPara = new Parameters();
        innerPara.setAlgorithmType(algorithmType);

        innerPara.setRouteMaxDuration(routeMaxDuration); // How many seconds a route can last at most. Default is 24 hours = 86400 seconds

        if (StrUtil.isNotBlank(optimize)) {
            innerPara.setOptimize(optimize.trim()); // "Distance", "Time", "timeWithTraffic"
        }

        ZonedDateTime now = null;
        String today = null;
        String yyyyMMdd = null;
        if (StrUtil.isNotBlank(timezone)) {
            now = ZonedDateTime.now();
        } else {
            now = ZonedDateTime.now(ZoneId.of(timezone));
        }
        today = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        yyyyMMdd = now.format(DateTimeFormatter.ofPattern("yyMMdd"));

        routeName += ("-" + yyyyMMdd);

        //  select count(1) cont from nb_transfer_batch where owner_sc_id = ? and add_time >= ? and add_time <= ?", scId, today + " 00:00:00", today + " 23:59:59").getInt("cont");
        LambdaQueryWrapper<NbTransferBatchEntity> qw = Wrappers.lambdaQuery(NbTransferBatchEntity.class);
        qw.eq(NbTransferBatchEntity::getOwnerScId, scId)
                .ge(NbTransferBatchEntity::getAddTime, today + " 00:00:00")
                .le(NbTransferBatchEntity::getAddTime, today + " 23:59:59");
        int todayLQTotal = Math.toIntExact(nbTransferBatchMapper.selectCount(qw));
        log.info("当前路区数量:" + todayLQTotal + ",today=" + today + ",owner_sc_id=" + scId + ",timezone=" + timezone);

        routeName += ("-" + String.format("%02d", todayLQTotal));
        innerPara.setRouteName(routeName); // 20230829 修改路区命名规则

        innerPara.setTravelMode(Constants.TravelMode.DRIVING.toString()); // ["Driving", "Walking", "Bicycling"],
        innerPara.setDistanceUnit(Constants.DistanceUnit.MI.toString());
        innerPara.setIsDynamicStartTime(isDynamicStartTime);
        innerPara.setRt(isRt);

        if (maxTourSize != null) {
            innerPara.setMaxTourSize(maxTourSize);
        }
        if (StrUtil.isNotBlank(balance)) {
            innerPara.setBalance(new Balance(balance.trim()));
        }

        List<NbDriverEntity> drivers = new ArrayList<>();
        if (driverIds != null && driverIds.size() > 0) {
            //  select * from nb_driver where is_valid = true and driver_id in (" + StringUtils.join(driverIds, ',') + ")");
            LambdaQueryWrapper<NbDriverEntity> wrapper = Wrappers.lambdaQuery(NbDriverEntity.class);
            wrapper.eq(NbDriverEntity::getIsValid, true);
            wrapper.in(NbDriverEntity::getDriverId, driverIds);
            drivers = nbDriverMapper.selectList(wrapper);
        }

        //        User user = getUser();
        R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
        SysUser user = sysUserR.getData();
        DataObject dataObject = route4MeUtil.createAnOptimization(batchId, startTc, startSc, allOrders, drivers, innerPara, false, Math.toIntExact(user.getUserId()));

        saveOptimizationResult(dataObject, ob, startTc, startSc, scId, today, now);

        NbOrderEntity existOrder;
        // 检查本批次是否全部完成
        if (ob.getIsMain()) {
            // "select * from nb_order where batch_no = ? and order_status = ? and is_routed = false and express_type in (0,1) limit 1", ob.getBatchNo(), Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的主批次
            LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
            wrapper.eq(NbOrderEntity::getBatchNo, ob.getBatchNo())
                    .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                    .eq(NbOrderEntity::getIsRouted, false)
                    .in(NbOrderEntity::getExpressType, 0, 1);
            existOrder = nbOrderMapper.selectOne(wrapper);
        } else {
            //  select * from nb_order where sub_batch_no = ? and order_status = ? and is_routed = false and express_type in (0,1) limit 1", ob.getBatchNo(), Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的子批次
            LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
            wrapper.eq(NbOrderEntity::getSubBatchNo, ob.getBatchNo())
                    .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                    .eq(NbOrderEntity::getIsRouted, false)
                    .in(NbOrderEntity::getExpressType, 0, 1);
            existOrder = nbOrderMapper.selectOne(wrapper);
        }

        if (existOrder == null) {
            ob.setIsRouted(true);
            ob.setRoutedTime(new Date());
            ob.setIsScaning(0);
            nbOrderBatchMapper.updateById(ob);
        }
        return R.ok();
    }

    /**
     * 创建空批次
     */
    @Override
    public R createEmptyBatch(String batchNo) {
        if (StrUtil.isBlank(batchNo)) {
            // 空的需要自动生成
            String today = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd");
            //  select count(1) cont from nb_order_batch where create_time >= ? and create_time <= ? and batch_type = ?", today + " 00:00:00", today + " 23:59:59", OrderBatch.BATCH_TYPE_3_EMPTY).getInt("cont");
            LambdaQueryWrapper<NbOrderBatchEntity> wrapper = Wrappers.lambdaQuery(NbOrderBatchEntity.class);
            wrapper.ge(NbOrderBatchEntity::getCreateTime, today + " 00:00:00")
                    .le(NbOrderBatchEntity::getCreateTime, today + " 23:59:59")
                    .eq(NbOrderBatchEntity::getBatchType, OrderBatchDto.BATCH_TYPE_3_EMPTY);
            int count = Math.toIntExact(nbOrderBatchMapper.selectCount(wrapper));
            batchNo = DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMdd");

            batchNo = batchNo + (String.format("%04d", count+1));
        } else {
            //  select * from nb_order_batch where batch_no = ? limit 1", batchNo);
            LambdaQueryWrapper<NbOrderBatchEntity> wrapper = Wrappers.lambdaQuery(NbOrderBatchEntity.class);
            wrapper.eq(NbOrderBatchEntity::getBatchNo, batchNo);
            NbOrderBatchEntity ob = nbOrderBatchMapper.selectOne(wrapper);
            if (ob != null) {
                return LocalizedR.failed("nborderBatch.batch.number.already.exists", batchNo);
            }
        }
        NbOrderBatchEntity sub = new NbOrderBatchEntity();
        sub.setBatchNo(batchNo);
        sub.setOrderTotal(0);
        sub.setCreateTime(new Date());
        sub.setIsValid(true);
        sub.setBatchTotal(0);
        sub.setIsRouted(false);
        sub.setIsMain(false);
        sub.setPBatchId(0);
        sub.setBatchType(OrderBatchDto.BATCH_TYPE_3_EMPTY);
        nbOrderBatchMapper.insert(sub);

        return R.ok();
    }

    /**
     * 开启扫描
     */
    @Override
    public R openingScan(Integer orderId) {
        String currentSubBatchNo = null;

        if (orderId != null) {
            NbOrderEntity order = nbOrderMapper.selectById(orderId);
            if (order != null) {
                currentSubBatchNo = order.getSubBatchNo();
            }
        }
        final String finalCurrentSubBatchNo = currentSubBatchNo;

        //  select * from nb_order_batch where is_valid = true and is_scaning = true and batch_type in (? , ?)", OrderBatch.BATCH_TYPE_2_SUB, OrderBatch.BATCH_TYPE_3_EMPTY);
        LambdaQueryWrapper<NbOrderBatchEntity> wrapper = Wrappers.lambdaQuery(NbOrderBatchEntity.class);
        wrapper.eq(NbOrderBatchEntity::getIsValid, true)
                .eq(NbOrderBatchEntity::getIsScaning, true)
                .in(NbOrderBatchEntity::getBatchType, OrderBatchDto.BATCH_TYPE_2_SUB, OrderBatchDto.BATCH_TYPE_3_EMPTY);
        List<NbOrderBatchEntity> batchs = nbOrderBatchMapper.selectList(wrapper);

        JSONArray ja = batchs.stream().map(ob -> {
            JSONObject jo = new JSONObject();
            jo.set("batchId", ob.getBatchId());
            jo.set("batchNo", ob.getBatchNo());
            jo.set("orderTotal", ob.getOrderTotal());
            jo.set("note", ob.getNote());
            jo.set("createTime", DateFormatUtils.format(ob.getCreateTime(), "yyyy-MM-dd"));

            if (ob.getBatchNo().equals(finalCurrentSubBatchNo)) {
                jo.set("current", true);
            } else {
                jo.set("current", false);
            }
            return jo;
        }).collect(Collectors.toCollection(JSONArray::new));

        return R.ok(ja);
    }

    /**
     * 移动订单到其他批次
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R orderMoveToBatch(Integer batchId, Integer orderId) {
        NbOrderEntity order = nbOrderMapper.selectById(orderId);
        if (order.getOrderStatus() > OrderDto.ORDER_STATUS_200_PARCEL_SCANNED) {
            return LocalizedR.failed("nborderBatch.status.cannot.be.transferred", Optional.ofNullable(null));
        }

        NbOrderBatchEntity ob = getById(batchId);
        if (ob.getBatchType() == OrderBatchDto.BATCH_TYPE_1_MAIN) {
            return LocalizedR.failed("nborderBatch.Only.manually.created.orders.can.be.transferred", Optional.ofNullable(null));
        }

        String orderOriSubBatch = order.getSubBatchNo();
        if (ob.getBatchNo().equals(orderOriSubBatch)) {
            return LocalizedR.failed("nborderBatch.batch.consistency.no.need.to.transfer", Optional.ofNullable(null));
        }

        //        order.setSubBatchNo(ob.getBatchNo());
        //        order.updateById(order);
        LambdaUpdateWrapper<NbOrderEntity> upWrapper = Wrappers.lambdaUpdate();
        upWrapper.set(NbOrderEntity::getSubBatchNo, ob.getBatchNo());
        nbOrderMapper.update(upWrapper);

        if (StrUtil.isNotBlank(orderOriSubBatch)) {
            // "select count(1) cont from nb_order where sub_batch_no = ?", orderOriSubBatch).getInt("cont");
            LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
            wrapper.eq(NbOrderEntity::getSubBatchNo, orderOriSubBatch);
            int oriBatchOrderTotal = Math.toIntExact(nbOrderMapper.selectCount(wrapper));
            // "select * from nb_order_batch where batch_no = ? limit 1", orderOriSubBatch);
            LambdaQueryWrapper<NbOrderBatchEntity> wrapper1 = Wrappers.lambdaQuery(NbOrderBatchEntity.class);
            wrapper1.eq(NbOrderBatchEntity::getBatchNo, orderOriSubBatch);
            NbOrderBatchEntity oob = nbOrderBatchMapper.selectOne(wrapper1);

            oob.setBatchTotal(oriBatchOrderTotal);
            nbOrderBatchMapper.updateById(oob);
        }
        // "select count(1) cont from nb_order where sub_batch_no = ?", ob.getBatchNo()).getInt("cont");
        LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
        wrapper.eq(NbOrderEntity::getSubBatchNo, ob.getBatchNo());
        int currentBatchOrderTotal = Math.toIntExact(nbOrderMapper.selectCount(wrapper));
        ob.setOrderTotal(currentBatchOrderTotal);
        ob.setBatchTotal(currentBatchOrderTotal);
        nbOrderBatchMapper.updateById(ob);

        JSONObject retJo = new JSONObject();
        retJo.set("targetOrderTotal", currentBatchOrderTotal);

        return R.ok(retJo);
    }

    /**
     * 导出给route4me，Order
     */
    @Override
    public R exportToR4mOrder(String batchIds) {
        if (StrUtil.isBlank(batchIds)) {
            return LocalizedR.failed("nborderBatch.no.batch.selected", Optional.ofNullable(null));
        }

        String[] batchIdArr = batchIds.split(",");
        List<Integer> batchIdList = new ArrayList<>();
        for (String batchIdStr : batchIdArr) {
            batchIdList.add(Integer.valueOf(batchIdStr));
        }

        List<OrderSyncR4mData> syncData = new ArrayList<>();

        int orderTotal = 0;
        for (Integer batchId : batchIdList) {
            NbOrderBatchEntity batch = getById(batchId);
            log.info("检查批次数据合规性" + batch.getBatchNo());
            wsMessage(batchIds, 0, 0, "检查批次数据合规性" + batch.getBatchNo());

            if (batch.getSyncR4mOrderStatus() == OrderBatchDto.R4M_SYNC_ORDER_STATUS_3_SUCCESS) {
                return LocalizedR.failed("nborderBatch.This.batch.has.already.been.synchronized", batch.getBatchNo());
            }

            if (batch.getSyncR4mOrderStatus() == OrderBatchDto.R4M_SYNC_ORDER_STATUS_2_SYNCING) {
                return LocalizedR.failed("nborderBatch.is.being.synchronized.not.open.it.again", batch.getBatchNo());
            }

            List<NbOrderEntity> orders;
            List<NbOrderEntity> ordersInMainWith190 = null; // 未到货且主批次的订单
            if (batch.getIsMain()) {
                //  select * from nb_order where batch_no = ? and order_status in (?, ?, ?) and r4m_order_id = 0", batch.getBatchNo(), Order.ORDER_STATUS_190_ORDER_RECEIVED, Order.ORDER_STATUS_199_GATEWAY_TRANSIT, Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的主批次
                LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
                wrapper.eq(NbOrderEntity::getBatchNo, batch.getBatchNo())
                        .and(i -> i.in(NbOrderEntity::getOrderStatus,
                                OrderDto.ORDER_STATUS_190_ORDER_RECEIVED, OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED))
                        .eq(NbOrderEntity::getR4mOrderId, 0);
                orders = nbOrderMapper.selectList(wrapper);
            } else {
                //  select * from nb_order where sub_batch_no = ? and order_status in (?, ?) and r4m_order_id = 0", batch.getBatchNo(), Order.ORDER_STATUS_199_GATEWAY_TRANSIT, Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的子批次
                LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
                wrapper.eq(NbOrderEntity::getSubBatchNo, batch.getBatchNo())
                        .and(i -> i.in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED))
                        .eq(NbOrderEntity::getR4mOrderId, 0);
                orders = nbOrderMapper.selectList(wrapper);
            }

            if (batch.getIsLtl()) {
                String log1 = "批次：" + batch.getBatchNo() + "为卡派批次，开始筛选主订单作为同步订单";
                log.info(log1);
                wsMessage(batchIds, 0, 0, log1);

                Map<String, NbOrderEntity> mainOrderMap = new HashMap<>();
                List<NbOrderEntity> unsetCustomerPOrderNo = new ArrayList<>();

                for (NbOrderEntity order : orders) {
                    if (StrUtil.isNotBlank(order.getCustomerPOrderNo())) {
                        NbOrderMpsEntity mp = new NbOrderMpsEntity();
                        if (mainOrderMap.containsKey(order.getCustomerPOrderNo())) {
                            String logSubTip = "批次：" + batch.getBatchNo() + "订单：" + order.getPkgNo() + "为子订单，主订单号：" + order.getCustomerPOrderNo() + ",跳过";
                            log.info(logSubTip);
                            wsMessage(batchIds, 0, 0, logSubTip);

                            NbOrderEntity pOrder = mainOrderMap.get(order.getCustomerPOrderNo());

                            mp.setOrderId(order.getOrderId());
                            mp.setPOrderId(pOrder.getOrderId());
                            mp.setAddTime(new Date());
                            nbOrderMpsMapper.insert(mp);
                        } else {
                            String logSubTip = "批次：" + batch.getBatchNo() + "订单：" + order.getPkgNo() + "为主订单";
                            log.info(logSubTip);
                            wsMessage(batchIds, 0, 0, logSubTip);
                            mainOrderMap.put(order.getCustomerPOrderNo(), order);

                            mp.setOrderId(order.getOrderId());
                            mp.setPOrderId(0);
                            mp.setAddTime(new Date());
                            nbOrderMpsMapper.insert(mp);
                        }

                    } else {
                        unsetCustomerPOrderNo.add(order);
                    }

                }

                List<NbOrderEntity> mainOrderList = mainOrderMap.values().stream().collect(Collectors.toList());
                if (mainOrderList.size() > 0) {
                    unsetCustomerPOrderNo.addAll(mainOrderList);
                }

                orders = unsetCustomerPOrderNo;
            }

            orderTotal += orders.size();

            String logStatTip = "批次" + batch.getBatchNo() + "共" + orders.size() + "个订单";
            log.info(logStatTip);
            wsMessage(batchIds, 0, 0, logStatTip);

            if (orders.size() == 0) {
                return LocalizedR.failed("nborderBatch.no.orders.available.for.planning", batch.getBatchNo());
            }

            Map<Integer, NbTransferCenterEntity> tcMap = new HashMap<>();
            Map<String, List<NbOrderEntity>> orderMap = new HashMap<>();

            Map<String, List<NbTransferCenterEntity>> tcRegionCodeMap = new HashMap<>();

            NbSortingCenterEntity sc = null;
            for (NbOrderEntity order : orders) {
                Integer tcId = order.getTcId();
                if (tcId == null || tcId == 0) {
                    log.info("发现有未分配转运中心的订单，暂时不规划： " + order.getOrderId());

                    batch.setRoutedErrmsg("发现有未分配转运中心的订单，暂时不能同步： " + order.getOrderId());
                    nbOrderBatchMapper.updateById(batch);

                    return LocalizedR.failed("nborderBatch.no.assigned.transfer.center.no.operation", order.getOrderId());
                }

                if (order.getExpressType() == OrderDto.EXPRESS_TYPE_2_PICKUP) {
                    log.info("自提件不同步orderId=" + order.getOrderId() + ",pkgNo=" + order.getPkgNo() + ",expressType=" + order.getExpressType());
                    continue;
                }

                List<NbOrderEntity> tcOrders;
                NbTransferCenterEntity tc;
                if (tcMap.containsKey(tcId)) {
                    tc = tcMap.get(tcId);
                } else {
                    tc = nbTransferCenterMapper.selectById(tcId);
                    tcMap.put(tcId, tc);
                }

                if (StrUtil.isBlank(tc.getRegionCode())) {
                    batch.setRoutedErrmsg("转运中心[" + tc.getCenterName() + "]没有设置大区号,无法为订单完成分组,请检查");
                    nbOrderBatchMapper.updateById(batch);

//                    return LocalizedR.failed("-5", batch.getRoutedErrmsg());
                    return LocalizedR.failed("nborderBatch.five", batch.getRoutedErrmsg());
                }

                if (orderMap.containsKey(tc.getRegionCode())) {
                    tcOrders = orderMap.get(tc.getRegionCode());
                } else {
                    tcOrders = Lists.newArrayList();
                }

                tcOrders.add(order);
                orderMap.put(tc.getRegionCode(), tcOrders);

                List<NbTransferCenterEntity> tcs = tcRegionCodeMap.getOrDefault(tc.getRegionCode(), new ArrayList<NbTransferCenterEntity>());
                tcs.add(tc);
                tcRegionCodeMap.put(tc.getRegionCode(), tcs);

                if (sc == null) {
                    sc = nbSortingCenterMapper.selectById(order.getScId());
                }
            }

            OrderSyncR4mData data = new OrderSyncR4mData();
            data.setSc(sc);
            data.setOrderBatch(batch);
            data.setOrderMap(orderMap);
            data.setTcMap(tcMap);
            data.setTcRegionCodeMap(tcRegionCodeMap);

            syncData.add(data);

            if (ordersInMainWith190 != null && ordersInMainWith190.size() > 0) {
                // "select * from nb_order_batch where batch_type = 5 order by batch_id desc limit 1"); // 获取最新的未到货批次
                LambdaQueryWrapper<NbOrderBatchEntity> wrapper = Wrappers.lambdaQuery(NbOrderBatchEntity.class);
                wrapper.eq(NbOrderBatchEntity::getBatchType, 5)
                        .orderByDesc(NbOrderBatchEntity::getBatchId);
                NbOrderBatchEntity nonReceiptBatch = getOne(wrapper);
                for (NbOrderEntity order190 : ordersInMainWith190) {
                    order190.setSubBatchNo(nonReceiptBatch.getBatchNo());
                    nbOrderMapper.updateById(order190);
                }

                // "select count(1) cont from nb_order where sub_batch_no = ? limit 1", nonReceiptBatch.getBatchNo()).getInt("cont");
                LambdaQueryWrapper<NbOrderEntity> wrapper1 = Wrappers.lambdaQuery(NbOrderEntity.class);
                wrapper1.eq(NbOrderEntity::getSubBatchNo, nonReceiptBatch.getBatchNo());
                int total = Math.toIntExact(nbOrderMapper.selectCount(wrapper1));
                nonReceiptBatch.setOrderTotal(total);
                nonReceiptBatch.setBatchTotal(total);
                nonReceiptBatch.setImpTotal(total);
                nbOrderBatchMapper.updateById(nonReceiptBatch);
            }
        }

        int finalOrderTotal = orderTotal;

        AtomicInteger current = new AtomicInteger(0);
        AtomicInteger allcount = new AtomicInteger(0);

        for (OrderSyncR4mData data : syncData) {
            NbOrderBatchEntity batch = data.getOrderBatch();

            wsMessage(batchIds, 0, 0, "开始同步" + batch.getBatchNo());

            NbSortingCenterEntity sc = data.getSc();
            Map<String, List<NbOrderEntity>> orderMap = data.getOrderMap();
            Map<String, List<NbTransferCenterEntity>> tcRegionCodeMap = data.getTcRegionCodeMap();

            batch.setSyncR4mOrderStatus(OrderBatchDto.R4M_SYNC_ORDER_STATUS_2_SYNCING);
            nbOrderBatchMapper.updateById(batch);

            String timezone = sc.getScTimezone();
            String scheduledFor = Instant.now().atOffset(ZoneOffset.of(timezone)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            log.info("共" + orderMap.size() + "个大区等待导入R4M");

            ExecutorService executorService = Executors.newFixedThreadPool(10);

            Iterator<Map.Entry<String, List<NbOrderEntity>>> iter = orderMap.entrySet().iterator();
            while (iter.hasNext()) {
                Map.Entry<String, List<NbOrderEntity>> entry = iter.next();
                String regionCode = entry.getKey();
                List<NbOrderEntity> tcOrders = entry.getValue();


                List<NbTransferCenterEntity> tcs = tcRegionCodeMap.get(regionCode);
                List<NbTransferCenterEntity> uniqueTcs = new ArrayList<>();
                List<Integer> checked = Lists.newArrayList();
                for (NbTransferCenterEntity tc : tcs) {
                    if (checked.contains(tc.getTcId())) {
                        continue;
                    }

                    uniqueTcs.add(tc);
                    checked.add(tc.getTcId());
                }

                for (NbOrderEntity order : tcOrders) {
                    executorService.execute(new Runnable() {
                        @Override
                        public void run() {
                            com.route4me.sdk.services.orders.Order r4mOrder = new com.route4me.sdk.services.orders.Order();
                            r4mOrder.setStatusID(OrderStatus.NEW.getValue());
                            r4mOrder.setCachedLatitude(order.getDestLat());
                            r4mOrder.setCachedLongitude(order.getDestLng());
                            r4mOrder.setAddressZip(StringUtils.deleteWhitespace(order.getDestPostalCode()));
                            r4mOrder.setAddressCountryID(order.getDestCountry());
                            r4mOrder.setStateID(order.getDestProvince());
                            r4mOrder.setCity(order.getDestCity());
                            r4mOrder.setAddress1(order.getDestAddress1());
                            r4mOrder.setAddress2(order.getDestAddress2());
                            r4mOrder.setGroup(batch.getBatchNo());
                            r4mOrder.setWeight(order.getPkgWeight().setScale(2, RoundingMode.HALF_UP).doubleValue());
                            r4mOrder.setAddressAlias(order.getOrderId().toString());
                            r4mOrder.setFirstName(order.getDestName());
                            r4mOrder.setServiceTime(300);

                            r4mOrder.setPhone(order.getDestTel());
                            r4mOrder.setEmail(order.getDestEmail());
                            r4mOrder.setTrackingNumber(order.getPkgNo());
                            r4mOrder.setAddressStopType(Constants.AddressStopType.DELIVERY.getValue());
                            r4mOrder.setDateScheduled(scheduledFor);
                            r4mOrder.setWeight(order.getPkgWeight().setScale(2, RoundingMode.HALF_UP).doubleValue());

                            R r = route4MeUtil.importOrder(r4mOrder);

                            int innerAllCount = allcount.incrementAndGet();
                            if (r.isOk()) {
                                com.route4me.sdk.services.orders.Order r4mOrderSaved = (com.route4me.sdk.services.orders.Order) r.getData();
                                order.setR4mOrderId(r4mOrderSaved.getId());
                                nbOrderMapper.updateById(order);

                                int innerCurrent = current.incrementAndGet();

                                wsMessage(batchIds, innerCurrent, finalOrderTotal, null);
                            } else {
                                String errmsg = r.getMsg();
                                try {
                                    String json = errmsg.substring(errmsg.indexOf("{"));
                                    JSONObject jo = JSONUtil.parseObj(json);
                                    errmsg = jo.getJSONArray("errors").getStr(0);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                                wsMessage(batchIds, current.get(), finalOrderTotal, "导入失败：tracknumber=" + order.getPkgNo() + ", errmsg=" + errmsg + "," + innerAllCount);
                            }
                        }
                    });

                }

            }

            executorService.shutdown();

            while (true) {
                if (executorService.isTerminated()) {
                    break;

                } else {
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }

            batch.setRoutedErrmsg(null);
            if (batch.getBatchType() == OrderBatchDto.BATCH_TYPE_5_NOT_ARRIVED) {
                batch.setSyncOrderTotal(finalOrderTotal + batch.getSyncOrderTotal());
            } else {
                batch.setSyncR4mOrderStatus(OrderBatchDto.R4M_SYNC_ORDER_STATUS_3_SUCCESS);
                batch.setSyncOrderTotal(finalOrderTotal);
            }
            batch.setSyncR4mOrderTime(Instant.now().getEpochSecond() * 1000);
            nbOrderBatchMapper.updateById(batch);

            log.info("批次同步记录batchId=" + batch.getBatchId() + ",orderTotal=" + finalOrderTotal);
        }
        return R.ok();
    }


    /**
     * Custom Release
     */
    @Override
    public R customRelease(Integer batchId) {
        NbOrderBatchEntity batch = getById(batchId);
        if (batch.getIsMain() == false) {
            return LocalizedR.failed("nborderBatch.only.main.batch.operation", Optional.ofNullable(null));
        }

        //select * from nb_order where batch_no = ? and order_status in (?, ?, ?)",batch.getBatchNo(),190, 191, 192);
        LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
        wrapper.eq(NbOrderEntity::getBatchNo, batch.getBatchNo())
                .in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_190_ORDER_RECEIVED, OrderDto.ORDER_STATUS_191_ORDER_CANCELED, OrderDto.ORDER_STATUS_192_CUSTOM_HOLD);
        List<NbOrderEntity> orders = nbOrderMapper.selectList(wrapper);
        if (orders.size() == 0) {
            return LocalizedR.failed("nborderBatch.no.modifiable.orders.within.the.batch", Optional.ofNullable(null));
        }

        Map<Integer, NbSortingCenterEntity> scMap = new HashMap<>();
        Map<Integer, JSONObject> cityMap = new HashMap<>();

        for (NbOrderEntity order : orders) {
            NbSortingCenterEntity sc;
            if (scMap.containsKey(order.getScId())) {
                sc = scMap.get(order.getScId());
            } else {
                sc = nbSortingCenterMapper.selectById(order.getScId());
                if (sc != null) {
                    scMap.put(order.getScId(), sc);

                    if (!cityMap.containsKey(sc.getCityId())) {
                        JSONObject cityJo = commonDataUtil.getCityById(sc.getCityId());
                        cityMap.put(sc.getCityId(), cityJo);
                    }
                }
            }
            processOrder(order, sc);
//            Db.tx(new IAtom() {
//                @Override
//                public boolean run() throws SQLException {
//                    order.setOrderStatus(OrderDto.ORDER_STATUS_198_CUSTOM_RELEASE_DIRECT);
//                    order.setLastRouteTime(new Date());
//                    order.updateById(order);
//
//                    String address = null;
//                    String timezone = ZoneId.systemDefault().getId();
//                    if (sc != null) {
//                        address = commonDataUtil.getAddress(sc.getProvinceId(), sc.getCityId());
//                    }
//
//                    NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), 0, 0d, 0d, address, timezone);
//                    op.setStaffId(SecurityUtils.getUser().getId().intValue());
//                    op.save();
//
//                    return true;
//                }
//            });

        }

        return LocalizedR.ok("nborderBatch.successfully.updated.orders", orders.size());
    }

    @Transactional(rollbackFor = Exception.class)
    public void processOrder(NbOrderEntity order, NbSortingCenterEntity sc) {
        order.setOrderStatus(OrderDto.ORDER_STATUS_198_CUSTOM_RELEASE_DIRECT);
        order.setLastRouteTime(new Date());
        nbOrderMapper.updateById(order);

        String address = null;
        String timezone = ZoneId.systemDefault().getId();
        if (sc != null) {
            address = commonDataUtil.getAddress(sc.getProvinceId(), sc.getCityId());
        }

        NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), 0, 0d, 0d, address, timezone);
        op.setStaffId(SecurityUtils.getUser().getId());
        nbOrderPathMapper.insert(op);
    }

    private void wsMessage(String batchId, int current, Integer total, String errmsg) {
        try {
            JSONObject wsData = new JSONObject();
            wsData.set("batchId", batchId);
            wsData.set("current", current);
            wsData.set("total", total);
            if (StrUtil.isNotBlank(errmsg)) {
                wsData.set("errmsg", errmsg);
            }
            // Tio.sendToGroup(WsKit.use(), "_syncToRoute4meOrder." + batchId, WsResponse.fromText(wsData.toString(), "UTF-8"));
            R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
            SysUser user = sysUserR.getData();
            String username = user.getUsername();
            WebSocketMessageSender.send(username, wsData.toString());
            log.info("sessionId {} ,msg {}", username, wsData);
        } catch (Exception e) {
            e.printStackTrace();

        }
    }

    private void saveOptimizationResult(DataObject dataObject, NbOrderBatchEntity batch, NbTransferCenterEntity startTc, NbSortingCenterEntity startSc, Integer ownerScId, String today, ZonedDateTime now) {
        if (dataObject != null) {
            String problemId = dataObject.getOptimizationProblemId();
            Links links = dataObject.getLinks();

            Constants.OptimizationState os = Constants.OptimizationState.get(dataObject.getState().intValue());
            log.info("state:" + dataObject.getState() + "," + (os == null ? "-" : os.name()));

            List<Route> routes = dataObject.getRoutes();

            Map<String, List<Address>> route4meAddressMap = new HashMap<>();
            List<Address> route4meAddressList = dataObject.getAddresses();
            for (Address route4meAdderss : route4meAddressList) {
                if (route4meAdderss.getRouteId() == null) {
                    continue;
                }
                List<Address> innerList = route4meAddressMap.getOrDefault(route4meAdderss.getRouteId(), Lists.newArrayList());
                innerList.add(route4meAdderss);

                route4meAddressMap.put(route4meAdderss.getRouteId(), innerList);
            }

            String ymdStr = now.format(DateTimeFormatter.ofPattern("yyMMdd"));

            String fullDateTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date timezoneDate = null;
            try {
                timezoneDate = sdf.parse(fullDateTime);
            } catch (ParseException e1) {
                e1.printStackTrace();
            }

            List<R4mTobeUpdateRouter> tobeUpdateRouterList = new ArrayList<>();

            NbSortingCenterEntity sc = nbSortingCenterMapper.selectById(ownerScId);
            // "select count(1) cont from nb_transfer_batch where owner_sc_id = ? and add_time >= ? and add_time <= ?", ownerScId, today + " 00:00:00", today + " 23:59:59").getInt("cont");
            LambdaQueryWrapper<NbTransferBatchEntity> queryWrapper = Wrappers.lambdaQuery(NbTransferBatchEntity.class);
            queryWrapper.eq(NbTransferBatchEntity::getOwnerScId, ownerScId);
            queryWrapper.ge(NbTransferBatchEntity::getAddTime, today + " 00:00:00");
            queryWrapper.le(NbTransferBatchEntity::getAddTime, today + " 23:59:59");
            int todayLQTotal = Math.toIntExact(nbTransferBatchMapper.selectCount(queryWrapper));
            for (Route route : routes) {
                log.info("处理Route:" + route);
                String routeView = route.getLinks().getRoute();

                int driverId = 0;
                Long memberId = route.getMemberId();
                if (memberId != null && memberId > 0) {
                    //  select * from nb_driver where route4me_member_id = ? limit 1
                    LambdaQueryWrapper<NbDriverEntity> wrapper = Wrappers.lambdaQuery(NbDriverEntity.class);
                    wrapper.eq(NbDriverEntity::getRoute4meMemberId, memberId);
                    NbDriverEntity driver = nbDriverMapper.selectOne(wrapper);

                    if (driver != null) {
                        driverId = driver.getDriverId();
                    }
                }

                NbTransferBatchEntity tb = new NbTransferBatchEntity();
                tb.setBatchNo(sc.getScCode() + "-" + ymdStr + "-" + String.format("%02d", (todayLQTotal + 1)));
                tb.setAddTime(timezoneDate);
                tb.setDriverId(driverId);
                tb.setOrderTotal(route.getRoutePieces());

//                tb.setEstimatedHour(new BigDecimal(route.getPlannedTotalRouteDuration() / 60 / 60.0d).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                BigDecimal plannedDuration = BigDecimal.valueOf(route.getPlannedTotalRouteDuration());
                BigDecimal estimatedHour = plannedDuration.divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP); // 将总秒数转换为小时，保留两位小数
                tb.setEstimatedHour(estimatedHour);

                try {
                    // int hour = route.getPlannedTotalRouteDuration() / 3600;
                    // int min = (route.getPlannedTotalRouteDuration() % 3600) / 60;
                    // Double hour2 = Double.valueOf(hour + "." + min);
                    // tb.setEstimatedHour(hour2);
                    BigDecimal[] hourAndMinute = plannedDuration.divideAndRemainder(BigDecimal.valueOf(3600)); // 小时部分和剩余秒数
                    BigDecimal hour = hourAndMinute[0]; // 小时部分
                    BigDecimal minute = hourAndMinute[1].divide(BigDecimal.valueOf(60), 0, RoundingMode.FLOOR); // 转换为整分钟

                    // 合并小时和分钟为小数形式（例如：2.15 表示 2 小时 15 分钟）
                    BigDecimal hourWithMinute = hour.add(minute.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
                    tb.setEstimatedHour(hourWithMinute);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (driverId == 0) {
                    tb.setGetType(TransferBatchDto.GET_TYPE_2_SNATCH);
                } else {
                    tb.setGetType(TransferBatchDto.GET_TYPE_1_ALLOCATION);
                }

                tb.setBatchCode(tb.getBatchNo());
                tb.setTranferDriverId(0);
                if (startTc != null) {
                    tb.setTcId(startTc.getTcId()); // -> 这里在用
                }
                if (startSc != null) {
                    tb.setScId(startSc.getScId());
                }
                tb.setRouteId(route.getId());
                tb.setOptimizationProblemId(problemId);
                tb.setTripDistance(route.getTripDistance());
                tb.setPlannedTotalRouteDuration(route.getPlannedTotalRouteDuration());
                tb.setMemberId(route.getMemberId());
                tb.setRouteView(routeView);
                tb.setOwnerScId(ownerScId);
                tb.setOrderBatchId(batch.getBatchId());
                nbTransferBatchMapper.insert(tb);

                Parameters parameters = route.getParameters();
                parameters.setRouteName(tb.getBatchNo());
                route.setParameters(parameters);

                List<Address> routeAddressList = route.getAddresses();
                int index = 1;
                for (Address address : routeAddressList) {
                    if (address.getDepot()) {
                        log.info("仓库跳过->" + address);
                        continue;
                    }

                    int sequenceNo = Math.toIntExact(address.getSequenceNo());
                    Integer orderId = Integer.valueOf(address.getCustom_fields().get("__orderId").toString());

                    String pickNo = today.split("-")[2] + "-" + String.format("%02d", (todayLQTotal + 1)) + "-" + String.format("%02d", sequenceNo);

                    NbTransferBatchOrderEntity tbo = new NbTransferBatchOrderEntity();
                    tbo.setBatchId(tb.getBatchId());
                    tbo.setOrderId(orderId);
                    tbo.setPickNo(pickNo);
                    tbo.setRouteDestinationId(address.getRouteDestinationId());
                    tbo.setMemberId(address.getMemberId().longValue());
                    tbo.setRouteId(address.getRouteId());
                    tbo.setOptimizationProblemId(address.getOptimizationProblemId());
                    tbo.setSequenceNo(Math.toIntExact(address.getSequenceNo()));
                    tbo.setChannelName(address.getChannelName());
                    tbo.setDriveTimetoNextDestination(address.getDriveTimetoNextDestination());
                    tbo.setTrackingNumber(address.getTrackingNumber());
                    nbTransferBatchOrderMapper.insert(tbo);

                    address.setAlias(pickNo);

                    //  NbOrderEntity order = nbOrderMapper.selectById(tbo.getOrderId());
                    LambdaUpdateWrapper<NbOrderEntity> wrapper = Wrappers.lambdaUpdate(NbOrderEntity.class);
                    wrapper.eq(NbOrderEntity::getOrderId, tbo.getOrderId());
                    //  order.setPickNo(tbo.getPickNo());
                    wrapper.set(NbOrderEntity::getPickNo, tbo.getPickNo());
                    if (driverId > 0) {
                    //  order.setDriverId(driverId);
                        wrapper.set(NbOrderEntity::getDriverId, driverId);
                    }
                    //  order.setIsRouted(true);
                    wrapper.set(NbOrderEntity::getIsRouted, true);
                    nbOrderMapper.update(wrapper);

                    index ++;
                }

                tobeUpdateRouterList.add(new R4mTobeUpdateRouter(route));
                todayLQTotal++;
            }

            // 开始更新
            route4MeUtil.updateRoute(tobeUpdateRouterList);
        }
    }

    // 派送批次导出Excel
    @Override
    public List<NbOrderBatchExcelVo> getExcel(NbOrderBatchPageVo vo, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids);
        return nbOrderBatchMapper.selectJoinList(NbOrderBatchExcelVo.class, wrapper);
    }

    private MPJLambdaWrapper getWrapper(NbOrderBatchPageVo vo, Integer[] ids) {
        MPJLambdaWrapper<NbOrderBatchEntity> wrapper = new MPJLambdaWrapper<NbOrderBatchEntity>();
        wrapper.eq(ObjectUtil.isNotNull(vo.getBatchId()), NbOrderBatchEntity::getBatchId, vo.getBatchId());     // 条件查询-批次ID
        wrapper.like(StrUtil.isNotBlank(vo.getBatchNo()), NbOrderBatchEntity::getBatchNo, vo.getBatchNo());     // 条件查询-批次号

        String addTime = vo.getAddTime();   //创建时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        if (addTime != null) {
            int splitIndex = addTime.indexOf(":", addTime.indexOf(":") + 1) + 3;
            String startAddTime = addTime.substring(0, splitIndex);
            String endAddTime = addTime.substring(splitIndex + 1);
            wrapper.ge(NbOrderBatchEntity::getCreateTime, startAddTime).le(NbOrderBatchEntity::getCreateTime, endAddTime);  // 条件查询-创建时间
        }
        String eta = vo.getEtaTime();   //预计到达时间  2024-03-25 00:00:00-2024-12-31 00:00:00
        if (eta != null) {
            int splitIndex = eta.indexOf(":", eta.indexOf(":") + 1) + 3;
            String startEtaTime = eta.substring(0, splitIndex);
            String endEtaTime = eta.substring(splitIndex + 1);
            wrapper.ge(NbOrderBatchEntity::getEta, startEtaTime).le(NbOrderBatchEntity::getEta, endEtaTime);    // 条件查询-预计到达时间
        }
        String etd = vo.getEtdTime();   //预计送出时间  2024-03-25 00:00:00-2024-12-31 00:00:00
        if (etd != null) {
            int splitIndex = etd.indexOf(":", etd.indexOf(":") + 1) + 3;
            String startEtdTime = etd.substring(0, splitIndex);
            String endEtdTime = etd.substring(splitIndex + 1);
            wrapper.ge(NbOrderBatchEntity::getEtd, startEtdTime).le(NbOrderBatchEntity::getEtd, endEtdTime);    // 条件查询-预计送出时间
        }
        wrapper.eq(ObjectUtil.isNotNull(vo.getIsMain()), NbOrderBatchEntity::getIsMain, vo.getIsMain())         // 条件查询-是否主批次
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbOrderBatchEntity::getBatchId, ids)
                .orderByDesc(NbOrderBatchEntity::getBatchId);
        return wrapper;
    }

    /**
     * API-订单更新批次信息
     * @param paramsVo
     * @param vo
     * @return
     */
    @Override
    public OldResult updateBatch(ApiRequestParamsVo paramsVo, ApiOrderUpdateVo vo) {
        String batchNo = vo.getBatchNo();
        NbMerchantEntity merchant = nbMerchantMapper.selectById(paramsVo.getAppId());

        // "select * from nb_order_batch where merchant_id = ? and batch_no = ? limit 1", merchant.getMerchantId(), batchNo);
        NbOrderBatchEntity ob = getOne(new LambdaQueryWrapper<NbOrderBatchEntity>()
                .eq(NbOrderBatchEntity::getMerchantId, merchant.getMerchantId()).eq(NbOrderBatchEntity::getBatchNo, batchNo));
        if (ob == null) {
            return OldResult.fail("500801", "batchNo inexistence:" + batchNo);
        }
        String eta = vo.getEta();
        String etd = vo.getEtd();
        String ata = vo.getAta();
        String atd = vo.getAtd();

        Date etaDate = null, etdDate = null, ataDate = null, atdDate = null;

        String dateFormat = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        if (StrUtil.isNotBlank(eta)) {
            try {
                etaDate = sdf.parse(eta);
                ob.setEta(etaDate);
            } catch (ParseException e) {
                e.printStackTrace();
                return OldResult.fail("500201", "eta time format invalid. act:" + eta + ",expect:" + dateFormat);
            }
        }
        if (StrUtil.isNotBlank(etd)) {
            try {
                etdDate = sdf.parse(etd);
                ob.setEtd(etdDate);
            } catch (ParseException e) {
                e.printStackTrace();
                return OldResult.fail("500202", "etd time format invalid. act:" + etd + ",expect:" + dateFormat);
            }
        }
        if (StrUtil.isNotBlank(ata)) {
            try {
                ataDate = sdf.parse(ata);
                ob.setAta(ataDate);
            } catch (ParseException e) {
                e.printStackTrace();
                return OldResult.fail("500203", "ata time format invalid. act:" + ata + ",expect:" + dateFormat);
            }
        }
        if (StrUtil.isNotBlank(atd)) {
            try {
                atdDate = sdf.parse(atd);
                ob.setAtd(atdDate);
            } catch (ParseException e) {
                e.printStackTrace();
                return OldResult.fail("500204", "atd time format invalid. act:" + atd + ",expect:" + dateFormat);
            }
        }

        final Date finalEtaDate = etaDate, finalEtdDate = etdDate, finalAtaDate = ataDate, finalAtdDate = atdDate;
        processUpdate(finalEtaDate, finalEtdDate, finalAtaDate, finalAtdDate, batchNo, merchant.getMerchantId(), ob);

        return OldResult.ok("0");
    }

    @Transactional(rollbackFor = Exception.class)
    public void processUpdate(Date finalEtaDate, Date finalEtdDate, Date finalAtaDate, Date finalAtdDate, String batchNo, Integer merchantId, NbOrderBatchEntity ob) {
        updateById(ob);

        LambdaUpdateWrapper<NbOrderEntity> updateWrapper = Wrappers.lambdaUpdate(NbOrderEntity.class);
        if (finalEtaDate != null) {
            updateWrapper.set(NbOrderEntity::getEta, finalEtaDate);
        }
        if (finalEtdDate != null) {
            updateWrapper.set(NbOrderEntity::getEtd, finalEtdDate);
        }
        if (finalAtaDate != null) {
            updateWrapper.set(NbOrderEntity::getAta, finalAtaDate);
        }
        if (finalAtdDate != null) {
            updateWrapper.set(NbOrderEntity::getAtd, finalAtdDate);
        }

        updateWrapper.eq(NbOrderEntity::getMerchantId, merchantId).eq(NbOrderEntity::getBatchNo, batchNo);
        nbOrderMapper.update(updateWrapper);
        log.info("订单航空信息更新batchNo=" + batchNo);
    }

    /**
     * API-更新订单主批次
     * @param paramsVo
     * @param batchNo
     * @param pkgNos
     * @return
     */
    @Override
    public OldResult updateOrderBatchNo(ApiRequestParamsVo paramsVo, String batchNo, String pkgNos) {
        Integer batchTotal = 0; // 通知该批次订单数量
        NbMerchantEntity merchant = nbMerchantMapper.selectById(paramsVo.getAppId());
        if (StrUtil.isBlank(pkgNos)) {
            return OldResult.fail("500300", "Package number is empty");
        }
        if (StrUtil.isBlank(batchNo)) {
            return OldResult.fail("500301", "Batch number is empty");
        }

        // select * from nb_order where batch_no = ? limit 1", batchNo);
        NbOrderEntity checkOrder = nbOrderMapper.selectOne(new LambdaQueryWrapper<NbOrderEntity>()
                .eq(NbOrderEntity::getBatchNo, batchNo), false);
        if (checkOrder != null && checkOrder.getMerchantId().intValue() != merchant.getMerchantId()) {
            return OldResult.fail("500302", "Upon inspection, this batch number is found to be used by another vendor. Please check for any discrepancies." + batchNo);
        }
        String[] pkgNoArr = pkgNos.split(",");
        List<NbOrderEntity> orders = nbOrderMapper.selectList(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getMerchantId, merchant.getMerchantId())
                .in(NbOrderEntity::getPkgNo, Arrays.asList(pkgNoArr)));

        if (orders.size() == 0) {
            return OldResult.fail("500303", "No orders found.");
        }
        JSONArray updateData = new JSONArray();
        List<NbOrderEntity> updateOrders = new ArrayList<>();
        List<NbOrderUpdateLogEntity> saveLogs = new ArrayList<>();
        for (NbOrderEntity order : orders) {
            if (!batchNo.equals(order.getBatchNo())) {
                String oriBatchNo = order.getBatchNo();
                order.setBatchNo(batchNo);
                updateOrders.add(order);

                NbOrderUpdateLogEntity updateLog = new NbOrderUpdateLogEntity();
                updateLog.setOrderId(order.getOrderId());
                updateLog.setLogTime(new Date());
                updateLog.setAction("主批次(" + oriBatchNo + "->" + batchNo + ")," + merchant.getName());
                updateLog.setAdminId(0l);
                updateLog.setLogType(1);
                updateLog.setColnumName("batch_no");
                updateLog.setRemark("接口更新");
//				log.save();
                saveLogs.add(updateLog);

                JSONObject jo = new JSONObject();
                jo.set("pkgNo", order.getPkgNo());
                jo.set("batchNo", batchNo);
                updateData.add(jo);
            }
        }
        JSONObject retJs = new JSONObject();
        retJs.set("updatedData", updateData);

        NbOrderBatchEntity ob = null;
        if (updateOrders.size() > 0) {
            int scId = updateOrders.get(0).getScId();
            NbSortingCenterEntity sc = nbSortingCenterMapper.selectById(scId);
            // 检查批次是否存在
            // "select * from nb_order_batch where batch_no = ? limit 1", batchNo);
            ob = nbOrderBatchMapper.selectOne(new LambdaQueryWrapper<NbOrderBatchEntity>().eq(NbOrderBatchEntity::getBatchNo, batchNo), false);
            if (ob == null) {
                ob = new NbOrderBatchEntity();
                ob.setBatchNo(batchNo);
                ob.setCreateTime(NBDUtils.getLocalDate(sc.getScTimezone()));
                ob.setBatchTotal(0);
                ob.setMerchantId(merchant.getMerchantId());
                nbOrderBatchMapper.insert(ob);
            }
        } else {
            // "select count(1) cont from nb_order where batch_no = ?", batchNo).getInt("cont");
            Long count = nbOrderMapper.selectCount(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getBatchNo, batchNo));
            int orderTotal = Math.toIntExact(count);
            retJs.set("orderTotal", orderTotal);
            return OldResult.ok("0", retJs);
        }

        Integer finalBatchId = ob.getBatchId();
        Integer orderTotal = processUpdateBatch(updateOrders, saveLogs, batchNo, batchTotal, finalBatchId);
        retJs.set("orderTotal", orderTotal);
        return OldResult.ok("0", retJs);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer processUpdateBatch(List<NbOrderEntity> updateOrders, List<NbOrderUpdateLogEntity> saveLogs, String batchNo, Integer batchTotal, Integer finalBatchId) {
        if (updateOrders.size() > 0) {
//            Db.batchUpdate(updateOrders, updateOrders.size());
            updateOrders.forEach(order -> nbOrderMapper.updateById(order));
        }
        if (saveLogs.size() > 0) {
//            Db.batchSave(saveLogs, saveLogs.size());
            saveLogs.forEach(updateLog -> orderUpdateLogMapper.insert(updateLog));
        }

        // "select count(1) cont from nb_order where batch_no = ?", batchNo).getInt("cont");
        Long count = nbOrderMapper.selectCount(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getBatchNo, batchNo));
        Integer orderTotal = Math.toIntExact(count);

        // "update nb_order_batch set order_total = ?, batch_total = ? where batch_id = ?", orderTotal, batchTotal, finalBatchId);
        update(new LambdaUpdateWrapper<NbOrderBatchEntity>()
                .set(NbOrderBatchEntity::getOrderTotal, orderTotal).set(NbOrderBatchEntity::getBatchTotal, batchTotal)
                .eq(NbOrderBatchEntity::getBatchId, finalBatchId));

        return orderTotal;
    }

    @Override
    public List<NbOrderBatchEntity> findNoRouted(String batchNo) {
        // 第一个查询：is_main = 0   子批次
        MPJLambdaWrapper<NbOrderBatchEntity> wrapper1 = new MPJLambdaWrapper<NbOrderBatchEntity>()
                .selectAll(NbOrderBatchEntity.class)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getSubBatchNo, NbOrderBatchEntity::getBatchNo)
                .eq(NbOrderBatchEntity::getIsMain, 0)
                .eq(NbOrderBatchEntity::getBatchTotal, NbOrderBatchEntity::getOrderTotal)
                .eq(NbOrderBatchEntity::getIsRouted, 0)
                .ne(NbOrderEntity::getR4mOrderId, 0)
                .eq(NbOrderBatchEntity::getBatchNo, batchNo)
                .last("limit 1");

        List<NbOrderBatchEntity> result1 = nbOrderBatchMapper.selectJoinList(NbOrderBatchEntity.class, wrapper1).stream().distinct().collect(Collectors.toList());

        // 第二个查询：is_main != 0   主批次
        MPJLambdaWrapper<NbOrderBatchEntity> wrapper2 = new MPJLambdaWrapper<NbOrderBatchEntity>()
                .selectAll(NbOrderBatchEntity.class)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getBatchNo, NbOrderBatchEntity::getBatchNo)
                .ne(NbOrderBatchEntity::getIsMain, 0)
                .eq(NbOrderBatchEntity::getBatchTotal, NbOrderBatchEntity::getOrderTotal)
                .eq(NbOrderBatchEntity::getIsRouted, 0)
                .ne(NbOrderEntity::getR4mOrderId, 0)
                .eq(NbOrderBatchEntity::getBatchNo, batchNo)
                .last("limit 1");

        List<NbOrderBatchEntity> result2 = nbOrderBatchMapper.selectJoinList(NbOrderBatchEntity.class, wrapper2).stream().distinct().collect(Collectors.toList());
        // 合并结果
        List<NbOrderBatchEntity> result = new ArrayList<>();
        result.addAll(result1);
        result.addAll(result2);

        return result;
    }

}