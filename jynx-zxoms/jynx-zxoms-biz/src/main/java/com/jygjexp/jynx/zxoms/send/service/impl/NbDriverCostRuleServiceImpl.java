package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbDriverCostRuleEntity;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbDriverCostRuleMapper;
import com.jygjexp.jynx.zxoms.send.service.NbDriverCostRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
/**
 * 司机费用规则
 *
 * <AUTHOR>
 * @date 2024-10-14 16:21:53
 */
@Service
@RequiredArgsConstructor
public class NbDriverCostRuleServiceImpl extends ServiceImpl<NbDriverCostRuleMapper, NbDriverCostRuleEntity> implements NbDriverCostRuleService {
    private final NbDriverCostRuleMapper nbDriverCostRuleMapper;

    @Override
    public Page search(Page page, NbDriverCostRuleEntity entity) {
        // select driver_id, first_name, last_name, email, mobile from nb_driver where is_leader = true; ds=nbd;
        MPJLambdaWrapper<NbDriverCostRuleEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbDriverCostRuleEntity.class)
                .select(NbDriverEntity::getFirstName)
                .select(NbDriverEntity::getLastName)
                .select(NbDriverEntity::getMobile)
                .select(NbDriverEntity::getEmail)
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbDriverCostRuleEntity::getDriverId);
//                .eq(ObjectUtil.isNotNull(entity.getDriverId()), NbDriverCostRuleEntity::getDriverId, entity.getDriverId())
        return nbDriverCostRuleMapper.selectPage(page, wrapper);
    }
}