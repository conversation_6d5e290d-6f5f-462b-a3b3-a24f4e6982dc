package com.jygjexp.jynx.zxoms.send.mapper;

import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.zxoms.entity.NbTransferCenterEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface NbTransferCenterMapper extends JynxBaseMapper<NbTransferCenterEntity> {

    NbTransferCenterEntity findFirst(Integer scId, String code, Integer tcId);

    NbTransferCenterEntity findOneByTcCodeAndTcId(String transferCenterCode, Integer tcId);

    NbTransferCenterEntity findTc(Integer scId, String beforePostalCode);

}