package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbPriceRuleEntity;
import com.jygjexp.jynx.zxoms.vo.NbPriceRulePageVo;

public interface NbPriceRuleService extends IService<NbPriceRuleEntity> {

    /**
     * 报价表分页查询
     * @param page
     * @param nbPriceRule
     * @return
     */
    Page<NbPriceRulePageVo> search(Page page, NbPriceRuleEntity nbPriceRule);

}