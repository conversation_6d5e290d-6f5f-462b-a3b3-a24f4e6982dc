package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.dto.DriverScanedDto;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.entity.NbDriverScanedEntity;
import com.jygjexp.jynx.zxoms.nbapp.dto.APPDriverOrderBatchCountDto;

import java.util.List;

public interface NbDriverScanedService extends IService<NbDriverScanedEntity> {
    R auditPass(Integer batchId);   //审核通过

    R auditRefuse(Integer batchId);// 审核拒绝

    R processQueryAfter(List<DriverScanedDto> records1, NbDriverScanedEntity nbDriverScaned);

    R getList(NbDriverEntity loginDriver, Double[] latlng);

}