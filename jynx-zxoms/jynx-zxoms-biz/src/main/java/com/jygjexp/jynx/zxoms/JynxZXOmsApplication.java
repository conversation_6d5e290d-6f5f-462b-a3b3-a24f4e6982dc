package com.jygjexp.jynx.zxoms;

import com.jygjexp.jynx.common.feign.annotation.EnableJynxFeignClients;
import com.jygjexp.jynx.common.job.annotation.EnableJynxXxlJob;
import com.jygjexp.jynx.common.security.annotation.EnableJynxResourceServer;
import com.jygjexp.jynx.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR> archetype
 * <p>
 * 项目启动类
 */
@EnableOpenApi("zxoms")
@EnableJynxXxlJob
@EnableJynxFeignClients
@EnableDiscoveryClient
@EnableJynxResourceServer
@SpringBootApplication
public class JynxZXOmsApplication {
    public static void main(String[] args) {
        SpringApplication.run(JynxZXOmsApplication.class, args);
    }
}
