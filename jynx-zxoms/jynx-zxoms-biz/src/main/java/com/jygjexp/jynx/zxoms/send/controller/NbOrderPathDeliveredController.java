package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderPathEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderPathDeliveredService;
import com.jygjexp.jynx.zxoms.vo.NbOrderPathDeliveredPageVo;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderPathExcelVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: chenchang
 * @Description: 订单轨迹-签收记录
 * @Date: 2024/10/18 19:54
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrderPathDelivered" )
@Tag(description = "nbOrderPathDelivered" , name = "签收记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderPathDeliveredController {

    private final NbOrderPathDeliveredService nbOrderPathDeliveredService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 订单轨迹-签收记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('orderPathDelivered_view')" )
    public R getBasicCitiesPage(@ParameterObject Page page, @ParameterObject NbOrderPathDeliveredPageVo vo) {
        vo.setLocalTime(LocalDateTime.now());
        return R.ok(nbOrderPathDeliveredService.search(page, vo));
    }


    /**
     * 通过id查询订单轨迹-签收记录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('orderPathDelivered_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(nbOrderPathDeliveredService.getById(id));
    }

    /**
     * 新增订单轨迹-签收记录
     * @param nbOrderPathEntity 订单轨迹-签收记录
     * @return R
     */
    @Operation(summary = "新增订单轨迹-签收记录" , description = "新增订单轨迹-签收记录" )
    @SysLog("新增订单轨迹-签收记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('orderPathDelivered_add')" )
    public R save(@RequestBody NbOrderPathEntity nbOrderPathEntity) {
        return R.ok(nbOrderPathDeliveredService.save(nbOrderPathEntity));
    }

    /**
     * 修改订单轨迹-签收记录
     * @param nbOrderPathEntity 订单轨迹-签收记录
     * @return R
     */
    @Operation(summary = "修改订单轨迹-签收记录" , description = "修改订单轨迹-签收记录" )
    @SysLog("修改订单轨迹-签收记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('orderPathDelivered_edit')" )
    public R updateById(@RequestBody NbOrderPathEntity nbOrderPathEntity) {
        return R.ok(nbOrderPathDeliveredService.updateById(nbOrderPathEntity));
    }

    /**
     * 通过id删除订单轨迹-签收记录
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除订单轨迹-签收记录" , description = "通过id删除订单轨迹-签收记录" )
    @SysLog("通过id删除订单轨迹-签收记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('orderPathDelivered_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderPathDeliveredService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param vo 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @Operation(summary = "导出签收记录" , description = "导出签收记录" )
    @SysLog("导出签收记录" )
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('orderPathDelivered_export')" )
    public List<NbOrderPathExcelVo> export(NbOrderPathDeliveredPageVo vo, Integer[] ids) {
        return nbOrderPathDeliveredService.getExcel(vo, ids);
    }
}
