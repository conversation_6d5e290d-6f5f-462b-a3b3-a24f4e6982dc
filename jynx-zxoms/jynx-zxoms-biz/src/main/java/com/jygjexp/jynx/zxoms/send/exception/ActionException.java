package com.jygjexp.jynx.zxoms.send.exception;

import cn.hutool.core.util.StrUtil;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/10 19:13
 */
public class ActionException extends RuntimeException{
    private static final long serialVersionUID = 1998063243843477017L;
    private int errorCode;
    private String errorMessage;

    public ActionException(int errorCode, String viewOrJson) {
        if (StrUtil.isBlank(viewOrJson)) {
            throw new IllegalArgumentException("The parameter viewOrJson can not be blank.");
        }

        this.errorCode = errorCode;
    }

}
