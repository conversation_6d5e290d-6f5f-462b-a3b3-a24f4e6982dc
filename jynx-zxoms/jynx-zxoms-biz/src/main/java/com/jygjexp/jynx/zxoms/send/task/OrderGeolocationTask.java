package com.jygjexp.jynx.zxoms.send.task;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbSortingCenterEntity;
import com.jygjexp.jynx.zxoms.entity.NbTransferCenterEntity;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.Route4MeUtil;

import com.route4me.sdk.services.routing.Geocodings;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;


/**
 * @Author: chenchang
 * @Description: 订单经纬度解析
 * @Date: 2024/10/9 22:29
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OrderGeolocationTask {
    private final NbOrderService nbOrderService;
    private final NbSortingCenterService nbSortingCenterService;
    private final CitiesService citiesService;
    private final CountriesService countriesService;
    private final StatesService statesService;
    private final NbTransferCenterService nbTransferCenterService;
    private final Route4MeUtil route4MeUtil;

    @SneakyThrows
    @XxlJob("orderGeolocationHandler")
    public void orderGeolocationHandler() {
//        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务：【订单经纬度解析】于:{}，输入参数{}", LocalDateTime.now(), "运行中");

        // 补全经纬度
        // "select * from nb_order where dest_lat = 0 and geo_times = 0");
        LambdaQueryWrapper<NbOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NbOrderEntity::getGeoTimes, 0).eq(NbOrderEntity::getGeoTimes, 0);
        List<NbOrderEntity> orders = nbOrderService.list(wrapper);
        for (NbOrderEntity order : orders) {
            String address = order.getDestPostalCode() + " " + order.getDestAddress1() + " " + order.getDestCity() + " " + order.getDestProvince() + " " + order.getDestCountry();
            Geocodings geo = route4MeUtil.forwardGeocodeAddress(address);

            XxlJobHelper.log("地址解析经纬度：orderId=" + order.getOrderId() + ",address=" + address + ",result=" + (geo == null ? "null" : (geo.getCoordinates().getLatitude() + "," + geo.getCoordinates().getLongitude())));
            if (geo == null) {
                // "update nb_order set geo_times = geo_times + 1 where order_id = ?", order.getOrderId());
                LambdaUpdateWrapper<NbOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(NbOrderEntity::getOrderId, order.getOrderId());
                updateWrapper.set(NbOrderEntity::getGeoTimes, order.getGeoTimes() + 1);
                nbOrderService.update(updateWrapper);
            } else {
//                Db.use("nbd").update("update nb_order set dest_lat = ?, dest_lng = ?, geo_times = geo_times + 1 where order_id = ?",
//                        geo.getCoordinates().getLatitude(), geo.getCoordinates().getLongitude(), order.getOrderId());
                LambdaUpdateWrapper<NbOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(NbOrderEntity::getOrderId, order.getOrderId());
                updateWrapper.set(NbOrderEntity::getDestLat, geo.getCoordinates().getLatitude());
                updateWrapper.set(NbOrderEntity::getDestLng, geo.getCoordinates().getLongitude());
                updateWrapper.set(NbOrderEntity::getGeoTimes, order.getGeoTimes() + 1);
                nbOrderService.update(updateWrapper);
            }
        }

        // "select * from nb_sorting_center where lat = 0");
        LambdaQueryWrapper<NbSortingCenterEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NbSortingCenterEntity::getLat, 0);
        List<NbSortingCenterEntity> scList = nbSortingCenterService.list(queryWrapper);
        for (NbSortingCenterEntity sc : scList) {
            int cityId = sc.getCityId();
            int prodinceId = sc.getProvinceId();
            int countryId = sc.getCountryId();

            String cityName = citiesService.getById(cityId).getName();
            String provinceName = statesService.getById(prodinceId).getName();
            String countryName = countriesService.getById(countryId).getName();

            String fullAddress = sc.getPostalCode() + " " + sc.getAddress() + " " + cityName + " " + provinceName + " " + countryName;
            Geocodings geo = route4MeUtil.forwardGeocodeAddress(fullAddress);

            if (geo == null) {
                sc.setLat(-1d);
                sc.setLng(-1d);
                nbSortingCenterService.updateById(sc);
            } else {
                XxlJobHelper.log("地址解析经纬度：scId=" + sc.getScId() + ",address=" + sc.getAddress() + ",result=" + (geo == null ? "null" : (geo.getCoordinates().getLatitude() + "," + geo.getCoordinates().getLongitude())));
                sc.setLat(geo.getCoordinates().getLatitude());
                sc.setLng(geo.getCoordinates().getLongitude());
                nbSortingCenterService.updateById(sc);
            }
        }

        // "select * from nb_transfer_center where lat = 0 and is_hidden = false and is_valid = true";
        LambdaQueryWrapper<NbTransferCenterEntity> tcWrapper = new LambdaQueryWrapper<>();
        tcWrapper.eq(NbTransferCenterEntity::getLat, 0).eq(NbTransferCenterEntity::getIsHidden, false).eq(NbTransferCenterEntity::getIsValid, true);
        List<NbTransferCenterEntity> tcList = nbTransferCenterService.list(tcWrapper);
        for (NbTransferCenterEntity tc : tcList) {
            int cityId = tc.getCityId();
            int prodinceId = tc.getProvinceId();
            int countryId = tc.getCountryId();

            String cityName = citiesService.getById(cityId).getName();
            String provinceName = statesService.getById(prodinceId).getName();
            String countryName = countriesService.getById(countryId).getName();

            String fullAddress = tc.getPostalCode() + " " + tc.getAddress() + " " + cityName + " " + provinceName + " " + countryName;
            Geocodings geo = route4MeUtil.forwardGeocodeAddress(fullAddress);

            if (geo == null) {
                tc.setLat(-1d);
                tc.setLng(-1d);
                nbTransferCenterService.updateById(tc);
            } else {
                XxlJobHelper.log("地址解析经纬度：tcId=" + tc.getTcId() + ",address=" + tc.getAddress() + ",result=" + (geo == null ? "null" : (geo.getCoordinates().getLatitude() + "," + geo.getCoordinates().getLongitude())));

                tc.setLat(geo.getCoordinates().getLatitude());
                tc.setLng(geo.getCoordinates().getLongitude());
                nbTransferCenterService.updateById(tc);
            }
        }

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【订单经纬度解析】执行结束，时间: {}", LocalDateTime.now());
    }
}
