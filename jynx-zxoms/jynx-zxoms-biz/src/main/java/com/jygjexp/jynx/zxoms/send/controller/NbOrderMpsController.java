package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderMpsEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderMpsService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单跟踪
 *
 * <AUTHOR>
 * @date 2024-10-17 21:38:26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrderMps" )
@Tag(description = "nbOrderMps" , name = "订单跟踪管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderMpsController {

    private final  NbOrderMpsService nbOrderMpsService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbOrderMps 订单跟踪
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderMps_view')" )
    public R getNbOrderMpsPage(@ParameterObject Page page, @ParameterObject NbOrderMpsEntity nbOrderMps) {
        LambdaQueryWrapper<NbOrderMpsEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbOrderMpsService.page(page, wrapper));
    }


    /**
     * 通过id查询订单跟踪
     * @param mpsId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{mpsId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderMps_view')" )
    public R getById(@PathVariable("mpsId" ) Integer mpsId) {
        return R.ok(nbOrderMpsService.getById(mpsId));
    }

    /**
     * 新增订单跟踪
     * @param nbOrderMps 订单跟踪
     * @return R
     */
    @Operation(summary = "新增订单跟踪" , description = "新增订单跟踪" )
    @SysLog("新增订单跟踪" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderMps_add')" )
    public R save(@RequestBody NbOrderMpsEntity nbOrderMps) {
        return R.ok(nbOrderMpsService.save(nbOrderMps));
    }

    /**
     * 修改订单跟踪
     * @param nbOrderMps 订单跟踪
     * @return R
     */
    @Operation(summary = "修改订单跟踪" , description = "修改订单跟踪" )
    @SysLog("修改订单跟踪" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderMps_edit')" )
    public R updateById(@RequestBody NbOrderMpsEntity nbOrderMps) {
        return R.ok(nbOrderMpsService.updateById(nbOrderMps));
    }

    /**
     * 通过id删除订单跟踪
     * @param ids mpsId列表
     * @return R
     */
    @Operation(summary = "通过id删除订单跟踪" , description = "通过id删除订单跟踪" )
    @SysLog("通过id删除订单跟踪" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderMps_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderMpsService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbOrderMps 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderMps_export')" )
    public List<NbOrderMpsEntity> export(NbOrderMpsEntity nbOrderMps,Integer[] ids) {
        return nbOrderMpsService.list(Wrappers.lambdaQuery(nbOrderMps).in(ArrayUtil.isNotEmpty(ids), NbOrderMpsEntity::getMpsId, ids));
    }
}