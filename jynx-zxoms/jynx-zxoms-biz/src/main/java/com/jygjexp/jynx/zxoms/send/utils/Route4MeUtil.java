package com.jygjexp.jynx.zxoms.send.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.dto.OrderBatchDto;
import com.jygjexp.jynx.zxoms.entity.*;

import com.jygjexp.jynx.zxoms.send.dto.R4mTobeUpadateAddress;
import com.jygjexp.jynx.zxoms.send.dto.R4mTobeUpdateRouter;
import com.jygjexp.jynx.zxoms.send.mapper.*;
import com.route4me.sdk.exception.APIException;
import com.route4me.sdk.responses.StatusResponse;
import com.route4me.sdk.services.addressbook.AddressBookManager;
import com.route4me.sdk.services.addressbook.Contact;
import com.route4me.sdk.services.geocoding.GeocodingManager;
import com.route4me.sdk.services.notes.Note;
import com.route4me.sdk.services.notes.NotesManager;
import com.route4me.sdk.services.orders.OrderStatus;
import com.route4me.sdk.services.orders.OrdersManager;
import com.route4me.sdk.services.routing.*;
import com.route4me.sdk.services.routing.Constants.AddressStopType;
import com.route4me.sdk.services.routing.Constants.DistanceUnit;
import com.route4me.sdk.services.routing.Constants.OptimizationState;
import com.route4me.sdk.services.routing.Constants.TravelMode;
import com.route4me.sdk.services.routing.advancedconstraints.AdvancedConstraints;
import com.route4me.sdk.services.users.User;
import com.route4me.sdk.services.users.UsersManager;
import com.route4me.sdk.services.v5.V5Array;
import com.route4me.sdk.services.v5.V5Manager;
import com.route4me.sdk.services.v5.V5Object;
import com.route4me.sdk.services.v5.VehicleCapacity;
import com.route4me.sdk.services.vehicles.Vehicles;
import com.route4me.sdk.services.vehicles.VehiclesManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class Route4MeUtil {
	private final NbOrderBatchMapper nbOrderBatchMapper;
	private final NbConfigMapper nbConfigMapper;
	private final NbR4mOptimizationLogMapper r4mOptimizationLogMapper;
	private final NbVehicleTypeMapper nbVehicleTypeMapper;
	private final NbTransferCenterMapper nbTransferCenterMapper;
	private final NbSortingCenterMapper nbSortingCenterMapper;
	private final NbOrderMapper nbOrderMapper;
	private final NbDriverMapper nbDriverMapper;

	private static final String key = "099992A5644B5669E2FC89428AE84801";
//private static final String key ="CE44B31F803D8F45BC6FA777D6D409E9";
//	private static final String key = xx.getConfig("r4mApiKey");
	
	public Vehicles createVehicle(Vehicles vehicle) {
		log.info("车辆待创建记录：" + vehicle);
		VehiclesManager manager = new VehiclesManager(key);
		try {
			vehicle = manager.newVehicle(vehicle);
			
			log.info("车辆创建记录：" + vehicle);
			
			return vehicle;

		} catch (APIException e) {
			e.printStackTrace();
		}
		return vehicle;
	}
	
	public Vehicles createVehicle(NbDriverEntity driver) {
		VehiclesManager manager = new VehiclesManager(key);
		
		Vehicles vehicle = new Vehicles();
		if (driver.getFirstName() == null) {
			vehicle.setVehicleAlias(driver.getMobile() != null ? driver.getMobile() : "Unknown");
		} else {
			vehicle.setVehicleAlias(driver.getFirstName() + " " +  (driver.getLastName() != null ? driver.getLastName() : "")); // Internal name of the vehicle
		}
		//vehicle.setMemberId(driver.getDriverId().toString());
		vehicle.setMemberId(driver.getRoute4meMemberId().toString());
		vehicle.setTimestampAdded(String.valueOf(driver.getRegTime().getTime()));
		
		// "sedan", "suv", "pickup_truck", "van", "18wheeler", "cabin", "hatchback", "motorcyle", "waste_disposal", "tree_cutting", "bigrig", "cement_mixer", "livestock_carrier", "dairy","tractor_trailer"
		Integer vehicleTypeId = driver.getVehicleTypeId();
		if (vehicleTypeId != null) {
			NbVehicleTypeEntity vt = nbVehicleTypeMapper.selectById(vehicleTypeId);
			if (vt != null && vt.getRoute4meKey() != null && vt.getRoute4meKey().length() > 0) {
				vehicle.setVehicleTypeId(vt.getRoute4meKey());
			}
		}
		
		if (driver.getPlateNumber() != null && driver.getPlateNumber().length() > 0) {
			vehicle.setVehicleLicensePlate(driver.getPlateNumber());
		}
		
		try {
/*
			vehicle.setVehicleId("0B83A07705BE4A357E8425AD82C93F99");
			vehicle.setVehicleVin("WBADE6322VBW51984");
			vehicle.setVehicleRegStateId("4");
			vehicle.setVehicleRegCountryId("223");
			vehicle.setVehicleMake("Ford");
			vehicle.setLicenseStartDate("2022-07-29");
			vehicle.setLicenseEndDate("2025-08-19");
			vehicle.setOperational(true);
			vehicle.setFuelType("unleaded 87");
			vehicle.setFuelConsumptionCityUnit("km/l");
			vehicle.setFuelConsumptionHighwayUnit("km/l");
*/


			Vehicles vehicle2 = new Vehicles();
			vehicle2 = manager.newVehicle(vehicle);
			log.info("车辆创建记录：" + vehicle2);
			return vehicle2;
		} catch (APIException e) {
			e.printStackTrace();
		}
		return vehicle;
	}
	
	public void updateVehicle(NbDriverEntity driver) {
		VehiclesManager manager = new VehiclesManager(key);
		Vehicles vehicle = new Vehicles();
		vehicle.setVehicleId(driver.getRoute4meVehicleId());
		if (driver.getFirstName() == null) {
			vehicle.setVehicleAlias(driver.getMobile());
		} else {
			vehicle.setVehicleAlias(driver.getFirstName() + " " + driver.getLastName()); // Internal name of the vehicle
		}
		
		// "sedan", "suv", "pickup_truck", "van", "18wheeler", "cabin", "hatchback", "motorcyle", "waste_disposal", "tree_cutting", "bigrig", "cement_mixer", "livestock_carrier", "dairy","tractor_trailer"
		Integer vehicleTypeId = driver.getVehicleTypeId();
		if (vehicleTypeId != null) {
			NbVehicleTypeEntity vt = nbVehicleTypeMapper.selectById(vehicleTypeId);
			if (vt != null && vt.getRoute4meKey() != null && vt.getRoute4meKey().length() > 0) {
				vehicle.setVehicleTypeId(vt.getRoute4meKey());
			}
		}
		if (driver.getPlateNumber() != null && driver.getPlateNumber().length() > 0) {
			vehicle.setVehicleLicensePlate(driver.getPlateNumber());
		}
		try {
			vehicle = manager.updateVehicle(vehicle);
			log.info("车辆更新记录：" + vehicle);
		} catch (APIException e) {
			e.printStackTrace();
		}
	}


	public R createUser(NbDriverEntity driver) {
        User user = new User();
        user.setMemberFirstName(driver.getFirstName());
        user.setMemberLastName(driver.getLastName());
        user.setMemberEmail(driver.getEmail());
//        user.setMemberPassword(driver.getPassword());
		// 拼接FirstName, LastName和Email的某些部分来设置密码 比如FirstName = "John"  LastName = "Doe"  Email = "<EMAIL>"  生成的密码将是：JohnDoejohn.doe
		//String password = driver.getFirstName() + driver.getLastName() + driver.getEmail().split("@")[0];
		String password="123456";
		if (StrUtil.isBlank(driver.getPassword())) {
			driver.setPassword(password);
			nbDriverMapper.updateById(driver);    //储存R4m登录密码
		}
		user.setMemberPassword(driver.getPassword()); // 设置密码
//        user.setDateOfBirth("1978");
        if (driver.getBirthday() != null) {
        	user.setDateOfBirth(DateFormatUtils.format(driver.getBirthday(), "yyyy-MM-dd"));
        }
        
        if (driver.getTcId() != null && driver.getTcId() > 0) {
			NbTransferCenterEntity tc = nbTransferCenterMapper.selectById(driver.getTcId());
			if (tc != null) {
        		Integer scId = tc.getScId();
        		if (scId != null && scId > 0) {
					NbSortingCenterEntity sc = nbSortingCenterMapper.selectById(scId);
					if (sc != null) {
        				Integer memberId = sc.getR4mMemberId();
        				if (memberId != null && memberId > 0) {
        					user.setOwnerMemberID(memberId.toString());			
        				}
        			}
        		}
        	}
        }
        
        user.setMemberPhone(driver.getMobile());
        user.setMemberZipcode(driver.getPostalCode());
        user.setMemberType(Constants.MemberType.SUB_ACCOUNT_DRIVER.toString());
        user.setHideRoutedAddresses(Constants.MemberPropertyValue.FALSE.toString());
        user.setHideVisitedAddresses(Constants.MemberPropertyValue.FALSE.toString());
        user.setReadOnlyUser(Constants.MemberPropertyValue.TRUE.toString()); //如果为true，则用户只是只读用户
        UsersManager manager = new UsersManager(key);
        try {
            user = manager.createUser(user);
			if (null != user){
				log.info("用户创建记录：" + user);
				return R.ok(user);
			}
			return R.failed("errmsg","司机R4M用户创建失败");
        } catch (APIException ex) {
            ex.printStackTrace();
            log.error("用户创建失败：" + user.getMemberEmail() + "->" + ex.getMessage());
			return R.failed("errmsg",ex.getMessage());
        }
//        User(memberId=2630582, memberFirstName=中, memberLastName=文, memberEmail=<EMAIL>, memberPassword=null, accountTypeId=null, memberType=SUB_ACCOUNT_DRIVER, phoneNumber=null, readonlyUser=null, showSuperuserAddresses=null, apiKey=246E26FCE451AE849525BB3AEE3E2B35, hashedMemberId=null, ownerMemberID=2623213, preferredUnits=null, preferredLanguage=, hideRoutedAddresses=FALSE, hideVisitedAddresses=FALSE, hideNonFutureRoutes=FALSE, showAllDrivers=FALSE, showAllVehicles=FALSE, readOnlyUser=null, memberPhone=123, memberZipcode=d98D34, timeZone=America/New_York, dateOfBirth=null, userRegStateID=null, userRegCountryID=null, memberPicture=null, level=0, customData={})
	}
	
	public void getUsers() {
		UsersManager manager = new UsersManager(key);
	    try {
	        List<User> users = manager.getUsers(50, 0);
	        for (User user : users) {
	            System.out.println(user);
	        }
	    } catch (APIException e) {
	        e.printStackTrace();
	    }
	}

	public static void getUserByMemberId(Long memberId) {
		UsersManager manager = new UsersManager(key); // 初始化 UsersManager，传入 API Key
		try {
			// 调用方法获取特定用户
			User user = manager.getUser(String.valueOf(memberId));
			if (user != null) {
				System.out.println("User Details:");
				System.out.println("ID: " + user.getMemberId());
				System.out.println("Name: " + user.getMemberFirstName());
				System.out.println("Email: " + user.getMemberEmail());
				System.out.println("Role: " + user.getOwnerMemberID());
			} else {
				System.out.println("No user found with member_id: " + memberId);
			}
		} catch (APIException e) {
			System.out.println("Failed to fetch user details:");
			e.printStackTrace();
		}
	}

	public static void main(String[] args) {
		//deleteOrder(999999l);
		//getUsers();
//		getUserByMemberId(3040247L);
	}
	
	
	public User updateUser(NbDriverEntity driver) {
		UsersManager manager = new UsersManager(key);
	    try {
	    	 User user = new User();
	    	 user.setMemberId(driver.getRoute4meMemberId().toString());
	         user.setMemberFirstName(driver.getFirstName());
	         user.setMemberLastName(driver.getLastName());
	         user.setMemberEmail(driver.getEmail());
	         user.setMemberPhone(driver.getMobile());
	         user.setMemberZipcode(driver.getPostalCode());
//	         user.setReadonlyUser(Constants.MemberPropertyValue.TRUE.toString());
	         user.setReadOnlyUser(Constants.MemberPropertyValue.TRUE.toString());
	    	
	         user = manager.updateUser(user);
	         
	         log.info("用户更新：" + user);
	    } catch (APIException e) {
	        e.printStackTrace();
	    }
		return null;
	}
	
	public R assignDriver(String routeId, String memberId) {
        RoutingManager routeManager = new RoutingManager(key);
        try {
            Route newRoute = routeManager.assignDriver(routeId, memberId);
            System.out.println(newRoute);
            return R.ok();
        } catch (APIException e) {
            e.printStackTrace();
            return R.failed("msg", e.getMessage());
        }
	}
	
	private void addCustomFields(Address address, NbOrderEntity order) {
		Map<String, Object> customFields = address.getCustom_fields();
		if (customFields == null) {
			customFields = Maps.newHashMap();
		}
		
		customFields.put("Barcode", order.getPkgNo());
		customFields.put("Email", order.getDestEmail());
		customFields.put("Sub Reference", order.getBatchNo());
		customFields.put("Phone Number", order.getDestTel());
		customFields.put("name", order.getDestName());
		customFields.put("__orderId", order.getOrderId());
		customFields.put("Shipping Remarks", order.getOrderRemark());
		customFields.put("Driver Notes", order.getDriverRemark());
		
		address.setCustom_fields(customFields);
	}
	
	public DataObject createAnOptimization(int batchId, NbTransferCenterEntity startTc, NbSortingCenterEntity startSc, List<NbOrderEntity> orders, List<NbDriverEntity> drivers, Parameters innerPara, boolean isAuto, int adminId) {

		List<Address> addressList = Lists.newArrayList();
		
		// 添加转运中心
		if (startTc != null) {
			addressList.add(new Address(startTc.getAddress(), true, startTc.getLat(), startTc.getLng(), 0));
		}
		// 添加分拣中心
		if (startSc != null) {
			addressList.add(new Address(startSc.getAddress(), true, startSc.getLat(), startSc.getLng(), 0));
		}
		// findIntegerByKey(Config.r4m_stop_service_time);
		Long count = nbConfigMapper.selectCount(new LambdaQueryWrapper<NbConfigEntity>().eq(NbConfigEntity::getCKey, ConfigUtil.r4m_stop_service_time));
		Integer stopServiceTime = Math.toIntExact(count);
		for (NbOrderEntity order : orders) {
			String destAddress = 
					order.getDestPostalCode() + " " +
				order.getDestAddress1() + Optional.ofNullable(order.getDestAddress2()).orElse("") + Optional.ofNullable(order.getDestAddress3()).orElse("") + " " + 
				order.getDestCity() + " " + order.getDestProvince();

			Address address = new Address(destAddress, false, order.getDestLat(), order.getDestLng(), 300);
			address.setWeight(order.getPkgWeight().setScale(2, RoundingMode.HALF_UP).doubleValue());
			address.setAlias(order.getOrderId().toString());
			address.setFirstName(order.getDestName());
			address.setPhone(order.getDestTel());
			address.setEmail(order.getDestEmail());
			address.setTime(stopServiceTime.longValue());
			
			addCustomFields(address, order);
			
			addressList.add(address);
		}

		NbOrderBatchEntity ob = nbOrderBatchMapper.selectById(batchId);
		RoutingManager manager = new RoutingManager(key);
		OptimizationParameters parameters = new OptimizationParameters();
		parameters.setAddresses(addressList);

		Long count1 = nbConfigMapper.selectCount(new LambdaQueryWrapper<NbConfigEntity>().eq(NbConfigEntity::getCKey, ConfigUtil.r4m_route_time));
		Long count2 = nbConfigMapper.selectCount(new LambdaQueryWrapper<NbConfigEntity>().eq(NbConfigEntity::getCKey, ConfigUtil.r4m_start_date_plus_day));
		Integer routeTime = Math.toIntExact(count1);
		Integer startDatePlusDay = Math.toIntExact(count2);

		//log.info("规划参数：optimize=" + innerPara.getOptimize() + ",balance=" +innerPara.getBalance() + ",minTourSize=" + innerPara.getMinTourSize() +
		//",isDynamicStartTime=" + innerPara.getIsDynamicStartTime() + ",routeMaxDuration=" + innerPara.getRouteMaxDuration());
		log.info("参与优化规划参数：optimize(优化目标)=" + innerPara.getOptimize() + ",balance(时间和距离的优先级)=" +innerPara.getBalance() + ",algorithmType(优化算法)=" + innerPara.getAlgorithmType() +
				",isRt(是否实时优化)=" + innerPara.getRt() + ",routeMaxDuration(路线最大持续时间)=" + innerPara.getRouteMaxDuration()+ ",isDynamicStartTime(起始时间)=" + innerPara.getIsDynamicStartTime());
		
		ZonedDateTime utcDateTime = ZonedDateTime.now(ZoneId.of("UTC")).plusDays(startDatePlusDay);
		Long startDateConvertSecond = utcDateTime.toEpochSecond();
		innerPara.setRouteDate(startDateConvertSecond);
		innerPara.setRouteTime(routeTime); // 2023-07-02 Charles：派送开始时间设置为每日早上9点吧
		innerPara.setTravelMode(TravelMode.DRIVING.toString()); // ["Driving", "Walking", "Bicycling"],
		innerPara.setDistanceUnit(DistanceUnit.MI.toString());

		List<Integer> route4meMembersID = Lists.newArrayList();
		for (NbDriverEntity driver : drivers) {
			if (driver.getRoute4meMemberId() != null && driver.getRoute4meMemberId() > 0) {
				route4meMembersID.add(driver.getRoute4meMemberId());
			}
		}
		
		if (route4meMembersID != null && route4meMembersID.size() > 0) {
			List<AdvancedConstraints> advancedConstraints = Lists.newArrayList();
			AdvancedConstraints ac1 = new AdvancedConstraints();

			ac1.setRoute4meMembersID(route4meMembersID);
			advancedConstraints.add(ac1);
			innerPara.setAdvancedConstraints(advancedConstraints);
		}
		parameters.setParameters(innerPara);

		//记录路线生成异常信息
		NbR4mOptimizationLogEntity r4mlog = new NbR4mOptimizationLogEntity();
		try {
			JSONArray orderIdJa = orders.stream().map(o -> o.getOrderId()).collect(Collectors.toCollection(JSONArray::new));
			JSONArray driverIds = drivers.stream().map(d -> d.getDriverId()).collect(Collectors.toCollection(JSONArray::new));

			r4mlog.setOrderBatchId(ob.getBatchId());
			if (startTc != null) {
				r4mlog.setTcId(startTc.getTcId());
			}
			if (startSc != null) {
				r4mlog.setScId(startSc.getScId());
			}
			r4mlog.setOrderIds(orderIdJa.toJSONString());
			r4mlog.setDriverIds(driverIds.toJSONString());
			r4mlog.setAlgorithmType(innerPara.getAlgorithmType());
			r4mlog.setOptimize(innerPara.getOptimize());
			if (innerPara.getMinTourSize() != null) {
				r4mlog.setMinTourSize(innerPara.getMinTourSize().toString());
			}
			if (innerPara.getRt() != null) {
				r4mlog.setIsRt(innerPara.getRt().toString());
			}
			if (innerPara.getBalance() != null) {
				r4mlog.setBalance(innerPara.getBalance().toString());
			}
			if (innerPara.getMaxTourSize() != null) {
				r4mlog.setMaxTourSize(innerPara.getMaxTourSize().intValue());
			}
			r4mlog.setRouteMaxDuration(innerPara.getRouteMaxDuration().toString());
			r4mlog.setOptimizationTime(new Date());
			r4mlog.setIsAuto(isAuto);
			r4mlog.setAdminId(adminId);

			//导入优化参数，生成路线
			DataObject dataObject = manager.runOptimization(parameters);

			System.out.println("Optimization Problem ID:" + dataObject.getOptimizationProblemId());
            System.out.println("State:" + dataObject.getState().intValue()+"--转换后："+OptimizationState.get(dataObject.getState().intValue()));
            if (dataObject.getAddresses() != null) {
                for (Address address : dataObject.getAddresses()) {
                    System.out.println(address);
                }
            }

			r4mlog.setR4mState(dataObject.getState().intValue());
            
            if (dataObject.getState().intValue() == 8) {
            	// 规划失败，一般是路径问题
            	String errrmsg = "规划失败:" + dataObject.getState().intValue() + ",OptimizationProblemId=" + dataObject.getOptimizationProblemId();
    			ob.setRoutedErrmsg(errrmsg);
    			ob.setRoutedTime(new Date());
				ob.setSyncR4mOrderStatus(OrderBatchDto.R4M_SYNC_ORDER_STATUS_4_FAILURE);
				nbOrderBatchMapper.updateById(ob);

				r4mlog.setRoutedErrmsg(errrmsg);
    			r4mOptimizationLogMapper.insert(r4mlog);
				throw new RuntimeException("OptimizationProblemId=" + dataObject.getOptimizationProblemId());
            }

			r4mlog.setOptimiationProblemId(dataObject.getOptimizationProblemId());
			r4mlog.setViewUrl(dataObject.getLinks().getView());
			r4mOptimizationLogMapper.insert(r4mlog);
            
            return dataObject;
		} catch (APIException e) {
			//e.printStackTrace();
			String errrmsg = e.getMessage();
			ob.setRoutedErrmsg(errrmsg);
			ob.setRoutedTime(new Date());
			ob.setSyncR4mOrderStatus(OrderBatchDto.R4M_SYNC_ORDER_STATUS_4_FAILURE);
			nbOrderBatchMapper.updateById(ob);

			r4mlog.setRoutedErrmsg(errrmsg);
			r4mOptimizationLogMapper.insert(r4mlog);

			log.error(errrmsg, e);
			throw new RuntimeException(errrmsg, e);  // 抛出异常到上层
		} catch (Exception e) {
			String errorMessage = "(Path planning - error while solving optimization)路径规划——求解优化时出错: " + e.getMessage();
			log.error(errorMessage, e);
			throw new RuntimeException(errorMessage, e);  // 抛出异常到上层
		}
	}
	
	public void createVehicle() {
		
		VehiclesManager manager = new VehiclesManager(key);
		Vehicles vehicle = new Vehicles();

		try {
			manager.newVehicle(vehicle);
		} catch (APIException e) {
			e.printStackTrace();
		}
	}
	
	public Geocodings forwardGeocodeAddress(String address) {
		GeocodingManager manager = new GeocodingManager(key);
		
		try {
			Geocodings[] geos = manager.geocode(address);
			System.out.println(geos);
			System.out.println(geos[0]);
			
			if (geos != null && geos.length > 0) {
				return geos[0];
			}
		} catch (APIException e) {
			e.printStackTrace();
		}
		
		return null;
	}
	
	// EFB3C95A4888E88AB8C9F0FF308EECC2
	public Optimization getOptimization(String problemId) {
		RoutingManager manager = new RoutingManager(key);
		OptimizationParameters parameters = new OptimizationParameters();
		parameters.setProblemId(problemId);
		try {
			Optimization optimization = manager.getOptimization(parameters);
			System.out.println(optimization);
			return optimization;
		} catch (APIException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public DataObject getOptimization2(String problemId) {
		RoutingManager manager = new RoutingManager(key);
		OptimizationParameters parameters = new OptimizationParameters();
		parameters.setProblemId(problemId);
		try {
			DataObject optimization = manager.getOptimization2(parameters);
			System.out.println(optimization);
			return optimization;
		} catch (APIException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	// routeId=205C3C22617258AC48C7CBA2F3C134F6
	public Route getRoute(String routeId) {
		RoutingManager manager = new RoutingManager(key);
		RoutesRequest request = new RoutesRequest();
		request.setId(routeId);
		try {
			Route route = manager.getRoute(request);
			System.out.println(route);
			return route;
		} catch (APIException e) {
			e.printStackTrace();
		}

		return null;
	}

	public String removeUser(NbDriverEntity driver) {
		UsersManager manager = new UsersManager(key);
		try {
			String delResult = manager.removeUser(driver.getRoute4meMemberId().toString());
			log.info("route4me用户已被删除: " + delResult);
			return delResult;
		} catch (APIException e) {
			e.printStackTrace();
		}
		return null;
				
	}
	
	public void getRoutes() {
		RoutingManager routeManager = new RoutingManager(key);
		try {
			List<Route> routeList = routeManager.getRoutes(new RoutesRequest().setLimit(10));
			for (Route route : routeList) {
				System.out.println(route);
				
				System.out.println(route.getAddresses());
			}
		} catch (APIException e) {
			e.printStackTrace();
		}
	}
	
	public List<Note> getNotes(String routeId, String routeDestinationId) {
		NotesManager notesManager = new NotesManager(key);
		try {
			List<Note> notes = notesManager.getAddressNotes(routeId, routeDestinationId);
			for (Note note : notes) {
				log.info("note{}", note);
			}
		} catch (APIException e) {
			e.printStackTrace();
		}
		return Lists.newArrayList();
	}
	
	/**
	 * 更新地址属性，自动重新编写整个数据
	 * @param routeId
	 */
	public void updateAddressAttribute(String routeId, Number routeDestinationId, NbOrderEntity order) {
		RoutingManager manager = new RoutingManager(key);
		
		try {
			Address address = manager.getAddress(routeId, routeDestinationId);
			
			addCustomFields(address, order);
			
			address = manager.updateAddressAttribute(routeId, routeDestinationId, address);
			
			log.info("地址更新：" + address);
		} catch (APIException e1) {
			e1.printStackTrace();
			
			log.info("地址更新失败：" + e1);
		}

	}
	

	public void updateAddressAttribute(String routeId, Number routeDestinationId, Address updateAddr, NbOrderEntity order) {
		RoutingManager manager = new RoutingManager(key);
		
		try {
			addCustomFields(updateAddr, order);
			
			Address address = manager.updateAddressAttribute(routeId, routeDestinationId, updateAddr);
			
			log.info("地址更新：" + address);
		} catch (APIException e1) {
			e1.printStackTrace();
			
			log.info("地址更新失败：" + e1);
		}
	}
	
	/**
	 * 更新地址数据，按传入值修改
	 * @param tobeUpdateAddress
	 */
	public void updateAddressAttribute(List<R4mTobeUpadateAddress> tobeUpdateAddress) {
		RoutingManager manager = new RoutingManager(key);
		for (R4mTobeUpadateAddress address : tobeUpdateAddress) {
			try {
				manager.updateAddressAttribute(address.getRouteId(), address.getRouteDestinationId(), address.getAddress());
				log.info("地址更新：" + "routeId=" + address.getRouteId() + ",address=" + address.getAddress());
			} catch (APIException e) {
				e.printStackTrace();
				
				log.info("地址更新失败：" + e);
			}
		}
			
	}
	
	public void updateRoute(List<R4mTobeUpdateRouter> tobeUpdateRouterList) {
		RoutingManager manager = new RoutingManager(key);
		for (R4mTobeUpdateRouter router : tobeUpdateRouterList) {
			try {
				Route route = manager.updateRoute(router.getRouter());
				
				log.info("Route更新：" + "routeId=" + route.getId() + ",name=" + route.getParameters().getRouteName());
			} catch (APIException e) {
				e.printStackTrace();
				
				log.info("Route更新失败：" + e.getMessage() + "," + router.getRouter());
			}
		}
	}

	public void updateRoute(Route updateRoute) {
		RoutingManager manager = new RoutingManager(key);
		try {
			Route route = manager.updateRoute(updateRoute);
			
			log.info("Route更新：" + "routeId=" + route.getId() + ",name=" + route.getParameters().getRouteName());
		} catch (APIException e) {
			e.printStackTrace();
			
			log.info("Route更新失败：" + e.getMessage() + "," + updateRoute);
		}
		
	}
	
	public Address getAddress(String routeId, Number routeDestinationId) {
		RoutingManager manager = new RoutingManager(key);
		Address address = null;
		try {
			address = manager.getAddress(routeId, routeDestinationId);
		} catch (APIException e) {
			e.printStackTrace();
		}
		return address;
	}
	
	public R removeRoute(String routeId) {
		RoutingManager manager = new RoutingManager(key);
		RouteDeletedResponse response = null;
		try {
			response = manager.deleteRoutes(routeId);
			
			log.info("Route删除结果:" + response);
		} catch (APIException e) {
			e.printStackTrace();
//			Ret ret = Ret.fail();
//			ret.set("errmsg", e.getMessage());
//			return ret;
			return R.failed("errmsg", e.getMessage());
		}
		
//		Ret ret = Ret.ok();
//		ret.set("response", response);
//		return ret;
		return R.ok(response);
	}
	
	public void importAddress(String regionCode, List<NbOrderEntity> orders, List<NbTransferCenterEntity> tcs, NbSortingCenterEntity sc) {
		AddressBookManager addressBookManager = new AddressBookManager(key);
		for (NbOrderEntity order : orders) {
			Contact contact = new Contact();
//			Address address = new Address(destAddress, false, order.getDestLat(), order.getDestLng(), 300);
			
			contact.setCachedLat(order.getDestLat());
			contact.setCachedLng(order.getDestLng());
			contact.setAddressZip(order.getDestPostalCode());
			contact.setAddressCountryId(order.getDestCountry());
			contact.setAddressStateId(order.getDestProvince());
			contact.setAddressCity(order.getDestCity());
			contact.setAddress1(order.getDestAddress1());
			contact.setAddress2(order.getDestAddress2());
			contact.setAddressGroup(regionCode);
			contact.setAddressWeight(String.valueOf(order.getPkgWeight()));
			contact.setAddressAlias(order.getOrderId().toString());
			contact.setFirstName(order.getDestName());
			contact.setServiceTime(300d);
			contact.setAddressPhoneNumber(order.getDestTel());
			contact.setAddressEmail(order.getDestEmail());
			contact.setAddressStopType("DELIVERY");
			
			Map<String, Object> customFields = Maps.newHashMap();
			customFields.put("Barcode", order.getPkgNo());
			customFields.put("Email", order.getDestEmail());
			customFields.put("Sub Reference", order.getBatchNo());
			customFields.put("Phone Number", order.getDestTel());
			customFields.put("name", order.getDestName());
			customFields.put("__orderId", order.getOrderId());
			customFields.put("Shipping Remarks", order.getOrderRemark());
			customFields.put("Driver Notes", order.getDriverRemark());
			customFields.put("type", "order");
			
//			contact.setCustom_fields(customFields);
			contact.setAddressCustomData(customFields);
			
			try {
				contact = addressBookManager.createContact(contact);
				log.info("同步订单地址:regionCode=" + regionCode + ",订单=" + contact);
			} catch (APIException e) {
				e.printStackTrace();
			}
		}
		
		for (NbTransferCenterEntity tc : tcs) {
			Contact contact = new Contact();
			
			contact.setCachedLat(tc.getLat());
			contact.setCachedLng(tc.getLng());
			contact.setAddressZip(tc.getPostalCode());
			contact.setAddress1(tc.getAddress());
			contact.setAddressGroup(regionCode);
			contact.setAddressAlias(tc.getTransferCenterCode());
			contact.setFirstName(tc.getTransferCenterCode());
			contact.setEligibleDepot(true);
			
			Map<String, Object> customFields = Maps.newHashMap();
			customFields.put("type", "tc");
			customFields.put("tcCode", tc.getTransferCenterCode());
			
			try {
				contact = addressBookManager.createContact(contact);
				log.info("同步转运中心地址:regionCode=" + regionCode + ",转运中心=" + contact);
			} catch (APIException e) {
				e.printStackTrace();
			}
		}
		
		if (sc != null) {
			Contact contact = new Contact();
			
			contact.setCachedLat(sc.getLat());
			contact.setCachedLng(sc.getLng());
			contact.setAddressZip(sc.getPostalCode());
			contact.setAddress1(sc.getAddress());
			contact.setAddressGroup(regionCode);
			contact.setAddressAlias(sc.getScCode());
			contact.setFirstName(sc.getScCode());
			contact.setEligibleDepot(true);
			
			Map<String, Object> customFields = Maps.newHashMap();
			customFields.put("type", "sc");
			customFields.put("scCode", sc.getScCode());
			
			try {
				contact = addressBookManager.createContact(contact);
				log.info("同步分拣中心地址:regionCode=" + regionCode + ",分拣中心=" + contact);
			} catch (APIException e) {
				e.printStackTrace();
			}
		}
	}
	
	public void importOrder(String timezone, String scheduledFor, String regionCode, List<NbOrderEntity> orders, List<NbTransferCenterEntity> uniqueTcs, NbSortingCenterEntity sc) {
		OrdersManager orderManager = new OrdersManager(key);
		for (NbOrderEntity order : orders) {
			com.route4me.sdk.services.orders.Order r4mOrder = new com.route4me.sdk.services.orders.Order();
			r4mOrder.setStatusID(OrderStatus.NEW.getValue());
			r4mOrder.setCachedLatitude(order.getDestLat());
			r4mOrder.setCachedLongitude(order.getDestLng());
			r4mOrder.setAddressZip(order.getDestPostalCode());
			r4mOrder.setAddressCountryID(order.getDestCountry());
			r4mOrder.setStateID(order.getDestProvince());
			r4mOrder.setCity(order.getDestCity());
			r4mOrder.setAddress1(order.getDestAddress1());
			r4mOrder.setAddress2(order.getDestAddress2());
			r4mOrder.setGroup(regionCode);
			r4mOrder.setWeight(order.getPkgWeight().setScale(2, RoundingMode.HALF_UP).doubleValue());
			r4mOrder.setAddressAlias(order.getOrderId().toString());
			r4mOrder.setFirstName(order.getDestName());
			r4mOrder.setServiceTime(300);
			r4mOrder.setPhone(order.getDestTel());
			r4mOrder.setEmail(order.getDestEmail());
			r4mOrder.setTrackingNumber(order.getPkgNo());
			r4mOrder.setAddressStopType(AddressStopType.DELIVERY.getValue()); // ["DELIVERY","PICKUP","BREAK","MEETUP","SERVICE","VISIT","DRIVEBY"]
			r4mOrder.setDateScheduled(scheduledFor);
			r4mOrder.setTimezoneString(timezone);

			try {
				r4mOrder = orderManager.addOrder(r4mOrder);
				
				order.setR4mOrderId(r4mOrder.getId());
				nbOrderMapper.updateById(order);
				log.info("同步订单地址:regionCode=" + regionCode + ",订单=" + r4mOrder);
			} catch (APIException e) {
				e.printStackTrace();
				log.info("导入订单失败：" + order.getOrderId() + "," + order.getPkgNo());
			}
		}
	}
	
	public R importOrder(com.route4me.sdk.services.orders.Order r4mOrder) {
		OrdersManager orderManager = new OrdersManager(key);
		try {
			r4mOrder = orderManager.addOrder(r4mOrder);
			log.info("同步订单地址:Group=" + r4mOrder.getGroup() + ",orderId=" + r4mOrder.getAddressAlias() + ",订单=" + r4mOrder);
			return R.ok(r4mOrder);
		} catch (APIException e) {
			// 将异常重新抛出，以便外部方法能够捕获
			log.error("导入订单失败：" + r4mOrder.getAddressAlias() + "," + r4mOrder.getTrackingNumber(), e);
			throw new RuntimeException("导入订单失败：" + r4mOrder.getAddressAlias() + "," + r4mOrder.getTrackingNumber(), e);
			//return R.failed("errmsg", e.getMessage());
		}
	}

	public R deleteOrder(Long[] r4mOrderId) {
		OrdersManager orderManager = new OrdersManager(key);
		for (long orderId : r4mOrderId) {
			try {
				// 调用 OrdersManager 删除订单方法
				StatusResponse statusResponse = orderManager.deleteOrders(orderId);
				if (statusResponse.getStatus()) {
					log.info("订单删除成功：orderId=" + orderId);
					//return R.ok("订单删除成功：" + r4mOrderId);
				} else {
					log.warn("订单删除失败，未找到订单：orderId=" + orderId);
					//return R.failed("订单删除失败，未找到订单：" + r4mOrderId);
				}

			} catch (APIException e) {
				// 将异常重新抛出，以便外部方法能够捕获
				log.error("删除订单失败异常：orderId=" + orderId, e);
				//throw new RuntimeException("删除订单失败：orderId=" + r4mOrderId, e);
			}
		}
		return R.ok();
	}
	
	public List<Route> getRoutes(int pageNo, int pageSize) {
		RoutingManager manager = new RoutingManager(key);
		RoutesRequest request = new RoutesRequest();
		request.setLimit(pageSize);
		request.setOffset((pageNo - 1) * pageSize);
		try {
			List<Route> routes = manager.getRoutes(request);
			return routes;
		} catch (APIException e) {
			e.printStackTrace();
		}
		return null;
	}

	public V5Object<VehicleCapacity> createVehicleCapacity(VehicleCapacity vc) {
		V5Manager<VehicleCapacity> manager = new V5Manager<VehicleCapacity>(key);
		manager.setEndpoint(V5Manager.VEHICLES_CAPACITY_EP);
		try {
			V5Object<VehicleCapacity> wcw = manager.add(vc);
			log.info("司辆创建结果:" + wcw);
			return wcw;
		} catch (APIException e) {
			e.printStackTrace();
		}
		return null;
	}

	public V5Object<VehicleCapacity> updateVehicleCapacity(VehicleCapacity vc) {
		V5Manager<VehicleCapacity> manager = new V5Manager<VehicleCapacity>(key);
		manager.setEndpoint(V5Manager.VEHICLES_CAPACITY_EP);
		try {
			V5Object<VehicleCapacity> wcw = manager.update(vc, vc.getVehicleCapacityProfileId().toString());
			log.info("司辆创建结果:" + wcw);
			return wcw;
		} catch (APIException e) {
			e.printStackTrace();
		}
		return null;
	}

	public void updateVehicle(Vehicles vehicle) {
		log.info("车辆待更新记录：" + vehicle);
		VehiclesManager manager = new VehiclesManager(key);
		
		try {
			vehicle = manager.updateVehicle(vehicle);
			log.info("车辆更新记录：" + vehicle);
		} catch (APIException e) {
			e.printStackTrace();
		}		
	}

	public void assignVehicle(String routeId, String vehicleId) {
		RoutingManager manager = new RoutingManager(key);
		try {
			Route route = manager.assignVehicle(routeId, vehicleId);
			log.info("Route车辆分配完成routeId=" + routeId + ",vehicleId=" + vehicleId);
		} catch (APIException e) {
			e.printStackTrace();
		}
	}

	public V5Array<VehicleCapacity> listVehicleCapacity() {
		V5Manager<VehicleCapacity> manager = new V5Manager<VehicleCapacity>(key);
		manager.setEndpoint(V5Manager.VEHICLES_CAPACITY_EP);
		try {
			V5Array<VehicleCapacity> array = manager.gets(new TypeToken<V5Array<VehicleCapacity>>() {}.getType());
			return array;
		} catch (APIException e) {
			e.printStackTrace();
		}
		return null;
	}

}
