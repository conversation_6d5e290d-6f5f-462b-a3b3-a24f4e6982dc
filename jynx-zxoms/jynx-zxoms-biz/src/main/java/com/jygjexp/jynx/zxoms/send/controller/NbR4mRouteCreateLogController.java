package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbR4mRouteCreateLogEntity;
import com.jygjexp.jynx.zxoms.send.service.NbR4mRouteCreateLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 线路创建记录
 *
 * <AUTHOR>
 * @date 2024-10-30 02:18:19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbR4mRouteCreateLog" )
@Tag(description = "nbR4mRouteCreateLog" , name = "线路创建记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbR4mRouteCreateLogController {

    private final  NbR4mRouteCreateLogService nbR4mRouteCreateLogService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbR4mRouteCreateLog 线路创建记录
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbR4mRouteCreateLog_view')" )
    public R getNbR4mRouteCreateLogPage(@ParameterObject Page page, @ParameterObject NbR4mRouteCreateLogEntity nbR4mRouteCreateLog) {
        LambdaQueryWrapper<NbR4mRouteCreateLogEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbR4mRouteCreateLogService.page(page, wrapper));
    }


    /**
     * 通过id查询线路创建记录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbR4mRouteCreateLog_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(nbR4mRouteCreateLogService.getById(id));
    }

    /**
     * 新增线路创建记录
     * @param nbR4mRouteCreateLog 线路创建记录
     * @return R
     */
    @Operation(summary = "新增线路创建记录" , description = "新增线路创建记录" )
    @SysLog("新增线路创建记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbR4mRouteCreateLog_add')" )
    public R save(@RequestBody NbR4mRouteCreateLogEntity nbR4mRouteCreateLog) {
        return R.ok(nbR4mRouteCreateLogService.save(nbR4mRouteCreateLog));
    }

    /**
     * 修改线路创建记录
     * @param nbR4mRouteCreateLog 线路创建记录
     * @return R
     */
    @Operation(summary = "修改线路创建记录" , description = "修改线路创建记录" )
    @SysLog("修改线路创建记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbR4mRouteCreateLog_edit')" )
    public R updateById(@RequestBody NbR4mRouteCreateLogEntity nbR4mRouteCreateLog) {
        return R.ok(nbR4mRouteCreateLogService.updateById(nbR4mRouteCreateLog));
    }

    /**
     * 通过id删除线路创建记录
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除线路创建记录" , description = "通过id删除线路创建记录" )
    @SysLog("通过id删除线路创建记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbR4mRouteCreateLog_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbR4mRouteCreateLogService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbR4mRouteCreateLog 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbR4mRouteCreateLog_export')" )
    public List<NbR4mRouteCreateLogEntity> export(NbR4mRouteCreateLogEntity nbR4mRouteCreateLog,Integer[] ids) {
        return nbR4mRouteCreateLogService.list(Wrappers.lambdaQuery(nbR4mRouteCreateLog).in(ArrayUtil.isNotEmpty(ids), NbR4mRouteCreateLogEntity::getId, ids));
    }
}