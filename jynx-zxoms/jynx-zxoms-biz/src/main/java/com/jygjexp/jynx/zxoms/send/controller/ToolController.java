package com.jygjexp.jynx.zxoms.send.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.jygjexp.jynx.zxoms.send.service.NbCustomerAddressService;
import com.jygjexp.jynx.zxoms.send.service.StatesService;
import com.jygjexp.jynx.zxoms.send.utils.MatrixToImageWriter;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.krysalis.barcode4j.impl.code128.Code128Bean;
import org.krysalis.barcode4j.output.bitmap.BitmapCanvasProvider;
import org.krysalis.barcode4j.tools.UnitConv;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: chenchang
 * @Description: 展示二维码
 * @date 2024-09-30 17:48:13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tool")
@Tag(description = "tool", name = "工具类")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ToolController {

    private final NbCustomerAddressService customerService;

    @Operation(summary = "获取二维码Qr", description = "获取二维码Qr")
    @SysLog("获取二维码Qr")
    @GetMapping("/qr/{code}")
    public R showQr(@PathVariable("code") String code, HttpServletResponse response) {
        int width = 500; // 图像宽度
        int height = 500; // 图像高度
        String format = "jpg";// 图像类型
        Map<EncodeHintType, Object> hints = new HashMap<>();
        //内容编码格式
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        // 指定纠错等级
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        //设置二维码边的空度，非负数
        hints.put(EncodeHintType.MARGIN, 1);
        BitMatrix bitMatrix;
        try {
            bitMatrix = new MultiFormatWriter().encode(code, BarcodeFormat.QR_CODE, width, height, hints);
            MatrixToImageWriter.writeToStream(bitMatrix, format, response.getOutputStream());
        } catch (WriterException | IOException e) {
            e.printStackTrace();
        }
        return R.ok();
    }

    @Operation(summary = "获取条形码Bar", description = "获取条形码Bar")
    @SysLog("获取条形码Bar")
    @GetMapping("/bar/{code}")
    public R showBar(@PathVariable("code") String code, HttpServletResponse response) {
        // 精细度
        final int dpi = 150;
        // module宽度
        final double moduleWidth = UnitConv.in2mm(1.0f / dpi);

        Double height = 20d;
        Double width = 0.5d;

        Code128Bean bean = new Code128Bean();
        bean.doQuietZone(true);
        bean.setBarHeight((double) ObjectUtils.defaultIfNull(height, 9.0D));
        bean.setModuleWidth(width);
        bean.setFontSize(6d);
        bean.doQuietZone(false);

        String format = "image/png";
        try {
            // 输出到流
            ByteArrayOutputStream ous = new ByteArrayOutputStream();
            BitmapCanvasProvider canvas = new BitmapCanvasProvider(ous, format, dpi, BufferedImage.TYPE_BYTE_BINARY, false, 0);

            // 生成条形码
            bean.generateBarcode(canvas, code);

            // 结束绘制
            canvas.finish();

            byte[] bytes = ous.toByteArray();
            InputStream in = new ByteArrayInputStream(bytes);

            int len;
            byte[] buf = new byte[1024];
            response.setContentType("image/png");

            ServletOutputStream output = response.getOutputStream();

            while ((len = in.read(buf)) != -1) {
                output.write(buf, 0, len);
            }
            output.flush();
            output.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return R.ok();
    }

    //根据手机号发送短信
    @Operation(summary = "发送短信", description = "发送短信")
    @PostMapping("/sendSms")
    public R sendSms(@RequestParam String phone, @RequestParam String postName, @RequestParam String orderNo) {
        // 校验传入的参数
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(postName) || StringUtils.isBlank(orderNo)) {
            return R.failed("The required parameter is missing：phone, postName, orderNo");
        }
        Boolean b = customerService.sendSms(phone, postName, orderNo);
        if (!b){
            return  R.failed("SMS sending exception");
        }
        return R.ok(b);
    }
}
