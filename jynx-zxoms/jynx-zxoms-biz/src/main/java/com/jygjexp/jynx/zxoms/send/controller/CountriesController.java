package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.zxoms.send.service.CountriesService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.CountriesEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 国家
 *
 * <AUTHOR>
 * @date 2024-09-30 18:40:38
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/countries" )
//@Tag(description = "countries" , name = "国家管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CountriesController {

    private final CountriesService countriesService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param basicCountries 国家
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('basic_countries_view')" )
    public R getBasicCountriesPage(@ParameterObject Page page, @ParameterObject CountriesEntity basicCountries) {
        LambdaQueryWrapper<CountriesEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(countriesService.page(page, wrapper));
    }


    /**
     * 通过id查询国家
     * @param id id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('basic_countries_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(countriesService.getById(id));
    }

    /**
     * 新增国家
     * @param basicCountries 国家
     * @return R
     */
//    @Operation(summary = "新增国家" , description = "新增国家" )
    @SysLog("新增国家" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('basic_countries_add')" )
    public R save(@RequestBody CountriesEntity basicCountries) {
        return R.ok(countriesService.save(basicCountries));
    }

    /**
     * 修改国家
     * @param basicCountries 国家
     * @return R
     */
//    @Operation(summary = "修改国家" , description = "修改国家" )
    @SysLog("修改国家" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('basic_countries_edit')" )
    public R updateById(@RequestBody CountriesEntity basicCountries) {
        return R.ok(countriesService.updateById(basicCountries));
    }

    /**
     * 通过id删除国家
     * @param ids id列表
     * @return R
     */
//    @Operation(summary = "通过id删除国家" , description = "通过id删除国家" )
    @SysLog("通过id删除国家" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('basic_countries_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(countriesService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param basicCountries 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('basic_countries_export')" )
    public List<CountriesEntity> export(CountriesEntity basicCountries, Integer[] ids) {
        return countriesService.list(Wrappers.lambdaQuery(basicCountries).in(ArrayUtil.isNotEmpty(ids), CountriesEntity::getId, ids));
    }
}