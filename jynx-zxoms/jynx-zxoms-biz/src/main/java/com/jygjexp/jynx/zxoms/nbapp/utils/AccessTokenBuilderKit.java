package com.jygjexp.jynx.zxoms.nbapp.utils;

import javax.servlet.http.HttpServletRequest;
import java.security.SecureRandom;
import java.util.Random;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/8 15:42
 */
public class AccessTokenBuilderKit {

    private static Random random;
    private static boolean weakRandom;
    private static int hashCode = new AccessTokenBuilderKit().hashCode();

    private AccessTokenBuilderKit() {
        try {
            // This operation may block on some systems with low entropy. See
            // this page for workaround suggestions:
            // http://docs.codehaus.org/display/JETTY/Connectors+slow+to+startup
            // System.out.println("Init SecureRandom.");
            random = new SecureRandom();
            weakRandom = false;
        } catch (Exception e) {
            random = new Random();
            weakRandom = true;
            System.err.println("Could not generate SecureRandom for accessToken randomness");
        }
    }

    public static String getAccessToken(HttpServletRequest request) {
        String accessToken = null;
        while (accessToken == null || accessToken.length() == 0) {
            long r0 = weakRandom ? (hashCode ^ Runtime.getRuntime().freeMemory() ^ random.nextInt() ^ (((long)request.hashCode()) << 32)) : random.nextLong();
            long r1 = random.nextLong();
            if (r0 < 0) r0 = -r0;
            if (r1 < 0) r1 = -r1;
            accessToken = Long.toString(r0, 36) + Long.toString(r1, 36);
        }
        return accessToken;
    }
}
