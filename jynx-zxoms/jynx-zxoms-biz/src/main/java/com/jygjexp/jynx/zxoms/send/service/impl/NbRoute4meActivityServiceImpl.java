package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.common.sentinel.handle.GlobalBizExceptionHandler;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.dto.R4mRouteCreateLogDto;
import com.jygjexp.jynx.zxoms.dto.TransferBatchDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.mapper.NbRoute4meActivityMapper;
import com.jygjexp.jynx.zxoms.nbapp.utils.HttpKit;
import com.jygjexp.jynx.zxoms.nbapp.utils.OrderTagUtil;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.send.utils.Route4MeUtil;

import com.route4me.sdk.services.notes.Note;
import com.route4me.sdk.services.routing.Address;
import com.route4me.sdk.services.routing.Route;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * route4me推送日志
 *
 * <AUTHOR>
 * @date 2024-10-22 15:40:52
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NbRoute4meActivityServiceImpl extends ServiceImpl<NbRoute4meActivityMapper, NbRoute4meActivityEntity> implements NbRoute4meActivityService {
    private final NbRoute4meActivityMapper nbRoute4meActivityMapper;
    private final NbTransferBatchOrderService transferBatchOrderService;
    private final NbOrderService orderService;
    private final NbOrderSignImageService orderSignImageService;
    private final NbDriverService driverService;
    private final NbTransferBatchService transferBatchService;
    private final NbOrderPathService orderPathService;
    private final NbTransferCenterService transferCenterService;
    private final NbR4mRouteCreateLogService r4mRouteCreateLogService;
    private final CommonDataUtil commonDataUtil;
    private final Route4MeUtil route4MeUtil;

    @Override
    public OldResult getIndex() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        String reqData = HttpKit.readData(servletRequestAttributes.getRequest());

        log.info("收到Activity:" + reqData);

        JSONObject jo = JSONUtil.parseObj(reqData);
        String activityType = jo.getStr("activity_type");
        String activityId = jo.getStr("activity_id");
        String routeId = jo.getStr("route_id");

        // "select * from nb_route4me_activity where activity_id = ? limit 1", activityId);
        NbRoute4meActivityEntity ra = nbRoute4meActivityMapper.selectOne(new LambdaQueryWrapper<NbRoute4meActivityEntity>().eq(NbRoute4meActivityEntity::getActivityId, activityId), false);
        if (ra == null) {
            ra = new NbRoute4meActivityEntity();
            ra.setLogTime(new Date());
            ra.setLogData(reqData);
            ra.setActivityType(activityType);
            ra.setActivityId(activityId);
            ra.setRouteId(routeId);
            nbRoute4meActivityMapper.insert(ra);

            try {
                businessProcess(jo);    //  业务处理
            } catch (Exception e) {
                e.printStackTrace();
                // ExceptionKit.handler(e, "/nb/driver/api/route4me", this);
                new GlobalBizExceptionHandler().handleGlobalException(e);
            }
        }
        return OldResult.ok("0", jo);
    }

    @Override
    public OldResult getFixed() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        String reqData = HttpKit.readData(servletRequestAttributes.getRequest());

        log.info("收到Activity:" + reqData);

        JSONObject jo = JSONUtil.parseObj(reqData);
        String activityType = jo.getStr("activity_type");
        String activityId = jo.getStr("activity_id");
        String routeId = jo.getStr("route_id");

        try {
            businessProcess(jo);
        } catch (Exception e) {
            e.printStackTrace();
//            ExceptionKit.handler(e, "/api/route4me", this);
            new GlobalBizExceptionHandler().handleGlobalException(e);
        }
        return OldResult.ok("0", jo);
    }

    // Route4Me订单业务处理
    private void businessProcess(JSONObject jo) {
        String routeId = jo.getStr("route_id");
        String activityType = jo.getStr("activity_type");

        if ("note-save".equals(activityType)) {
            // 有可能是上传图片，需要检查

            Long routeDestinationId = jo.getLong("route_destination_id");

            // "select * from nb_transfer_batch_order where route_destination_id = ? limit 1", routeDestinationId);
            NbTransferBatchOrderEntity tbo = transferBatchOrderService.getOne(new LambdaQueryWrapper<>(transferBatchOrderService.getOne(new LambdaQueryWrapper<NbTransferBatchOrderEntity>()
                    .eq(NbTransferBatchOrderEntity::getRouteDestinationId, routeDestinationId))));
            if (tbo == null) {
                log.info("没有找到对应订单：routeId=" + routeId + ",routeDestinationId=" + routeDestinationId);
                return;
            }

            Integer orderId = tbo.getOrderId();
            NbOrderEntity order = orderService.getById(orderId);
            // 20230804修改照片无经纬度，是Note节点数据没有经纬度导致，所以改为读取事件数据
            Double note_lat = jo.getDouble("note_lat");
            Double note_lng = jo.getDouble("note_lng");

//			Note(routeDestinationId=984536611, noteId=62946324, uploadExtension=jpeg, routeId=B57C311E9BB90686AB91CEF85EBBD82F,
//			uploadId=c7d3f8436cb84c45369ac2215aae3a1a, uploadType=ADDRESS_IMG, deviceType=null, lat=49.130111, lng=-122.896132,
//					uploadURL=https://storage.googleapis.com/r4m-uploaded-content/c7d3f8436cb84c45369ac2215aae3a1a.jpeg, tsAdded=1688101156, content=, activityType=paid)
            List<Note> notes = route4MeUtil.getNotes(routeId, Long.toString(routeDestinationId));
            for (Note note : notes) {
                if ("ADDRESS_IMG".equals(note.getUploadType())) {
                    String uploadId = note.getUploadId();
                    String uploadUrl = note.getUploadURL();
//					Double lat = note.getLat().doubleValue();
//					Double lng = note.getLng().doubleValue();
                    Long tsAdded = note.getTsAdded().longValue();

                    // "select * from nb_order_sign_image where r4m_upload_id = ? limit 1", uploadId);
                    NbOrderSignImageEntity image = orderSignImageService.getOne(new LambdaQueryWrapper<>(orderSignImageService.getOne(new LambdaQueryWrapper<NbOrderSignImageEntity>()
                            .eq(NbOrderSignImageEntity::getR4mUploadId, uploadId))));
                    if (image == null) {
                        image = new NbOrderSignImageEntity();
                        image.setOrderId(orderId);
                        image.setPkgImage(uploadUrl);
                        image.setAddTime(new Date(tsAdded * 1000l));
                        image.setDriverId(order.getDriverId());
                        image.setR4mUploadId(uploadId);
                        image.setLat(note_lat);
                        image.setLng(note_lng);
                        orderSignImageService.save(image);

                        log.info("处理图片上传：routeId=" + routeId + ",orderId=" + orderId + ",url=" + uploadUrl);
                    }

                    try {
                        // 20230909 虚拟子订单更新后,主订单状态也随之更新
                        if (order.getPOrderId() > 0) {

                            image.setImageId(null);
                            image.setOrderId(order.getPOrderId());
                            orderSignImageService.save(image);

                            log.info("处理图片上传：routeId=" + routeId + ",orderId=" + orderId + ",url=" + uploadUrl);
                        }
                    } catch (Exception e) {
//                        ExceptionKit.handler(e, "route4me hook", this);
                        log.error("route4me hook 处理图片上传异常：routeId=" + routeId + ",orderId=" + orderId, e);
                        new GlobalBizExceptionHandler().handleGlobalException(e);
                    }

                }
            }
        } else if ("route-owner-changed".equals(activityType)) {
            Integer memberId = jo.getInt("new_member_id");
            // "select * from nb_driver where route4me_member_id = ? limit 1", memberId);
            NbDriverEntity driver = driverService.getOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getRoute4meMemberId, memberId));
            // "select * from nb_transfer_batch where route_id = ? limit 1", routeId);
            NbTransferBatchEntity tb = transferBatchService.getOne(new LambdaQueryWrapper<NbTransferBatchEntity>().eq(NbTransferBatchEntity::getRouteId, routeId));
            if (tb != null && driver != null) {

                log.info("更改路区司机：routeId=" + routeId + ",memberId=" + memberId);
                tb.setDriverId(driver.getDriverId());
                tb.setGetType(TransferBatchDto.GET_TYPE_3_R4M);
                transferBatchService.updateById(tb);

                // "select o.* from nb_transfer_batch_order tbo, nb_order o where tbo.order_id = o.order_id and tbo.batch_id = ?", tb.getBatchId());
                List<NbOrderEntity> orders = transferBatchOrderService.findByTboBatchId(tb.getBatchId());
                for (NbOrderEntity order : orders) {
                    if (order.getOrderStatus() == OrderDto.ORDER_STATUS_205_DELIVERED) {
                        log.info("订单已经派送成功，不能再修改订单状态:orderId=" + order.getOrderId());
                        continue;
                    }
                    order.setDriverId(driver.getDriverId());
                    orderService.updateById(order);

                    log.info("司机改变：orderId=" + order.getOrderId() + ",driverId=" + driver.getDriverId());
                }

            }
        } else if ("move-destination".equals(activityType)) {
            moveDestination(routeId, jo);
        } else if ("update-destinations".equals(activityType)) {
            updateDestinations(routeId, jo);
        } else if ("route-destination-status".equals(activityType)) {
            JSONObject address_custom_data = jo.getJSONObject("address_custom_data");
            Double loaded_lat = address_custom_data.getDouble("loaded_lat");
            Double loaded_lng = address_custom_data.getDouble("loaded_lng");
            Long loaded_on_utc = address_custom_data.getLong("loaded_on_utc");
            Integer orderId = address_custom_data.getInt("__orderId");
            Double unloaded_lat = address_custom_data.getDouble("unloaded_lat");
            Double unloaded_lng = address_custom_data.getDouble("unloaded_lng");
            Long unloaded_on_utc = address_custom_data.getLong("unloaded_on_utc");

            if (orderId == null) {
                // "select * from nb_transfer_batch_order where route_id = ? and route_destination_id = ? limit 1", routeId, jo.getLong("route_destination_id"));
                NbTransferBatchOrderEntity tbo = transferBatchOrderService.getOne(new LambdaQueryWrapper<NbTransferBatchOrderEntity>().eq(NbTransferBatchOrderEntity::getRouteId, routeId)
                        .eq(NbTransferBatchOrderEntity::getRouteDestinationId, jo.getLong("route_destination_id")));
                if (tbo == null) {
                    log.info("没有找到对应订单：routeId=" + routeId + ",routeDestinationId=" + jo.getLong("route_destination_id"));
                    return;
                }
                orderId = tbo.getOrderId();
            }

            NbOrderEntity order = orderService.getById(orderId);
            // 标记成功或者失败
            String detailed_event = jo.getStr("detailed_event");

            // "select 1 from nb_order_path where order_id = ? and order_status = ? limit 1", orderId, Order.ORDER_STATUS_200_PARCEL_SCANNED);
            NbOrderPathEntity op200 = orderPathService.getOne(new LambdaQueryWrapper<NbOrderPathEntity>()
                    .eq(NbOrderPathEntity::getOrderId, orderId).eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED));
            if (op200 == null) {
                log.info("更改订单状态orderId=" + orderId + ",pkgNo=" + order.getPkgNo() + ",到" + detailed_event + "失败, 原因未发现200状态");

                try {
                    // 20240620 如果是失败状态，则需要标记供PDA扫码时识别
                    if ("failed".equals(detailed_event)) {
                        String newTag = OrderTagUtil.bitwiseOr(order.getTag(), OrderTagUtil.TAG_ORDER_FAILED_IN_TRANSFER_BATCH_BUT_NO_OS_200);
                        order.setTag(newTag);
                        orderService.updateById(order);

                        log.info("更改订单状态失败时，发现订单所属路区已经start，修改订单tag=" + newTag + ",orderId=" + orderId);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return;
            }

            if ("completed".equals(detailed_event)) {
                Long event_timestamp = jo.getLong("event_timestamp");

                order.setOrderStatus(OrderDto.ORDER_STATUS_205_DELIVERED);
                order.setDeliveryedTime(new Date(event_timestamp * 1000));
                order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_4_FINISHED);
                orderService.updateById(order);

                JSONObject province = commonDataUtil.getProvinceByNameOrIso2(39l, order.getDestProvince());
                String zoneId = ZoneId.systemDefault().getId();
                if (province != null) {
                    zoneId = province.getStr("zoneId");
                }

                NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), order.getDriverId(), unloaded_lat, unloaded_lng, order.getDestCity() + " " + order.getDestProvince(), zoneId, event_timestamp * 1000);
                // 2024-02-18 快件送达时，发送短信通知
                op.setSmsStatus(OrderPathDto.SMS_STATUS_10_UNSEND);
                orderPathService.save(op);

                log.info("订单状态更新:orderId=" + order.getOrderId() + ",status=" + order.getOrderStatus());

                // 20230909 虚拟子订单更新后,主订单状态也随之更新
                try {
                    if (order.getPOrderId() > 0) {
                        NbOrderEntity porder = orderService.getById(order.getPOrderId());
                        porder.setOrderStatus(order.getOrderStatus());
                        orderService.updateById(porder);

                        op.setPathId(null);
                        op.setSmsStatus(OrderPathDto.SMS_STATUS_0_NONE);
                        op.setOrderId(porder.getOrderId());
                        op.setOrderStatus(porder.getOrderStatus());
                        orderPathService.save(op);

                        log.info("父订单状态更新:orderId=" + porder.getOrderId() + ",status=" + porder.getOrderStatus());
                    }
                } catch (Exception e) {
                    // ExceptionKit.handler(e, "route4me hook", this);
                    log.error("route4me hook", e);
                    new GlobalBizExceptionHandler().handleGlobalException(e);
                }

                log.info("订单标记为:" + order.getOrderStatus() + "->orderId=" + order.getOrderId());
            } else if ("failed".equals(detailed_event)) {
                Long event_timestamp = jo.getLong("event_timestamp");

                // 20240222 由于订单可以直接就标记失败，没走开始流程，所以deliveryTry可能为0
                if (order.getDeliveryTry() == 0 || order.getDeliveryTry() == 1) {
                    order.setOrderStatus(OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1);
                } else if (order.getDeliveryTry() == 2) {
                    order.setOrderStatus(OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2);
                } else {
                    order.setOrderStatus(OrderDto.ORDER_STATUS_210_FAILED_DELIVERY);
                }
                order.setDeliveryedTime(new Date(event_timestamp * 1000));
                order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_5_FAILURED);
                orderService.updateById(order);

                JSONObject province = commonDataUtil.getProvinceByNameOrIso2(39l, order.getDestProvince());
                String zoneId = ZoneId.systemDefault().getId();
                if (province != null) {
                    zoneId = province.getStr("zoneId");
                }

                // 2024-03-07: 211状态需要发送短信通知
                NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), order.getDriverId(), unloaded_lat, unloaded_lng, order.getDestCity() + " " + order.getDestProvince(), zoneId, event_timestamp * 1000);
                if (order.getOrderStatus() == OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1) {
                    op.setSmsStatus(OrderPathDto.SMS_STATUS_10_UNSEND);
                }
                orderPathService.save(op);

                log.info("订单状态更新:orderId=" + order.getOrderId() + ",status=" + order.getOrderStatus());

                // 20230909 虚拟子订单更新后,主订单状态也随之更新
                try {
                    if (order.getPOrderId() > 0) {
                        NbOrderEntity porder = orderService.getById(order.getPOrderId());
                        porder.setOrderStatus(order.getOrderStatus());
                        orderService.updateById(porder);

                        op.setPathId(null);
                        op.setOrderId(porder.getOrderId());
                        op.setOrderStatus(porder.getOrderStatus());
                        orderPathService.save(op);

                        log.info("父订单状态更新:orderId=" + porder.getOrderId() + ",status=" + porder.getOrderStatus());
                    }
                } catch (Exception e) {
                    // ExceptionKit.handler(e, "route4me hook", this);
                    log.error("route4me hook", e);
                    new GlobalBizExceptionHandler().handleGlobalException(e);
                }
            }
        } else if ("mark-destination-visited".equals(activityType)) {

            Long event_timestamp = jo.getLong("event_timestamp");
            JSONObject address_custom_data = jo.getJSONObject("address_custom_data");
            Double loaded_lat = address_custom_data.getDouble("loaded_lat");
            Double loaded_lng = address_custom_data.getDouble("loaded_lng");
            Long loaded_on_utc = address_custom_data.getLong("loaded_on_utc");
            Integer orderId = address_custom_data.getInt("__orderId");
            Double unloaded_lat = address_custom_data.getDouble("unloaded_lat");
            Double unloaded_lng = address_custom_data.getDouble("unloaded_lng");
            Long unloaded_on_utc = address_custom_data.getLong("unloaded_on_utc");


        } else if ("route-started".equals(activityType)) {
            routeStarted(jo, routeId);

//			tb.setst
        } else if ("route-completed".equals(activityType)) {
            // 路区完成
            Long event_timestamp = jo.getLong("event_timestamp");

            // "select * from nb_transfer_batch where route_id = ? limit 1", routeId);
            NbTransferBatchEntity tb = transferBatchService.getById(routeId);
            if (tb == null) {
                log.info("没有找到对应route：" + routeId);
                return;
            }
            tb.setDeliveryStatus(TransferBatchDto.DELIVERY_STATUS_3_END);
            tb.setStartTime(new Date(event_timestamp * 1000));
            transferBatchService.updateById(tb);
        } else if ("route-delete".equals(activityType)) {
            // 删除规划
            routeDelete(routeId, jo);
        } else if ("route-optimized".equals(activityType)) {
            // 20230828 重新规划后，需要同步路区号
//			routeOptimized(routeId); // 2023-09-05 关闭路区号重排
            try {
                checkRouteCreated(routeId, jo); //  检查是否首次创建
            } catch (Exception e) {
                e.printStackTrace();
                // ExceptionKit.handler(e, "/route4me/api/routeOptimized", this);
                log.error("/route4me/api/routeOptimized", e);
                new GlobalBizExceptionHandler().handleGlobalException(e);
            }
        }
    }

    private void moveDestination(String routeId, JSONObject jo) {
        String to_route_id = jo.getStr("to_route_id");
        Long route_destination_id = jo.getLong("route_destination_id");

        log.info("节点移动：routeId=" + routeId + ",toRouteId=" + to_route_id + ",destination_id=" + route_destination_id);

        // "select * from nb_transfer_batch_order where route_destination_id = ? limit 1", route_destination_id);
        NbTransferBatchOrderEntity tbo = transferBatchOrderService.getOne(new LambdaQueryWrapper<NbTransferBatchOrderEntity>()
                .eq(NbTransferBatchOrderEntity::getRouteDestinationId, route_destination_id));

        // "select * from nb_transfer_batch where route_id = ? limit 1", to_route_id);
        NbTransferBatchEntity targetTb = transferBatchService.getOne(new LambdaQueryWrapper<NbTransferBatchEntity>()
                .eq(NbTransferBatchEntity::getRouteId, to_route_id));
        if (tbo != null && targetTb != null) {
            tbo.setRouteId(to_route_id);
            tbo.setBatchId(targetTb.getBatchId());
            transferBatchOrderService.updateById(tbo);

            Route sourceRoute = route4MeUtil.getRoute(routeId);
            // "select * from nb_transfer_batch where route_id = ? limit 1", routeId);
            NbTransferBatchEntity sourceTb = transferBatchService.getOne(new LambdaQueryWrapper<NbTransferBatchEntity>()
                    .eq(NbTransferBatchEntity::getRouteId, routeId));
            if (sourceTb != null) {
                sourceTb.setOrderTotal(sourceRoute.getRoutePieces());
//                sourceTb.setEstimatedHour(new BigDecimal(sourceRoute.getPlannedTotalRouteDuration() / 60 / 60.0d).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                sourceTb.setEstimatedHour(BigDecimal.valueOf(sourceRoute.getPlannedTotalRouteDuration()).divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP));    // 将总秒数转换为小时，保留两位小数
                sourceTb.setTripDistance(sourceRoute.getTripDistance());
                sourceTb.setPlannedTotalRouteDuration(sourceRoute.getPlannedTotalRouteDuration());
                transferBatchService.updateById(sourceTb);
            }

            Route toRoute = route4MeUtil.getRoute(to_route_id);
            targetTb.setOrderTotal(toRoute.getRoutePieces());
//            targetTb.setEstimatedHour(new BigDecimal(toRoute.getPlannedTotalRouteDuration() / 60 / 60.0d).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            targetTb.setEstimatedHour(BigDecimal.valueOf(sourceRoute.getPlannedTotalRouteDuration()).divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP));
            targetTb.setTripDistance(toRoute.getTripDistance());
            targetTb.setPlannedTotalRouteDuration(toRoute.getPlannedTotalRouteDuration());
            transferBatchService.updateById(targetTb);

            if (targetTb.getDriverId() != null && targetTb.getDriverId() > 0) {
                Integer orderId = tbo.getOrderId();
                NbOrderEntity order = orderService.getById(orderId);
                order.setDriverId(targetTb.getDriverId());
                orderService.updateById(order);
            }
        }

        if (tbo != null && targetTb == null) {
            // 移动时,目标路区不存在,则同同移出该订单
            tbo.setRouteId(to_route_id);
            tbo.setBatchId(0);
            transferBatchOrderService.updateById(tbo);
        }
    }

    private void updateDestinations(String routeId, JSONObject jo) {
        String detailed_event = jo.getStr("detailed_event");
        if ("loaded".equals(detailed_event)) {
            // 取件
            JSONObject address_custom_data = jo.getJSONObject("address_custom_data");
            Double loaded_lat = address_custom_data.getDouble("loaded_lat");
            Double loaded_lng = address_custom_data.getDouble("loaded_lng");
            Long loaded_on_utc = address_custom_data.getLong("loaded_on_utc");
            Integer orderId = address_custom_data.getInt("__orderId");

            if (orderId == null) {
                Long routeDestinationId = jo.getLong("route_destination_id");
                // "select * from nb_transfer_batch_order where route_id = ? and route_destination_id = ? limit 1", routeId, routeDestinationId);
                NbTransferBatchOrderEntity tbo = transferBatchOrderService.getOne(new LambdaQueryWrapper<NbTransferBatchOrderEntity>()
                        .eq(NbTransferBatchOrderEntity::getRouteId, routeId).eq(NbTransferBatchOrderEntity::getRouteDestinationId, routeDestinationId));
                orderId = tbo.getOrderId();
            }

            // "select 1 from nb_order_path where order_id = ? and order_status = ? limit 1", orderId, Order.ORDER_STATUS_200_PARCEL_SCANNED);
            NbOrderPathEntity op200 = orderPathService.getOne(new LambdaQueryWrapper<NbOrderPathEntity>()
                    .eq(NbOrderPathEntity::getOrderId, orderId).eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED));
            if (op200 == null) {
                log.info("更改订单状态orderId=" + orderId + "到203失败, 原因未发现200状态");
                return;
            }

            NbOrderEntity order = orderService.getById(orderId);
            order.setOrderStatus(OrderDto.ORDER_STATUS_203_LOAD_SCANNED);
            order.setDriverPickupTime(new Date(loaded_on_utc * 1000));
            orderService.updateById(order);

            String address = null;
            NbTransferCenterEntity tc = null;
            String zoneId = ZoneId.systemDefault().getId();
            if (order.getTcId() != null) {
                tc = transferCenterService.getById(order.getTcId());
                if (tc != null) {
                    JSONObject province = commonDataUtil.getProvinceById(tc.getProvinceId());
                    JSONObject city = commonDataUtil.getCityById(tc.getCityId());
                    address = city.getStr("enName") + " " + province.getStr("enName");

                    if (tc.getTcTimezone() != null) {
                        zoneId = tc.getTcTimezone();
                    }
                }
            }

            NbOrderPathEntity op = new OrderPathDto().createPath(order.getOrderId(), order.getOrderStatus(), order.getDriverId(), loaded_lat, loaded_lng, address, zoneId, loaded_on_utc * 1000);
            orderPathService.save(op);
            log.info("订单状态更新:orderId=" + order.getOrderId() + ",status=" + order.getOrderStatus());

            // 20230909 虚拟子订单更新后,主订单状态也随之更新
            try {
                if (order.getPOrderId() > 0) {
                    NbOrderEntity porder = orderService.getById(order.getPOrderId());
                    porder.setOrderStatus(order.getOrderStatus());
                    orderService.updateById(porder);

                    op.setPathId(null);
                    op.setOrderId(porder.getOrderId());
                    op.setOrderStatus(porder.getOrderStatus());
                    orderPathService.updateById(op);

                    log.info("父订单状态更新:orderId=" + porder.getOrderId() + ",status=" + porder.getOrderStatus());
                }
            } catch (Exception e) {
                // ExceptionKit.handler(e, "route4me hook", this);
                log.error("route4me hook", e);
                new GlobalBizExceptionHandler().handleGlobalException(e);
            }

        } else if ("unload".equals(detailed_event)) {

        }
    }

    private void routeStarted(JSONObject jo, String routeId) {
        // 路区开始
        Long event_timestamp = jo.getLong("event_timestamp");

        // "select * from nb_transfer_batch where route_id = ? limit 1", routeId);
        NbTransferBatchEntity tb = transferBatchService.getOne(new LambdaQueryWrapper<NbTransferBatchEntity>().eq(NbTransferBatchEntity::getRouteId, routeId));
        tb.setDeliveryStatus(TransferBatchDto.DELIVERY_STATUS_2_START);
        if (event_timestamp == null) {
            event_timestamp = jo.getLong("activity_timestamp");
            log.info("发现eventTime为空,使用activityTime->" + event_timestamp);
        }
        tb.setStartTime(new Date(event_timestamp * 1000));
        transferBatchService.updateById(tb);

        // 20230823 路区开始时，内订单全变为派送中
        Route route = route4MeUtil.getRoute(routeId);
        if (route == null || route.getAddresses() == null) {
            return;
        }

        List<Address> addressList = route.getAddresses();
        for (Address address : addressList) {
            if (address.getDepot()) {
                continue;
            }

            Map<String, Object> fields = address.getCustom_fields();
            if (fields == null) {
                continue;
            }

            Integer orderId;
            if (fields.containsKey("__orderId")) {
                orderId = Integer.valueOf(fields.get("__orderId").toString());
            } else {
                // "select * from nb_transfer_batch_order where route_id = ? and route_destination_id = ? limit 1", routeId, address.getRouteDestinationId());
                NbTransferBatchOrderEntity tbo = transferBatchOrderService.getOne(new LambdaQueryWrapper<NbTransferBatchOrderEntity>().eq(NbTransferBatchOrderEntity::getRouteId, routeId).eq(NbTransferBatchOrderEntity::getRouteDestinationId, address.getRouteDestinationId()));
                orderId = tbo.getOrderId();
            }

            if (orderId == null) {
                log.info("订单信息为空：" + address);
                continue;
            }

            // 20230908 卟卟卟:已经delivery的订单不能回in transit
            NbOrderEntity order = orderService.getById(orderId);
            if (order.getOrderStatus() == OrderDto.ORDER_STATUS_205_DELIVERED) {
                continue;
            }

            // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", orderId, Order.ORDER_STATUS_204_IN_TRANSIT);
            NbOrderPathEntity op = orderPathService.getOne(new LambdaQueryWrapper<NbOrderPathEntity>()
                    .eq(NbOrderPathEntity::getOrderId, orderId).eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_204_IN_TRANSIT));
            if (op != null) {
                continue;
            }

            // 2024-05-16 sophie 没有parcel scan 不产生 in transit
            // "select 1 from nb_order_path where order_id = ? and order_status = ? limit 1", order.getOrderId(), Order.ORDER_STATUS_200_PARCEL_SCANNED);
            NbOrderPathEntity op200 = orderPathService.getOne(new LambdaQueryWrapper<NbOrderPathEntity>()
                    .eq(NbOrderPathEntity::getOrderId, orderId).eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED));
            if (op200 != null) {
                if (order.getOrderStatus() != OrderDto.ORDER_STATUS_205_DELIVERED) {
                    order.setOrderStatus(OrderDto.ORDER_STATUS_204_IN_TRANSIT);
                    order.setDeliveryTry(order.getDeliveryTry() + 1);
                    orderService.updateById(order);
                }

                String pathAddress = order.getDestCity() + " " + order.getDestProvince();

                String zoneId = ZoneId.systemDefault().getId();
                JSONObject provinceInfo = commonDataUtil.getProvinceByNameOrIso2(39l, order.getDestProvince());
                if (provinceInfo != null && provinceInfo.containsKey("zoneId")) {
                    zoneId = provinceInfo.getStr("zoneId");
                }

                op = new OrderPathDto().createPath(order.getOrderId(), order.getOrderStatus(), order.getDriverId(), null, null, pathAddress, zoneId, event_timestamp * 1000);
                orderPathService.save(op);
                log.info("订单状态更新:orderId=" + order.getOrderId() + ",status=" + order.getOrderStatus());

                // 20230909 虚拟子订单更新后,主订单状态也随之更新
                try {
                    if (order.getPOrderId() > 0) {
                        NbOrderEntity porder = orderService.getById(order.getPOrderId());
                        porder.setOrderStatus(order.getOrderStatus());
                        orderService.updateById(porder);

                        op.setPathId(null);
                        op.setOrderId(porder.getOrderId());
                        op.setOrderStatus(porder.getOrderStatus());
                        orderPathService.save(op);

                        log.info("父订单状态更新:orderId=" + porder.getOrderId() + ",status=" + porder.getOrderStatus());
                    }
                } catch (Exception e) {
                    // ExceptionKit.handler(e, "route4me hook", this);
                    log.error("route4me hook", e);
                }
            }

        }
    }

    // 删除规划
    private void routeDelete(String routeId, JSONObject jo) {
        // "select * from nb_transfer_batch where route_id = ? limit 1", routeId);
        NbTransferBatchEntity tb = transferBatchService.getOne(new LambdaQueryWrapper<NbTransferBatchEntity>().eq(NbTransferBatchEntity::getRouteId, routeId));
        Long event_timestamp = jo.getLong("activity_timestamp");
        if (tb != null) {
            tb.setIsDelete(true);
            tb.setDeleteTime(new Date(event_timestamp * 1000));
            transferBatchService.updateById(tb);

            log.info("路区删除：" + routeId);
        }

        try {
            // "select * from nb_r4m_route_create_log where route_id = ? limit 1", routeId);
            NbR4mRouteCreateLogEntity createLog = r4mRouteCreateLogService.getOne(new LambdaQueryWrapper<NbR4mRouteCreateLogEntity>()
                    .eq(NbR4mRouteCreateLogEntity::getRouteId, routeId));
            if (createLog != null) {
                createLog.setStatus(R4mRouteCreateLogDto.STATUS_10_DELETED);
                createLog.setUpdateTime(Instant.now().toEpochMilli());
                r4mRouteCreateLogService.updateById(createLog);
            }
        } catch (Exception e) {
            e.printStackTrace();
            // ExceptionKit.handler(e, "/route4me/api/routeDelete", this);
            log.error("/route4me/api/routeDelete", e);
            new GlobalBizExceptionHandler().handleGlobalException(e);
        }
    }

    /**
     * 检查是否首次创建
     *
     * @param routeId
     * @param jo
     */
    private void checkRouteCreated(String routeId, JSONObject jo) {
        // "select * from nb_r4m_route_create_log where route_id = ? limit 1", routeId);
        NbR4mRouteCreateLogEntity createLog = r4mRouteCreateLogService.getOne(new LambdaQueryWrapper<NbR4mRouteCreateLogEntity>()
                .eq(NbR4mRouteCreateLogEntity::getRouteId, routeId));
        if (createLog == null) {
            // 首次创建
            createLog = new NbR4mRouteCreateLogEntity();
            createLog.setRouteId(routeId);
            createLog.setAddTime(Instant.now().toEpochMilli());
            createLog.setUpdateTime(Instant.now().toEpochMilli());
            createLog.setStatus(R4mRouteCreateLogDto.STATUS_1_CREATE);
            r4mRouteCreateLogService.save(createLog);
        }
    }

    private void routeOptimized(String routeId) {
        Route route = route4MeUtil.getRoute(routeId);
        if (route == null || route.getAddresses() == null) {
            return;
        }

        List<Address> addressList = route.getAddresses();
        for (Address address : addressList) {
            if (address.getDepot()) {
                continue;
            }

            Map<String, Object> fields = address.getCustom_fields();
            if (fields == null) {
                continue;
            }

            Integer orderId = Integer.valueOf(fields.get("__orderId").toString());
            if (orderId == null) {
                log.info("订单信息为空：" + address);
                continue;
            }

            long routeDestId = address.getRouteDestinationId();

            // "select * from nb_transfer_batch_order where route_id = ? and route_destination_id = ? limit 1", routeId, routeDestId);
            NbTransferBatchOrderEntity tbo = transferBatchOrderService.getOne(new LambdaQueryWrapper<NbTransferBatchOrderEntity>()
                    .eq(NbTransferBatchOrderEntity::getRouteId, routeId)
                    .eq(NbTransferBatchOrderEntity::getRouteDestinationId, routeDestId));
            if (tbo != null) {
                String pickNo = tbo.getPickNo();

                String before = pickNo.substring(0, pickNo.lastIndexOf("-"));
                String newPickNo = before + "-" + String.format("%02d", address.getSequenceNo());

                tbo.setSequenceNo(address.getSequenceNo().intValue());
                tbo.setPickNo(newPickNo);
                tbo.setRouteId(routeId);
                tbo.setOptimizationProblemId(route.getOptimizationProblemId());
                transferBatchOrderService.updateById(tbo);

                NbOrderEntity order = orderService.getById(orderId);
                order.setPickNo(newPickNo);
                orderService.updateById(order);
                log.info("订单线路号更新:orderId=" + orderId + "->old=" + pickNo + ",new=" + newPickNo);
            }

        }
    }

}