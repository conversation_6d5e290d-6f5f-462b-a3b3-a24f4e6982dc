package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbOrderStatusModifyRelEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderStatusModifyRelMapper;
import com.jygjexp.jynx.zxoms.send.service.NbOrderStatusModifyRelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单修改关系
 *
 * <AUTHOR>
 * @date 2024-10-14 15:31:44
 */
@Service
@RequiredArgsConstructor
public class NbOrderStatusModifyRelServiceImpl extends ServiceImpl<NbOrderStatusModifyRelMapper, NbOrderStatusModifyRelEntity> implements NbOrderStatusModifyRelService {
    private final  NbOrderStatusModifyRelMapper nbOrderStatusModifyRelMapper;

    /**
     * 查询订单状态维护目标状态
     * @param fromStatus 当前状态
     * @return
     */
    @Override
    public List<NbOrderStatusModifyRelEntity> listTargetOrderStatus(Integer fromStatus) {
        LambdaQueryWrapper<NbOrderStatusModifyRelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotNull(fromStatus), NbOrderStatusModifyRelEntity::getFromStatus, fromStatus);
        return nbOrderStatusModifyRelMapper.selectList(wrapper);
    }

}