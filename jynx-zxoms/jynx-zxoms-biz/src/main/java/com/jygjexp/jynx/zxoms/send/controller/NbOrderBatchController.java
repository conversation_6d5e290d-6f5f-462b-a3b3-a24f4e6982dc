package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderBatchEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderBatchService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderBatchExcelVo;
import com.jygjexp.jynx.zxoms.vo.NbOrderBatchPageVo;
import com.jygjexp.jynx.zxoms.vo.OrderBatchVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 派送批次.
 *
 * <AUTHOR>
 * @date 2024-10-12 16:48:25
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrderBatch" )
@Tag(description = "nbOrderBatch" , name = "派送批次.管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderBatchController {

    private final NbOrderBatchService nbOrderBatchService;
    private final NbOrderService nbOrderService;

    /**
     * 派送批次.分页查询
     * @param page 分页对象
     * @param vo 派送批次.
     * @return
     */
    @Operation(summary = "派送批次.分页查询" , description = "派送批次.分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderBatch_view')" )
    public R getNbOrderBatchPage(@ParameterObject Page page, @ParameterObject NbOrderBatchPageVo vo) {
//        return R.ok(nbOrderBatchService.page(page, new LambdaQueryWrapper<NbOrderBatchEntity>().orderByDesc(NbOrderBatchEntity::getBatchId)));
        return R.ok(nbOrderBatchService.search(page, vo));
    }


    /**
     * 通过id查询派送批次.
     * @param batchId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{batchId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderBatch_view')" )
    public R getById(@PathVariable("batchId" ) Integer batchId) {
        return R.ok(nbOrderBatchService.getById(batchId));
    }

    /**
     * 新增派送批次.
     * @param zxOmsNbOrderBatch 派送批次.
     * @return R
     */
    @Operation(summary = "新增派送批次." , description = "新增派送批次." )
    @SysLog("新增派送批次." )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderBatch_add')" )
    public R save(@RequestBody NbOrderBatchEntity zxOmsNbOrderBatch) {
        return R.ok(nbOrderBatchService.save(zxOmsNbOrderBatch));
    }

    /**
     * 修改派送批次.
     * @param zxOmsNbOrderBatch 派送批次.
     * @return R
     */
    @Operation(summary = "修改派送批次." , description = "修改派送批次." )
    @SysLog("修改派送批次." )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderBatch_edit')" )
    public R updateById(@RequestBody NbOrderBatchEntity zxOmsNbOrderBatch) {
        return R.ok(nbOrderBatchService.updateById(zxOmsNbOrderBatch));
    }

    /**
     * 通过id删除派送批次.
     * @param ids batchId列表
     * @return R
     */
    @Operation(summary = "通过id删除派送批次." , description = "通过id删除派送批次." )
    @SysLog("通过id删除派送批次." )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderBatch_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderBatchService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param vo 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderBatch_export')" )
    public List<NbOrderBatchExcelVo> export(NbOrderBatchPageVo vo, Integer[] ids) {
//        return nbOrderBatchService.list(Wrappers.lambdaQuery(zxOmsNbOrderBatch).in(ArrayUtil.isNotEmpty(ids), NbOrderBatchEntity::getBatchId, ids));
        return nbOrderBatchService.getExcel(vo, ids);
    }

    @Operation(summary = "通过批次号查询条形码列表" , description = "通过批次号查询条形码列表" )
    @GetMapping("/showBarListByBatchNo/{batchNo}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderBatch_showBar')" )
    public R showBarListByBatchNo(@PathVariable("batchNo" ) String batchNo){
        return R.ok(nbOrderService.showBarListByBatchNo(batchNo));
    }

    @Operation(summary = "添加子批次" , description = "添加子批次" )
    @SysLog("添加子批次" )
    @PostMapping("/addSub")
    public R addSub(@RequestParam Integer batchId){
        return nbOrderBatchService.addSub(batchId);
    }

    @Operation(summary = "派送批次.按转运中心分组" , description = "派送批次.按转运中心分组" )
    @SysLog("派送批次.按转运中心分组" )
    @PostMapping("/tcGroup")
    public R tcGroup(@RequestParam Integer batchId){
        return nbOrderBatchService.tcGroup(batchId);
    }

    @Operation(summary = "按一个转运中心进行路径规划" , description = "按一个转运中心进行路径规划" )
    @SysLog("按一个转运中心进行路径规划" )
    @PostMapping("/optimizationByTc")
    public R optimizationByTc(@RequestBody  OrderBatchVo vo){
        return nbOrderBatchService.optimizationByTc(vo);
    }

    @Operation(summary = "创建空批次" , description = "创建空批次" )
    @SysLog("创建空批次" )
    @PostMapping("/createEmptyBatch")
    public R createEmptyBatch(@RequestParam(value = "batchNo", required = false) String batchNo){
        return nbOrderBatchService.createEmptyBatch(batchNo);
    }

    @Operation(summary = "开启扫描" , description = "开启扫描" )
    @SysLog("开启扫描" )
    @PostMapping("/openingScan")
    public R openingScan(@RequestParam Integer orderId){
        return nbOrderBatchService.openingScan(orderId);
    }
    @Operation(summary = "移动订单到其他批次" , description = "移动订单到其他批次" )
    @SysLog("移动订单到其他批次" )
    @PostMapping("/orderMoveToBatch")
    public R orderMoveToBatch(@RequestParam Integer batchId, @RequestParam Integer orderId){
        return nbOrderBatchService.orderMoveToBatch(batchId, orderId);
    }

//    @Operation(summary = "导出给route4me，Order" , description = "导出给route4me，Order" )
//    @SysLog("导出给route4me，Order" )
//    @PostMapping("/exportToR4mOrder")
//    public R exportToR4mOrder(@RequestParam String batchIds){
//        return nbOrderBatchService.exportToR4mOrder(batchIds);
//    }

    @Operation(summary = "Custom Release" , description = "Custom Release" )
    @SysLog("Custom Release" )
    @PostMapping("/customRelease")
    @PreAuthorize("@pms.hasPermission('orderBatch_customRelease')" )
    public R customRelease(@RequestParam Integer batchId){
        return nbOrderBatchService.customRelease(batchId);
    }

}