package com.jygjexp.jynx.zxoms.send.task;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenchang
 * @Description: 【订单】检查UNI派送
 * @Date: 2024/10/9 22:48
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class CheckUniDeliveryTask {
    private final NbOrderService nbOrderService;

    @SneakyThrows
    @XxlJob("checkUniDeliveryHandler")
    public void checkUniDeliveryHandler() {
//        String param = XxlJobHelper.getJobParam();
//        log.info("匹配定时任务开始时间: {}, 入参:{}", LocalDateTime.now(), param);
        XxlJobHelper.log("定时任务：【订单检查UNI派送】于:{}，输入参数{}", LocalDateTime.now(), "运行中");

        // 搜索190状态，且超过5天的
        String before5Days = LocalDateTime.now().minusDays(5).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String last30Days = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // "select * from nb_order where order_id > 1365632 and merchant_id = 1001 and uni_delivered = false and order_status = " + 190 + " and add_time < '" + before5Days + "' and add_time >= '" + last30Days + "' limit 5000");
        List<NbOrderEntity> orders = nbOrderService.findOrderByUniDeliveredAndStatus190(before5Days, last30Days);
        int process = 0;
        for (NbOrderEntity order : orders) {
            process++;
            log.info(process + "/" + orders.size() + "->" + order.getPkgNo());
            checkDelivery(order);
        }

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【订单检查UNI派送】执行结束，时间: {}", LocalDateTime.now());
    }

    private void checkDelivery(NbOrderEntity order) {
        String ret = requestByOrerNo(order.getPkgNo());

        if (ret.startsWith("Delivered")) {
            String[] statusAndTime = ret.split(",");

            String status = statusAndTime[0];
            String time = statusAndTime[1];

            Date date = Date.from(LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toInstant(OffsetDateTime.now().getOffset()));

            order.setUniDelivered(true);
            order.setUniDeliveredTime(date);
            nbOrderService.updateById(order);
        }
    }

    public String requestByOrerNo(String orderNo) {
        JSONArray trcknos = new JSONArray();
        trcknos.add(orderNo);

        HttpRequest request = HttpRequest.post("http://api.jygjexp.com/v1/api/tracking/query/trackNB");
        request.header("apiKey", "675bfe2fd67105e9a88e564bf0f0344c");
        request.body(trcknos.toJSONString());
        HttpResponse response = request.execute();
        String result = response.body();
        XxlJobHelper.log("checkUniDelivery JL::result：{}" + result);

        JSONObject retJo = JSON.parseObject(result);
        if ("success".equals(retJo.getString("message")) && retJo.containsKey("data")) {
            JSONObject tracks = retJo.getJSONArray("data").getJSONObject(0);

            String status = tracks.getString("status");
            String deliveryedTime = null;
            if ("Delivered".equals(status)) {
                JSONArray fromDetails = tracks.getJSONArray("fromDetail");

                if (fromDetails != null && fromDetails.size() > 0) {
                    JSONArray localJa = new JSONArray();

                    for (int i = 0; i < fromDetails.size(); i++) {
                        JSONObject item = fromDetails.getJSONObject(i);
                        if ("Delivered".equalsIgnoreCase(item.getString("pathInfo")) || "520".equals(item.getString("pathCode"))) {
                            deliveryedTime = item.getString("pathTime");
                        }

                    }
                }
            }
            return status + "," + deliveryedTime;
        }
        return "";
    }

}
