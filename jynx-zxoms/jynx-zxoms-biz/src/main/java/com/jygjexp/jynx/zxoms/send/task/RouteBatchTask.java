package com.jygjexp.jynx.zxoms.send.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.zxoms.dto.DriverDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.Route4MeUtil;

import com.route4me.sdk.services.routing.*;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Author: chenchang
 * @Description: 【规划】订单批次路径规划
 * @Date: 2024/10/9 22:04
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class RouteBatchTask {
    private final NbOrderBatchService nbOrderBatchService;
    private final NbOrderService nbOrderService;
    private final NbDriverService nbDriverService;
    private final NbTransferCenterService nbTransferCenterService;
    private final NbTransferBatchService nbTransferBatchService;
    private final NbTransferBatchOrderService nbTransferBatchOrderService;
    private final Route4MeUtil route4MeUtil;

    @SneakyThrows
    @XxlJob("routeBatchHandler")
    public void routeBatchHandler(){
//        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务：【订单批次路径规划】于:{}，输入参数{}", LocalDateTime.now(), "运行中");

        //order_total = batch_total  ： 实际扫描数量=批次期望数量    is_routed:是否规划
        // "select * from nb_order_batch where order_total = batch_total and is_routed = false limit 10"); // 盲扫齐全，立马规划
        //修改成    select a.* from nb_order_batch a left join nb_order b on a.batch_no=b.batch_no
        //        where a.batch_total= a.order_total and a.is_routed = 0 and  b.r4m_order_id != 0 limit 10
        NbOrderBatchEntity batch = nbOrderBatchService.findNoRouted();  // 盲扫齐全，立马规划
        if (batch != null) {
        // "select * from nb_order where batch_no = ?", batch.getBatchNo());
            List<NbOrderEntity> orders = nbOrderService.list(batch.getBatchNo());
            Map<Integer, List<NbOrderEntity>> orderMap = new HashMap<>();
            for (NbOrderEntity order : orders) {
                Integer tcId = order.getTcId();
                if (tcId == null || tcId == 0) {
                    XxlJobHelper.log("发现有未分配转运中心的订单，暂时不规划： " + order.getOrderId());

                    batch.setRoutedErrmsg("发现有未分配转运中心的订单，暂时不规划： " + order.getOrderId());
                    nbOrderBatchService.updateById(batch);
                }

                List<NbOrderEntity> tcOrders;

                if (orderMap.containsKey(tcId)) {
                    tcOrders = orderMap.get(tcId);
                } else {
                    tcOrders = Lists.newArrayList();
                }

                tcOrders.add(order);
                //转运中心订单分组
                orderMap.put(tcId, tcOrders);
            }


            Iterator<Map.Entry<Integer, List<NbOrderEntity>>> iter = orderMap.entrySet().iterator();
            while (iter.hasNext()) {
                Map.Entry<Integer, List<NbOrderEntity>> entry = iter.next();
                //转运中心
                int tcId = entry.getKey();
                //转运中心下对应的订单列表
                List<NbOrderEntity> tcOrders = entry.getValue();
                    /*
                    *      查询当前转运中心下的 audit_status = 3：司机审核状态已通过      auto_route_planning = true：自动路径规划表示是        route4me_member_id> 0：route成员大于0 的列表
                    * */
                // "select * from nb_driver where audit_status = 3 and tc_id = ? and auto_route_planning = true and route4me_member_id > 0", tcId);
                LambdaQueryWrapper<NbDriverEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(NbDriverEntity::getAuditStatus, DriverDto.AUDIT_STATUS_3_PASS)
                        .eq(NbDriverEntity::getTcId, tcId)
                        .eq(NbDriverEntity::getAutoRoutePlanning, true)
                        .gt(NbDriverEntity::getRoute4meMemberId, 0);
                List<NbDriverEntity> drivers = nbDriverService.list(wrapper);
                NbTransferCenterEntity tc = nbTransferCenterService.getById(tcId);

                /*
                *
                        parameters（必需）：用于指定优化任务的参数设置，定义优化的细节和规则。主要字段包括：
                        route_name：为该路线指定一个名称，方便管理。
                        algorithm_type：算法类型，用于指定优化的策略（例如，1 表示短距离，2 表示最短时间等）。
                        route_date：路线生成日期，以 Unix 时间戳格式表示。
                        route_time：起始时间，以秒为单位，表示当天的时间（例如，早上 8 点为 8 * 3600）。
                        optimize：优化目标（可选），如 "Distance" 或 "Time"。
                        distance_unit：距离单位，通常为 "mi"（英里）或 "km"（公里）。
                        device_type：设备类型，用于指定该任务使用的设备（例如，web, android, ios 等）。
                * */
                //创建路线并返回路线规划数据
                DataObject dataObject = route4MeUtil.createAnOptimization(batch.getBatchId(), tc, null, tcOrders, drivers, null, true, 0);
                if (dataObject != null) {
                    log.info("-----------------------------------------路线规划成功-------------------------------------------");
                    XxlJobHelper.log("-----------------------------------------路线规划成功-------------------------------------------");
                    //优化问题id
                    String problemId = dataObject.getOptimizationProblemId();
                    //接口地址链接
                    Links links = dataObject.getLinks();

                    //根据传入的整数值来查找并返回对应的 OptimizationState 枚举常量                          dataObject.getState().intValue():获取优化任务的状态，并将其转换为整数值
                    Constants.OptimizationState os = Constants.OptimizationState.get(dataObject.getState().intValue());
                    String state = Constants.OptimizationState.get(dataObject.getState().intValue()).name();
                    XxlJobHelper.log("state:" + dataObject.getState() + "," + (os == null ? "-" : os.name()));

                    //获取路线
                    List<Route> routes = dataObject.getRoutes();
                    //[Route(id=EEEEE061D44BA5547097462ED286E0FA, memberId=2623213, memberEmail=<EMAIL>, isUnrouted=false, vehicleAlias=null, routeCost=0.0, routeRevenue=0.0, netRevenuePerDistanceUnit=0.0, created_timestamp=1687270911, mpg=10.0, tripDistance=200.44, gasPrice=2.0, routeDurationSec=21057, plannedTotalRouteDuration=26157, uduDistanceUnit=km, uduTripDistance=322.58, routeWeight=118.63, routeCube=0.0, routePieces=17, totalWaitTime=0, uduActualTravelDistance=0.0, actualTravelDistance=0.0, actualFootsteps=0, workingTime=0, drivingTime=0, idlingTime=0, payingMiles=0.0, destinationCount=17, notesCount=0, actualTravelTime=0, vehicle=null, routeDirections=[], notes=[], path=[], uniqueDestinationCount=17)]

                    //获取到路线中的信息，并将routeid和路线信息进行分组绑定
                    Map<String, List<Address>> route4meAddressMap = new HashMap<>();
                    List<Address> route4meAddressList = dataObject.getAddresses();
                    for (Address route4meAdderss : route4meAddressList) {
                        if (route4meAdderss.getRouteId() == null) {
                            continue;
                        }
                        List<Address> innerList = route4meAddressMap.getOrDefault(route4meAdderss.getRouteId(), Lists.newArrayList());
                        innerList.add(route4meAdderss);

                        route4meAddressMap.put(route4meAdderss.getRouteId(), innerList);
                    }


                    //插入规划id
                    batch.setOptimizationProblemId(problemId);
                    //是否规划，改变成true
                    batch.setIsRouted(true);
                    //插入规划时间
                    batch.setRoutedTime(new Date());
                    //规划返回提示
                    batch.setRoutedErrmsg("");
                    //插入route4me view
                    batch.setBatchView(links.getView());
                    nbOrderBatchService.updateById(batch);

                    String ymdStr = DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss");
                    String today = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd");
                    String startTime = today + " 00:00:00";
                    String endTime = today + " 23:59:59";

                    // "select count(1) cont from nb_transfer_batch where add_time >= ? and add_time <= ?", today + " 00:00:00", today + " 23:59:59").getInt("cont");
                    LambdaQueryWrapper<NbTransferBatchEntity> wrapper2 = new LambdaQueryWrapper<>();
                    wrapper2.ge(NbTransferBatchEntity::getAddTime, startTime)
                            .le(NbTransferBatchEntity::getAddTime, endTime);
                    Long count = nbTransferBatchService.count(wrapper2);
                    Integer todayLQTotal = Math.toIntExact(count);

                    for (Route route : routes) {
                        XxlJobHelper.log("处理Route:" + route);
                        String routeView = route.getLinks().getRoute();

                        int driverId = 0;
                        Long memberId = route.getMemberId();
                        if (memberId != null && memberId > 0) {
                            // "select * from nb_driver where route4me_member_id = ? limit 1", memberId);
                            LambdaQueryWrapper<NbDriverEntity> wrapper1 = new LambdaQueryWrapper<>();
                            wrapper1.eq(NbDriverEntity::getRoute4meMemberId, memberId);
                           NbDriverEntity driver = nbDriverService.getOne(wrapper1, false);
                            if (driver != null) {
                                driverId = driver.getDriverId();
                            }
                        }

                        NbTransferBatchEntity tb = new NbTransferBatchEntity();
                        tb.setBatchNo("LQ" + ymdStr + String.valueOf(todayLQTotal) + batch.getBatchId());
                        tb.setAddTime(new Date());
                        tb.setDriverId(driverId);
                        tb.setOrderTotal(route.getRoutePieces());
//                        tb.setEstimatedHour(new BigDecimal(route.getPlannedTotalRouteDuration() / 60 / 60.0d).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                        tb.setEstimatedHour(BigDecimal.valueOf(route.getPlannedTotalRouteDuration()).divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP));
                        //1分配，2抢单
                        if (driverId == 0) {
                            tb.setGetType(2);
                        } else {
                            tb.setGetType(1);
                        }

                        tb.setBatchCode(tb.getBatchNo());
                        tb.setTranferDriverId(0);
                        tb.setTcId(tc.getTcId());
                        tb.setRouteId(route.getId());
                        tb.setOptimizationProblemId(problemId);
                        tb.setTripDistance(route.getTripDistance());
                        tb.setPlannedTotalRouteDuration(route.getPlannedTotalRouteDuration());
                        tb.setMemberId(route.getMemberId());
                        tb.setRouteView(routeView);
                        nbTransferBatchService.save(tb);

                        List<Address> routeAddressList = route.getAddresses();
                        int index = 1;
                        for (Address address : routeAddressList) {
                            //Depot仓库
                            if (address.getDepot()) {
                                log.info("仓库跳过->" + address);
                                XxlJobHelper.log("仓库跳过->" + address);
                                continue;
                            }
                            //String alias = address.getAlias();
                            Integer orderId = Integer.valueOf(address.getCustom_fields().get("__orderId").toString());

                            //路区订单
                            NbTransferBatchOrderEntity tbo = new NbTransferBatchOrderEntity();
                            tbo.setBatchId(tb.getBatchId());
                            tbo.setOrderId(orderId);
                            tbo.setPickNo(today.split("-")[2] + "-" + String.format("%02d", (todayLQTotal + 1)) + "-" + String.format("%02d", index));
                            tbo.setRouteDestinationId(address.getRouteDestinationId());
                            tbo.setMemberId(address.getMemberId().longValue());
                            tbo.setRouteId(address.getRouteId());
                            tbo.setOptimizationProblemId(address.getOptimizationProblemId());
                            tbo.setSequenceNo(Math.toIntExact(address.getSequenceNo()));
                            tbo.setChannelName(address.getChannelName());
                            tbo.setDriveTimetoNextDestination(address.getDriveTimetoNextDestination());
                            tbo.setTrackingNumber(address.getTrackingNumber());
                            nbTransferBatchOrderService.save(tbo);

                            NbOrderEntity order = nbOrderService.getById(tbo.getOrderId());
                            order.setPickNo(tbo.getPickNo());
                            if (driverId > 0) {
                                order.setDriverId(driverId);
                            }
                            nbOrderService.updateById(order);

                            index ++;
                        }

                        todayLQTotal++;
                    }
                }
                XxlJobHelper.log("Route4Me数据处理完成-------------------------------------"+dataObject);
            }
        }

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【订单批次路径规划】执行结束，时间: {}", LocalDateTime.now());
    }
}
