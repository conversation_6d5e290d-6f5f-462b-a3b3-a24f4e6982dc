package com.jygjexp.jynx.zxoms.handler;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

import java.util.Locale;

/**
 * @Author: xiongpegnfei
 * @Description: 动态国际化配置语言切换
 * @Date: 2024/12/04 16:24
 */
@Configuration
public class LocaleConfig {

    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver localeResolver = new AcceptHeaderLocaleResolver();
        localeResolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
//        localeResolver.setDefaultLocale(Locale.ENGLISH);
        return localeResolver;
    }
}
