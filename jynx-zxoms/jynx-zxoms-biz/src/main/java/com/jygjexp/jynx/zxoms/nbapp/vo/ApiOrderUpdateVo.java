package com.jygjexp.jynx.zxoms.nbapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: chenchang
 * @Description: 更新批次信息
 * @Date: 2024/11/29 14:52
 */
@Data
public class ApiOrderUpdateVo {
    @NotBlank(message = "批次号不能为空")
    @Schema(description = "批次号")
    private String batchNo; // 批次号

    // 2023-12-17 增加ETA、ETD、ATA、ATD
    @NotBlank(message = "ETA不能为空")
    @Schema(description = "ETA")
    private String eta;

    @NotBlank(message = "ETD不能为空")
    @Schema(description = "ETD")
    private String etd;

    @NotBlank(message = "ATA不能为空")
    @Schema(description = "ATA")
    private String ata;

    @NotBlank(message = "ATD不能为空")
    @Schema(description = "ATD")
    private String atd;

}
