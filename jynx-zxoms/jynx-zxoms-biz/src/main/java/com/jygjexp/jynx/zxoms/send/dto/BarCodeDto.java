package com.jygjexp.jynx.zxoms.send.dto;

public class BarCodeDto {

	private String code;
	
	private Boolean fontHide;
	
	private Float x;
	
	private Integer barHeight;
	
	private int positionX;
	
	private int positionY;
	
	private float fontSize = 1f;
	
	private Float baseLine;
	
	private String type = "code39";
	
	public BarCodeDto(String code, Boolean fontHide, Float x, Integer barHeight, int positionX, int positionY) {
		super();
		this.code = code;
		this.fontHide = fontHide;
		this.x = x;
		this.barHeight = barHeight;
		this.positionX = positionX;
		this.positionY = positionY;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Boolean getFontHide() {
		return fontHide;
	}

	public void setFontHide(Boolean fontHide) {
		this.fontHide = fontHide;
	}

	public Float getX() {
		return x;
	}

	public void setX(Float x) {
		this.x = x;
	}

	public Integer getBarHeight() {
		return barHeight;
	}

	public void setBarHeight(Integer barHeight) {
		this.barHeight = barHeight;
	}

	public int getPositionX() {
		return positionX;
	}

	public void setPositionX(int positionX) {
		this.positionX = positionX;
	}

	public int getPositionY() {
		return positionY;
	}

	public void setPositionY(int positionY) {
		this.positionY = positionY;
	}

	public float getFontSize() {
		return fontSize;
	}

	public void setFontSize(float fontSize) {
		this.fontSize = fontSize;
	}

	public Float getBaseLine() {
		return baseLine;
	}

	public void setBaseLine(Float baseLine) {
		this.baseLine = baseLine;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

}
