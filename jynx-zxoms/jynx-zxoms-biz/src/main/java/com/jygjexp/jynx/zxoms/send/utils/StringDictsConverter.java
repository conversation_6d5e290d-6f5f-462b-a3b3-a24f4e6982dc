package com.jygjexp.jynx.zxoms.send.utils;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.jygjexp.jynx.common.core.util.SpringContextHolder;
import com.jygjexp.jynx.zxoms.annotation.ConvertType;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * @Author: chenchang
 * @Description: 字符串字段字典转换器
 * @Date: 2024/12/6 23:25
 */
public class StringDictsConverter implements Converter<String> {
    private final StringRedisTemplate redisTemplate;

    public StringDictsConverter() {
        // 从 Spring 容器中获取 RedisTemplate
        this.redisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
    }

    @Override
    public Class<String> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        ConvertType annotation = contentProperty.getField().getAnnotation(ConvertType.class);
        if (annotation == null) {
            return new WriteCellData<>(value);
        }

        String type = annotation.value();
        switch (type) {
//            case "country":
//                return new WriteCellData<>(convertCountryName(value));
//            case "province":
//                return new WriteCellData<>(convertProvinceName(value));
            default:
                return new WriteCellData<>(value);
        }
    }

    /**
     * 根据国家代码获取国家名称
     */
//    private String convertCountryName(String countryCode) {
//        // 从 Redis 获取国家字典
//        String countryData = redisTemplate.opsForValue().get(DictConvertConstants.BASE_KEY + DictConvertConstants.COUNTRY);
//        List<SysDictItem> dictList = JsonParser.getDictValue(countryData);
//        for (SysDictItem item : dictList) {
//            if (item.getValue().equals(countryCode)) {
//                return item.getLabel();
//            }
//        }
//        return countryCode; // 如果未找到匹配，则返回原值
//    }

    /**
     * 根据省代码获取省名称
     */
//    private String convertProvinceName(String provinceCode) {
//        // 从 Redis 获取省字典
//        String provinceData = redisTemplate.opsForValue().get(DictConvertConstants.BASE_KEY + DictConvertConstants.PROVINCE);
//        List<SysDictItem> dictList = JsonParser.getDictValue(provinceData);
//        for (SysDictItem item : dictList) {
//            if (item.getValue().equals(provinceCode)) {
//                return item.getLabel();
//            }
//        }
//        return provinceCode; // 如果未找到匹配，则返回原值
//    }
}
