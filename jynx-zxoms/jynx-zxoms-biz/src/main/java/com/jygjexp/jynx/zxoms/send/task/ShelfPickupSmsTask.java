package com.jygjexp.jynx.zxoms.send.task;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.api.feign.RemoteReturnService;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.dto.SmsLogDto;
import com.jygjexp.jynx.zxoms.dto.SmsTemplateDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.service.*;

import com.jygjexp.jynx.zxoms.send.utils.ALiYunSms;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import freemarker.template.Configuration;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenchang
 * @Description: 【短信】自提通知
 * @Date: 2024/10/9 22:40
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ShelfPickupSmsTask {
    private final NbSmsTemplateService nbSmsTemplateService;
    private final NbOrderPathService nbOrderPathService;
    private final NbOrderService nbOrderService;
    private final NbSmsLogService nbSmsLogService;
    private final NbShelfPkgLogService nbShelfPkgLogService;
    private final NbShelfService nbShelfService;
    private final NbTransferCenterService nbTransferCenterService;
    private final NbSortingCenterService nbSortingCenterService;

    @SneakyThrows
    @XxlJob("smsDeliveryHandler")
    public void smsDeliveryHandler() {
//        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务：【短信自提通知】于:{}，输入参数{}", LocalDateTime.now(), "运行中");
        shelfBizz(); // 自提短信业务
        deliverydBizz(); // 配送成功签收业务


        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【短信自提通知】执行结束，时间: {}", LocalDateTime.now());
    }

    /**
     * 配送成功签收通知
     * @throws Exception
     */
    private void deliverydBizz() throws Exception {
        NbSmsTemplateEntity st = nbSmsTemplateService.getOne(new LambdaQueryWrapper<NbSmsTemplateEntity>().eq(NbSmsTemplateEntity::getTplKey, SmsTemplateDto.KEY_DELIVERYED), false);
        String smsTemplateContent = st.getContent();

        // "select * from nb_order_path where order_status = ? and sms_status = ? order by path_id desc limit 100", Order.ORDER_STATUS_205_DELIVERED, OrderPath.SMS_STATUS_10_UNSEND);
        LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_205_DELIVERED)
                .eq(NbOrderPathEntity::getSmsStatus, OrderPathDto.SMS_STATUS_10_UNSEND);
        List<NbOrderPathEntity> ops = nbOrderPathService.list(wrapper);

        for (NbOrderPathEntity op : ops) {
            // "select order_id, dest_name, pkg_no, dest_tel from nb_order where order_id = ? limit 1", op.getOrderId());
            NbOrderEntity order = nbOrderService.getById(op.getOrderId());
            String smsContent = smsTemplateContent.replace("#(customerName)", order.getDestName()).replace("#(trackingNumber)", order.getPkgNo());

            NbSmsLogEntity sl = new NbSmsLogEntity();
            sl.setTemplateId(st.getTemplateId());
            sl.setOrderId(order.getOrderId().toString());
            sl.setPathId(op.getPathId());
            sl.setSmsType(SmsLogDto.SMS_TYPE_2_DELIVERED);
            sl.setMobile(order.getDestTel());
            sl.setContent(smsContent);
            sl.setDriverId(op.getDriverId());
            sl.setSendTime(new Date());
            sl.setSmsStatus(SmsLogDto.SMS_STATUS_10_UNSEND);
            nbSmsLogService.save(sl);

            try {
                if (StrUtil.isNotBlank(sl.getMobile())) {
                    ALiYunSms kit = new ALiYunSms();
                    R r = kit.sentMes(sl.getMobile(), smsContent);
                    XxlJobHelper.log("发送结果：签收通知：mobile=" + sl.getMobile() + ",pkgNo=" + order.getPkgNo() + ",=>" + r);
                    if (r.isOk()) {
                        op.setSmsStatus(OrderPathDto.SMS_STATUS_15_SEND_SUC);
                        op.setSmsDate(new Date());
                        nbOrderPathService.updateById(op);

                        sl.setSmsStatus(SmsLogDto.SMS_STATUS_15_SEND_SUC);
                        nbSmsLogService.updateById(sl);
                        XxlJobHelper.log("发送成功" + sl.getMobile());
                    } else {
                        op.setSmsStatus(OrderPathDto.SMS_STATUS_20_SEND_FAIL);
                        nbOrderPathService.updateById(op);
                        XxlJobHelper.log("发送失败" + sl.getMobile() + ",orderId=" + order.getOrderId());

                        sl.setSmsStatus(SmsLogDto.SMS_STATUS_20_SEND_FAIL);
                        nbSmsLogService.updateById(sl);
                    }
                } else {
                    XxlJobHelper.log("手机号为空，跳过：mobile=" + sl.getMobile() + ",orderId=" + order.getOrderId());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 自提通知
     * @throws Exception
     */
    private void shelfBizz() throws Exception {
        // "select * from nb_shelf_pkg_log where is_sms_notify = false and sms_fail_count < 5 limit 1");
        LambdaQueryWrapper<NbShelfPkgLogEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NbShelfPkgLogEntity::getIsSmsNotify, false)
                .lt(NbShelfPkgLogEntity::getSmsFailCount, 5);
        List<NbShelfPkgLogEntity> logs = nbShelfPkgLogService.list(wrapper);
        for (NbShelfPkgLogEntity pkgLog : logs) {
            int orderId = pkgLog.getOrderId();
            NbOrderEntity order = nbOrderService.getById(orderId);
            String mobile = order.getDestTel();
            if (mobile.contains(",") || mobile.contains("#")) {
                XxlJobHelper.log("手机号包含,/#，为网络电话转接，跳过：mobile=" + mobile + ",orderId=" + order.getOrderId());
                pkgLog.setIsSmsNotify(true);
                nbShelfPkgLogService.updateById(pkgLog);
                return;
            }
            if (mobile.contains("-")) {
                mobile = mobile.replaceAll("-", "");
            }
            if (mobile.startsWith("0001")) {
                mobile = mobile.substring(4);
            }

            NbSmsTemplateEntity st = nbSmsTemplateService.getOne(new LambdaQueryWrapper<NbSmsTemplateEntity>().eq(NbSmsTemplateEntity::getTplKey, SmsTemplateDto.KEY_SHELF_PUTAWAY), false);
            String smsTemplateContent = st.getContent();

            // 取件地址，取件时间，订单号，货架号
            Integer shelfId = pkgLog.getShelfId();
            NbShelfEntity shelf = nbShelfService.getById(shelfId);

            String address = "";
            String businessHours = "";
            String timezone = "";
            String serviceTel = "";
            if (ObjectUtil.isNotNull(shelf)) {
                if (shelf.getTcId() > 0) {
                    NbTransferCenterEntity tc = nbTransferCenterService.getById(shelf.getTcId());
                    address = tc.getAddress();
                    businessHours = tc.getBusinessHours();
                    serviceTel = tc.getServiceTel();

                    NbSortingCenterEntity sc = nbSortingCenterService.getById(tc.getScId());
                    timezone = sc.getScTimezone();
                } else if (shelf.getScId() > 0){
                    NbSortingCenterEntity sc = nbSortingCenterService.getById(shelf.getScId());
                    address = sc.getAddress();
                    businessHours = sc.getBusinessHours();
                    timezone = sc.getScTimezone();
                    serviceTel = sc.getServiceTel();
                }
            }
            String pkgNo = order.getPickNo();
            String putawayCode = pkgLog.getPutawayCode();
            String pickTime = Instant.ofEpochSecond(pkgLog.getPutawayTime()).atZone(ZoneId.of(timezone)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));

            String content = smsTemplateContent.replace("#(customerName)", order.getDestName()).replace("#(pickupAddress)", address).replace("#(orderNumber)", pkgNo)
                    .replace("#(shelfNumber)", putawayCode).replace("#(pickupTime)", pickTime).replace("#(businessHours)", businessHours)
                    .replace("#(serviceTel)", serviceTel).replace("#(trackingNumber)", order.getPkgNo());

            NbSmsLogEntity sl = new NbSmsLogEntity();
            sl.setTemplateId(st.getTemplateId());
            sl.setOrderId(order.getOrderId().toString());
            sl.setMobile(order.getDestTel());
            sl.setContent(content);
            sl.setDriverId(0);
            sl.setSendTime(new Date());
            sl.setSmsStatus(SmsLogDto.SMS_STATUS_10_UNSEND);
            sl.setSmsType(SmsLogDto.SMS_TYPE_1_SELF_PICKUP);
            nbSmsLogService.save(sl);

            try {
                ALiYunSms kit = new ALiYunSms();
                if (StrUtil.isNotBlank(sl.getMobile())) {
                    R r = kit.sentMes(sl.getMobile(), content);
                    XxlJobHelper.log("发送结果：自提通知：mobile=" + mobile + ",pkgNo=" + pkgNo + ",=>" + r);
                    if (r.isOk()) {
                        pkgLog.setIsSmsNotify(true);
                        pkgLog.setSmsNotifyTime(new Date());
                        nbShelfPkgLogService.updateById(pkgLog);

                        sl.setSmsStatus(SmsLogDto.SMS_STATUS_15_SEND_SUC);
                        nbSmsLogService.updateById(sl);
                        XxlJobHelper.log("发送成功" + mobile + ",orderId=" + order.getOrderId());
                    } else {
                        pkgLog.setSmsFailCount(pkgLog.getSmsFailCount() + 1);
                        nbShelfPkgLogService.updateById(pkgLog);
                        XxlJobHelper.log("发送失败" + mobile + ",orderId=" + order.getOrderId());

                        sl.setSmsStatus(SmsLogDto.SMS_STATUS_20_SEND_FAIL);
                        nbSmsLogService.updateById(sl);
                    }
                } else {
                    XxlJobHelper.log("手机号为空，跳过：orderId=" + order.getOrderId());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 短信失败通知
     */
    private void failure211Bizz() throws Exception {
        NbSmsTemplateEntity st = nbSmsTemplateService.getOne(new LambdaQueryWrapper<NbSmsTemplateEntity>().eq(NbSmsTemplateEntity::getTplKey, SmsTemplateDto.KEY_DELIVERY_FAILURE), false);
        String smsTemplateContent = st.getContent();

        // "select * from nb_order_path where order_status = ? and sms_status = ? order by path_id desc limit 100", Order.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1, OrderPath.SMS_STATUS_10_UNSEND);
        LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<NbOrderPathEntity>().eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1)
                .eq(NbOrderPathEntity::getSmsStatus, OrderPathDto.SMS_STATUS_10_UNSEND);
        List<NbOrderPathEntity> ops = nbOrderPathService.list(wrapper);
        for (NbOrderPathEntity op : ops) {
            // "select order_id, dest_name, pkg_no, dest_tel from nb_order where order_id = ? limit 1", op.getOrderId());
            NbOrderEntity order = nbOrderService.getById(op.getOrderId());
            String smsContent = smsTemplateContent.replace("#(customerName)", order.getDestName()).replace("#(trackingNumber)", order.getPkgNo());

            NbSmsLogEntity sl = new NbSmsLogEntity();
            sl.setTemplateId(st.getTemplateId());
            sl.setOrderId(order.getOrderId().toString());
            sl.setPathId(op.getPathId());
            sl.setSmsType(SmsLogDto.SMS_TYPE_3_DELIVERY_FAILURE);
            sl.setMobile(order.getDestTel());
            sl.setContent(smsContent);
            sl.setDriverId(op.getDriverId());
            sl.setSendTime(new Date());
            sl.setSmsStatus(SmsLogDto.SMS_STATUS_10_UNSEND);
            nbSmsLogService.save(sl);
            try {
                if (StrUtil.isBlank(sl.getMobile())) {
                    ALiYunSms kit = new ALiYunSms();
                    R r = kit.sentMes(sl.getMobile(), smsContent);
                    XxlJobHelper.log("发送结果：失败通知：mobile=" + sl.getMobile() + ",pkgNo=" + order.getPkgNo() + ",=>" + r);
                    if (r.isOk()) {
                        op.setSmsStatus(OrderPathDto.SMS_STATUS_15_SEND_SUC);
                        op.setSmsDate(new Date());
                        nbOrderPathService.updateById(op);

                        sl.setSmsStatus(SmsLogDto.SMS_STATUS_15_SEND_SUC);
                        nbSmsLogService.updateById(sl);
                        XxlJobHelper.log("发送成功" + sl.getMobile());
                    } else {
                        op.setSmsStatus(OrderPathDto.SMS_STATUS_20_SEND_FAIL);
                        nbOrderPathService.updateById(op);

                        XxlJobHelper.log("发送失败" + sl.getMobile() + ",orderId=" + order.getOrderId());

                        sl.setSmsStatus(SmsLogDto.SMS_STATUS_20_SEND_FAIL);
                        nbSmsLogService.updateById(sl);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}
