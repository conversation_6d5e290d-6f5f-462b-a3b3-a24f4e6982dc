package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbJySyncLogEntity;
import com.jygjexp.jynx.zxoms.send.service.NbJySyncLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 佳邮同步日志
 *
 * <AUTHOR>
 * @date 2024-10-17 00:49:00
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbJySyncLog" )
@Tag(description = "nbJySyncLog" , name = "佳邮同步日志管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbJySyncLogController {

    private final  NbJySyncLogService nbJySyncLogService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbJySyncLog 佳邮同步日志
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbJySyncLog_view')" )
    public R getNbJySyncLogPage(@ParameterObject Page page, @ParameterObject NbJySyncLogEntity nbJySyncLog) {
        LambdaQueryWrapper<NbJySyncLogEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbJySyncLogService.page(page, wrapper));
    }


    /**
     * 通过id查询佳邮同步日志
     * @param logId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{logId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbJySyncLog_view')" )
    public R getById(@PathVariable("logId" ) Integer logId) {
        return R.ok(nbJySyncLogService.getById(logId));
    }

    /**
     * 新增佳邮同步日志
     * @param nbJySyncLog 佳邮同步日志
     * @return R
     */
    @Operation(summary = "新增佳邮同步日志" , description = "新增佳邮同步日志" )
    @SysLog("新增佳邮同步日志" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbJySyncLog_add')" )
    public R save(@RequestBody NbJySyncLogEntity nbJySyncLog) {
        return R.ok(nbJySyncLogService.save(nbJySyncLog));
    }

    /**
     * 修改佳邮同步日志
     * @param nbJySyncLog 佳邮同步日志
     * @return R
     */
    @Operation(summary = "修改佳邮同步日志" , description = "修改佳邮同步日志" )
    @SysLog("修改佳邮同步日志" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbJySyncLog_edit')" )
    public R updateById(@RequestBody NbJySyncLogEntity nbJySyncLog) {
        return R.ok(nbJySyncLogService.updateById(nbJySyncLog));
    }

    /**
     * 通过id删除佳邮同步日志
     * @param ids logId列表
     * @return R
     */
    @Operation(summary = "通过id删除佳邮同步日志" , description = "通过id删除佳邮同步日志" )
    @SysLog("通过id删除佳邮同步日志" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbJySyncLog_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbJySyncLogService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbJySyncLog 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbJySyncLog_export')" )
    public List<NbJySyncLogEntity> export(NbJySyncLogEntity nbJySyncLog,Integer[] ids) {
        return nbJySyncLogService.list(Wrappers.lambdaQuery(nbJySyncLog).in(ArrayUtil.isNotEmpty(ids), NbJySyncLogEntity::getLogId, ids));
    }
}