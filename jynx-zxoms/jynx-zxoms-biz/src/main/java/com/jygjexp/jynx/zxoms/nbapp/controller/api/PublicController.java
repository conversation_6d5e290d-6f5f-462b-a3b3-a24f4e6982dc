package com.jygjexp.jynx.zxoms.nbapp.controller.api;

import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton.ReCaptchaCheck;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiRequestParamsVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.validation.constraints.NotBlank;

/**
 * @Author: chenchang
 * @Description: API-开发接口-佳邮轨迹查询
 * @Date: 2024/11/5 11:08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/public")
@Tag(description = "apppublic", name = "APP-开放接口")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PublicController {
    private final NbOrderService orderService;

    /**
     * 官网获取轨迹
     */
    @ReCaptchaCheck
    @Operation(summary = "官网获取轨迹", description = "官网获取轨迹")
    @PostMapping("/track")
    @Inner(value = false)
    public OldResult track(String pkgNo) {
        return orderService.getTrack(pkgNo);
    }

    /**
     * 官网获取批量轨迹
     */
    @ReCaptchaCheck
    @Operation(summary = "官网获取批量轨迹", description = "官网获取批量轨迹")
    @PostMapping("/tracks")
    @Inner(value = false)
    public OldResult tracks(@RequestParam("pkgNos") String pkgNos, @RequestParam(value = "zip", required = false) String zip) {
        return orderService.getTracks(pkgNos, zip);
    }

    /**
     * 官网获取批量轨迹-人机校验不通过
     */
    @Operation(summary = "官网获取批量轨迹-人机校验不通过", description = "官网获取批量轨迹-人机校验不通过")
    @PostMapping("/tracksNoCaptcha")
    @Inner(value = false)
    public OldResult tracksNoCaptcha(@RequestParam("pkgNos") String pkgNos, @RequestParam(value = "zip", required = false) String zip) {
        return orderService.getTracks(pkgNos, zip);
    }

    /**
     * 开放接口-_17track
     * @param trackingNumber
     * @return
     */
    @Operation(summary = "APP-开放接口-_17track", description = "APP-开放接口-_17track")
    @GetMapping("/_17track")
    @Inner(value = false)
    public OldResult _17track(String trackingNumber) {
        return orderService.get_17Track(trackingNumber);
    }

}
