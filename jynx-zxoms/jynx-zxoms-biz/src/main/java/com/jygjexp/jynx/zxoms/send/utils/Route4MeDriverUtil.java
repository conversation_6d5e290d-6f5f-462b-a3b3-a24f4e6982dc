package com.jygjexp.jynx.zxoms.send.utils;

import com.jygjexp.jynx.common.core.util.R;
import com.route4me.sdk.exception.APIException;
import com.route4me.sdk.services.vehicles.Vehicles;
import com.route4me.sdk.services.vehicles.VehiclesManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/11 20:56
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class Route4MeDriverUtil {
    private static final String key = "099992A5644B5669E2FC89428AE84801";
//	private static final String key = xx.getConfig("r4mApiKey");

    public R createVehicle(Vehicles vehicle) {
        log.info("创建车辆的源数据：" + vehicle);
        VehiclesManager manager = new VehiclesManager(key);
        try {
            vehicle = manager.newVehicle(vehicle);

            log.info("车辆创建记录：" + vehicle);

            R ret = R.ok();
            ret.setData(vehicle);
            return ret;

        } catch (APIException e) {
            e.printStackTrace();

            R ret = R.failed();
            ret.failed("errmsg", e.getMessage());
            return ret;
        }
    }

    public R updateVehicle(Vehicles vehicle) {
        log.info("更新车辆的源数据：" + vehicle);
        VehiclesManager manager = new VehiclesManager(key);

        try {
            vehicle = manager.updateVehicle(vehicle);
            log.info("车辆更新记录：" + vehicle);

            R ret = R.ok();
            ret.setData(vehicle);
            return ret;
        } catch (APIException e) {
            e.printStackTrace();

            R ret = R.failed();
            ret.failed("errmsg", e.getMessage());
            return ret;
        }
    }

}
