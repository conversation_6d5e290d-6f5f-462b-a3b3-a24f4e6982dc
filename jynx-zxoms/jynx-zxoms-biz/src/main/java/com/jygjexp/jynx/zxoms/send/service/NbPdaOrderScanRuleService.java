package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbPdaOrderScanRuleEntity;
import com.jygjexp.jynx.zxoms.vo.NbPdaOrderScanRulePageVo;

public interface NbPdaOrderScanRuleService extends IService<NbPdaOrderScanRuleEntity> {

    Page<NbPdaOrderScanRulePageVo> search(Page page, NbPdaOrderScanRuleEntity entity);
}