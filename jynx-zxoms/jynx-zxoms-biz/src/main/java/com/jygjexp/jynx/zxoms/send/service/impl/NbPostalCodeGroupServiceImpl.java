package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbPostalCodeGroupEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbPostalCodeGroupMapper;
import com.jygjexp.jynx.zxoms.send.service.NbPostalCodeGroupService;
import com.jygjexp.jynx.zxoms.vo.NbPostalCodeGroupPageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 邮编分区组
 *
 * <AUTHOR>
 * @date 2025-01-09 15:08:19
 */
@Service
@RequiredArgsConstructor
public class NbPostalCodeGroupServiceImpl extends ServiceImpl<NbPostalCodeGroupMapper, NbPostalCodeGroupEntity> implements NbPostalCodeGroupService {
    private final NbPostalCodeGroupMapper nbPostalCodeGroupMapper;

    @Override
    public Page<NbPostalCodeGroupPageVo> search(Page page, NbPostalCodeGroupPageVo vo) {
        LambdaQueryWrapper<NbPostalCodeGroupEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(vo.getNameZh()), NbPostalCodeGroupEntity::getNameZh, vo.getNameZh())
                .like(StrUtil.isNotBlank(vo.getNameEn()), NbPostalCodeGroupEntity::getNameEn, vo.getNameEn());

        String creatTime = vo.getCreatTimeVo();   // 创建时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        if (creatTime != null) {
            int splitIndex = creatTime.indexOf(":", creatTime.indexOf(":") + 1) + 3;
            String startTime = creatTime.substring(0, splitIndex);
            String endTime = creatTime.substring(splitIndex + 1);
            wrapper.ge(NbPostalCodeGroupEntity::getCreateTime, startTime).le(NbPostalCodeGroupEntity::getCreateTime, endTime);
        }
        wrapper.orderByDesc(NbPostalCodeGroupEntity::getCreateTime);

        return nbPostalCodeGroupMapper.selectPage(page, wrapper);
    }

}