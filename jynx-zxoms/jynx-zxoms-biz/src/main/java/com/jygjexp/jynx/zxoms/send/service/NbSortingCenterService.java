package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.entity.NbSortingCenterEntity;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiRequestParamsVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;
import com.jygjexp.jynx.zxoms.vo.NbSortingCenterPageVo;
import com.jygjexp.jynx.zxoms.send.vo.SortingCenterExcelVo;

import java.util.List;

public interface NbSortingCenterService extends IService<NbSortingCenterEntity> {
    List<NbSortingCenterPageVo> listSc();   // 查询登录用户的分拣中心列表-公共接口

    List<NbSortingCenterPageVo> listAllSc();   // 查询所有的分拣中心列表

    R doCheckCode(String scanCode, NbDriverEntity loginDriver);

    List<NbSortingCenterEntity> findScByUserId(Integer userId);

    // 导出分拣中心到Excel
    List<SortingCenterExcelVo> getExcel(NbSortingCenterPageVo vo, Integer[] ids);

    Page<NbSortingCenterPageVo> search(Page page, NbSortingCenterPageVo vo);

    List<Integer> getNbdScIdThisUser(); // 获取用户登录下的分拣中心

    OldResult getWarehouseList(ApiRequestParamsVo paramsVo);  // 获取仓库列表

}