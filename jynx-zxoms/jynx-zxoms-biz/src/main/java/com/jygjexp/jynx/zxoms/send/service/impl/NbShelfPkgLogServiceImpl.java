package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbShelfEntity;
import com.jygjexp.jynx.zxoms.entity.NbShelfPkgLogEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbShelfPkgLogMapper;
import com.jygjexp.jynx.zxoms.send.service.NbShelfPkgLogService;
import com.jygjexp.jynx.zxoms.send.vo.NbShelfPkgLogExcelVo;
import com.jygjexp.jynx.zxoms.vo.NbShelfPkgLogPageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 存储柜日志(货架)
 *
 * <AUTHOR>
 * @date 2024-10-16 23:27:50
 */
@Service
@RequiredArgsConstructor
public class NbShelfPkgLogServiceImpl extends ServiceImpl<NbShelfPkgLogMapper, NbShelfPkgLogEntity> implements NbShelfPkgLogService {
    private final NbShelfPkgLogMapper nbShelfPkgLogMapper;

    // 在架包裹分页查询
    @Override
    public Page<NbShelfPkgLogPageVo> search(Page page, NbShelfPkgLogPageVo vo) {
        //select shelf_id, shelf_code from nb_shelf; ds=nbd;
        //select order_id, pkg_no, dest_name, dest_tel from nb_order; ds=nbd;
        //putaway_driver_id(上架者)	select driver_id, first_name from nb_driver; ds=nbd;
        //pickup_driver_id(下架者)	select driver_id, first_name from nb_driver; ds=nbd;
        MPJLambdaWrapper wrapper = getWrapper(vo, null);
        return nbShelfPkgLogMapper.selectJoinPage(page, NbShelfPkgLogPageVo.class, wrapper);
    }

    // 在架包裹导出Excel
    @Override
    public List<NbShelfPkgLogExcelVo> getExcel(NbShelfPkgLogPageVo vo, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids);
        return nbShelfPkgLogMapper.selectJoinList(NbShelfPkgLogExcelVo.class, wrapper);
    }

    private MPJLambdaWrapper getWrapper(NbShelfPkgLogPageVo vo, Integer[] ids) {
        MPJLambdaWrapper<NbShelfPkgLogEntity> wrapper = new MPJLambdaWrapper<NbShelfPkgLogEntity>();
        String selectSql = "t3.driver_name as putawayDriverName, t4.driver_name as pickupDriverName";
        String leftJoinSql = "nb_driver t3 ON t.putaway_driver_id = t3.driver_id LEFT JOIN nb_driver t4 ON t.pickup_driver_id = t4.driver_id ";
        wrapper.selectAll(NbShelfPkgLogEntity.class)
                .select(NbOrderEntity::getOrderId)
                .selectAs(NbShelfEntity::getShelfCode, NbShelfPkgLogPageVo.Fields.shelfCode)
                .select(selectSql)
                .eq(ObjectUtil.isNotNull(vo.getShelfId()), NbShelfPkgLogEntity::getShelfId, vo.getShelfId())                            // 条件查询-货架
                .eq(StrUtil.isNotBlank(vo.getPutawayCode()), NbShelfPkgLogEntity::getPutawayCode, vo.getPutawayCode())                  // 条件查询-上架编码
                .eq(ObjectUtil.isNotNull(vo.getPutawayDriverId()), NbShelfPkgLogEntity::getPutawayDriverId, vo.getPutawayDriverId())    // 条件查询-上架者
                .eq(ObjectUtil.isNotNull(vo.getPickupDriverId()), NbShelfPkgLogEntity::getPickupDriverId, vo.getPickupDriverId())       // 条件查询-下架者
                .leftJoin(NbShelfEntity.class, NbShelfEntity::getShelfId, NbShelfPkgLogEntity::getShelfId)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbShelfPkgLogEntity::getOrderId)
                .leftJoin(leftJoinSql);

        String putawayDatetime = vo.getQueryTime(); // 上架时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        if (putawayDatetime != null){
            int splitIndex = putawayDatetime.indexOf(":", putawayDatetime.indexOf(":") + 1) + 3;
            String startPutawayTime = putawayDatetime.substring(0, splitIndex);
            String endPutAwayTime = putawayDatetime.substring(splitIndex + 1);
            wrapper.ge(NbShelfPkgLogEntity::getPutawayDatetime, startPutawayTime).le(NbShelfPkgLogEntity::getPutawayDatetime, endPutAwayTime);  // 条件查询-上架时间
        }
        wrapper.in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbShelfPkgLogEntity::getLogId, ids)
                .orderByDesc(NbShelfPkgLogEntity::getPutawayTime);
        return wrapper;
    }
}