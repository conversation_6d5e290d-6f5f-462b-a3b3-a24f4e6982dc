package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbDriverScanedLqReportEntity;
import com.jygjexp.jynx.zxoms.entity.NbTransferBatchEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbDriverScanedLqReportMapper;
import com.jygjexp.jynx.zxoms.send.service.NbDriverScanedLqReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
/**
 * 扫描后提交的批次针对路区的报表
 *
 * <AUTHOR>
 * @date 2024-11-02 21:23:39
 */
@Service
@RequiredArgsConstructor
public class NbDriverScanedLqReportServiceImpl extends ServiceImpl<NbDriverScanedLqReportMapper, NbDriverScanedLqReportEntity> implements NbDriverScanedLqReportService {
    private final NbDriverScanedLqReportMapper nbDriverScanedLqReportMapper;

    @Override
    public Page search(Page page, NbDriverScanedLqReportEntity nbDriverScanedLqReport) {
        //  select batch_id, batch_no from nb_transfer_batch; ds=nbd;
        MPJLambdaWrapper<NbDriverScanedLqReportEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbDriverScanedLqReportEntity.class)
                .select(NbTransferBatchEntity::getBatchId,
                        NbTransferBatchEntity::getBatchNo)
                .leftJoin(NbTransferBatchEntity.class, NbTransferBatchEntity::getBatchId, NbDriverScanedLqReportEntity::getTransferBatchId);
//                .orderByDesc(NbDriverScanedLqReportEntity::getReportId);
        return nbDriverScanedLqReportMapper.selectPage(page, wrapper);
    }
}