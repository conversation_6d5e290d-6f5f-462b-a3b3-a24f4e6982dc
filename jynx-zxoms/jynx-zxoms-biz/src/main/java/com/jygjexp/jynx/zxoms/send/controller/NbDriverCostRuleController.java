package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbDriverCostRuleEntity;
import com.jygjexp.jynx.zxoms.send.service.NbDriverCostRuleService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 司机费用规则主表
 *
 * <AUTHOR>
 * @date 2024-10-14 16:21:53
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbDriverCostRule" )
@Tag(description = "nbDriverCostRule" , name = "司机费用规则管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbDriverCostRuleController {

    private final NbDriverCostRuleService nbDriverCostRuleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param entity 司机费用规则
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/search" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverCostRule_view')" )
    public R getNbDriverCostRulePage(@ParameterObject Page page, @ParameterObject NbDriverCostRuleEntity entity) {
        return R.ok(nbDriverCostRuleService.search(page, entity));
    }


    /**
     * 通过id查询司机费用规则
     * @param ruleId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{ruleId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverCostRule_view')" )
    public R getById(@PathVariable("ruleId" ) Integer ruleId) {
        return R.ok(nbDriverCostRuleService.getById(ruleId));
    }

    /**
     * 新增司机费用规则
     * @param basicNbDriverCostRule 司机费用规则
     * @return R
     */
    @Operation(summary = "新增司机费用规则" , description = "新增司机费用规则" )
    @SysLog("新增司机费用规则" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverCostRule_add')" )
    public R save(@RequestBody NbDriverCostRuleEntity basicNbDriverCostRule) {
        return R.ok(nbDriverCostRuleService.save(basicNbDriverCostRule));
    }

    /**
     * 修改司机费用规则
     * @param basicNbDriverCostRule 司机费用规则
     * @return R
     */
    @Operation(summary = "修改司机费用规则" , description = "修改司机费用规则" )
    @SysLog("修改司机费用规则" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverCostRule_edit')" )
    public R updateById(@RequestBody NbDriverCostRuleEntity basicNbDriverCostRule) {
        return R.ok(nbDriverCostRuleService.updateById(basicNbDriverCostRule));
    }

    /**
     * 通过id删除司机费用规则
     * @param ids ruleId列表
     * @return R
     */
    @Operation(summary = "通过id删除司机费用规则" , description = "通过id删除司机费用规则" )
    @SysLog("通过id删除司机费用规则" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverCostRule_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbDriverCostRuleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param basicNbDriverCostRule 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverCostRule_export')" )
    public List<NbDriverCostRuleEntity> export(NbDriverCostRuleEntity basicNbDriverCostRule, Integer[] ids) {
        return nbDriverCostRuleService.list(Wrappers.lambdaQuery(basicNbDriverCostRule).in(ArrayUtil.isNotEmpty(ids), NbDriverCostRuleEntity::getRuleId, ids));
    }
}