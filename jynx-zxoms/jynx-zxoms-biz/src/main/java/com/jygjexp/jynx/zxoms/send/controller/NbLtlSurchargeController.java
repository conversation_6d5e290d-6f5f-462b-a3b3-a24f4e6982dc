package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbLtlSurchargeEntity;
import com.jygjexp.jynx.zxoms.send.service.NbLtlSurchargeService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import org.springdoc.api.annotations.ParameterObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 附加费
 *
 * <AUTHOR>
 * @date 2024-11-08 11:53:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbLtlSurcharge" )
//@Tag(description = "nbLtlSurcharge" , name = "附加费管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbLtlSurchargeController {

    private final  NbLtlSurchargeService nbLtlSurchargeService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbLtlSurcharge 附加费
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlSurcharge_view')" )
    public R getNbLtlSurchargePage(@ParameterObject Page page, @ParameterObject NbLtlSurchargeEntity nbLtlSurcharge) {
        LambdaQueryWrapper<NbLtlSurchargeEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbLtlSurchargeService.page(page, wrapper));
    }


    /**
     * 通过id查询附加费
     * @param surchargeId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{surchargeId}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlSurcharge_view')" )
    public R getById(@PathVariable("surchargeId" ) Integer surchargeId) {
        return R.ok(nbLtlSurchargeService.getById(surchargeId));
    }

    /**
     * 新增附加费
     * @param nbLtlSurcharge 附加费
     * @return R
     */
//    @Operation(summary = "新增附加费" , description = "新增附加费" )
//    @SysLog("新增附加费" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlSurcharge_add')" )
    public R save(@RequestBody NbLtlSurchargeEntity nbLtlSurcharge) {
        return R.ok(nbLtlSurchargeService.save(nbLtlSurcharge));
    }

    /**
     * 修改附加费
     * @param nbLtlSurcharge 附加费
     * @return R
     */
//    @Operation(summary = "修改附加费" , description = "修改附加费" )
//    @SysLog("修改附加费" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlSurcharge_edit')" )
    public R updateById(@RequestBody NbLtlSurchargeEntity nbLtlSurcharge) {
        return R.ok(nbLtlSurchargeService.updateById(nbLtlSurcharge));
    }

    /**
     * 通过id删除附加费
     * @param ids surchargeId列表
     * @return R
     */
//    @Operation(summary = "通过id删除附加费" , description = "通过id删除附加费" )
//    @SysLog("通过id删除附加费" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlSurcharge_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbLtlSurchargeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbLtlSurcharge 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlSurcharge_export')" )
    public List<NbLtlSurchargeEntity> export(NbLtlSurchargeEntity nbLtlSurcharge,Integer[] ids) {
        return nbLtlSurchargeService.list(Wrappers.lambdaQuery(nbLtlSurcharge).in(ArrayUtil.isNotEmpty(ids), NbLtlSurchargeEntity::getSurchargeId, ids));
    }
}