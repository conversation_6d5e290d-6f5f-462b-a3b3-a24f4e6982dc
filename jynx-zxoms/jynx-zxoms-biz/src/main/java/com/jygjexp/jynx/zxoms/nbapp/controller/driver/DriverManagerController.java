package com.jygjexp.jynx.zxoms.nbapp.controller.driver;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.zxoms.send.constants.NBDConstants;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.*;

import com.jygjexp.jynx.zxoms.send.utils.ConfigUtil;
import com.jygjexp.jynx.zxoms.dto.OrderBatchDto;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.utils.OrderTagUtil;
import com.jygjexp.jynx.zxoms.nbapp.utils.RedisUtil;
import com.jygjexp.jynx.zxoms.nbapp.vo.TransferBatchOrderVo;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.utils.NBDUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: xiongpengfei
 * @Description: APP司机-仓库方面
 * @Date: 2024/11/8 9:38
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver/manager" )
@Tag(description = "appDriverManager" , name = "APP司机-仓库方面" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Slf4j
public class DriverManagerController extends BaseController {
    private final NbOrderService orderService;
    private final NbOrderPathService orderPathService;
    private final NbTransferBatchService transferBatchService;
    private final NbTransferBatchOrderService orderBatchOrderService;
    private final NbTransferCenterService transferCenterService;
    private final NbSortingCenterService sortingCenterService;
    private final NbOrderBatchService orderBatchService;
    private final RedisTemplate<String, String> redisTemplate;
    private final CommonDataUtil commonDataUtil;
    private final ConfigUtil configUtil;
    private final NbShelfPkgLogService shelfPkgLogService;
    private final RedisUtil redisUtil;

    /**
     * 扫描返仓     value:  pkgNo:包裹号
     *
     * 20230929 改为失败的直接扫描到Return office  from tranait
     */
    @Operation(summary = "扫描返仓" , description = "扫描返仓" )
    @PostMapping("/returnWarehousing")
    public R returnWarehousing(@RequestParam("pkgNo") @NotBlank(message = "pkgNo.is.required") String pkgNo) {
        // "select * from nb_order where pkg_no = ? limit 1", pkgNo);
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo,pkgNo),false);
        if (order == null){
            return LocalizedR.failed("driverorder.package.does.not.exist", pkgNo);
        }

        List<Integer> accessStatus = Lists.newArrayList();
        accessStatus.add(OrderDto.ORDER_STATUS_206_FAILED_DELIVERY_WRONG_ADDRESS);
        accessStatus.add(OrderDto.ORDER_STATUS_210_FAILED_DELIVERY);
        accessStatus.add(OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1);
        accessStatus.add(OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2);
        accessStatus.add(OrderDto.ORDER_STATUS_207_SCANNED_PARCEL_MISSING);
        accessStatus.add(OrderDto.ORDER_STATUS_208_SCANNED_PARCEL_FOUND);
        accessStatus.add(OrderDto.ORDER_STATUS_280_FAILURE);


        if (!accessStatus.contains(order.getOrderStatus())) {
            return LocalizedR.failed("driverorder.only.failed.orders.can.be.scanned", Optional.ofNullable(null));
        }

        //修改订单状态
        order.setOrderStatus(OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT);
        orderService.updateById(order);

        NbDriverEntity loginDriver = getLoginDriver();

        Double[] latlng = getLatLng();
        Double lat = null, lng = null;
        if (latlng != null) {
            lat = latlng[0];
            lng = latlng[1];
        }

        String address = null;
        String zoneId = ZoneId.systemDefault().getId();

        NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(),SecurityUtils.getUser().getId(), lat, lng, address, zoneId);
        op.setScId(order.getScId());
        orderPathService.save(op);
        return  R.ok();
    }

    /**
     * 退件返仓列表
     */
    @Operation(summary = "退件返仓列表" , description = "退件返仓列表" )
    @GetMapping("/returnList")
    public R returnList() {
        //286所有对应的派送失败节点“206，210，211，212"状态的包裹，通过PDA的退件返仓扫描后生成
        //290所有对应”286，200“状态的包裹，通过PDA上架操作中扫描货架号再扫描包裹号后自动生成随机4位的上架自取号码，将号码写再面单上后放上对应货架及会生成此节点，并短信通知客人自取或电话联系客服安排二次派送。
        List<NbOrderEntity> nbOrderEntities = orderService.list(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getOrderStatus, 286).ge(NbOrderEntity::getDeliveryTry, 1));
        return R.ok(nbOrderEntities);
    }


    /**
     * 查看包裹信息
     */
    @Operation(summary = "查看包裹信息" , description = "查看包裹信息" )
    @PostMapping("/info")
    public R info(@RequestParam("pkgNo") String pkgNo) {
        if (pkgNo == null){
            return R.failed("-1", "package no is empty");
        }

        // "select * from nb_order where pkg_no = ? limit 1", pkgNo);
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo,pkgNo),false);
        if (order == null) {
            return R.failed("-1", "package not exist." + pkgNo);
        }
        JSONObject jo = new OrderDto().toDriverListJson(order);
        // "select tbo.*, tb.batch_code, tb.order_total from nb_transfer_batch_order tbo, nb_transfer_batch tb where tbo.batch_id = tb.batch_id and order_id = ? limit 1", order.getOrderId());
        TransferBatchOrderVo tbo = orderBatchOrderService.findFirstOrderId(order.getOrderId());
        if (tbo == null) {
            jo.put("isRoute", false);
        } else {
            jo.put("isRoute", true);
            jo.put("batchCode", tbo.getBatchCode());
            jo.put("orderTotal", tbo.getOrderTotal());
        }

        Integer tcId = order.getTcId();
        if (tcId != null && tcId > 0) {
            NbTransferCenterEntity tc = transferCenterService.getById(tcId);
            jo.set("tc", toAppJson(tc));

        }

        NbShelfPkgLogEntity shelfOne = shelfPkgLogService.getOne(new LambdaQueryWrapper<NbShelfPkgLogEntity>().eq(NbShelfPkgLogEntity::getOrderId, order.getOrderId()), false);
        if (shelfOne != null) {
            jo.put("putawayCode", shelfOne.getPutawayCode());
            jo.put("putawayTime", shelfOne.getPutawayTime());
        }

        return R.ok(jo);
    }


    /**
     *  Sorting
     */
    @Operation(summary = "分拣中心相关操作" , description = "分拣中心相关操作" )
    @PostMapping("/loadScan")
    public R loadScan(@RequestParam("pkgNo") String pkgNo) {
        if (pkgNo == null) {
            return R.failed("-1", "package no is empty");
        }
        // "select * from nb_order where pkg_no = ? limit 1", pkgNo);
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo,pkgNo),false);
        if (order == null) {
            return R.failed("-1", "package not exist." + pkgNo);
        }

        JSONObject jo =new OrderDto().toDriverListJson(order);
        // select tbo.*, tb.batch_code, tb.order_total, tb.batch_id, tb.start_time
        // from nb_transfer_batch_order tbo, nb_transfer_batch tb where tbo.batch_id = tb.batch_id and order_id = ? order by tb.batch_id desc limit 1", order.getOrderId());
        TransferBatchOrderVo tbo = orderBatchOrderService.findFirstOrderId(order.getOrderId());

        //获取路区订单信息
        NbTransferBatchOrderEntity tboEntity = orderBatchOrderService.getById(tbo.getBoId());

        if (tbo == null) {
            jo.put("isRoute", false);
        } else {
            jo.put("isRoute", true);
            jo.put("batchCode", tbo.getBatchCode());
            jo.put("orderTotal", tbo.getOrderTotal());
            jo.put("TbPickNo", tboEntity.getPickNo());

            if (tbo.getIsLoadScan() == false) {
                tboEntity.setIsLoadScan(true);
                tboEntity.setLoadScanTime(new Date());
                orderBatchOrderService.updateById(tboEntity);
            }

            // "select count(1) cont from nb_transfer_batch_order where batch_id = ? and is_load_scan = true",tbo.getInt("batch_id")).getInt("cont");
            Integer count = Math.toIntExact(orderBatchOrderService.count(new LambdaQueryWrapper<NbTransferBatchOrderEntity>()
                    .eq(NbTransferBatchOrderEntity::getBatchId, tbo.getBatchId()).eq(NbTransferBatchOrderEntity::getIsLoadScan, true)));
            jo.put("scanedTotal", count);
        }

        Integer tcId = order.getTcId();
        if (tcId != null && tcId > 0) {
            NbTransferCenterEntity tc = transferCenterService.getById(tcId);
            jo.put("tc", toAppJson(tc));
        }

        boolean isFailedOrderWithNoOs200 = false;
        try {
            String tag = order.getTag();
            //isFailedOrderWithNoOs200 = OrderTagKit.check(tag, OrderTagKit.TAG_ORDER_FAILED_IN_TRANSFER_BATCH_BUT_NO_OS_200); // 没有200状态的失败订单
            isFailedOrderWithNoOs200 = OrderTagUtil.check(tag, OrderTagUtil.TAG_ORDER_FAILED_IN_TRANSFER_BATCH_BUT_NO_OS_200);
        } catch (Exception e) {
            e.printStackTrace();
        }
        jo.put("isFailedOrderWithNoOs200", isFailedOrderWithNoOs200);

        // 2024-05-16 sophie 增加自动到200状态的处理 ↓↓↓↓↓
        if (order.getOrderStatus() < OrderDto.ORDER_STATUS_200_PARCEL_SCANNED) {
            NbDriverEntity loginDriver = getLoginDriver();
//            NbDriverEntity loginDriver = new NbDriverEntity();

            order.setOrderStatus(OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
            order.setLastRouteTime(new Date());
            orderService.updateById(order);


            Double[] latlng = getLatLng();
            Double lat = null, lng = null;
            if (latlng != null) {
                lat = latlng[0];
                lng = latlng[1];
            }

            NbSortingCenterEntity sc = sortingCenterService.getById(order.getScId());

            String address = null;
            if (sc != null) {
                Integer provinceId = sc.getProvinceId();
                Integer cityId = sc.getCityId();
                address = commonDataUtil.getAddress(provinceId, cityId);
            }

            //OrderPath op199 = new OrderPath().build(order.getOrderId(), Order.ORDER_STATUS_200_PARCEL_SCANNED, loginDriver.getDriverId(), lat, lng, address, sc.getScTimezone());
            NbOrderPathEntity op199 = new OrderPathDto().build(order.getOrderId(), OrderDto.ORDER_STATUS_200_PARCEL_SCANNED, SecurityUtils.getUser().getId(), lat, lng, address, sc.getScTimezone());
            op199.setScId(order.getScId());
            orderPathService.save(op199);

            // 20240620 检查，如果订单在某个路区内，而且该路区已经操作过start，则直接补充一个
            if (tbo != null && tbo.getStartTime() != null) {
                // 在路区里面，而且操作过start
                // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", order.getOrderId(), Order.ORDER_STATUS_204_IN_TRANSIT);
                NbOrderPathEntity op204 = orderPathService.getOne(new LambdaQueryWrapper<NbOrderPathEntity>().eq(NbOrderPathEntity::getOrderId,order.getOrderId())
                        .eq(NbOrderPathEntity::getOrderStatus,OrderDto.ORDER_STATUS_204_IN_TRANSIT),false);
                if (op204 == null) {
                    // 没有in transit节点
                    order.setOrderStatus(OrderDto.ORDER_STATUS_204_IN_TRANSIT);
                    order.setLastRouteTime(new Date());
                    orderService.updateById(order);

                    Integer incrMin = RandomUtils.nextInt(10, 30); // 增加的分钟
                    long time204 = System.currentTimeMillis() + 1000 * 60 * incrMin;

                    op204 = new OrderPathDto().createPath(order.getOrderId(), OrderDto.ORDER_STATUS_204_IN_TRANSIT, loginDriver != null ? loginDriver.getDriverId() : 0, lat, lng, address, sc.getScTimezone(), time204);
                    orderPathService.save(op204);

                    log.info("路区内订单被普扫时发现没有InTransit节点，自动补充。orderId=" + order.getOrderId() + ",to status=" + OrderDto.ORDER_STATUS_204_IN_TRANSIT);
                }
            }
        }else {
            log.info("订单状态已经大于200，跳过，orderId=" + order.getOrderId());
            return R.failed("The current status of the order cannot be operated，orderId=" + order.getOrderId());
        }
        // 2024-05-16 sophie 增加自动到200状态的处理 ↑↑↑↑↑
        return R.ok(jo);
    }

    /**
     * 自提签收
     */

    @Operation(summary = "自提签收" , description = "自提签收" )
    @PostMapping("/pickup")
    public R pickup(@RequestParam("pkgNo") String pkgNo) {
        NbDriverEntity loginDriver = getLoginDriver();

        // "select * from nb_order where pkg_no = ? limit 1", pkgNo);
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo, pkgNo), false);
        if (order == null) {
            return LocalizedR.failed("driverorder.package.does.not.exist", pkgNo);
        }

        if (order.getOrderStatus() >= OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER) {
            return LocalizedR.failed("driverorder.cannot.be.signed.for.in.the.warehouse", (OrderDto.orderStatusMap.getOrDefault(order.getOrderStatus(), "")));
        }

        if (order.getOrderStatus() == OrderDto.ORDER_STATUS_205_DELIVERED) {
            String method = "派送";
            if (order.getDeliveryStatus() == OrderDto.DELIVERY_STATUS_10_SELF_PICKUP) {
                method = "(签收)";
            }

            return R.failed("-2", "" + "   " + method + ":" + DateFormatUtils.format(order.getDeliveryedTime(), "yyyy-MM-dd HH:mm:ss"));
        }

        order.setOrderStatus(OrderDto.ORDER_STATUS_205_DELIVERED);
        order.setDeliveryedTime(new Date());
        order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_10_SELF_PICKUP);
        orderService.updateById(order);

        Integer scId = order.getScId();
        NbSortingCenterEntity sc = sortingCenterService.getById(scId);

        Double[] latlng = getLatLng();
        Double lat = null, lng = null;
        if (latlng != null) {
            lat = latlng[0];
        }
        if (latlng != null) {
            lng = latlng[1];
        }

        String address = null;
        if (sc != null) {
            Integer provinceId = sc.getProvinceId();
            Integer cityId = sc.getCityId();

            JSONObject province = commonDataUtil.getProvinceById(provinceId);
            JSONObject city = commonDataUtil.getCityById(cityId);

            address = province.getStr("enName") + " " + city.getStr("enName");
        }
        NbOrderPathEntity path = new OrderPathDto().createPath(order.getOrderId(), order.getOrderStatus(),loginDriver != null ? loginDriver.getDriverId() : 0, lat, lng, address, sc.getScTimezone());
        path.setScId(scId);
        path.setTcId(order.getTcId());
        orderPathService.save(path);

        return R.ok();
    }


    /**
     * 子批次
     */
    @Operation(summary = "子批次" , description = "子批次" )
    @PostMapping("/listEnableScanSubBatch")
    public R listEnableScanSubBatch() {
        NbDriverEntity loginDriver = getLoginDriver();
//        NbDriverEntity loginDriver = new NbDriverEntity();

        LambdaQueryWrapper wrapper = new LambdaQueryWrapper<NbOrderBatchEntity>()
                .eq(NbOrderBatchEntity::getIsMain,false)
                .eq(NbOrderBatchEntity::getIsScaning,true)
                .eq(NbOrderBatchEntity::getIsLtl,false) // 2024-02-17 卡派的不能直接扫描，容易出现多重循环问题
                .eq(NbOrderBatchEntity::getIsHeavyCargo,false) // 2024-03-07 重货批次不能直接扫描
                .in(NbOrderBatchEntity::getBatchType,2,3,4); // 2024-06-02 防止未到货批次进入盲扫

        // "select * from nb_order_batch " + sql.getSql(), sql.getParam());
        List<NbOrderBatchEntity> batchs = orderBatchService.list(wrapper);
        JSONArray ja = batchs.stream().map(batch -> {
            JSONObject jo = new JSONObject();
            jo.put("batchId", batch.getBatchId());
            jo.put("batchNo", batch.getBatchNo());
            jo.put("orderTotal", batch.getOrderTotal());
            jo.put("note", batch.getNote());
            jo.put("createTime", DateFormatUtils.format(batch.getCreateTime(), "yyyy-MM-dd"));
            return jo;
        }).collect(Collectors.toCollection(JSONArray::new));

        // 生成本次扫描码
        String key = "driver_listEnableScanSubBatch_" + loginDriver.getDriverId();

        //Redis.use().del(key);
        redisTemplate.delete(key); // 删除指定的 Redis 键
        return R.ok(ja);
    }

    /*
    *   根据批次号获取批次里面的订单信息
    * */
    @Operation(summary = "批次号查询订单",description = "批次号查询订单")
    @GetMapping("/batchOrderList")
    public R batchOrderList(@RequestParam("batchNo") String batchNo){
        List<NbOrderEntity> list = orderService.list(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getSubBatchNo, batchNo).orderByDesc(NbOrderEntity::getLastRouteTime) );
        return R.ok(list);
    }

    /**
     * 订单添加到子批次
     */
    @Operation(summary = "订单添加到子批次" , description = "订单添加到子批次" )
    @PostMapping("/orderAddToSubBatch")
    public R orderAddToSubBatch(@RequestParam("pkgNo") String pkgNo,@RequestParam("batchNo") String batchNo ) {

        if (batchNo == null) {
            return LocalizedR.failed("driverorder.no.batch.selected.please.reselect", Optional.ofNullable(null));
        }

        LambdaQueryWrapper<NbOrderBatchEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(NbOrderBatchEntity::getBatchNo, batchNo);
        NbOrderBatchEntity ob = orderBatchService.getOne(wrapper,false);
        if (ob == null) {
            return LocalizedR.failed("driverorder.batch.does.not.exist.please.reselect", batchNo);
        }

        if (ob.getIsMain()) {
            return LocalizedR.failed("driverorder.only.sub.batches.can.be.operated", Optional.ofNullable(null));
        }

        if (ob.getIsLtl()) {
            return LocalizedR.failed("driverorder.Truck.delivery.batches.cannot.be.directly.scanned", Optional.ofNullable(null));
        }

        if (ob.getIsScaning() != 1) {
            return LocalizedR.failed("driverorder.batch.not.enabled.scanning", Optional.ofNullable(null));
        }

        if (ob.getSyncR4mOrderStatus() > 1) {
            return LocalizedR.failed("driverorder.already.started.not.performed", Optional.ofNullable(null));
        }

        // "select * from nb_order where pkg_no = ? limit 1", pkgNo);
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo,pkgNo),false);

        if (order == null) {
            return LocalizedR.failed("nborder.order.does.not.exist", pkgNo);
        }

        if (order.getOrderStatus() == OrderDto.ORDER_STATUS_340_PACKAGE_INTERCEPTED) {
            return LocalizedR.failed("driverorder.order.has.been.intercepted", Optional.ofNullable(null));
        }


        if (order.getExpressType() == OrderDto.EXPRESS_TYPE_2_PICKUP) {
            // 2024-02-18 老版本app，依然不能生成新节点，防止听不到自提件的语音提示
            Integer vc = getAppVersionCode();
            if (vc == null || vc <= 125) {
                return LocalizedR.failed("driverorder.self.pickup.items", pkgNo);
            }
        }

        if (order.getOrderStatus() > OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER) {
            return LocalizedR.failed("driverorder.has.been.sent.to.the.transfer.center.no.create.sub.batch", Optional.ofNullable(null));
        }

        boolean isNotArrivedOrder = false;
        if (StrUtil.isNotBlank(order.getSubBatchNo()) && NBDConstants.NotArrivedBatchNo.equals(order.getSubBatchNo())) {
            isNotArrivedOrder = true;
        }

        if (StrUtil.isNotBlank(order.getSubBatchNo()) && !NBDConstants.NotArrivedBatchNo.equals(order.getSubBatchNo())) { // 2024-06-02 未到货批次允许盲扫
            if (order.getSubBatchNo().equals(ob.getBatchNo())) {
                return LocalizedR.failed("driverorder.repeat.scanning", pkgNo);
            }

            return LocalizedR.failed("driverorder.this.package.is.in.a.sub.batch", order.getSubBatchNo());
        }

        // 2024-05-17 sophie  盲扫后，改为199状态
//		if (order.getOrderStatus() < Order.ORDER_STATUS_200_PARCEL_SCANNED) {
//			order.setOrderStatus(Order.ORDER_STATUS_200_PARCEL_SCANNED);
//			order.setLastRouteTime(new Date());
//		}
        if (order.getOrderStatus() < OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT) {
            order.setOrderStatus(OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT);
            order.setLastRouteTime(new Date());
        }

        NbSortingCenterEntity sc = sortingCenterService.getById(order.getScId());

        NbOrderBatchEntity kpOb = null;
        NbOrderBatchEntity zhOb = null;

        boolean heavyCargo = false; // 重货标记
        Integer expressType = order.getExpressType();

        if (expressType == OrderDto.EXPRESS_TYPE_3_TLT) {
            // 2024-02-16 卡派订单自动创批次并加入
            String kpBatchNo = ob.getBatchNo() + "-KP";
            log.info("发现卡派订单orderNo=" + order.getOrderNo() + ",使用批次号=" + kpBatchNo);

            String timezone = sc == null ? ZoneId.systemDefault().getId() : sc.getScTimezone();
            kpOb = getAliveOrderBatch(ob, timezone, kpBatchNo, "LTL");

            order.setSubBatchNo(kpOb.getBatchNo());
            orderService.updateById(order);

            // "select count(1) cont from nb_order where sub_batch_no = ? limit 1", kpOb.getBatchNo()).getInt("cont");
            Integer total = Math.toIntExact(orderService.count(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getSubBatchNo, kpOb.getBatchNo())));

            kpOb.setOrderTotal(total);
            kpOb.setBatchTotal(total);
            orderBatchService.updateById(kpOb);

        } else if (expressType == OrderDto.EXPRESS_TYPE_0_NORMAL || expressType == OrderDto.EXPRESS_TYPE_1_EXPRESS) {
            // 2024-03-07 判断重量是否为重货
            BigDecimal maxWeight = new OrderDto().calMaxWeight();
            BigDecimal thresholdWeight = configUtil.findDecimalByKey(ConfigUtil.nb_auto_order_batch_threshold_weight);

            if (maxWeight.compareTo(thresholdWeight) >= 0) {
                String zhBatchNo = ob.getBatchNo() + "-ZH";
                log.info("发现重货订单orderNo=" + order.getOrderNo() + ",使用批次号=" + zhBatchNo);

                String timezone = sc == null ? ZoneId.systemDefault().getId() : sc.getScTimezone();
                zhOb = getAliveOrderBatch(ob, timezone, zhBatchNo, "ZH");

                order.setSubBatchNo(zhOb.getBatchNo());
                orderService.updateById(order);

                // "select count(1) cont from nb_order where sub_batch_no = ? limit 1", zhOb.getBatchNo()).getInt("cont");
                Integer total = Math.toIntExact(orderService.count(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getSubBatchNo, zhOb.getBatchNo())));

                zhOb.setOrderTotal(total);
                zhOb.setBatchTotal(total);
                orderBatchService.updateById(zhOb);

                log.info("盲扫orderId=" + order.getOrderId() + ",pkgNo=" + order.getPkgNo() + ",判断为重货");
                heavyCargo = true;
            } else {
                order.setSubBatchNo(ob.getBatchNo());
                orderService.updateById(order);

                // "select count(1) cont from nb_order where sub_batch_no = ? limit 1", ob.getBatchNo()).getInt("cont");
                Integer total = Math.toIntExact(orderService.count(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getSubBatchNo, ob.getBatchNo())));
                ob.setOrderTotal(total);
                ob.setBatchTotal(total);
                orderBatchService.updateById(ob);
            }

        } else {
            order.setSubBatchNo(ob.getBatchNo());
            orderService.updateById(order);

            // "select count(1) cont from nb_order where sub_batch_no = ? limit 1", ob.getBatchNo()).getInt("cont");
            Integer total = Math.toIntExact(orderService.count(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getSubBatchNo, ob.getBatchNo())));
            ob.setOrderTotal(total);
            ob.setBatchTotal(total);
            orderBatchService.updateById(ob);
        }

        if (isNotArrivedOrder) {
            // "select count(1) cont from nb_order where sub_batch_no = ?", NBDConstants.NotArrivedBatchNo).getInt("cont");
            Integer notArrivedTotal = Math.toIntExact(orderService.count(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getSubBatchNo, NBDConstants.NotArrivedBatchNo)));

            //Db.update("update nb_order_batch set order_total = ?, batch_total = ? where batch_no = ?", notArrivedTotal, notArrivedTotal, NBDConstants.NotArrivedBatchNo);
            orderBatchService.update(new LambdaUpdateWrapper<NbOrderBatchEntity>()
                    .set(NbOrderBatchEntity::getOrderTotal, notArrivedTotal).set(NbOrderBatchEntity::getBatchTotal, notArrivedTotal)
                    .eq(NbOrderBatchEntity::getBatchNo, NBDConstants.NotArrivedBatchNo));
            log.info("未到货批次更新数量为total=" + notArrivedTotal);
        }

        NbDriverEntity loginDriver = getLoginDriver();
        String address = null;
        if (sc != null) {
            Integer provinceId = sc.getProvinceId();
            Integer cityId = sc.getCityId();

            JSONObject province = commonDataUtil.getProvinceById(provinceId);
            JSONObject city = commonDataUtil.getCityById(cityId);

            address = city.getStr("enName") + " " + province.getStr("enName");
        }

        Double[] latlng = getLatLng();
        Double lat = null, lng = null;
        if (latlng != null) {
            lat = latlng[0];
            lng = latlng[1];
        }

        // 2024-05-16 sophie 盲扫时改为生成199状态
        //OrderPath op199 = new OrderPath().build(order.getOrderId(), Order.ORDER_STATUS_199_GATEWAY_TRANSIT, loginDriver.getDriverId(), lat, lng, address, sc.getScTimezone());
        NbOrderPathEntity op199 = new OrderPathDto().build(order.getOrderId(), OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT, SecurityUtils.getUser().getId(), lat, lng, address, sc.getScTimezone());
        op199.setScId(order.getScId());
        orderPathService.save(op199);

        String key = "driver_listEnableScanSubBatch_" + loginDriver.getDriverId();
        String field = "batch_id_" + ob.getBatchId();
        Object o = redisTemplate.opsForHash().get(key, field);

        Long counter;
        if (o != null) {
            counter = Long.valueOf(o.toString());
            counter+=1;
        }else {
            counter=1l;
        }

        redisTemplate.opsForHash().put(key, field, counter.toString());

        // 20230909 如果是子订单,则需要将状态同步给主订单
        if (order.getPOrderId() > 0) {
            Integer pOrderId = order.getPOrderId();

            NbOrderEntity pOrder = orderService.getById(pOrderId);
            pOrder.setOrderStatus(order.getOrderStatus());
            orderService.updateById(pOrder);

            op199.setPathId(null);
            op199.setOrderId(pOrder.getOrderId());
            orderPathService.save(op199);
        }


        JSONObject ret = new JSONObject();
        if (order.getExpressType() == OrderDto.EXPRESS_TYPE_3_TLT) {
            ret.set("batch", new OrderBatchDto().toAppJson(kpOb));
        } else if (heavyCargo) {
            ret.set("batch", new OrderBatchDto().toAppJson(zhOb));
        } else {
            ret.set("batch", new OrderBatchDto().toAppJson(ob));
        }
        ret.set("currentScanedTotal", counter);

        NbTransferCenterEntity tc = transferCenterService.getById(order.getTcId());

        if (tc == null) {
            ret.set("tc", new JSONObject());
        } else {
            JSONObject tcJo = toAppJson(tc);
            tcJo.put("regionCode", tc.getRegionCode());
            ret.set("tc", tcJo);
        }

        ret.set("expressType", order.getExpressType());
        ret.set("heavyCargo", heavyCargo);
        ret.set("order", new OrderDto().toDriverListJson(order));

        return R.ok(ret);
    }


    /**
     * 获取有效的自动创建批次
     *
     * @param pob 父批次
     * @param timezone 时区
     * @param autoBatchNo 检测的原批次号
     * @param bizzType 业务类型  LTL、ZH
     * @return
     */
    private NbOrderBatchEntity getAliveOrderBatch (NbOrderBatchEntity pob, String timezone, String autoBatchNo, String bizzType) {
        // "select * from nb_order_batch where batch_no = ? limit 1", autoBatchNo);
        NbOrderBatchEntity autoOb = orderBatchService.getOne(new LambdaQueryWrapper<NbOrderBatchEntity>().eq(NbOrderBatchEntity::getBatchNo, autoBatchNo),false );
        if (autoOb != null && (autoOb.getSyncR4mOrderStatus() > 1)) {
            // 同步中的话，需要进行批次号+1

            // "select count(1) cont from nb_order_batch where batch_no like ? limit 1", autoBatchNo + "%").getInt("cont");
            Integer count = Math.toIntExact(orderBatchService.count(new LambdaQueryWrapper<NbOrderBatchEntity>().like(NbOrderBatchEntity::getBatchNo, autoBatchNo + "%")));

            autoBatchNo = autoBatchNo + "-" + count;

            autoOb = null; // 用新批次号重新创建新的
        }

        if (autoOb == null) {
            String keyToString = "nb_manager_orderAddToSubBatch_auto_createob_" + autoBatchNo;
            String requestId = String.valueOf(System.currentTimeMillis());
            try {
                while (redisUtil.tryLock(keyToString, requestId, 5) == false) {
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }

                // "select * from nb_order_batch where batch_no = ? limit 1", autoBatchNo);
                autoOb = orderBatchService.getOne(new LambdaQueryWrapper<NbOrderBatchEntity>().eq(NbOrderBatchEntity::getBatchNo, autoBatchNo),false );
                if (autoOb == null) {
                    autoOb = new NbOrderBatchEntity();
                    if ("LTL".equals(bizzType)) {
                        autoOb.setIsLtl(true);
                    }
                    if ("ZH".equals(bizzType)) {
                        autoOb.setIsHeavyCargo(1);
                    }
                    autoOb.setIsMain(false);
                    autoOb.setBatchNo(autoBatchNo);
                    autoOb.setOrderTotal(0);
                    try {
                        autoOb.setCreateTime(NBDUtils.getLocalDate(timezone));
                    } catch (Exception e) {
                        e.printStackTrace();

                        autoOb.setCreateTime(new Date());
                    }
                    autoOb.setIsValid(true);
                    autoOb.setBatchTotal(0);
                    autoOb.setIsRouted(false);
                    autoOb.setPBatchId(pob.getBatchId());
                    autoOb.setMerchantId(pob.getMerchantId());
                    autoOb.setBatchType(OrderBatchDto.BATCH_TYPE_3_EMPTY);
                    orderBatchService.save(autoOb);

                    log.info("创建自动批次，batchNo=" + autoOb.getBatchNo() + ",zh=" + autoOb.getIsHeavyCargo() + ",ltl=" + autoOb.getIsLtl());
                }
            } finally {
                redisUtil.releaseLock(keyToString, requestId);
            }
        }

        return autoOb;
    }


    public JSONObject toAppJson(NbTransferCenterEntity nbTransferCenterEntity) {
        JSONObject jo = new JSONObject();
        jo.set("tcId", nbTransferCenterEntity.getTcId());
        jo.set("code", nbTransferCenterEntity.getTransferCenterCode());
        jo.set("provinceId", nbTransferCenterEntity.getProvinceId());
        jo.set("province", commonDataUtil.getProvinceById(nbTransferCenterEntity.getProvinceId()));
        jo.set("cityId", nbTransferCenterEntity.getCityId());
        jo.set("city", commonDataUtil.getCityById(nbTransferCenterEntity.getCityId()));
        jo.set("address", nbTransferCenterEntity.getAddress());
        jo.set("postalCode", nbTransferCenterEntity.getPostalCode());
        jo.set("centerName", nbTransferCenterEntity.getCenterName());
        return jo;
    }

}
