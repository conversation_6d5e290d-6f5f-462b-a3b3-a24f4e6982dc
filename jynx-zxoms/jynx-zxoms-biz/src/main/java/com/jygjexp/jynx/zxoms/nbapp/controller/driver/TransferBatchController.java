package com.jygjexp.jynx.zxoms.nbapp.controller.driver;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.dto.DriverDto;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.entity.*;

import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton.AtomApi;
import com.jygjexp.jynx.zxoms.send.service.*;

import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.utils.NBDUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * @Author: chenchang
 * @Description: 普通司机用的
 * @Date: 2024/11/12 11:12
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver/transferBatch")
@Tag(description = "appdrivertransferBatch", name = "APP-普通司机用的")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TransferBatchController extends BaseController {
    private final NbOrderService orderService;
    private final NbTransferBatchOrderService transferBatchOrderService;
    private final NbTransferBatchService transferBatchService;
    private final NbSortingCenterService sortingCenterService;
    private final NbTransferCenterService transferCenterService;
    private final NbDriverService driverService;
    private final NbOrderPathService orderPathService;
    private final CommonDataUtil commonDataUtil;

    /**
     * 笼车包裹扫描
     */
    @AtomApi(key = "pkgNo")
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "笼车包裹扫描", description = "笼车包裹扫描")
    @PostMapping("/cageScan")
    public void cageScan(@RequestParam("pkgNo") String pkgNo) {
        NbDriverEntity loginDriver = getLoginDriver();
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo, pkgNo), false);
        if (order == null) {
            renderAppErr("-1", "包裹不存在[" + pkgNo + "]");
            return;
        }
        // "select * from nb_transfer_batch_order where order_id = ? order by bo_id desc limit 1", order.getOrderId());
        NbTransferBatchOrderEntity tbo = transferBatchOrderService.getOne(new LambdaQueryWrapper<NbTransferBatchOrderEntity>()
                .eq(NbTransferBatchOrderEntity::getOrderId, order.getOrderId()).orderByDesc(NbTransferBatchOrderEntity::getBoId));
        if (tbo == null) {
            renderAppErr("-2", "订单不在路区内，无法完成扫描：" + pkgNo);
            return;
        }

        NbTransferBatchEntity tb = transferBatchService.getById(tbo.getBatchId());
        if (tb.getDriverId().intValue() != loginDriver.getDriverId()) {
            renderAppErr("-3", "不是你的订单无法完成扫描操作：" + pkgNo);
            return;
        }
        NbSortingCenterEntity sc = sortingCenterService.getById(order.getScId());
        String timezone = sc.getScTimezone();
        if (StrUtil.isBlank(timezone)) {
            renderAppErr("-10", "分拣中心未设置时区，无法完成该操作");
            return;
        }
        if (tbo.getIsDriverScan()) {
            renderAppErr("-10", "重复扫描： " + pkgNo);
            return;
        }
        tbo.setIsDriverScan(true);
        tbo.setDriverScanTime(new Date());
        transferBatchOrderService.updateById(tbo);

        // "select count(1) cont from nb_transfer_batch_order where batch_id = ? and is_driver_scan = true", tb.getBatchId()).getInt("cont");
        Integer scanedTotal = Math.toIntExact(transferBatchOrderService.count(new LambdaQueryWrapper<NbTransferBatchOrderEntity>()
                .eq(NbTransferBatchOrderEntity::getBatchId, tb.getBatchId()).eq(NbTransferBatchOrderEntity::getIsDriverScan, true)));

        JSONObject ret = new JSONObject();
        ret.put("pkgNo", pkgNo);

        JSONObject tbJo = new JSONObject();
        tbJo.set("batchId", tb.getBatchId());
        tbJo.set("batchNo", tb.getBatchNo());
        tbJo.set("batchCode", tb.getBatchCode());
        tbJo.set("driverId", tb.getDriverId());
        tbJo.set("orderTotal", tb.getOrderTotal());
        tbJo.set("estimatedHour", tb.getEstimatedHour());

        ret.set("batch", tbJo);
        ret.set("scanedTotal", scanedTotal);
        ret.set("requireConfirm", false);
        ret.set("pickNo", order.getPickNo());
        if (scanedTotal == tb.getOrderTotal()) {
            ret.set("requireConfirm", true);
        }
        ret.set("repeat", false);

        Integer tcId = order.getTcId();
        if (tcId != null && tcId > 0) {
            NbTransferCenterEntity tc = transferCenterService.getById(tcId);
            JSONObject tcJo = new JSONObject();
            tcJo.set("tcId", tc.getTcId());
            tcJo.set("code", tc.getTransferCenterCode());
            tcJo.set("provinceId", tc.getProvinceId());
            tcJo.set("province", commonDataUtil.getProvinceById(tc.getProvinceId()));
            tcJo.set("cityId", tc.getCityId());
            tcJo.set("city", commonDataUtil.getCityById(tc.getCityId()));
            tcJo.set("address", tc.getAddress());
            tcJo.set("postalCode", tc.getPostalCode());
            tcJo.set("centerName", tc.getCenterName());
            ret.set("tc", tcJo);
        }

        if (order.getDriverId() > 0) {
            NbDriverEntity driver = driverService.getById(order.getDriverId());
            ret.set("driver", new DriverDto().toAppJson(driver));
        }

        renderAppData(ret);
    }

    /**
     * 笼车包裹整车确认并生成轨迹
     */
    @AtomApi(key = "batchId")
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "笼车确认并生成轨迹", description = "笼车确认并生成轨迹")
    @PostMapping("/cageConfirm")
    public void cageConfirm(@RequestParam("batchId") Integer batchId) {
        NbTransferBatchEntity tb = transferBatchService.getById(batchId);
        if (tb == null) {
            renderAppErr("-1", "路区不存在");
            return;
        }

        if (tb.getIsDriverLoad()) {
            renderAppErr("-1", "路区已于" + DateFormatUtils.format(tb.getDriverLoadTime(), "yyyy-MM-dd HH:mm:ss") + "确认过");
            return;
        }

        NbDriverEntity loginDriver = getLoginDriver();
        int scId = loginDriver.getScId();
        NbSortingCenterEntity driverSc = sortingCenterService.getById(scId);
        String scTimezone = driverSc.getScTimezone();

        // "select o.* from nb_order o, nb_transfer_batch_order tbo where o.order_id = tbo.order_id and tbo.batch_id = ?", tb.getBatchId());
        MPJLambdaWrapper<NbOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbOrderEntity.class)
                .select(NbTransferBatchOrderEntity::getOrderId)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbTransferBatchOrderEntity::getOrderId)
                .eq(NbTransferBatchOrderEntity::getBatchId, tb.getBatchId());
        List<NbOrderEntity> orders = orderService.selectJoinList(NbOrderEntity.class, wrapper);

        NbSortingCenterEntity sc = sortingCenterService.getById(orders.get(0).getScId());
        tb.setIsDriverLoad(true);
        tb.setDriverLoadTime(NBDUtils.getLocalDate(scTimezone));
        transferBatchService.updateById(tb);

        String address = null;
        if (driverSc != null) {
            int provinceId = driverSc.getProvinceId();
            int cityId = driverSc.getCityId();
            address = commonDataUtil.getAddress(provinceId, cityId);
        }

        for (NbOrderEntity order : orders) {
            // 2024-03-12 非200状态订单，确认笼车不修改
            if (order.getOrderStatus() != OrderDto.ORDER_STATUS_200_PARCEL_SCANNED) {
                log.info("订单状态不是200，跳过修改201，orderId=" + order.getOrderId());
                continue;
            }

            // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", order.getOrderId(), Order.ORDER_STATUS_201_TO_TRANSIT_CENTER);
            NbOrderPathEntity op201 = orderPathService.getOne(new LambdaQueryWrapper<NbOrderPathEntity>()
                    .eq(NbOrderPathEntity::getOrderId, order.getOrderId()).eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER));
            if (op201 != null) {
                log.info("订单存在201状态，跳过，orderId=" + order.getOrderId());
                continue;
            }

            order.setOrderStatus(OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER);
            orderService.updateById(order); // 开往转运中心

            NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), loginDriver != null ? loginDriver.getDriverId() : 0, sc.getLat(), sc.getLng(), address, sc.getScTimezone());
            op.setScId(sc.getScId());
            orderPathService.save(op);

            log.info("订单状态更新:orderId=" + order.getOrderId() + ",status=" + order.getOrderStatus());
            if (order.getPOrderId() > 0) {
                NbOrderEntity porder = orderService.getById(order.getPOrderId());
                porder.setOrderStatus(order.getOrderStatus());
                orderService.updateById(porder);

                op.setPathId(null);
                op.setOrderId(porder.getOrderId());
                orderPathService.save(op);

                log.info("父订单状态更新:orderId=" + porder.getOrderId() + ",status=" + porder.getOrderStatus());
            }
        }

        renderAppSuc();
    }

}
