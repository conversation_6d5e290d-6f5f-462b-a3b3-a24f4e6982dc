package com.jygjexp.jynx.zxoms.send.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Author: chenchang
 * @Description: 派送批次导出实体
 * @Date: 2024/12/7 21:57
 */
@Data
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 10) // 设置表头字体样式
@Schema(description = "派送批次导出实体")
public class NbOrderBatchExcelVo {

    @ColumnWidth(6)
    @ExcelProperty("(Enable scan)开启扫描")
    @Schema(description="开启扫描")
    private Integer isScaning;

    @ColumnWidth(20)
    @ExcelProperty("(Batch number)批次号")
    @Schema(description="批次号")
    private String batchNo;

    @ColumnWidth(8)
    @ExcelProperty("(Quantity in storage)入库数量")
    @Schema(description="导入数量")
    private Integer impTotal;

    @ColumnWidth(20)
    @ExcelProperty("(Creation time)创建时间")
    @Schema(description="创建时间")
    private Date createTime;

    @ColumnWidth(22)
    @ExcelProperty("(remark)备注")
    @Schema(description="备注")
    private String note;

    @ColumnWidth(6)
    @ExcelProperty("(Whether it is valid or not)是否有效")
    @Schema(description="是否有效")
    private Boolean isValid;

    @ColumnWidth(8)
    @ExcelProperty("(Interface notification quantity)接口通知数量")
    @Schema(description="接口通知数量")
    private Integer batchTotal;

    @ColumnWidth(8)
    @ExcelProperty("(Planned or not)是否已规划")
    @Schema(description="已规划")
    private Boolean isRouted;

    @ColumnWidth(20)
    @ExcelProperty("(Planning time)规划时间")
    @Schema(description="规划时间")
    private Date routedTime;

    @ColumnWidth(20)
    @ExcelProperty("(Planned return data)规划返回数据")
    @Schema(description="规划返回数据")
    private String routedErrmsg;

    @ColumnWidth(20)
    @ExcelProperty("(Expected arrival)预计到达")
    @Schema(description="预计到达")
    private Date eta;

    @ColumnWidth(20)
    @ExcelProperty("(Anticipated delivery)预计送出")
    @Schema(description="预计送出")
    private Date etd;

    @ColumnWidth(20)
    @ExcelProperty("(Physical arrival)实际到达")
    @Schema(description="实际到达")
    private Date ata;

    @ColumnWidth(20)
    @ExcelProperty("(Actual delivery)实际送出")
    @Schema(description="实际送出")
    private Date atd;

    @ColumnWidth(6)
    @ExcelProperty("(Master batch)主批次")
    @Schema(description="主批次")
    private Boolean isMain;

    @ColumnWidth(6)
    @ExcelProperty("(R4m synchronization status)R4m同步状态")
    @Schema(description="是否同步了r4m order")
    private Integer isSyncR4mOrder;

    @ColumnWidth(15)
    @ExcelProperty("(Planning ID)规划ID")
    @Schema(description="规划ID")
    private String optimizationProblemId;

    @ColumnWidth(30)
    @ExcelProperty("route4me view")
    @Schema(description="route4me view")
    private String batchView;

}
