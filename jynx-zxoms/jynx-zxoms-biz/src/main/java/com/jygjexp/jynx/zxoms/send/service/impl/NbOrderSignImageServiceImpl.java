package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderSignImageEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderSignImageMapper;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiOrderVo;
import com.jygjexp.jynx.zxoms.send.service.NbOrderSignImageService;
import com.jygjexp.jynx.zxoms.vo.NbOrderSignImagePageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 签收照片
 *
 * <AUTHOR>
 * @date 2024-10-18 00:13:19
 */
@Service
@RequiredArgsConstructor
public class NbOrderSignImageServiceImpl extends ServiceImpl<NbOrderSignImageMapper, NbOrderSignImageEntity> implements NbOrderSignImageService {
    private final NbOrderSignImageMapper nbOrderSignImageMapper;

    /**
     * 签收照片分页查询
     * @param page
     * @param nbOrderSignImage
     * @return
     */
    @Override
    public Page<NbOrderSignImagePageVo> search(Page page, NbOrderSignImageEntity nbOrderSignImage) {
        //select driver_id, first_name from nb_driver;ds=nbd;
        MPJLambdaWrapper<NbOrderSignImageEntity> wrapper = new MPJLambdaWrapper<NbOrderSignImageEntity>();
        wrapper.selectAll(NbOrderSignImageEntity.class)
                .select(NbDriverEntity::getDriverId, NbDriverEntity::getFirstName, NbDriverEntity::getDriverName)
                .eq(ObjectUtil.isNotNull(nbOrderSignImage.getOrderId()), NbOrderSignImageEntity::getOrderId, nbOrderSignImage.getOrderId())
                .eq(ObjectUtil.isNotNull(nbOrderSignImage.getAddTime()), NbOrderSignImageEntity::getAddTime, nbOrderSignImage.getAddTime())
                .eq(ObjectUtil.isNotNull(nbOrderSignImage.getDriverId()), NbOrderSignImageEntity::getDriverId, nbOrderSignImage.getDriverId());
        if (ObjectUtil.isNotNull(nbOrderSignImage.getDistance())) {
            wrapper.eq(NbOrderSignImageEntity::getDistance, nbOrderSignImage.getDistance())  // 离收件距离 =
                    .or().gt(NbOrderSignImageEntity::getDistance, nbOrderSignImage.getDistance())  // 离收件距离 >
                    .or().lt(NbOrderSignImageEntity::getDistance, nbOrderSignImage.getDistance())  // 离收件距离 <
                    .or().ge(NbOrderSignImageEntity::getDistance, nbOrderSignImage.getDistance())  // 离收件距离 >=
                    .or().le(NbOrderSignImageEntity::getDistance, nbOrderSignImage.getDistance()); // 离收件距离 <=
        }
        wrapper.leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbOrderSignImageEntity::getDriverId)
                .orderByDesc(NbOrderSignImageEntity::getImageId);
        return nbOrderSignImageMapper.selectJoinPage(page, NbOrderSignImagePageVo.class, wrapper);
    }

    /**
     * 根据订单ID查询订单签收图片
     * @param orderId
     * @return
     */
    @Override
    public List<NbOrderSignImageEntity> listOrderSignImageByOrderId(Integer orderId) {
        MPJLambdaWrapper<NbOrderSignImageEntity> wrapper = new MPJLambdaWrapper<NbOrderSignImageEntity>();
        wrapper.selectAll(NbOrderSignImageEntity.class)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbOrderSignImageEntity::getOrderId)
                .eq(NbOrderEntity::getOrderId, orderId);
        return nbOrderSignImageMapper.selectJoinList(NbOrderSignImageEntity.class, wrapper);
    }

    @Override
    public List<ApiOrderVo> findImagesByOrderIds(List<Integer> orderIds) {
        return nbOrderSignImageMapper.findImagesByOrderIds(orderIds);
    }
}