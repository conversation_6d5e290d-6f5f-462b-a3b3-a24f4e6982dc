package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbAppVersionAssignEntity;
import com.jygjexp.jynx.zxoms.send.service.NbAppVersionAssignService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import org.springdoc.api.annotations.ParameterObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 指定可更新的用户
 *
 * <AUTHOR>
 * @date 2024-11-12 00:32:32
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbAppVersionAssign" )
//@Tag(description = "nbAppVersionAssign" , name = "指定可更新的用户管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbAppVersionAssignController {

    private final  NbAppVersionAssignService nbAppVersionAssignService;

    /**
     * 分页查询
     * @param page
     * @param entity
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/search" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersionAssign_view')" )
    public R getNbAppVersionAssignPage(@ParameterObject Page page, @ParameterObject NbAppVersionAssignEntity entity) {
        return R.ok(nbAppVersionAssignService.search(page, entity));
    }


    /**
     * 通过id查询指定可更新的用户
     * @param assignId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{assignId}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersionAssign_view')" )
    public R getById(@PathVariable("assignId" ) Integer assignId) {
        return R.ok(nbAppVersionAssignService.getById(assignId));
    }

    /**
     * 新增指定可更新的用户
     * @param nbAppVersionAssign 指定可更新的用户
     * @return R
     */
//    @Operation(summary = "新增指定可更新的用户" , description = "新增指定可更新的用户" )
//    @SysLog("新增指定可更新的用户" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersionAssign_add')" )
    public R save(@RequestBody NbAppVersionAssignEntity nbAppVersionAssign) {
        return R.ok(nbAppVersionAssignService.save(nbAppVersionAssign));
    }

    /**
     * 修改指定可更新的用户
     * @param nbAppVersionAssign 指定可更新的用户
     * @return R
     */
//    @Operation(summary = "修改指定可更新的用户" , description = "修改指定可更新的用户" )
//    @SysLog("修改指定可更新的用户" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersionAssign_edit')" )
    public R updateById(@RequestBody NbAppVersionAssignEntity nbAppVersionAssign) {
        return R.ok(nbAppVersionAssignService.updateById(nbAppVersionAssign));
    }

    /**
     * 通过id删除指定可更新的用户
     * @param ids assignId列表
     * @return R
     */
//    @Operation(summary = "通过id删除指定可更新的用户" , description = "通过id删除指定可更新的用户" )
//    @SysLog("通过id删除指定可更新的用户" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersionAssign_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbAppVersionAssignService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbAppVersionAssign 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersionAssign_export')" )
    public List<NbAppVersionAssignEntity> export(NbAppVersionAssignEntity nbAppVersionAssign,Integer[] ids) {
        return nbAppVersionAssignService.list(Wrappers.lambdaQuery(nbAppVersionAssign).in(ArrayUtil.isNotEmpty(ids), NbAppVersionAssignEntity::getAssignId, ids));
    }
}