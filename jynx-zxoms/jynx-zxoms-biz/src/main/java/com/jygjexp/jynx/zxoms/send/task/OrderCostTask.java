package com.jygjexp.jynx.zxoms.send.task;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderCostMapper;
import com.jygjexp.jynx.zxoms.send.mapper.NbTransferBatchOrderMapper;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.ConfigUtil;
import com.jygjexp.jynx.zxoms.dto.ModifyRecordDto;
import com.jygjexp.jynx.zxoms.dto.OrderCostDto;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.entity.*;

import com.jygjexp.jynx.zxoms.utils.NBDUtils;
import com.jygjexp.jynx.zxoms.vo.OrderCostVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: chenchang
 * @Description: 【订单】司机收入处理
 * @Date: 2024/10/9 23:10
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OrderCostTask {
    private final NbOrderService nbOrderService;
    private final NbOrderCostMapper nbOrderCostMapper;
    private final NbModifyRecordService nbModifyRecordService;
    private final NbOrderPathService nbOrderPathService;
    private final NbOrderMpsService nbOrderMpsService;
    private final NbDriverService nbDriverService;
    private final NbDriverCostRuleService nbDriverCostRuleService;
    private final NbDriverCostRuleItemService nbDriverCostRuleItemService;
    private final NbTransferBatchCostModifyLogService nbTransferBatchCostModifyLogService;
    private final NbTransferBatchOrderMapper nbTransferBatchOrderMapper;
    private final ConfigUtil configUtil;

    int startOrderId = 1442230;

    static boolean isRunning = false;

    @SneakyThrows
    @XxlJob("orderCostHandler")
    public void orderCostHandler() {

        if (isRunning) {
//        String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("定时任务：【订单司机收入处理】于:{}，输入参数{}", LocalDateTime.now(), "运行中");
        }

        try {
            isRunning = true;
            // 司机结算时，采用包裹重与体积重最大值
            Boolean autoVolWeight = configUtil.findBooleanByKey(ConfigUtil.nb_driver_cost_settle_auto_volume_weight);

            checkChangeDriver(autoVolWeight);

            discoverCostOrder(autoVolWeight);

            updateCostOrder(autoVolWeight);
        } finally {
            isRunning = false;
        }

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【订单司机收入处理】执行结束，时间: {}", LocalDateTime.now());
    }

    /**
     * 检查结算后又修改司机的
     *
     * @param autoVolWeight
     */
    private void checkChangeDriver(Boolean autoVolWeight) {
        int pageNumber = 1, pageSize = 100;
        boolean hasMore = true;

        while (hasMore) {
// paginate(pageNumber, pageSize, "select oc.*, o.order_id oOrderId, o.p_driver_id oPDriverId, o.driver_id oDriverId, o.order_no oOrderNo, o.pkg_no oPkgNo, o.order_status, o.dest_postal_code, " +
//                            "o.oPkgWeight o_oPkgWeight, o.oPkgLength, o.oPkgWidth, o.oPkgHeight, o.sc_id oScId, o.tc_id oTcId , o.driver_id new_driver_id",
//                    "from nb_order o, nb_order_cost oc where o.order_id = oc.order_id and o.driver_id != oc.driver_id");
            Page<OrderCostVo> pageObj = new Page<>(pageNumber, pageSize);

            MPJLambdaWrapper<NbOrderCostEntity> wrapper = new MPJLambdaWrapper<>();
            wrapper.select(NbOrderEntity::getOrderId, NbOrderEntity::getPDriverId, NbOrderEntity::getDriverId, NbOrderEntity::getOrderNo, NbOrderEntity::getPkgNo,
                            NbOrderEntity::getOrderStatus, NbOrderEntity::getDestPostalCode, NbOrderEntity::getPkgWeight, NbOrderEntity::getPkgLength,
                            NbOrderEntity::getPkgWidth, NbOrderEntity::getPkgHeight, NbOrderEntity::getScId, NbOrderEntity::getTcId)
                    .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbOrderCostEntity::getOrderId)
                    .ne(NbOrderEntity::getDriverId, NbOrderCostEntity::getDriverId);
            Page<OrderCostVo> page = nbOrderCostMapper.selectJoinPage(pageObj, OrderCostVo.class, wrapper);
            if (page.getRecords().isEmpty()) {
                hasMore = false;
            } else {
                // 根据total判断是否还有更多数据
                if (page.getTotal() > pageNumber * pageSize) {
                    pageNumber++;
                } else {
                    hasMore = false;
                }
            }

            Map<String, NbDriverCostRuleEntity> dcrMap = new HashMap<>();

            for (OrderCostVo oc : page.getRecords()) {
                int new_driver_id = oc.getDriverId();
                XxlJobHelper.log("结算订单司机变动orderId=" + oc.getOrderId() + ",oldDriverId=" + oc.getDriverId() + ",newOrderId=" + new_driver_id);

                NbModifyRecordEntity mr = new NbModifyRecordEntity();
                mr.setAddTime(LocalDateTime.now());
                mr.setTableName("nb_order_cost");
                mr.setPrimaryId(oc.getOrderId());
                mr.setFromValue(oc.getDriverId().toString());
                mr.setToValue(String.valueOf(new_driver_id));
                mr.setDescription("司机结算后发生改变" + oc.getDriverId() + "->" + new_driver_id);
                mr.setModifyOperator(ModifyRecordDto.OPERATOR_1_SYSTEM);
                nbModifyRecordService.save(mr);

                //  oc.setDriverId(new_driver_id);
                //  oc.setCostStatus(OrderCostDto.COST_STATUS_1_ESTIMATE);

                nbOrderCostMapper.update(new LambdaUpdateWrapper<NbOrderCostEntity>().eq(NbOrderCostEntity::getOrderId, oc.getOrderId())
                        .set(NbOrderCostEntity::getDriverId, new_driver_id).set(NbOrderCostEntity::getCostStatus, OrderCostDto.COST_STATUS_1_ESTIMATE));
                updateOrderCost(oc, dcrMap, autoVolWeight);
            }
        }
    }

    // 发现首次匹配订单
    private void discoverCostOrder(Boolean autoVolWeight) {
        int pageNo = 1;
        int pageSize = 1000;
        boolean hasMore = true;

        Map<String, NbDriverCostRuleEntity> dcrMap = new HashMap<>();
        while (hasMore) {
// paginate(pageNo, pageSize, "select o.order_id oOrderId, o.p_driver_id oPDriverId, o.driver_id oDriverId, o.order_no oOrderNo, o.pkg_no oPkgNo, o.order_status, o.oDestPostalCode,"
//                            + "o.oPkgWeight o_oPkgWeight, o.oPkgLength, o.oPkgWidth, o.oPkgHeight, o.sc_id oScId, o.tc_id oTcId, oc.*",
//                    " from nb_order o left join nb_order_cost oc on o.order_id = oc.order_id where o.order_status in (200, 201, 202, 203, 205) and o.order_id > " + startOrderId + " and oc.order_id is null");
            MPJLambdaWrapper<NbOrderCostEntity> wrapper = new MPJLambdaWrapper<>();
            wrapper.select(NbOrderEntity::getOrderId, NbOrderEntity::getPDriverId, NbOrderEntity::getDriverId, NbOrderEntity::getOrderNo, NbOrderEntity::getPkgNo,
                            NbOrderEntity::getOrderStatus, NbOrderEntity::getDestPostalCode, NbOrderEntity::getPkgWeight, NbOrderEntity::getPkgLength,
                            NbOrderEntity::getPkgWidth, NbOrderEntity::getPkgHeight, NbOrderEntity::getScId, NbOrderEntity::getTcId)
                    .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbOrderCostEntity::getOrderId)
                    .in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED, OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER,
                            OrderDto.ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER, OrderDto.ORDER_STATUS_203_LOAD_SCANNED, OrderDto.ORDER_STATUS_205_DELIVERED)
                    .gt(NbOrderEntity::getOrderId, startOrderId)
                    .isNull(NbOrderCostEntity::getOrderId);
            Page<OrderCostVo> page = nbOrderCostMapper.selectJoinPage(new Page<>(pageNo, pageSize), OrderCostVo.class, wrapper);

            if (page.hasNext()) {
                hasMore = false;
            }
            pageNo++;

            if (pageNo > 10) {
                hasMore = false;
            }

            for (OrderCostVo item : page.getRecords()) {
                updateOrderCost(item, dcrMap, autoVolWeight);
            }
        }
    }

    /**
     * 更新未完成订单
     *
     * @param autoVolWeight
     */
    private void updateCostOrder(Boolean autoVolWeight) {
        int pageNumber = 1, pageSize = 100;
        boolean hasMore = true;

        Map<String, NbDriverCostRuleEntity> dcrMap = new HashMap<>();

        int total = 0;
        while (hasMore) {
// paginate(pageNumber, pageSize, "select oc.*, o.order_id oOrderId, o.p_driver_id oPDriverId, o.driver_id oDriverId, o.order_no oOrderNo, o.pkg_no oPkgNo, o.order_status, o.oDestPostalCode, " +
//                            "o.oPkgWeight o_oPkgWeight, o.oPkgLength, o.oPkgWidth, o.oPkgHeight, o.sc_id oScId, o.tc_id oTcId ",
//                    "from nb_order_cost oc, nb_order o where oc.order_id = o.order_id and oc.cost_status in (?, ?, ?, ?)",
//                    OrderCost.COST_STATUS_1_ESTIMATE, OrderCost.COST_STATUS_10_OUTSIDE, OrderCost.COST_STATUS_11_PKG_VALUE_INVALID, OrderCost.COST_STATUS_12_RULE_NODATA);
            MPJLambdaWrapper<NbOrderCostEntity> autoVolWeightWrapper = new MPJLambdaWrapper<>();
            autoVolWeightWrapper.select(NbOrderEntity::getOrderId, NbOrderEntity::getPDriverId, NbOrderEntity::getDriverId, NbOrderEntity::getOrderNo, NbOrderEntity::getPkgNo,
                            NbOrderEntity::getOrderStatus, NbOrderEntity::getDestPostalCode, NbOrderEntity::getPkgWeight, NbOrderEntity::getPkgLength,
                            NbOrderEntity::getPkgWidth, NbOrderEntity::getPkgHeight, NbOrderEntity::getScId, NbOrderEntity::getTcId)
                    .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbOrderCostEntity::getOrderId)
                    .in(NbOrderCostEntity::getCostStatus, OrderCostDto.COST_STATUS_1_ESTIMATE, OrderCostDto.COST_STATUS_10_OUTSIDE, OrderCostDto.COST_STATUS_11_PKG_VALUE_INVALID, OrderCostDto.COST_STATUS_12_RULE_NODATA);
            Page<OrderCostVo> page = nbOrderCostMapper.selectJoinPage(new Page<>(pageNumber, pageSize), OrderCostVo.class, autoVolWeightWrapper);

            if (page.hasNext()) {
                hasMore = false;
            }
            pageNumber++;
            if (pageNumber > 100) {
                hasMore = false;
            }

            for (OrderCostVo oc : page.getRecords()) {

                total++;
                XxlJobHelper.log("process::" + total + "/" + page.getTotal());

                int orderStatus = oc.getOOrderStatus();

                // 2024-02-06 a.	目前所有司机的派送结算时间点为【205 Delivered】和【290（Storage_30_Days_From_Office）】
                if ((orderStatus == OrderDto.ORDER_STATUS_205_DELIVERED || orderStatus == OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE) && oc.getCostStatus() == OrderCostDto.COST_STATUS_1_ESTIMATE) {
                    // 配送成功，结算

                    NbOrderPathEntity op = null;
                    if (orderStatus == OrderDto.ORDER_STATUS_205_DELIVERED) {
                        // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", oc.getOrderId(), Order.ORDER_STATUS_205_DELIVERED);
                        LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(NbOrderPathEntity::getOrderId, oc.getOrderId()).eq(NbOrderPathEntity::getOrderStatus, orderStatus);
                        op = nbOrderPathService.getOne(wrapper, false);

                    } else if (orderStatus == OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE) {
                        // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", oc.getOrderId(), Order.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE);
                        LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(NbOrderPathEntity::getOrderId, oc.getOrderId()).eq(NbOrderPathEntity::getOrderStatus, orderStatus);
                        op = nbOrderPathService.getOne(wrapper, false);
                    }

                    if (op == null) {
                        NbOrderEntity order = nbOrderService.getById(oc.getOrderId());
                        if (order.getIsMps()) {
                            // "select * from nb_order_mps where order_id = ? limit 1", oc.getOrderId());
                            NbOrderMpsEntity om = nbOrderMpsService.findOrderMpsByOrderId(oc.getOrderId());
                            if (om != null) {
                                if (orderStatus == OrderDto.ORDER_STATUS_205_DELIVERED) {
                                    // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", om.getPOrderId(), Order.ORDER_STATUS_205_DELIVERED);
                                    LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
                                    wrapper.eq(NbOrderPathEntity::getOrderId, om.getPOrderId()).eq(NbOrderPathEntity::getOrderStatus, orderStatus);
                                    op = nbOrderPathService.getOne(wrapper, false);
                                } else if (orderStatus == OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE) {
                                    // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", om.getPOrderId(), Order.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE);
                                    LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
                                    wrapper.eq(NbOrderPathEntity::getOrderId, om.getPOrderId()).eq(NbOrderPathEntity::getOrderStatus, orderStatus);
                                    op = nbOrderPathService.getOne(wrapper, false);
                                }
                            }
                        }

                        if (op == null) {
                            XxlJobHelper.log("结算异常，未找到对应节点：orderId=" + oc.getOrderId() + "," + op);
                            continue;
                        }
                    }

                    int driverId = oc.getODriverId();
                    if (driverId != oc.getDriverId()) {
                        oc.setDriverId(driverId);
                    }

                    //  oc.setSettleTimestamp(System.currentTimeMillis());
                    //  oc.setSettleTime(op.getAddTime());
                    //  oc.setCostStatus(OrderCostDto.COST_STATUS_2_SETTLE);
                    nbOrderCostMapper.update(new LambdaUpdateWrapper<NbOrderCostEntity>().eq(NbOrderCostEntity::getOrderId, oc.getOrderId())
                            .set(NbOrderCostEntity::getSettleTimestamp, System.currentTimeMillis())
                            .set(NbOrderCostEntity::getSettleTime, op.getAddTime())
                            .set(NbOrderCostEntity::getCostStatus, OrderCostDto.COST_STATUS_2_SETTLE));

                    XxlJobHelper.log("订单标记结算:" + oc.getPkgNo());
                    continue;
                }
                updateOrderCost(oc, dcrMap, autoVolWeight);
            }
        }

    }

    private void updateOrderCost(OrderCostVo item, Map<String, NbDriverCostRuleEntity> dcrMap, Boolean autoVolWeight) {
        Integer oOrderId = item.getOOrderId();
        Integer oDriverId = item.getODriverId();
        Integer oPDriverId = item.getOPDriverId();
        String oOrderNo = item.getOOrderNo();
        String oPkgNo = item.getOPkgNo();
        Integer oOrderStatus = item.getOOrderStatus();
        Integer oScId = item.getOScId();
        Integer oTcId = item.getOTcId();
        String oDestPostalCode = item.getODestPostalCode();
        BigDecimal oPkgWeight = item.getOPkgWeight();
        BigDecimal oPkgLength = item.getOPkgLength();
        BigDecimal oPkgWidth = item.getOPkgWidth();
        BigDecimal oPkgHeight = item.getOPkgHeight();

        if (oDestPostalCode == null) {
            return;
        }
        if (oDriverId == 0) {
            log.info("未分配司机orderId=" + oOrderId);
            XxlJobHelper.log("未分配司机orderId=" + oOrderId);
            return;
        }
        String postalCode = oDestPostalCode.replaceAll(" ", "");
        if (postalCode.length() > 3) {
            String beforePostalCode = postalCode.substring(0, 3);
            NbDriverEntity orderDriver = nbDriverService.getById(oDriverId);
            String prefix = "0_" + beforePostalCode;
            int ruleDriverId = 0;
            if (orderDriver.getPDriverId() > 0 || orderDriver.getIsLeader()) {
                if (orderDriver.getIsLeader()) {
                    ruleDriverId = orderDriver.getDriverId();
                }
                if (orderDriver.getPDriverId() > 0) {
                    ruleDriverId = orderDriver.getPDriverId();
                }
                prefix = ruleDriverId + "_" + beforePostalCode;
                if (oPDriverId == 0 || oPDriverId != ruleDriverId) {
                    XxlJobHelper.log("订单司机leader发生变化：orderId=" + oOrderId + ",p_driver_id=" + ruleDriverId);
                    // "update nb_order set p_driver_id = " + ruleDriverId + " where order_id = " + oOrderId);
                    LambdaUpdateWrapper<NbOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(NbOrderEntity::getOrderId, oOrderId);
                    updateWrapper.set(NbOrderEntity::getPDriverId, ruleDriverId);
                    nbOrderService.update(updateWrapper);
                }
            } else if (oPDriverId > 0) {
                XxlJobHelper.log("订单司机leader发生变化：orderId=" + oOrderId + ",p_driver_id=" + 0);
                // Db.use("nbd").update("update nb_order set p_driver_id = " + 0 + " where order_id = " + oOrderId);
                LambdaUpdateWrapper<NbOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(NbOrderEntity::getOrderId, oOrderId);
                updateWrapper.set(NbOrderEntity::getPDriverId, 0);
                nbOrderService.update(updateWrapper);
            }

            NbDriverCostRuleEntity dcr;
            if (dcrMap.containsKey(prefix)) {
                dcr = dcrMap.get(prefix);
            } else {
                // "select * from nb_driver_cost_rule where driver_id = ? and is_valid = true and cover_postal_code like '%" + beforePostalCode + "%' limit 1", ruleDriverId); // TOOD 效率优化
                LambdaQueryWrapper<NbDriverCostRuleEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(NbDriverCostRuleEntity::getDriverId, ruleDriverId).eq(NbDriverCostRuleEntity::getIsValid, true)
                        .like(NbDriverCostRuleEntity::getCoverPostalCode, "%" + beforePostalCode + "%");
                dcr = nbDriverCostRuleService.getOne(wrapper, false);

                if (dcr == null) {
                    saveOcFail(oOrderId, oDriverId, oOrderNo, oPkgNo, oDestPostalCode, OrderCostDto.COST_STATUS_10_OUTSIDE);
                    log.error("邮编未覆盖:" + oPkgNo + "->" + postalCode);
                    XxlJobHelper.log("邮编未覆盖:" + oPkgNo + "->" + postalCode);
                    return;
                }

                dcrMap.put(prefix, dcr);
                XxlJobHelper.log("使用的规则rule=" + prefix + ",ruleId=" + JSONUtil.toJsonStr(dcr));
            }

            BigDecimal volumeWeight = BigDecimal.ZERO;
            if (checkValue(oPkgWeight) == false) {
                saveOcFail(oOrderId, oDriverId, oOrderNo, oPkgNo, oDestPostalCode, OrderCostDto.COST_STATUS_11_PKG_VALUE_INVALID);
                log.error("包裹数据不全:" + oPkgNo + "->" + postalCode + ",weight=" + oPkgWeight);
                XxlJobHelper.log("包裹数据不全:" + oPkgNo + "->" + postalCode + ",weight=" + oPkgWeight);
                return;
            }

            if (autoVolWeight) {
                if (checkValue(oPkgLength, oPkgWidth, oPkgHeight)) {
                    volumeWeight = NBDUtils.getVolumeWeightDecimal(oPkgLength, oPkgWidth, oPkgHeight);
                }
            }
            // double useWeight = Math.max(oPkgWeight, volumeWeight);
            BigDecimal useWeight = oPkgWeight.max(volumeWeight);    // 计算最大值

            // "select * from nb_driver_cost_rule_item where rule_id = ? and start_kg <= ? order by start_kg desc limit 1", dcr.getRuleId(), useWeight);
            LambdaQueryWrapper<NbDriverCostRuleItemEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(NbDriverCostRuleItemEntity::getRuleId, dcr.getRuleId())
                    .le(NbDriverCostRuleItemEntity::getStartKg, useWeight).orderByDesc(NbDriverCostRuleItemEntity::getStartKg);
            NbDriverCostRuleItemEntity ruleItem = nbDriverCostRuleItemService.getOne(queryWrapper, false);
            if (ruleItem == null) {
                // "select * from nb_driver_cost_rule_item where rule_id = ? order by start_kg asc limit 1", dcr.getRuleId());
                LambdaQueryWrapper<NbDriverCostRuleItemEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(NbDriverCostRuleItemEntity::getRuleId, dcr.getRuleId()).orderByAsc(NbDriverCostRuleItemEntity::getStartKg);
                ruleItem = nbDriverCostRuleItemService.getOne(wrapper, false);
            }

            if (ruleItem == null) {
                saveOcFail(oOrderId, oDriverId, oOrderNo, oPkgNo, oDestPostalCode, OrderCostDto.COST_STATUS_12_RULE_NODATA);
                log.error("规则无数据:" + oPkgNo + "->" + postalCode);
                XxlJobHelper.log("规则无数据:" + oPkgNo + "->" + postalCode);
                return;
            }
            BigDecimal baseCost = ruleItem.getPayment();
            BigDecimal driverWeightSubsidy = ruleItem.getSubsidy();

            NbOrderCostEntity oc = nbOrderCostMapper.selectById(oOrderId);
            if (oc == null) {
                // "select * from nb_order_path where order_id = ? and order_status = ? order by path_id desc limit 1", oOrderId, Order.ORDER_STATUS_200_PARCEL_SCANNED);
                LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(NbOrderPathEntity::getOrderId, oOrderId).eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
                NbOrderPathEntity op = nbOrderPathService.getOne(wrapper, false);
                oc = new NbOrderCostEntity();
                oc.setPDriverId(ruleDriverId);
                oc.setOrderId(oOrderId);
                oc.setOrderNo(oOrderNo);
                oc.setPkgNo(oPkgNo);
                oc.setPostalCode(oDestPostalCode);
                oc.setDriverId(oDriverId);
                oc.setDriverBase(baseCost);
                oc.setDriverWeightSubsidy(driverWeightSubsidy);
                oc.setDriverSubsidy(BigDecimal.ZERO);
                oc.setCostStatus(OrderCostDto.COST_STATUS_1_ESTIMATE);
                oc.setPkgWeight(oPkgWeight);
                oc.setPkgVolumeWeight(volumeWeight);
                oc.setEstimateTime(op.getAddTime());
                oc.setEstimateTimestamp(System.currentTimeMillis());
                oc.setCostRuleItemId(ruleItem.getItemId());
                oc.setScId(oScId);
                oc.setTcId(oTcId);
                if (op != null) {
                    oc.setParcelScannedTime(op.getAddTime());
                }
                nbOrderCostMapper.insert(oc);
            } else {
                if (oDriverId != oc.getDriverId()) {
                    if (oc.getSubsidyLogId() > 0) {
                        oc.setDriverSubsidy(BigDecimal.ZERO);
                        int logId = oc.getSubsidyLogId();
                        XxlJobHelper.log("检测到司机发生变化，重新分配补贴：subSidyLogId=" + logId + ",orderId=" + oc.getOrderId());

                        NbTransferBatchCostModifyLogEntity modifyLog = nbTransferBatchCostModifyLogService.getById(logId);
                        if (modifyLog != null) {
                            // "select * from nb_transfer_batch_order tbo, nb_order o where tbo.order_id = o.order_id and tbo.batch_id = ?", log.getBatchId());
                            MPJLambdaWrapper<NbTransferBatchOrderEntity> wrapper = new MPJLambdaWrapper<>();
                            wrapper.selectAll(NbOrderEntity.class)
                                    .eq(NbTransferBatchOrderEntity::getBatchId, modifyLog.getBatchId())
                                    .selectAll(NbOrderEntity.class)
                                    .leftJoin(NbTransferBatchOrderEntity.class, NbTransferBatchOrderEntity::getOrderId, NbOrderEntity::getOrderId);
                            List<NbOrderEntity> orders = nbTransferBatchOrderMapper.selectJoinList(NbOrderEntity.class, wrapper);

//                            double perSubsidy = new BigDecimal(String.valueOf(modifyLog.getSubsidyAmount())).divide(new BigDecimal(String.valueOf(orders.size())), 2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            BigDecimal perSubsidy = modifyLog.getSubsidyAmount().divide(new BigDecimal(orders.size()), 2, RoundingMode.HALF_UP);
                            modifyLog.setAvgAmount(perSubsidy);
                            modifyLog.setOrderTotal(orders.size());
                            nbTransferBatchCostModifyLogService.updateById(modifyLog);

                            XxlJobHelper.log("补贴每单价格更新subSidyLogId=" + modifyLog.getLogId() + ",avgAmount=" + perSubsidy + ",orderTotal=" + orders.size());
                            for (NbOrderEntity order : orders) {
                                NbOrderCostEntity oldOc = nbOrderCostMapper.selectById(order.getOrderId());
                                if (order.getHasSubOrder()) {
                                    // 创建子订单的订单不能给补贴
                                    if (oldOc.getDriverSubsidy().compareTo(BigDecimal.ZERO) > 0) {
                                        oldOc.setDriverSubsidy(BigDecimal.ZERO);
                                        nbOrderCostMapper.updateById(oldOc);
                                    }
                                    continue;
                                }
                                oldOc.setDriverSubsidy(perSubsidy);
                                nbOrderCostMapper.updateById(oldOc);
                                XxlJobHelper.log("路区补贴更新：batchId=" + modifyLog.getBatchId() + ",orderId=" + oldOc.getOrderId() + ",perSubsidy=" + perSubsidy);
                            }
                        }
                    }
                }
                oc.setPDriverId(ruleDriverId);
                oc.setDriverId(oDriverId);
                oc.setDriverBase(baseCost);
                oc.setDriverWeightSubsidy(driverWeightSubsidy);
                oc.setCostStatus(OrderCostDto.COST_STATUS_1_ESTIMATE);
                oc.setPkgWeight(oPkgWeight);
                oc.setPkgVolumeWeight(volumeWeight);
                oc.setCostRuleItemId(ruleItem.getItemId());
                oc.setScId(oScId);
                oc.setTcId(oTcId);

                if (oc.getParcelScannedTime() == null) {
                    // "select * from nb_order_path where order_id = ? and order_status = ? order by path_id desc limit 1", oOrderId, Order.ORDER_STATUS_200_PARCEL_SCANNED);
                    LambdaQueryWrapper<NbOrderPathEntity> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(NbOrderPathEntity::getOrderId, oOrderId).eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
                    NbOrderPathEntity op = nbOrderPathService.getOne(wrapper, false);
                    if (op != null) {
                        oc.setParcelScannedTime(op.getAddTime());
                    }
                }
                nbOrderCostMapper.updateById(oc);
            }
            XxlJobHelper.log("订单pkgNo=" + oPkgNo + ",orderId=" + oOrderId + ",基准费=" + baseCost + ",重量补贴=" + driverWeightSubsidy + ",体积autoVolWeight=" + autoVolWeight + ",useWeight=" + useWeight);

        } else {
            saveOcFail(oOrderId, oDriverId, oOrderNo, oPkgNo, oDestPostalCode, OrderCostDto.COST_STATUS_10_OUTSIDE);
            log.error("邮编无效:" + oPkgNo + "->" + postalCode);
            XxlJobHelper.log("邮编无效:" + oPkgNo + "->" + postalCode);
        }
    }

    private void saveOcFail(int oOrderId, int driverId, String oOrderNo, String oPkgNo, String oDestPostalCode, int costStatus) {
        NbOrderCostEntity oc = nbOrderCostMapper.selectById(oOrderId);
        if (oc == null) {
            oc = new NbOrderCostEntity();
            oc.setOrderId(oOrderId);
            oc.setDriverId(driverId);
            oc.setOrderNo(oOrderNo);
            oc.setPkgNo(oPkgNo);
            oc.setPostalCode(oDestPostalCode);
            oc.setDriverBase(BigDecimal.ZERO);
            oc.setDriverSubsidy(BigDecimal.ZERO);
            oc.setCostStatus(costStatus);
            nbOrderCostMapper.insert(oc);
        } else {
            if (oc.getCostStatus().intValue() != costStatus || oc.getDriverId() != driverId) {
                oc.setCostStatus(costStatus);
                oc.setDriverId(driverId);
                nbOrderCostMapper.updateById(oc);
            }
        }

    }

    private boolean checkValue(BigDecimal... pkgValues) {
        for (BigDecimal value : pkgValues) {
            if (value == null || value.compareTo(BigDecimal.ZERO) == 0) {
                return false;
            }
        }
        return true;
    }

//    private boolean checkValue(Double... pkgValues) {
//        for (Double value : pkgValues) {
//            if (value == null || value == 0) {
//                return false;
//            }
//        }
//        return true;
//    }

}
