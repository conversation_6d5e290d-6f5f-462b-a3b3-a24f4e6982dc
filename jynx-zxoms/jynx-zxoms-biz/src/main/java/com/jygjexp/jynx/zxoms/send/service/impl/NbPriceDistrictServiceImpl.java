package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Maps;
import com.jygjexp.jynx.zxoms.dto.MerchantDto;
import com.jygjexp.jynx.zxoms.send.dto.PriceDistrictDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.mapper.NbPriceDistrictMapper;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiQuoteDomesticVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiQuoteInternationalVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiRequestParamsVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;
import com.jygjexp.jynx.zxoms.send.service.NbMerchantService;
import com.jygjexp.jynx.zxoms.send.service.NbPriceDistrictService;
import com.jygjexp.jynx.zxoms.send.service.NbPriceRuleService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.send.utils.ConfigUtil;
import com.jygjexp.jynx.zxoms.send.utils.Md5Util;
import com.jygjexp.jynx.zxoms.vo.NbPriceDistrictPageVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 价格区域
 *
 * <AUTHOR>
 * @date 2024-11-08 09:56:07
 */
@Service
@RequiredArgsConstructor
public class NbPriceDistrictServiceImpl extends ServiceImpl<NbPriceDistrictMapper, NbPriceDistrictEntity> implements NbPriceDistrictService {
    private final NbPriceDistrictMapper nbPriceDistrictMapper;
    private final NbSortingCenterService nbSortingCenterService;
    private final NbPriceRuleService priceRuleService;
    private final NbMerchantService nbMerchantService;
    private final ConfigUtil configUtil;

    private static final String token = "1000";
    private static final String secrtkey = "1a2b3c4d5f";

    @Override
    public Page<NbPriceDistrictPageVo> search(Page page, NbPriceDistrictEntity nbPriceDistrict) {
        //select id, login_id from eova_user; ds=eova;
        //select sc_id ID, center_name CN from nb_sorting_center; ds=nbd;
        MPJLambdaWrapper<NbPriceDistrictEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbPriceDistrictEntity.class)
                .select(NbSortingCenterEntity::getScId)
                .selectAs(NbSortingCenterEntity::getCenterName, NbPriceDistrictPageVo.Fields.sortingCenterName)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbPriceDistrictEntity::getScId);
        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbPriceDistrictEntity::getScId, idList);
        return nbPriceDistrictMapper.selectJoinPage(page, NbPriceDistrictPageVo.class, wrapper);
    }

    /**
     * 国标件询价
     *
     * @param vo
     * @return
     */
    @Override
    public OldResult getInternational(ApiQuoteInternationalVo vo, ApiRequestParamsVo paramsVo) {
        NbMerchantEntity merchant = nbMerchantService.getById(paramsVo.getAppId());
        String portCode = vo.getPortCode(); // 口岸代码
        // select * from nb_sorting_center where sc_code = ? limit 1", portCode);
        NbSortingCenterEntity sc = nbSortingCenterService.getOne(new LambdaQueryWrapper<NbSortingCenterEntity>().eq(NbSortingCenterEntity::getScCode, portCode), false);
        if (sc == null) {
            return OldResult.fail("460", "portCode inexistence[" + portCode + "]");
        }
        BigDecimal weight = vo.getWeight();
        String weightUom = vo.getWeightUom(); // KG, LB
        BigDecimal length = vo.getLength();
        BigDecimal width = vo.getWidth();
        BigDecimal height = vo.getHeight();
        String dimensionsUom = vo.getDimensionsUom(); // CM, IN
        String destPostalCode = vo.getDestPostalCode();
        String beforePostalCode = null;
        try {
            beforePostalCode = destPostalCode.substring(0, 2).toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
            return OldResult.fail("570", "destination postal code invalid[" + destPostalCode + "]");
        }

        // "select * from nb_price_district where is_dest_addr = true and apply_area = ? and cover_postal_code like ?", PriceDistrict.APPLY_AREA_1_INTERNATIONAL, "%" + beforePostalCode + "%", merchant.getMerchantId());
        NbPriceDistrictEntity pd = nbPriceDistrictMapper.selectOne(new LambdaQueryWrapper<NbPriceDistrictEntity>().eq(NbPriceDistrictEntity::getIsDestAddr, true)
                        .eq(NbPriceDistrictEntity::getApplyArea, PriceDistrictDto.APPLY_AREA_1_INTERNATIONAL)
                        .like(NbPriceDistrictEntity::getCoverPostalCode, "%" + beforePostalCode + "%")
                // .eq(NbPriceDistrictEntity::getMerchantId, merchant.getMerchantId()
        );
        if (pd == null) {
            return OldResult.fail("580", " Not within the delivery range. [" + destPostalCode + "]");
        }

        // 判断实际计价重量
        int chargeModel = merchant.getChargeModel();
        BigDecimal searchWeight = BigDecimal.ZERO;
        String weightColnum;
        if ("KG".equals(weightUom)) {
            weightColnum = "weight_kg";
        } else {
            weightColnum = "weight_lb";
        }

        if (chargeModel == MerchantDto.CHARGE_MODEL_1_REAL_WEIGHT) { // 实际重
            searchWeight = weight;
        }
        if (chargeModel == MerchantDto.CHARGE_MODEL_2_VOLUME_WEIGHT) { // 体积重
            BigDecimal volumeWeight = calculateVolumeWeight(length, width, height, dimensionsUom);
            searchWeight = volumeWeight;
        }
        if (chargeModel == MerchantDto.CHARGE_MODEL_2_VOLUME_WEIGHT) { // 最大值
            BigDecimal volumeWeight = calculateVolumeWeight(length, width, height, dimensionsUom);
            searchWeight = volumeWeight.max(weight);
        }

// "select * from nb_price_rule where merchant_id = ? and target_pd_id = ? and is_international = true and " + weightColnum + " >= ? order by " + weightColnum + " asc limit 1",
// merchant.getMerchantId(), pd.getPdId(), searchWeight);
        LambdaQueryWrapper<NbPriceRuleEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(NbPriceRuleEntity::getTargetPdId, pd.getPdId());
        qw.eq(NbPriceRuleEntity::getIsInternational, true);
        switch (weightColnum) {
            case "weight_lb":
                qw.ge(NbPriceRuleEntity::getWeightLb, searchWeight).orderByAsc(NbPriceRuleEntity::getWeightLb);
                break;
            case "weight_kg":
                qw.ge(NbPriceRuleEntity::getWeightKg, searchWeight).orderByAsc(NbPriceRuleEntity::getWeightKg);
                break;
            default:
                log.warn("Unexpected value for weightColnum: " + weightColnum);
        }
        qw.last("limit 1");
        NbPriceRuleEntity pr = priceRuleService.getOne(qw);

        if (pr == null) {
            return OldResult.fail("590", "No price available for the delivery area. Please contact the administrator." + destPostalCode);
        }

        BigDecimal discount = BigDecimal.ONE; // 2023-10-25 不要折扣比例了，每个商家不同的价目表
        JSONObject jo = new JSONObject();
//        jo.set("normal", doubleSafe(pr.getPriceNornal() * discount));
//        jo.set("express", doubleSafe(pr.getPriceExpress() * discount));
//        jo.set("multi", doubleSafe(pr.getPriceMulti() * discount));
        jo.set("normal", safeMultiply(pr.getPriceNornal(), discount));
        jo.set("express", safeMultiply(pr.getPriceExpress(), discount));
        jo.set("multi", safeMultiply(pr.getPriceMulti(), discount));

        BigDecimal standerWeight = BigDecimal.ZERO;
        if ("KG".equals(weightUom)) {
            standerWeight = merchant.getMultiMinWeightKg();
        } else {
            standerWeight = merchant.getMultiMinWeightLb();
        }
        if (searchWeight.compareTo(standerWeight) < 0) {
            jo.remove("multi");
            jo.set("multiNote", "一票多件起重为" + standerWeight + weightUom);
        }

        JSONObject from = new JSONObject();
        from.set("destPostalCode", destPostalCode);
        from.set("weight", weight);
        from.set("weightUom", weightUom);
        from.set("length", length);
        from.set("width", width);
        from.set("height", height);
        from.set("dimensionsUom", dimensionsUom);

        jo.set("request", from);
        return OldResult.ok("0", jo);
    }

    // 体积重量计算方法
    private BigDecimal calculateVolumeWeight(BigDecimal length, BigDecimal width, BigDecimal height, String dimensionsUom) {
        BigDecimal denominator;

        if ("CM".equals(dimensionsUom)) {
            denominator = new BigDecimal(configUtil.findIntegerByKey(ConfigUtil.volume_weight_international_cm));
        } else {
            denominator = new BigDecimal(configUtil.findIntegerByKey(ConfigUtil.volume_weight_international_in));
        }

        // 使用 BigDecimal 进行精确计算，体积重 = (长 * 宽 * 高) / 除数
        return length.multiply(width).multiply(height)
                .divide(denominator, 2, RoundingMode.HALF_UP); // 保留2位小数，四舍五入
    }

    /**
     * 查询国内价格
     *
     * @param vo
     * @param paramsVo
     * @return
     */
    @Override
    public OldResult getDomestic(ApiQuoteDomesticVo vo, ApiRequestParamsVo paramsVo) {
        NbMerchantEntity merchant = nbMerchantService.getById(paramsVo.getAppId());
        BigDecimal weight = vo.getWeight();
        String weightUom = vo.getWeightUom(); // KG, LB
        BigDecimal length = vo.getLength();
        BigDecimal width = vo.getWidth();
        BigDecimal height = vo.getHeight();
        String dimensionsUom = vo.getDimensionsUom(); // CM, IN
        String destPostalCode = vo.getDestPostalCode();
        String senderPostalCode = vo.getSenderPostalCode();
        String preDestPostalCode = null;
        try {
            preDestPostalCode = destPostalCode.substring(0, 3).toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
            return OldResult.fail("570", "destination postal code invalid[" + destPostalCode + "]");
        }
        String preSenderPostalCode = null;
        try {
            preSenderPostalCode = senderPostalCode.substring(0, 3).toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
            return OldResult.fail("570", "sender postal code invalid[" + senderPostalCode + "]");
        }

        // "select * from nb_price_district where is_dest_addr = true and apply_area = ? and cover_postal_code like ?", PriceDistrict.APPLY_AREA_2_DOMESTIC, "%" + preDestPostalCode + "%");
        NbPriceDistrictEntity targetPd = nbPriceDistrictMapper.selectOne(new LambdaQueryWrapper<NbPriceDistrictEntity>().eq(NbPriceDistrictEntity::getIsDestAddr, true)
                .eq(NbPriceDistrictEntity::getApplyArea, PriceDistrictDto.APPLY_AREA_2_DOMESTIC)
                .like(NbPriceDistrictEntity::getCoverPostalCode, "%" + preDestPostalCode + "%")
        );
        if (targetPd == null) {
            return OldResult.fail("580", " Not within the delivery range. [" + destPostalCode + "]");
        }

        // "select * from nb_price_district where is_dest_addr = false and apply_area = ? and cover_postal_code like ?", PriceDistrict.APPLY_AREA_2_DOMESTIC, "%" + preSenderPostalCode + "%");
        NbPriceDistrictEntity fromPd = nbPriceDistrictMapper.selectOne(new LambdaQueryWrapper<NbPriceDistrictEntity>().eq(NbPriceDistrictEntity::getIsDestAddr, false)
                .eq(NbPriceDistrictEntity::getApplyArea, PriceDistrictDto.APPLY_AREA_2_DOMESTIC)
                .like(NbPriceDistrictEntity::getCoverPostalCode, "%" + preSenderPostalCode + "%")
        );
        if (fromPd == null) {
            return OldResult.fail("580", " Not within the sender range. [" + senderPostalCode + "]");
        }

        // 判断实际计价重量
        int chargeModel = merchant.getChargeModel();
        BigDecimal searchWeight = BigDecimal.ZERO;
        String weightColnum;
        if ("KG".equals(weightUom)) {
            weightColnum = "weight_kg";
        } else {
            weightColnum = "weight_lb";
        }
        if (chargeModel == MerchantDto.CHARGE_MODEL_1_REAL_WEIGHT) { // 实际重
            searchWeight = weight;
        }
        if (chargeModel == MerchantDto.CHARGE_MODEL_2_VOLUME_WEIGHT) { // 体积重
            BigDecimal volumeWeight = calculateVolumeWeight(length, width, height, dimensionsUom);
            searchWeight = volumeWeight;
        }
        if (chargeModel == MerchantDto.CHARGE_MODEL_2_VOLUME_WEIGHT) { // 最大值
            BigDecimal volumeWeight = calculateVolumeWeight(length, width, height, dimensionsUom);
            searchWeight = volumeWeight.max(weight);
        }

// "select * from nb_price_rule where merchant_id = ? and from_pd_id = ? and target_pd_id = ? and is_international = false and " + weightColnum + " >= ? order by " + weightColnum + " asc limit 1",
// merchant.getMerchantId(), fromPd.getPdId(), targetPd.getPdId(), searchWeight);
        LambdaQueryWrapper<NbPriceRuleEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(NbPriceRuleEntity::getMerchantId, merchant.getMerchantId());
        qw.eq(NbPriceRuleEntity::getFromPdId, fromPd.getPdId());
        qw.eq(NbPriceRuleEntity::getTargetPdId, targetPd.getPdId());
        qw.eq(NbPriceRuleEntity::getIsInternational, false);
        switch (weightColnum) {
            case "weight_lb":
                qw.ge(NbPriceRuleEntity::getWeightLb, searchWeight).orderByAsc(NbPriceRuleEntity::getWeightLb);
                break;
            case "weight_kg":
                qw.ge(NbPriceRuleEntity::getWeightKg, searchWeight).orderByAsc(NbPriceRuleEntity::getWeightKg);
                break;
            default:
                log.warn("Unexpected value for weightColnum: " + weightColnum);
        }
        qw.last("limit 1");
        NbPriceRuleEntity pr = priceRuleService.getOne(qw);

        if (pr == null) {
            // "select * from nb_price_rule where merchant_id = ? and from_pd_id = ? and target_pd_id = ? and is_international = false order by " + weightColnum + " desc limit 1",
            // merchant.getMerchantId(), fromPd.getPdId(), targetPd.getPdId());
            LambdaQueryWrapper<NbPriceRuleEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(NbPriceRuleEntity::getMerchantId, merchant.getMerchantId());
            wrapper.eq(NbPriceRuleEntity::getFromPdId, fromPd.getPdId());
            wrapper.eq(NbPriceRuleEntity::getTargetPdId, targetPd.getPdId());
            wrapper.eq(NbPriceRuleEntity::getIsInternational, false);
            switch (weightColnum) {
                case "weight_lb":
                    wrapper.orderByDesc(NbPriceRuleEntity::getWeightLb);
                    break;
                case "weight_kg":
                    wrapper.orderByDesc(NbPriceRuleEntity::getWeightKg);
                    break;
                default:
                    log.warn("Unexpected value for weightColnum: " + weightColnum);
            }
            wrapper.last("limit 1");
            pr = priceRuleService.getOne(wrapper);
        }
        if (pr == null) {
            return OldResult.fail("590", "No price available for the sender to delivery area. Please contact the administrator." + senderPostalCode + "->" + destPostalCode);
        }

        BigDecimal discount = BigDecimal.ONE; // 2023-10-25 不要折扣比例了，每个商家不同的价目表
        JSONObject jo = new JSONObject();
        jo.set("normal", safeMultiply(pr.getPriceNornal(), discount));
        jo.set("express", safeMultiply(pr.getPriceExpress(), discount));
        jo.set("multi", safeMultiply(pr.getPriceMulti(), discount));
        BigDecimal standerWeight = BigDecimal.ZERO;
        if ("KG".equals(weightUom)) {
            standerWeight = merchant.getMultiMinWeightKg();
        } else {
            standerWeight = merchant.getMultiMinWeightLb();
        }
        if (searchWeight.compareTo(standerWeight) < 0) {
            jo.remove("multi");
            jo.set("multiNote", "一票多件起重为" + standerWeight + weightUom);
        }
        JSONObject from = new JSONObject();
        from.set("senderPostalCode", senderPostalCode);
        from.set("destPostalCode", destPostalCode);
        from.set("weight", weight);
        from.set("weightUom", weightUom);
        from.set("length", length);
        from.set("width", width);
        from.set("height", height);
        from.set("dimensionsUom", dimensionsUom);
        jo.set("request", from);
        return OldResult.ok("0", jo);
    }

    // 测试International
    @Override
    public OldResult testInternational(ApiQuoteInternationalVo vo, ApiRequestParamsVo paramsVo, String destAddress1, String destAddress2) {
        String baseUrl = vo.getBaseUrl();   // http://driver.neighbourexpress.ca
        String portCode = vo.getPortCode();
        BigDecimal weight = vo.getWeight();
        String weightUom = vo.getWeightUom(); // KG, LB

        BigDecimal length = vo.getLength();
        BigDecimal width = vo.getWidth();
        BigDecimal height = vo.getHeight();
        String dimensionsUom = vo.getDimensionsUom(); // CM, IN
        String destPostalCode = vo.getDestPostalCode();

        Map<String, Object> formMap = getCommonParam();
        formMap.put("portCode", portCode);
        formMap.put("destAddress1", destAddress1);
        formMap.put("destAddress2", destAddress2);
        formMap.put("destPostalCode", destPostalCode);
        formMap.put("weight", String.valueOf(weight));
        formMap.put("weightUom", weightUom);
        formMap.put("length", String.valueOf(length));
        formMap.put("width", String.valueOf(width));
        formMap.put("height", String.valueOf(height));
        formMap.put("dimensionsUom", dimensionsUom);

        addSign(formMap);

        HttpResponse response = HttpRequest.post(baseUrl + "/api/quote/international").form(formMap).execute();
        return OldResult.ok("0", response.body());
    }

    // 测试Domestic
    @Override
    public OldResult testDomestic(ApiQuoteDomesticVo vo, ApiRequestParamsVo paramsVo, String destAddress1, String destAddress2) {
        String baseUrl = vo.getBaseUrl();
        BigDecimal weight = vo.getWeight();
        String weightUom = vo.getWeightUom(); // KG, LB
        BigDecimal length = vo.getLength();
        BigDecimal width = vo.getWidth();
        BigDecimal height = vo.getHeight();
        String dimensionsUom = vo.getDimensionsUom(); // CM, IN
        String senderPostalCode = vo.getSenderPostalCode();
        String destPostalCode = vo.getDestPostalCode();

        Map<String, Object> formMap = getCommonParam();
        formMap.put("senderPostalCode", senderPostalCode);
        formMap.put("destAddress1", destAddress1);
        formMap.put("destAddress2", destAddress2);
        formMap.put("destPostalCode", destPostalCode);
        formMap.put("weight", String.valueOf(weight));
        formMap.put("weightUom", weightUom);
        formMap.put("length", String.valueOf(length));
        formMap.put("width", String.valueOf(width));
        formMap.put("height", String.valueOf(height));
        formMap.put("dimensionsUom", dimensionsUom);

        addSign(formMap);
        HttpResponse response = HttpRequest.post(baseUrl + "/api/quote/domestic").form(formMap).execute();
        return OldResult.ok("0", response.body());
    }

    // 安全乘法
    private BigDecimal safeMultiply(BigDecimal value, BigDecimal multiplier) {
        return value.multiply(multiplier).setScale(2, RoundingMode.HALF_UP);
    }

    private Map<String, Object> getCommonParam() {
        String randomstr = RandomStringUtils.randomAlphabetic(8);
        String timestamp = System.currentTimeMillis() / 1000 + "";
        Map<String, Object> params = Maps.newTreeMap();
        params.put("appId", token);
        params.put("randomstr", randomstr);
        params.put("timestamp", timestamp);

        return params;
    }

    private void addSign(Map<String, Object> params) {
        String sign = getSign(params, secrtkey);
        params.put("sign", sign);
    }

    private String getSign(Map<String, Object> params, String secrtkey) {
        StringBuffer sb = new StringBuffer();
        Iterator<Map.Entry<String, Object>> iter = params.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<String, Object> entry = iter.next();

            if (entry.getValue() != null && StrUtil.isNotBlank(entry.getValue().toString())) {
                try {
                    sb.append(entry.getKey() + "=" + URLEncoder.encode(entry.getValue().toString(), "UTF-8")).append("&");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }
        }
        String before = sb.append("secretkey=").append(secrtkey).toString();

//		String before = "logiNo=" + logiNo + "&randomstr=" + randomstr + "&timestamp=" + timestamp + "&token=" + token + "&secretkey=" + secrtkey;
        System.out.println(before);
        String sign = Md5Util.getMD5(before);

        return sign;
    }

    //    private double doubleSafe(double d) {
//        return new BigDecimal(d).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//    }

//    private double volumeWeight(Double length, Double width, Double height, String dimensionsUom) {
//        int denominator = 0;
//        if ("CM".equals(dimensionsUom)) {
//            denominator = configUtil.findIntegerByKey(ConfigUtil.volume_weight_international_cm);
//        } else {
//            denominator = configUtil.findIntegerByKey(ConfigUtil.volume_weight_international_in);
//        }
//
//        double volumeWeight = new BigDecimal((length * width * height) / denominator).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//        return volumeWeight;
//    }

}