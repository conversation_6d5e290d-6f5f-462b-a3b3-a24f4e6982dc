package com.jygjexp.jynx.zxoms.nbapp.controller.manager;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.dto.ShelfPkgLogDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.dto.OrderShelfPkgLogDto;
import com.jygjexp.jynx.zxoms.nbapp.utils.RedisUtil;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.utils.NBDUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: chenchang
 * @Description: 存储柜-货架自提相关
 * @Date: 2024/10/31 23:24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver/manager/shelf")
@Tag(description = "appshelf", name = "APP-货架自提")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ShelfController extends BaseController {
    private final NbShelfService shelfService;
    private final NbSortingCenterService sortingCenterService;
    private final NbOrderService orderService;
    private final NbTransferCenterService transferCenterService;
    private final NbShelfPkgLogService shelfPkgLogService;
    private final NbOrderPathService orderPathService;
    private final NbOrderSignImageService orderSignImageService;
    private final CommonDataUtil commonDataUtil;
    private final RedisUtil redisUtil;

    @Operation(summary = "APP-货架识别扫码", description = "APP-货架识别扫码")
    @PostMapping("/checkCode")
    public R checkCode(@RequestParam("scanCode") @NotBlank(message = "扫描码不能为空") String scanCode) {
        NbDriverEntity loginDriver = getLoginDriver();
        return R.ok(sortingCenterService.doCheckCode(scanCode, loginDriver));
    }

    /**
     * 上架
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "APP-上架", description = "APP-上架")
    @PostMapping("/putaway")
    public R putaway(@RequestParam("pkgNo") @NotBlank(message = "包裹号不能为空") String pkgNo, @RequestParam("shelfCode") @NotBlank(message = "货架编号不能为空") String shelfCode,
                     HttpServletRequest request) {
        // "select * from nb_shelf where shelf_code = ? limit 1", shelfCode);
        NbShelfEntity shelf = shelfService.getOne(new LambdaQueryWrapper<NbShelfEntity>().eq(NbShelfEntity::getShelfCode, shelfCode), false);
        if (shelf == null) {
            return LocalizedR.failed("shelf.the.shelf.does.not.exist", shelfCode);
        }

        if (shelf.getIsValid() == false) {
            return LocalizedR.failed("shelf.the.shelf.has.been.set.as.invalid", shelfCode);
        }

        // "select * from nb_order where pkg_no = ? limit 1", pkgNo);
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo, pkgNo), false);
        if (order == null) {
            return LocalizedR.failed("driverorder.package.does.not.exist", pkgNo);
        }

        int orderStatus = order.getOrderStatus();

        List<Integer> accessOrderStatus = Lists.newArrayList();
        accessOrderStatus.add(OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT);
        accessOrderStatus.add(OrderDto.ORDER_STATUS_280_FAILURE);
        accessOrderStatus.add(OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);

        if (!accessOrderStatus.contains(orderStatus)) {
            return LocalizedR.failed("shelf.only.orders.in.status.can.be.listed", Optional.ofNullable(null));
        }

        NbDriverEntity loginDriver = getLoginDriver();

        NbSortingCenterEntity sc = sortingCenterService.getById(shelf.getScId());
        String timezone = sc.getScTimezone();

        order.setOrderStatus(OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE);
        orderService.updateById(order);
//        Date now = Date.from(ZonedDateTime.now(ZoneId.of(timezone)).toInstant());

        Double lat = null, lng = null;
        Double[] latlng = getLatLng();

        if (latlng != null) {
            lat = latlng[0];
            lng = latlng[1];
        }
        String address = commonDataUtil.getAddress(sc.getProvinceId(), sc.getCityId());

        String keyToString = "nb_shelf_putaway";
        log.info("LOCKKEY->".concat(keyToString));

        String requestId = String.valueOf(System.currentTimeMillis());
        try {
            while (!redisUtil.tryLock(keyToString, requestId, 10)) {
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                log.info("lock waiting");
            }

            // "select * from nb_shelf_pkglog where order_id = ? limit 1", order.getOrderId());
            NbShelfPkgLogEntity exist = shelfPkgLogService.getOne(new LambdaQueryWrapper<NbShelfPkgLogEntity>().eq(NbShelfPkgLogEntity::getOrderId, order.getOrderId()), false);
            if (exist != null) {
                return LocalizedR.failed("nbsortingCenter.the.package.has.been.shelved", exist.getPutawayCode());
            }

            // OrderPath op = new OrderPath().build(order.getOrderId(), order.getOrderStatus(), loginDriver.getDriverId(), lat, lng, address, timezone);
            NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), SecurityUtils.getUser().getId(), lat, lng, address, timezone);
            op.setScId(loginDriver.getScId());
            orderPathService.save(op);

            // "select count(1) cont from nb_shelf_pkglog where shelf_id = ? and pickup_driver_id = 0", shelf.getShelfId()).getInt("cont");
            Long count = shelfPkgLogService.count(new LambdaQueryWrapper<NbShelfPkgLogEntity>().eq(NbShelfPkgLogEntity::getShelfId, shelf.getShelfId()).eq(NbShelfPkgLogEntity::getPickupDriverId, 0));
            int total = Math.toIntExact(count);

            Date datetimeNow = NBDUtils.getLocalDate(timezone, Instant.now().toEpochMilli());
            String ymd = DateFormatUtils.format(datetimeNow, "yyyy-MM-dd");

            //  "select count(1) cont from nb_shelf_pkglog where putaway_datetime >= '" + ymd + " 00:00:00' and putaway_datetime <= '" + ymd + " 23:59:59'").getInt("cont");
            Long count1 = shelfPkgLogService.count(new LambdaQueryWrapper<NbShelfPkgLogEntity>().between(NbShelfPkgLogEntity::getPutawayDatetime, ymd + " 00:00:00", ymd + " 23:59:59"));
            int todayTotal = Math.toIntExact(count1);

            int day = LocalDate.now().getDayOfMonth();
            String putawayCode = String.format("%02d", day);
//            putawayCodeNew += String.format("%04d", (todayTotal + 1));

            // 2024-04-16
            // 那就用日期加四位数的方式吧，确保在货架上的号码是唯一值，在生成的时候和货架上现在的号码做一个校验，只有号码下架后才会再次采用，按照数字递增顺序生成
//			String putawayCode = shelfCode + "-" + String.format("%04d", total + 1);
			String putawayCodeNew = shelfCode + "-" + putawayCode + String.format("%04d", total + 1);

            NbShelfPkgLogEntity spl = new NbShelfPkgLogEntity();
            spl.setLogTime(Instant.now().toEpochMilli());
            spl.setShelfId(shelf.getShelfId());
            spl.setOrderId(order.getOrderId());
            spl.setPutawayCode(putawayCodeNew);
            spl.setPutawayTime(Instant.now().toEpochMilli());
            spl.setPutawayDriverId(loginDriver.getDriverId());
            spl.setPickupDriverId(0l);
            spl.setPickupTime(0l);
            spl.setPutawayDatetime(datetimeNow);
            spl.setSyncJyStatus(ShelfPkgLogDto.JY_SYNC_STATUS_0_NONE);
            shelfPkgLogService.save(spl);

            shelf.setOrderTotal(total + 1);
            shelfService.updateById(shelf);

            JSONObject ret = new JSONObject();
            ret.set("putawayCode", putawayCodeNew);
            ret.set("pkgNo", pkgNo);

            log.info("上架包裹:orderId=" + order.getOrderId() + ",shelfId=" + shelf.getShelfId() + ",putawayCode=" + putawayCodeNew);
            return R.ok(ret);
        } finally {
            redisUtil.releaseLock(keyToString, requestId);
        }

    }

    /**
     * 查询包裹
     */
    @Operation(summary = "APP-查询包裹", description = "APP-查询包裹")
    @PostMapping("/search")
    public R search(@RequestParam(value = "mobile", required = false) String mobile, @RequestParam(value = "pkgNo" , required = false)  String pkgNo,
                    @RequestParam(value = "putawayCode" , required = false)  String putawayCode) {
        // 参数至少传一个校验
        if (StrUtil.isAllBlank(mobile, pkgNo, putawayCode)) {
            return LocalizedR.failed("appshelf.at.least.one.parameter", Optional.ofNullable(null));
        }
        NbDriverEntity loginDriver = getLoginDriver();

        MPJLambdaWrapper<NbOrderEntity> wrapper = JoinWrappers.lambda(NbOrderEntity.class);
        wrapper.selectAll(NbOrderEntity.class).selectAs(NbShelfPkgLogEntity::getPutawayCode, OrderShelfPkgLogDto.Fields.putawayCode)
                .selectAs(NbShelfPkgLogEntity::getPutawayTime, OrderShelfPkgLogDto.Fields.putawayTime)
                .selectAs(NbShelfPkgLogEntity::getPickupDriverId, OrderShelfPkgLogDto.Fields.pickupDriverId)
                .selectAs(NbShelfPkgLogEntity::getPickupTime, OrderShelfPkgLogDto.Fields.pickupTime);
        wrapper.leftJoin(NbShelfPkgLogEntity.class,"pg", NbShelfPkgLogEntity::getOrderId, NbOrderEntity::getOrderId);
        wrapper.leftJoin(NbShelfEntity.class,"s", NbShelfEntity::getShelfId, NbShelfPkgLogEntity::getShelfId);
        wrapper.eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE).eq("pg.pickup_driver_id", 0);
        if (StrUtil.isNotBlank(mobile)) {
            wrapper.like("t.dest_tel", mobile);
        }
        if (StrUtil.isNotBlank(pkgNo)) {
            wrapper.eq("t.pkg_no", pkgNo);
        }
        if (StrUtil.isNotBlank(putawayCode)) {
            wrapper.eq("pg.putaway_code", putawayCode);
        }
        // 添加登录司机的条件
        wrapper.and(w -> w.eq("s.tc_id", loginDriver.getTcId()).or().eq("s.sc_id", loginDriver.getScId()));
        List<OrderShelfPkgLogDto> orders = orderService.selectJoinList(OrderShelfPkgLogDto.class, wrapper);

        JSONArray ja = orders.stream().map(o -> {
            JSONObject jo = new OrderDto().toDriverListJson(o);
            jo.put("putawayTime", o.getPutawayTime() == null ? "-" : DateFormatUtils.format(o.getPutawayTime(), "yyyy-MM-dd HH:mm:ss"));
            jo.put("pickupTime", o.getPickupTime() == null ? "-" : DateFormatUtils.format(o.getPickupTime(), "yyyy-MM-dd HH:mm:ss"));
            jo.put("putawayCode", o.getPutawayCode());
            jo.put("pickupDriverId", o.getPickupDriverId());
            return jo;
        }).collect(Collectors.toCollection(JSONArray::new));

        return R.ok(ja);
    }

    /**
     * 提货
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "APP-提货", description = "APP-提货")
    @PostMapping("/pickup")
    public R pickup(@RequestParam("orderId") @NotBlank(message = "订单号不能为空") Integer orderId,HttpServletRequest request) {
        // "select * from nb_shelf_pkg_log where order_id = ? limit 1", orderId);
        NbShelfPkgLogEntity shelfPkgLog = shelfPkgLogService.getOne(new LambdaQueryWrapper<NbShelfPkgLogEntity>().eq(NbShelfPkgLogEntity::getOrderId, orderId),false);
        if (shelfPkgLog == null) {
            return LocalizedR.failed("driverorder.package.does.not.exist", Optional.ofNullable(null));
        }
        NbDriverEntity loginDriver = getLoginDriver();
        if (shelfPkgLog.getPickupDriverId() > 0) {
            NbSortingCenterEntity sc = sortingCenterService.getById(loginDriver.getScId());
            String timezone = sc.getScTimezone();
            String pickupTime = Instant.ofEpochSecond(shelfPkgLog.getPickupTime()).atZone(ZoneId.of(timezone)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return LocalizedR.failed("shelf.package.has.been.picked.up", pickupTime);
        }

        //签收照片
//        JSONArray urlsJa = JSONUtil.parseArray(urls);
//        Double[] latlng = getLatLng(request);
//        for (int i = 0; i < urlsJa.size(); i++) {
//            JSONObject url = urlsJa.getJSONObject(i);
//
//            NbOrderSignImageEntity image = new NbOrderSignImageEntity();
//            image.setOrderId(orderId);
//            image.setPkgImage(url.getStr("thumb"));
//            image.setOriPkgImage(url.getStr("url"));
//            image.setFileId(url.getInt("fileId"));
//            image.setAddTime(new Date());
//            image.setDriverId(loginDriver.getDriverId());
//
//            if (latlng != null) {
//                image.setLat(latlng[0]);
//                image.setLng(latlng[1]);
//            }
//            orderSignImageService.save(image);
//        }

        NbShelfEntity shelf = shelfService.getById(shelfPkgLog.getShelfId());
        NbSortingCenterEntity sc = sortingCenterService.getById(loginDriver.getScId());

        if (sc.getScTimezone() == null) {
            return R.failed("Sorting Center timezone is null");
        }
        Date pickupDatetime = NBDUtils.getLocalDate(sc.getScTimezone(), Instant.now().toEpochMilli());

        shelfPkgLog.setPickupDriverId(loginDriver.getDriverId().longValue());
        shelfPkgLog.setPickupTime(Instant.now().toEpochMilli());
        shelfPkgLog.setPickupDatetime(pickupDatetime);
        shelfPkgLogService.updateById(shelfPkgLog);

        NbOrderEntity order = orderService.getById(shelfPkgLog.getOrderId());
        // 2024-04-16 自提由205改为295
        order.setOrderStatus(OrderDto.ORDER_STATUS_295_SELF_PICKUP);
        order.setDeliveryedTime(pickupDatetime);
        order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_10_SELF_PICKUP);
        orderService.updateById(order);

        Double[] latlng = getLatLng();
        Double lat = null, lng = null;
        if (latlng != null) {
            lat = latlng[0];
            lng = latlng[1];
        }
        String address = null;
        String timezone = ZoneId.systemDefault().getId();
        if (shelf.getScId() > 0) {
            address = commonDataUtil.getAddress(sc.getProvinceId(), sc.getCityId());
            timezone = sc.getScTimezone();
        }
        if (shelf.getTcId() > 0) {
            NbTransferCenterEntity tc = transferCenterService.getById(shelf.getTcId());
            address = commonDataUtil.getAddress(tc.getProvinceId(), tc.getCityId());

            timezone = tc.getTcTimezone();
        }
        NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), SecurityUtils.getUser().getId(), lat, lng, address, timezone);
        op.setScId(shelf.getScId());

        op.setTcId(shelf.getTcId());
        orderPathService.save(op);

        return R.ok();
    }

}
