package com.jygjexp.jynx.zxoms.nbapp.controller.driver;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.dto.AppOrderDto;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.*;

import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: xiongpengfei
 * @Description: app-路区相关操作
 * @Date: 2024/11/5 14:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver/routeGroup" )
@Tag(description = "appDriverRouteGroup" , name = "APP-路区相关" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Slf4j
public class RouteGroupController extends BaseController {
    private final NbTransferBatchService nbTransferBatchService;
    private final NbOrderService nbOrderService;
    private final NbDriverService driverService;
    private final NbOrderPathService orderPathService;
    private final NbTransferCenterService transferCenterService;
    private final CommonDataUtil commonDataUtil;


    /**
     * 获取有效路区和对应批次订单列表
     */
    @Operation(summary = "获取有效路区和对应批次订单列表" , description = "获取有效路区和对应批次订单列表" )
    @PostMapping("/listNotallocated" )
    public R listNotallocated() {
        Integer pageNo = 1;
        Integer pageSize = 10;
        // paginate(pageNo, pageSize, "select *", "from nb_transfer_batch where driver_id = 0 and is_delete = false order by batch_id desc");
        Page<NbTransferBatchEntity> page = nbTransferBatchService.page(new Page<>(pageNo, pageSize),
                new LambdaQueryWrapper<>(NbTransferBatchEntity.class).eq(NbTransferBatchEntity::getDriverId,0).orderByDesc(NbTransferBatchEntity::getBatchId));
        JSONArray ja = page.getRecords().stream().map(transferBatch -> {
            JSONObject jo = new JSONObject();
            jo.set("batchId", transferBatch.getBatchId());
            jo.set("batchNo", transferBatch.getBatchNo());
            jo.set("batchCode", transferBatch.getBatchCode());
            jo.set("driverId", transferBatch.getDriverId());
            jo.set("orderTotal", transferBatch.getOrderTotal());
            jo.set("estimatedHour", transferBatch.getEstimatedHour());
            // "select o.* from nb_order o, nb_transfer_batch_order tbo where tbo.order_id = o.order_id and tbo.batch_id = ?", tb.getBatchId());
            List<NbOrderEntity> orders=nbOrderService.findOrderByTransferBatchId(transferBatch.getBatchId());
            JSONArray orderJa=orders.stream().map(order -> new OrderDto().toDriverListJson(order))
                    .collect(Collectors.toCollection(JSONArray::new));
            jo.set("orders", orderJa);
            return jo;
        }).collect(Collectors.toCollection(JSONArray::new));
        return R.ok(ja);
    }


    /**
     * APP出库扫描     driverId司机id、routeNo路区编号、accessKey：sessionid
     */
    @Operation(summary = "APP出库扫描" , description = "APP出库扫描" )
    @PostMapping("/outbound" )
    public R outbound(@RequestParam("driverId") Integer driverId,@RequestParam("routeNo") String routeNo) {
        //查出当前登录司机的信息
        // "select * from nb_driver where session_id = ? limit 1", accessKey);
        //NbDriverEntity driver  = driverService.getOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getSessionId, accessKey));
         NbDriverEntity loginDriver = getLoginDriver();
//        NbDriverEntity loginDriver = new NbDriverEntity();

        // "select * from nb_transfer_batch where batch_code = ? limit 1", routeNo);
        NbTransferBatchEntity batch = nbTransferBatchService.getOne(new LambdaQueryWrapper<NbTransferBatchEntity>().eq(NbTransferBatchEntity::getBatchNo, routeNo));
        if (batch == null) {
            return LocalizedR.failed("routeGroup.route.does.not.exist", routeNo);
        }
        Double lat = null, lng = null;
        String address = null;
        //获取系统默认的时区标识符
        String timezone = ZoneId.systemDefault().getId();
        if (batch.getTranferDriverId() == 0){
            // 包裹内订单更新状态
            // "select o.* from nb_order o, nb_transfer_batch_order tbo where o.order_id = tbo.order_id and tbo.batch_id = ?", batch.getBatchId());
            List<NbOrderEntity> orders=nbOrderService.findOrderByTransferBatchId(batch.getBatchId());
            for (NbOrderEntity order: orders) {
                order.setOrderStatus(AppOrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER);
                //更新状态-开往转运中心
                nbOrderService.updateById(order);
                NbOrderPathEntity op = new OrderPathDto().createPath(order.getOrderId(),order.getOrderStatus(),loginDriver != null ? loginDriver.getDriverId() : 0,lat,lng,address,timezone);
                orderPathService.save(op);
            }
            batch.setTranferDriverId(driverId);
            batch.setLoadingTime(new Date());
            nbTransferBatchService.updateById(batch);
            return R.ok();
        } else {
            if (batch.getTranferDriverId() == driverId) {
                return LocalizedR.failed("driverorder.repeat.scanning", Optional.ofNullable(null));
            } else {
                Integer otherDriverId = batch.getTranferDriverId();
                NbDriverEntity otherDrive = driverService.getById(otherDriverId);
                return LocalizedR.failed("routeGroup.transferred.by.other.drivers", otherDrive.getDriverId());
            }
        }

    }


    /**
     * 撤销出库  routeNo路区编号
     */
    @Operation(summary = "APP撤销出库" , description = "APP撤销出库" )
    @PostMapping("/revocationOutbound" )
    public R revocationOutbound(@RequestParam("routeNo") String routeNo) {

        //("select * from nb_transfer_batch where batch_code = ? limit 1", routeNo);
        NbTransferBatchEntity batch = nbTransferBatchService.getOne(new LambdaQueryWrapper<NbTransferBatchEntity>().eq(NbTransferBatchEntity::getBatchCode,routeNo),false);
        if (batch == null){
            return LocalizedR.failed("routeGroup.route.does.not.exist", routeNo);
        }
        //判断是否有运输司机
        if (batch.getTranferDriverId() == 0){
            return LocalizedR.failed("routeGroup.unboarded.status.no.need.to.cancel", Optional.ofNullable(null));
        }
        //判断卸货时间是否存在
        if (batch.getUnloadingTime() != null){
            return LocalizedR.failed("routeGroup.scanned.unloading.cannot.be.revoked", Optional.ofNullable(null));
        }

        // 包裹内订单更新状态   ORDER_STATUS_200_PARCEL_SCANNED--到达配送中心   ORDER_STATUS_201_TO_TRANSIT_CENTER--送至转运中心
        // "select o.* from nb_order o, nb_transfer_batch_order tbo where o.order_id = tbo.order_id and tbo.batch_id = ?", batch.getBatchId());
        List<NbOrderEntity> orders=nbOrderService.findOrderByTransferBatchId(batch.getBatchId());
        for (NbOrderEntity order : orders) {
            order.setOrderStatus(AppOrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
            //更新状态到达配送中心
            nbOrderService.updateById(order);

            // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", order.getOrderId(), Order.ORDER_STATUS_201_TO_TRANSIT_CENTER);
            NbOrderPathEntity op = orderPathService.getOne(new LambdaQueryWrapper<NbOrderPathEntity>().eq(NbOrderPathEntity::getOrderId,order.getOrderId())
                    .eq(NbOrderPathEntity::getOrderStatus,AppOrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER),false);
            if (op != null) {
                orderPathService.removeById(op.getPathId());
            }

        }

        batch.setTranferDriverId(0);
        nbTransferBatchService.updateById(batch);
        return R.ok();
    }

    /**
     * 到达转运中心   code:路区码   tcId：战转运中心    accessKey：sessionid
     */
    @Operation(summary = "APP-到达转运中心" , description = "APP-到达转运中心" )
    @PostMapping("/arrival" )
    public R arrival(@RequestParam("code") String code, @RequestParam("tcId") Integer tcId) {

        //查出当前登录司机的信息
        // "select * from nb_driver where session_id = ? limit 1", accessKey);
        //NbDriverEntity driver  = driverService.getOne(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getSessionId, accessKey));
         NbDriverEntity loginDriver = getLoginDriver();
//        NbDriverEntity loginDriver = new NbDriverEntity();
        if (tcId == null || tcId == 0) {
            tcId = 0;  // 赋默认值 0
        }
        // "select * from nb_transfer_batch where batch_code = ? limit 1", code);
        NbTransferBatchEntity tb = nbTransferBatchService.getOne(new LambdaQueryWrapper<NbTransferBatchEntity>().eq(NbTransferBatchEntity::getBatchCode,code),false);
        if (tb == null){
            return LocalizedR.failed("routeGroup.no.corresponding.information.found", code);
        }

        if (tb.getUnloadingTime() != null) {
            return LocalizedR.failed("routeGroup.has.been.scanned.in.this.time", DateFormatUtils.format(tb.getUnloadingTime(), "MM-dd HH:mm:ss"));
        }

        if (tb.getTcId() != tcId) {
            return LocalizedR.failed("routeGroup.does.not.match.the.registered.transfer.center", Optional.ofNullable(null));
        }

        Double[] latlng = getLatLng();
        Double lat = null, lng = null;
        if (latlng != null) {
            lat = latlng[0];
            lng = latlng[1];
        }

        String address = null;
        String timezone = ZoneId.systemDefault().getId();

        NbTransferCenterEntity tc = transferCenterService.getById(tcId);
        if (tc != null) {
            address = commonDataUtil.getAddress(tc.getProvinceId(), tc.getCityId());
            timezone = tc.getTcTimezone();
        }

        // 包裹内订单更新状态   ORDER_STATUS_200_PARCEL_SCANNED--到达配送中心   ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER--到达转运中心
        // "select o.* from nb_order o, nb_transfer_batch_order tbo where o.order_id = tbo.order_id and tbo.batch_id = ?", tb.getBatchId());
        List<NbOrderEntity> orders=nbOrderService.findOrderByTransferBatchId(tb.getBatchId());
        for (NbOrderEntity order : orders) {
            order.setOrderStatus(AppOrderDto.ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER);
            //更新状态，到达转运中心
            nbOrderService.updateById(order);

            NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), loginDriver != null ? loginDriver.getDriverId() : 0, lat, lng, address, timezone);
            op.setTcId(tcId);
            orderPathService.save(op);

            log.info("订单状态更新:orderId=" + order.getOrderId() + ",status=" + order.getOrderStatus());

            // 20230909 虚拟子订单更新后,主订单状态也随之更新
            if (order.getPOrderId() > 0) {
                LambdaQueryWrapper<NbOrderEntity> Wrapper = new LambdaQueryWrapper<>();
                Wrapper.eq(NbOrderEntity::getPOrderId, order.getPOrderId());
                NbOrderEntity pOrder= nbOrderService.getOne(Wrapper);
                pOrder.setOrderStatus(order.getOrderStatus());
                nbOrderService.updateById(pOrder);

                op.setPathId(null);
                op.setOrderId(pOrder.getOrderId());
                op.setOrderStatus(pOrder.getOrderStatus());
                op.setAddTime(new Date());
                op.setTcId(tcId);
                op.setDriverId(loginDriver.getDriverId());
                orderPathService.save(op);
                log.info("父订单状态更新:orderId=" + pOrder.getOrderId() + ",status=" + pOrder.getOrderStatus());
            }
        }

        tb.setUnloadingTime(new Date());
        nbTransferBatchService.updateById(tb);
        return R.ok();
    }

}
