package com.jygjexp.jynx.zxoms.nbapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/11 11:37
 */
@Data
public class APPDriverVo {

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "邮编")
    private String postalCode;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "验证码")
    private String captcha;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "市")
    private Integer cityId;

    @Schema(description = "表单数据")
    private String formData;


}
