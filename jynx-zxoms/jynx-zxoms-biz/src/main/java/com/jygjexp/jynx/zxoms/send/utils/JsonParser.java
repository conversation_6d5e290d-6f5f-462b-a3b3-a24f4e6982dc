package com.jygjexp.jynx.zxoms.send.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jygjexp.jynx.zxoms.send.dto.SysDictItem;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public class JsonParser {
    public static List<SysDictItem> getDictValue(String jsonString) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // 由于JSON中的data字段是一个包含类型信息的数组，我们需要自定义类型引用来解析它
            TypeReference<List<SysDictItem>> typeRef = new TypeReference<List<SysDictItem>>() {
            };
            // 先解析整个对象，然后获取data字段
            Map<String, Object> parsedMap = objectMapper.readValue(jsonString, Map.class);
            List<Object> dataList = (List<Object>) parsedMap.get("data");

            // 跳过类型信息，直接获取真正的数据列表
            if (dataList != null && dataList.size() > 1 && dataList.get(1) instanceof List) {
                List<SysDictItem> sysDictItems = objectMapper.convertValue(dataList.get(1), typeRef);
                return sysDictItems;
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}