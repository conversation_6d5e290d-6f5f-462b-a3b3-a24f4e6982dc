package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderUpdateLogEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderUpdateLogMapper;
import com.jygjexp.jynx.zxoms.send.service.NbOrderUpdateLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单修改记录
 *
 * <AUTHOR>
 * @date 2024-10-23 23:47:18
 */
@Service
@RequiredArgsConstructor
public class NbOrderUpdateLogServiceImpl extends ServiceImpl<NbOrderUpdateLogMapper, NbOrderUpdateLogEntity> implements NbOrderUpdateLogService {
    private final NbOrderUpdateLogMapper nbOrderUpdateLogMapper;

    /**
     * 通过订单ID查询订单修改记录
     * @param orderId
     * @return
     */
    @Override
    public List<NbOrderUpdateLogEntity> listOrderUpdateLogByOrderId(Integer orderId) {
        MPJLambdaWrapper<NbOrderUpdateLogEntity> wrapper = new MPJLambdaWrapper<NbOrderUpdateLogEntity>();
        wrapper.selectAll(NbOrderUpdateLogEntity.class)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbOrderUpdateLogEntity::getOrderId)
                .eq(NbOrderEntity::getOrderId, orderId);
        return nbOrderUpdateLogMapper.selectJoinList(NbOrderUpdateLogEntity.class, wrapper);
    }

}