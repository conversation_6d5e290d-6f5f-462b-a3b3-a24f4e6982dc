package com.jygjexp.jynx.zxoms.send.constants;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/10 18:38
 */
public class NBDConstants {

//	public static final String GEOCODE_API = "https://maps.googleapis.com/maps/api/geocode/json?address=%s&components=country:CA&key=AIzaSyBD9PP2aIT3zCz1uXXjOPWVbov2V9ERRUQ";
//	public static final String GEOCODE_API = "https://maps.googleapis.com/maps/api/geocode/json?address=%s&components=country:CA&key=AIzaSyCWMCPsi5P2EfCumvfdsK_xVBAAbO8HKmI";
    /**
     * 使用佳邮自己的谷歌地图key
     */
    public static final String GEOCODE_API = "https://maps.googleapis.com/maps/api/geocode/json?address=%s&components=country:CA&key=AIzaSyA1zVWqFVJMLyEYyaWw8qQ8X3k2vbdjRvc";

    /**
     * 存放有你签名文件的目录
     */
    public static String UNI_SIGN_FILE_PATH;

    public static String STATIC_DOMAIN;

    public static Boolean ISPROD;

    /**
     * 有你到站后需要返回仓库的天数
     */
    public static final int UNI_RETURN_WAREHOUSE_LIMIT_DAYS = 10;

    // nb driver
    public static final String HEADER_ACCESS_KEY = "__NBD_ACCESS_TOKEN";

    public static final String REQ_ATTR_DRIVER_USER = "REQ_ATTR_DRIVER_USER";

    public static final String REQ_ATTR_MANAGER_USER = "REQ_ATTR_MANAGER_USER";

    public static final String REQ_ATTR_API_USER = "REQ_ATTR_API_USER";

    public static String COMMON_DB_NAME = "";

    public static String SIGN_IMAGE_PATH = "";

    public static String IMAGE_URL = "";

    /**
     * 未到货批次编码
     */
    public static final String NotArrivedBatchNo = "Not-Arrived";

}
