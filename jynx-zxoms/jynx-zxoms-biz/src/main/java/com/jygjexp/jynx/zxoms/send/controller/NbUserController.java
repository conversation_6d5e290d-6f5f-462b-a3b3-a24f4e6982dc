package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbUserEntity;
import com.jygjexp.jynx.zxoms.send.service.NbUserService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import org.springdoc.api.annotations.ParameterObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * APP用户-废弃
 *
 * <AUTHOR>
 * @date 2024-11-08 15:17:26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbUser" )
//@Tag(description = "nbUser" , name = "APP用户-废弃管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbUserController {

    private final  NbUserService nbUserService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbUser APP用户-废弃
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUser_view')" )
    public R getNbUserPage(@ParameterObject Page page, @ParameterObject NbUserEntity nbUser) {
        LambdaQueryWrapper<NbUserEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbUserService.page(page, wrapper));
    }


    /**
     * 通过id查询APP用户-废弃
     * @param userId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{userId}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUser_view')" )
    public R getById(@PathVariable("userId" ) Integer userId) {
        return R.ok(nbUserService.getById(userId));
    }

    /**
     * 新增APP用户-废弃
     * @param nbUser APP用户-废弃
     * @return R
     */
//    @Operation(summary = "新增APP用户-废弃" , description = "新增APP用户-废弃" )
//    @SysLog("新增APP用户-废弃" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUser_add')" )
    public R save(@RequestBody NbUserEntity nbUser) {
        return R.ok(nbUserService.save(nbUser));
    }

    /**
     * 修改APP用户-废弃
     * @param nbUser APP用户-废弃
     * @return R
     */
//    @Operation(summary = "修改APP用户-废弃" , description = "修改APP用户-废弃" )
//    @SysLog("修改APP用户-废弃" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUser_edit')" )
    public R updateById(@RequestBody NbUserEntity nbUser) {
        return R.ok(nbUserService.updateById(nbUser));
    }

    /**
     * 通过id删除APP用户-废弃
     * @param ids userId列表
     * @return R
     */
//    @Operation(summary = "通过id删除APP用户-废弃" , description = "通过id删除APP用户-废弃" )
//    @SysLog("通过id删除APP用户-废弃" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUser_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbUserService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbUser 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUser_export')" )
    public List<NbUserEntity> export(NbUserEntity nbUser,Integer[] ids) {
        return nbUserService.list(Wrappers.lambdaQuery(nbUser).in(ArrayUtil.isNotEmpty(ids), NbUserEntity::getUserId, ids));
    }
}