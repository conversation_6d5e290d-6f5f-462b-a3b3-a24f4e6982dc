package com.jygjexp.jynx.zxoms.nbapp.vo;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: chenchang
 * @Description: 获取仓库列表参数
 * @Date: 2024/12/17 14:54
 */
@Data
public class ApiRequestParamsVo {
    // http://************:8086/api/info/warehouseList?appId=1001&randomstr=Jo1Y6d7U7F&timestamp=1733454261&sign=ba589d8c53e75b863861c1e92acdc2e9
    // appId，不能为空
    @NotBlank(message = "[appId] cannot be empty")
    private String appId;

    // randomstr，不能为空
    @NotBlank(message = "[randomstr] cannot be empty")
    private String randomstr;

    // timestamp，不能为空并且必须是有效时间戳  时间戳在5分钟范围内有效
    @NotNull(message = "[timestamp] cannot be null")
    private Long timestamp;

    // sign，不能为空
    @NotBlank(message = "[sign] cannot be empty")
    private String sign;

    // 增加 getParaMap 方法，转换参数为 Map<String, String[]>
    public Map<String, String[]> getParaMap() {
        Map<String, String[]> paraMap = new HashMap<>();

        // 将字段加入到参数 Map 中
        paraMap.put("appId", new String[]{appId});
        paraMap.put("randomstr", new String[]{randomstr});
        paraMap.put("timestamp", new String[]{String.valueOf(timestamp)});
        paraMap.put("sign", new String[]{sign});

        return paraMap;
    }

}
