package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.CitiesEntity;
import com.jygjexp.jynx.zxoms.send.mapper.CitiesMapper;
import com.jygjexp.jynx.zxoms.send.service.CitiesService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 城市地区
 *
 * <AUTHOR>
 * @date 2024-09-30 18:58:30
 */
@Service
public class CitiesServiceImpl extends ServiceImpl<CitiesMapper, CitiesEntity> implements CitiesService {
    @Override
    public List<CitiesEntity> findCitysListById39() {
        LambdaQueryWrapper<CitiesEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CitiesEntity::getId ,39);
        return list(queryWrapper);
    }
}