package com.jygjexp.jynx.zxoms.send.dto;

import com.jygjexp.jynx.zxoms.entity.NbPriceDistrictEntity;
import lombok.Data;

import java.util.List;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/8 10:01
 */
@Data
public class PriceDistrictDto extends NbPriceDistrictEntity {
    public static final int APPLY_AREA_1_INTERNATIONAL = 1;

    public static final int APPLY_AREA_2_DOMESTIC = 2;

    /**
     * 初始化三字邮编库
     */
//    public void initMap() {
//          findAll();
//        for (NbPriceDistrictEntity pd : pds) {
//            String[] coverPostalCodes = pd.getCoverPostalCode().split(",");
//
//            String keyPrefix = pd.getIsDestAddr() + "_" + pd.getApplyArea() + "_";
//            for (String code : coverPostalCodes) {
//                String key = keyPrefix + code;
//
//
//            }
//        }
//
//    }
}
