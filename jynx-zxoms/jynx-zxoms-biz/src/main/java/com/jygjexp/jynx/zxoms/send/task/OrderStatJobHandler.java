package com.jygjexp.jynx.zxoms.send.task;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/23 2:04
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderStatJobHandler {
    @XxlJob("testHandler")
    public ReturnT<String> orderStatJobHandler(String s) {
        String param = XxlJobHelper.getJobParam();
        log.info("匹配orderStatJobHandler定时任务开始时间: {}, 入参:{}", LocalDateTime.now(), param);
        XxlJobHelper.log("我是测试我是测试." + XxlJobHelper.getShardIndex());
        return SUCCESS;
    }
}
