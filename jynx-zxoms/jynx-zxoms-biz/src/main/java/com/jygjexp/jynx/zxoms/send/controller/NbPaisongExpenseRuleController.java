package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbPaisongExpenseRuleEntity;
import com.jygjexp.jynx.zxoms.entity.NbSortingCenterEntity;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.send.service.NbPaisongExpenseRuleService;
import com.jygjexp.jynx.zxoms.vo.NbPaisongExpenseRuleVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 派送费用规则
 * <AUTHOR>
 * @date 2025-01-10 19:47:36
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbPaisongExpenseRule")
@Tag(description = "nbPaisongExpenseRule", name = "派送费用规则管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbPaisongExpenseRuleController {

    private final NbPaisongExpenseRuleService nbPaisongExpenseRuleService;
    private final NbSortingCenterService nbSortingCenterService;

    /**
     * 分页查询
     * @param page                 分页对象
     * @param nbPaisongExpenseRule 退件费用规则
     * @paisong
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('zxoms_paisongeRule_view')")
    public R getNbPaisongExpenseRulePage(@ParameterObject Page page, @ParameterObject NbPaisongExpenseRuleVo nbPaisongExpenseRule) {
        LambdaQueryWrapper<NbPaisongExpenseRuleEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotBlank(nbPaisongExpenseRule.getBillingWarehouse()), NbPaisongExpenseRuleEntity::getBillingWarehouse, nbPaisongExpenseRule.getBillingWarehouse())
                .eq(ObjectUtil.isNotNull(nbPaisongExpenseRule.getStatus()), NbPaisongExpenseRuleEntity::getStatus, nbPaisongExpenseRule.getStatus())
                .eq(ObjectUtil.isNotNull(nbPaisongExpenseRule.getPostalId()), NbPaisongExpenseRuleEntity::getPostalId, nbPaisongExpenseRule.getPostalId())
                .eq(ObjectUtil.isNotNull(nbPaisongExpenseRule.getWeightId()), NbPaisongExpenseRuleEntity::getWeightId, nbPaisongExpenseRule.getWeightId());
        //获取创建时间
        String time = nbPaisongExpenseRule.getCreateTimeVo();
        if (StringUtils.isNotBlank(time)) {
            String[] dates = time.split("-");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
            LocalDate startDate = LocalDate.parse(dates[0], formatter);
            LocalDate endDate = LocalDate.parse(dates[1], formatter);
            LocalDateTime startDateTime = startDate.atStartOfDay(); // 默认时间为 00:00:00
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59); // 默认时间为 23:59:59
            wrapper.between(NbPaisongExpenseRuleEntity::getCreateTime, startDateTime, endDateTime);
        }
        return R.ok(nbPaisongExpenseRuleService.page(page, wrapper));
    }


    /**
     * 通过id查询退件费用规则
     * @param reId id
     * @paisong R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{reId}")
    @PreAuthorize("@pms.hasPermission('zxoms_paisongeRule_view')")
    public R getById(@PathVariable("reId") Long reId) {
        return R.ok(nbPaisongExpenseRuleService.getById(reId));
    }

    /**
     * 新增退件费用规则
     * @param nbPaisongExpenseRule 退件费用规则
     * @paisong R
     */
    @Operation(summary = "新增退件费用规则", description = "新增退件费用规则")
    @SysLog("新增退件费用规则")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_paisongeRule_add')")
    public R save(@RequestBody NbPaisongExpenseRuleEntity nbPaisongExpenseRule) {
        return R.ok(nbPaisongExpenseRuleService.save(nbPaisongExpenseRule));
    }

    /**
     * 修改退件费用规则
     * @param nbPaisongExpenseRule 退件费用规则
     * @paisong R
     */
    @Operation(summary = "修改退件费用规则", description = "修改退件费用规则")
    @SysLog("修改退件费用规则")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_paisongeRule_edit')")
    public R updateById(@RequestBody NbPaisongExpenseRuleEntity nbPaisongExpenseRule) {
        return R.ok(nbPaisongExpenseRuleService.updateById(nbPaisongExpenseRule));
    }

    /**
     * 通过id删除退件费用规则
     * @param ids reId列表
     * @paisong R
     */
    @Operation(summary = "通过id删除退件费用规则", description = "通过id删除退件费用规则")
    @SysLog("通过id删除退件费用规则")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_paisongeRule_del')")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(nbPaisongExpenseRuleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbPaisongExpenseRule 查询条件
     * @param ids                  导出指定ID
     * @paisong excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_paisongeRule_export')")
    public List<NbPaisongExpenseRuleEntity> export(NbPaisongExpenseRuleEntity nbPaisongExpenseRule, Long[] ids) {
        return nbPaisongExpenseRuleService.list(Wrappers.lambdaQuery(nbPaisongExpenseRule).in(ArrayUtil.isNotEmpty(ids), NbPaisongExpenseRuleEntity::getReId, ids));
    }

    /**
     * 查询分拣中心下拉列表
     */
    @Operation(summary = "查询分拣中心下拉列表", description = "查询分拣中心下拉列表")
    @GetMapping("/selectDataList")
    public R<Map<String, Object>> selectDataList() {
        HashMap<String, Object> map = new HashMap<>();
        //查询分拣中心下拉列表
        LambdaQueryWrapper<NbSortingCenterEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(NbSortingCenterEntity::getScId, NbSortingCenterEntity::getCenterName, NbSortingCenterEntity::getIsValid).groupBy(NbSortingCenterEntity::getScId);
        List<NbSortingCenterEntity> warehouseList = nbSortingCenterService.list(wrapper);
        map.put("warehouseList", warehouseList);

        return R.ok(map);
    }

}