package com.jygjexp.jynx.zxoms.send.utils;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/10/24 19:11
 */
public class PathUtil {
    private static String webRootPath;
    private static String rootClassPath;

    public PathUtil() {
    }

    public static String getPath(Class clazz) {
        String path = clazz.getResource("").getPath();
        return (new File(path)).getAbsolutePath();
    }

    public static String getPath(Object object) {
        String path = object.getClass().getResource("").getPath();
        return (new File(path)).getAbsolutePath();
    }

    public static String getRootClassPath() {
        if (rootClassPath == null) {
            try {
                String path = getClassLoader().getResource("").toURI().getPath();
                rootClassPath = (new File(path)).getAbsolutePath();
            } catch (Exception var3) {
                try {
                    String path = PathUtil.class.getProtectionDomain().getCodeSource().getLocation().getPath();
                    path = URLDecoder.decode(path, "UTF-8");
                    if (path.endsWith(File.separator)) {
                        path = path.substring(0, path.length() - 1);
                    }

                    rootClassPath = path;
                } catch (UnsupportedEncodingException var2) {
                    throw new RuntimeException(var2);
                }
            }
        }

        return rootClassPath;
    }

    private static ClassLoader getClassLoader() {
        ClassLoader ret = Thread.currentThread().getContextClassLoader();
        return ret != null ? ret : PathUtil.class.getClassLoader();
    }

    public static void setRootClassPath(String rootClassPath) {
        PathUtil.rootClassPath = rootClassPath;
    }

    public static String getPackagePath(Object object) {
        Package p = object.getClass().getPackage();
        return p != null ? p.getName().replaceAll("\\.", "/") : "";
    }

    public static File getFileFromJar(String file) {
        throw new RuntimeException("Not finish. Do not use this method.");
    }

    public static String getWebRootPath() {
        if (webRootPath == null) {
            webRootPath = detectWebRootPath();
        }

        return webRootPath;
    }

    public static void setWebRootPath(String webRootPath) {
        if (webRootPath != null) {
            if (webRootPath.endsWith(File.separator)) {
                webRootPath = webRootPath.substring(0, webRootPath.length() - 1);
            }

            PathUtil.webRootPath = webRootPath;
        }
    }

    private static String detectWebRootPath() {
        try {
            String path = PathUtil.class.getResource("/").toURI().getPath();
            return (new File(path)).getParentFile().getParentFile().getCanonicalPath();
        } catch (Exception var1) {
            throw new RuntimeException(var1);
        }
    }

    public static boolean isAbsolutePath(String path) {
        return path.startsWith("/") || path.indexOf(58) == 1;
    }
}
