package com.jygjexp.jynx.zxoms.send.task;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.send.utils.ConfigUtil;
import com.jygjexp.jynx.zxoms.dto.ShelfPkgLogDto;
import com.jygjexp.jynx.zxoms.entity.*;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenchang
 * @Description: 佳邮自提码通知
 * @Date: 2024/10/21 19:48
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JySelfPickupCodeNotifyTask {
    private final NbShelfPkgLogService nbShelfPkgLogService;
    private final NbOrderService nbOrderService;
    private final NbShelfService nbShelfService;
    private final NbSortingCenterService nbSortingCenterService;
    private final NbTransferCenterService nbTransferCenterService;
    private final CommonDataUtil commonDataUtil;
    private final ConfigUtil configUtil;

    @SneakyThrows
    @XxlJob(value = "jySelfPickupCodeNotifyHandler")
    protected void jySelfPickupCodeNotifyHandler() {
//        String param = XxlJobHelper.getJobParam();
//        log.info("匹配定时任务开始时间: {}, 入参:{}", LocalDateTime.now(), param);
        XxlJobHelper.log("定时任务：【佳邮自提码通知】于:{}，输入参数{}", LocalDateTime.now(), "运行中");

        String notifyUrl = configUtil.findStrByKey(ConfigUtil.jy_pickup_code_notify_url);
        if (!StringUtils.isBlank(notifyUrl)) {

            // "select * from nb_shelf_pkg_log where sync_jy_status in (1, 20)");
            List<Integer> statusList = Arrays.asList(1, 20);
            LambdaQueryWrapper<NbShelfPkgLogEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(NbShelfPkgLogEntity::getSyncJyStatus, statusList);
            List<NbShelfPkgLogEntity> logs = nbShelfPkgLogService.list(queryWrapper);
            for (NbShelfPkgLogEntity log : logs) {
                int orderId = log.getOrderId();
                NbOrderEntity order = nbOrderService.getById(orderId);

                JSONObject jo = new JSONObject();
                jo.put("pkgNo", order.getPkgNo());
                jo.put("pickupCode", log.getPutawayCode());
                jo.put("putawayTimestamp", log.getPutawayTime());

                NbShelfEntity shelf = nbShelfService.getById(log.getShelfId());
                if (shelf.getScId() > 0) {
                    NbSortingCenterEntity sc = nbSortingCenterService.getById(shelf.getScId());

                    jo.put("serviceTel", sc.getServiceTel());
                    jo.put("openingHours", sc.getBusinessHours());

                    String cp = commonDataUtil.getAddress(sc.getProvinceId(), sc.getCityId());
                    jo.put("address", sc.getAddress() + " " + cp + " " + sc.getPostalCode());
                } else if (shelf.getTcId() > 0) {
                    NbTransferCenterEntity tc = nbTransferCenterService.getById(shelf.getTcId());
                    jo.put("serviceTel", tc.getServiceTel());
                    jo.put("openingHours", tc.getBusinessHours());

                    String cp = commonDataUtil.getAddress(tc.getProvinceId(), tc.getCityId());
                    jo.put("address", tc.getAddress() + " " + cp + " " + tc.getPostalCode());
                }

                try {
                    HttpRequest hr = HttpRequest.post(notifyUrl);
                    hr.header("user-agent", "Neighbour Express Server");
                    hr.body(jo.toJSONString());

                    HttpResponse response = hr.execute();
                    String retBody = response.body();
                    if ("ok".equalsIgnoreCase(retBody)) {
                        log.setSyncJyStatus(ShelfPkgLogDto.JY_SYNC_STATUS_10_SUCCESS);
                        log.setSyncJyTime(new Date());
                        nbShelfPkgLogService.updateById(log);
                    }
                } catch (Exception e) {
                    e.printStackTrace();

                    log.setSyncJyStatus(ShelfPkgLogDto.JY_SYNC_STATUS_20_FAILURE);
                    log.setFailureTimes(log.getFailureTimes() + 1);
                    log.setSyncJyTime(new Date());
                    nbShelfPkgLogService.updateById(log);
                }

            }
            XxlJobHelper.handleSuccess(); // 设置任务结果
            XxlJobHelper.log("定时任务：【佳邮自提码通知】执行结束，时间: {}", LocalDateTime.now());
        }

    }

}
