package com.jygjexp.jynx.zxoms.send.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: chenchang
 * @Description: 路区导出实体
 * @Date: 2024/12/7 19:25
 */
@Data
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 10) // 设置表头字体样式
@Schema(description = "路区导出实体")
public class NbTransferBatchExcelVo {

    @ColumnWidth(8)
    @ExcelProperty("(Lane ID)路区ID")
    @Schema(description="路区ID")
    private Integer batchId;

    @ColumnWidth(15)
    @ExcelProperty("(Lane code)路区码")
    @Schema(description="路区码")
    private String batchCode;

    @ColumnWidth(20)
    @ExcelProperty("(Creation time)创建时间")
    @Schema(description="添加时间")
    private Date addTime;

    @ColumnWidth(15)
    @ExcelProperty("(driver)司机")
    @Schema(description = "司机名称")
    private String driverName;

    @ColumnWidth(5)
    @ExcelProperty("(Order quantity)订单数量")
    @Schema(description="订单总数")
    private Integer orderTotal;

    @ColumnWidth(20)
    @ExcelProperty("(Estimated time)预估时间")
    @Schema(description="预估时间")
    private BigDecimal estimatedHour;

    @ColumnWidth(6)
    @ExcelProperty("(Allocation type)分配类型")
    @Schema(description="1分配，2抢单")
    private Integer getType;

    @ColumnWidth(15)
    @ExcelProperty("(Transport driver)运输司机")
    @Schema(description="运输司机")
    private Integer tranferDriverId;

    @ColumnWidth(15)
    @ExcelProperty("(Sorting center)分拣中心")
    @Schema(description = "分拣中心")
    private String sortingCenterName;

    @ColumnWidth(15)
    @ExcelProperty("(Target transit center)目标转运中心")
    @Schema(description = "目标转运中心")
    private String transferCenterName;

    @ColumnWidth(20)
    @ExcelProperty("(Loading time)装车时间")
    @Schema(description="装车时间")
    private Date loadingTime;

    @ColumnWidth(20)
    @ExcelProperty("(Unloading time)卸货时间")
    @Schema(description="卸货时间")
    private Date unloadingTime;

    @ColumnWidth(20)
    @ExcelProperty("route4me optimizationProblemID")
    @Schema(description="route4me optimizationProblemID")
    private String optimizationProblemId;

    @ColumnWidth(20)
    @ExcelProperty("route4me RouteID")
    @Schema(description="route4me RouteID")
    private String routeId;

    @ColumnWidth(8)
    @ExcelProperty("(R4m driving distance)R4m行驶距离")
    @Schema(description="route4me tripDistance")
    private Double tripDistance;

    @ColumnWidth(8)
    @ExcelProperty("(R4m Planning the total route duration)R4m规划路由总时长")
    @Schema(description="route4me plannedTotalRouteDuration")
    private Integer plannedTotalRouteDuration;

    @ColumnWidth(15)
    @ExcelProperty("(R4m Plan the driver ID)R4m规划司机ID")
    @Schema(description="route4me memberId")
    private Long memberId;

    @ColumnWidth(15)
    @ExcelProperty("route4me view")
    @Schema(description="route4me view")
    private String routeView;

    @ColumnWidth(6)
    @ExcelProperty("(Delivery status)派送状态")
    @Schema(description="派送状态：1=创建，2=开始，3=结束")
    private Integer deliveryStatus;

    @ColumnWidth(20)
    @ExcelProperty("(Start time)开始时间")
    @Schema(description="开始时间")
    private Date startTime;

    @ColumnWidth(20)
    @ExcelProperty("(End time)结束时间")
    @Schema(description="结束时间")
    private Date endTime;

    @ColumnWidth(8)
    @ExcelProperty("(Completed quantity)完成数量")
    @Schema(description="完成数量")
    private Integer completedTotal;

    @ColumnWidth(6)
    @ExcelProperty("(Delete or not)是否删除")
    @Schema(description="是否删除")
    private Boolean isDelete;

    @ColumnWidth(20)
    @ExcelProperty("(Deletion time)删除时间")
    @Schema(description="删除时间")
    private Date deleteTime;


}
