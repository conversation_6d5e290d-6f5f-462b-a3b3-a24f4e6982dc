package com.jygjexp.jynx.zxoms.send.mapper;

import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.zxoms.entity.NbShelfPkgLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NbShelfPkgLogMapper extends JynxBaseMapper<NbShelfPkgLogEntity> {

    Integer findShelfPkgLogFirstByScId(@Param("scId") Integer scId, @Param("dateTime") String dateTime);

    Integer findShelfPkgLogFirstByTcId(@Param("tcId") Integer tcId, @Param("dateTime") String dateTime);

}