package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderPathMapper;
import com.jygjexp.jynx.zxoms.send.service.NbOrderPathService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.vo.NbOrderPathPageVo;
import com.jygjexp.jynx.zxoms.vo.OrderOs290PageVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单路径
 *
 * <AUTHOR>
 * @date 2024-10-12 18:56:00
 */
@Service
@RequiredArgsConstructor
public class NbOrderPathServiceImpl extends ServiceImpl<NbOrderPathMapper, NbOrderPathEntity> implements NbOrderPathService {
    private final NbOrderPathMapper nbOrderPathMapper;
    private final NbSortingCenterService nbSortingCenterService;

    @Override
    public Page<NbOrderPathPageVo> search(Page page, NbOrderPathPageVo vo) {
        //select id ID,title CN from nb_order_status; ds=nbd;
        //select sc_id id, center_name name from nb_sorting_center; ds=nbd;
        //select tc_id id, center_name name from nb_transfer_center; ds=nbd;
        //select driver_id id, concat(first_name, last_name) name from nb_driver; ds=nbd;
        MPJLambdaWrapper<NbOrderPathEntity> wrapper = new MPJLambdaWrapper<NbOrderPathEntity>();
        wrapper.eq(ObjectUtil.isNotNull(vo.getOrderId()), NbOrderPathEntity::getOrderId, vo.getOrderId())
//                .eq(ObjectUtil.isNotNull(vo.getCreateTime()), NbOrderPathEntity::getAddTime, vo.getCreateTime())
                .eq(ObjectUtil.isNotNull(vo.getDriverId()), NbOrderPathEntity::getDriverId, vo.getDriverId());
        if (ObjectUtil.isNotNull(vo.getDistance())) {
            wrapper.eq(NbOrderPathEntity::getDistance, vo.getDistance())  // 离收件距离 =
                    .or().gt(NbOrderPathEntity::getDistance, vo.getDistance())  // 离收件距离 >
                    .or().lt(NbOrderPathEntity::getDistance, vo.getDistance())  // 离收件距离 <
                    .or().ge(NbOrderPathEntity::getDistance, vo.getDistance())  // 离收件距离 >=
                    .or().le(NbOrderPathEntity::getDistance, vo.getDistance());  // 离收件距离 <=
        }

//        String createTime = "2024-03-25 00:00:00-2024-12-31 00:00:00";
        String addTime = vo.getCreateTime();
        if (addTime != null) {
            int splitIndex = addTime.indexOf(":", addTime.indexOf(":") + 1) + 3;
            String startTime = addTime.substring(0, splitIndex);
            String endTime = addTime.substring(splitIndex + 1);
            wrapper.ge(NbOrderPathEntity::getAddTime, startTime).le(NbOrderPathEntity::getAddTime, endTime);
        }
        wrapper.selectAll(NbOrderPathEntity.class)
                .select(NbOrderStatusEntity::getId).selectAs(NbOrderStatusEntity::getTitle, NbOrderPathPageVo.Fields.orderStatusTitle)
                .select(NbSortingCenterEntity::getScId).selectAs(NbSortingCenterEntity::getCenterName, NbOrderPathPageVo.Fields.sortingCenterName)
                .select(NbTransferCenterEntity::getTcId).selectAs(NbTransferCenterEntity::getCenterName, NbOrderPathPageVo.Fields.transferCenterName)
                .select(NbDriverEntity::getDriverId, NbDriverEntity::getDriverName)
                .leftJoin(NbOrderStatusEntity.class, NbOrderStatusEntity::getId, NbOrderPathEntity::getOrderStatus)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbOrderEntity::getScId)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbOrderEntity::getTcId)
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbOrderEntity::getDriverId)
                .orderByDesc(NbOrderPathEntity::getAddTimestamp);
        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbOrderPathEntity::getScId, idList);
        return nbOrderPathMapper.selectJoinPage(page, NbOrderPathPageVo.class, wrapper);
    }


    // 配送失败订单，退回到分拣中心 -> STORAGE_30_DAYS_FROM_OFFICE
    @Override
    public Page<OrderOs290PageVo> pageOrderOs290(Page page, OrderOs290PageVo vo) {
        // "select * from nb_order_path where order_id = ? and order_status = ? limit 1", orderId, Order.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE);
        // r.set("v_storage30_days_time", DateFormatUtils.format(op.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
        //select  merchant_id ID, name CN from nb_merchant; ds=nbd;
        //select driver_id, first_name, last_name, email, mobile from nb_driver; ds=nbd;
        //select sc_id id, center_name cn from nb_sorting_center where is_valid = true; ds=nbd;
        //select tc_id id, center_name CenterName, transfer_center_code CenterCode from nb_transfer_center where is_valid = true order by tc_id desc; ds=nbd;
        //select id ID,title CN from nb_order_status; ds=nbd;
        MPJLambdaWrapper<NbOrderPathEntity> wrapper = new MPJLambdaWrapper<NbOrderPathEntity>();
        wrapper.selectAll(NbOrderPathEntity.class)
                .select(NbDriverEntity::getDriverId, NbDriverEntity::getDriverName, NbDriverEntity::getEmail, NbDriverEntity::getMobile)
                .select(NbSortingCenterEntity::getScId).selectAs(NbSortingCenterEntity::getCenterName, OrderOs290PageVo.Fields.sortingCenterName)
                .select(NbTransferCenterEntity::getTcId).selectAs(NbTransferCenterEntity::getCenterName, OrderOs290PageVo.Fields.transferCenterName)
                .selectAs(NbTransferCenterEntity::getTransferCenterCode, OrderOs290PageVo.Fields.transferCenterCode)
                .select(NbOrderStatusEntity::getId).selectAs(NbOrderStatusEntity::getTitle, OrderOs290PageVo.Fields.orderStatusTitle)
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbOrderPathEntity::getDriverId)
                .leftJoin(NbSortingCenterEntity.class, NbSortingCenterEntity::getScId, NbOrderPathEntity::getScId)
                .leftJoin(NbTransferCenterEntity.class, NbTransferCenterEntity::getTcId, NbOrderPathEntity::getTcId)
                .leftJoin(NbOrderStatusEntity.class, NbOrderStatusEntity::getId, NbOrderPathEntity::getOrderStatus)
                .eq(NbSortingCenterEntity::getIsValid, true)
                .eq(NbTransferCenterEntity::getIsValid, true)
                .eq(NbOrderPathEntity::getOrderId, vo.getOrderId())
                .eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE)
                .orderByDesc(NbTransferCenterEntity::getTcId)
                .last("limit 1");
        vo.setVStorage30DaysTime(DateFormatUtils.format(vo.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
        List<Integer> idList = nbSortingCenterService.getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbOrderPathEntity::getScId, idList);
        return nbOrderPathMapper.selectJoinPage(page, OrderOs290PageVo.class, wrapper);
    }

    @Override
    public void updateByOrderId(Integer orderId) {
        //  update nb_order_path set sync_jy_status = 1, sync_time = now() where order_id = ?
        LambdaUpdateWrapper<NbOrderPathEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(NbOrderPathEntity::getSyncJyStatus, 1);
        updateWrapper.set(NbOrderPathEntity::getSyncTime, LocalDateTime.now());
        updateWrapper.eq(NbOrderPathEntity::getOrderId, orderId);
        update(updateWrapper);
    }

    @Override
    public List<NbOrderPathEntity> findByOrderIdAsc(Integer orderId) {
        //  select op.* from nb_order_path op where op.order_id = ? order by op.path_id asc
        LambdaQueryWrapper<NbOrderPathEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(orderId), NbOrderPathEntity::getOrderId, orderId);
        queryWrapper.orderByAsc(NbOrderPathEntity::getPathId);
        return list(queryWrapper);
    }

    @Override
    public NbOrderPathEntity findOneOrderPathByOrderIdAndOrderStatus(Integer orderId, int orderStatus200ParcelScanned) {
        return nbOrderPathMapper.findOneOrderPathByOrderIdAndOrderStatus(orderId, orderStatus200ParcelScanned);
    }

    /**
     * 根据订单ID查询订单轨迹(订单路径表)
     * @param orderId
     * @return
     */
    @Override
    public List<NbOrderPathEntity> listOrderPathByOrderId(Integer orderId) {
        //SELECT * FROM `nb_order_path` op LEFT JOIN nb_order o on o.order_id=op.order_id where o.order_id='1687206'
        MPJLambdaWrapper<NbOrderPathEntity> wrapper = new MPJLambdaWrapper<NbOrderPathEntity>();
        wrapper.selectAll(NbOrderPathEntity.class)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbOrderPathEntity::getOrderId)
                .eq(NbOrderEntity::getOrderId, orderId).orderByAsc(NbOrderPathEntity::getAddTime);
        return nbOrderPathMapper.selectJoinList(NbOrderPathEntity.class, wrapper);
    }

}