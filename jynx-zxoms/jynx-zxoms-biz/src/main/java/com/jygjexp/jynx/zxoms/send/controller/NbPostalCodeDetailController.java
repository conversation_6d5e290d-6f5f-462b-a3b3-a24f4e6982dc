package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbPostalCodeDetailEntity;
import com.jygjexp.jynx.zxoms.send.service.NbPostalCodeDetailService;
import com.jygjexp.jynx.zxoms.vo.NbPostalCodeDetailPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 邮编分区-明细
 *
 * <AUTHOR>
 * @date 2025-01-09 15:15:24
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbPostalCodeDetail" )
@Tag(description = "nbPostalCodeDetail" , name = "邮编分区-明细管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbPostalCodeDetailController {

    private final NbPostalCodeDetailService nbPostalCodeDetailService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 邮编分区-明细
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbPostalCodeDetail_view')" )
    public R getNbPostalCodeDetailPage(@ParameterObject Page page, @ParameterObject NbPostalCodeDetailPageVo vo) {
//        LambdaQueryWrapper<NbPostalCodeDetailEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbPostalCodeDetailService.search(page, vo));
    }


    /**
     * 通过id查询邮编分区-明细
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbPostalCodeDetail_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(nbPostalCodeDetailService.getById(id));
    }

    /**
     * 新增邮编分区-明细
     * @param nbPostalCodeDetail 邮编分区-明细
     * @return R
     */
    @Operation(summary = "新增邮编分区-明细" , description = "新增邮编分区-明细" )
    @SysLog("新增邮编分区-明细" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbPostalCodeDetail_add')" )
    public R save(@RequestBody NbPostalCodeDetailEntity nbPostalCodeDetail) {
        // 判断新增邮编开始和截止区间是否重复
        List<NbPostalCodeDetailEntity> detailEntities = nbPostalCodeDetailService.list(new LambdaQueryWrapper<NbPostalCodeDetailEntity>()
                .eq(NbPostalCodeDetailEntity::getGroupId, nbPostalCodeDetail.getGroupId())
                .eq(NbPostalCodeDetailEntity::getStartPostalCode, nbPostalCodeDetail.getStartPostalCode())
                .eq(NbPostalCodeDetailEntity::getEndPostalCode, nbPostalCodeDetail.getEndPostalCode()).last("limit 1"));
        if (CollUtil.isNotEmpty(detailEntities)) {
            return R.failed("PostalCode detail interval repeats");
        }
        return R.ok(nbPostalCodeDetailService.save(nbPostalCodeDetail));
    }

    /**
     * 修改邮编分区-明细
     * @param nbPostalCodeDetail 邮编分区-明细
     * @return R
     */
    @Operation(summary = "修改邮编分区-明细" , description = "修改邮编分区-明细" )
    @SysLog("修改邮编分区-明细" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbPostalCodeDetail_edit')" )
    public R updateById(@RequestBody NbPostalCodeDetailEntity nbPostalCodeDetail) {
        //判断修改邮编开始和截止区间是否重复
        List<NbPostalCodeDetailEntity> detailEntities = nbPostalCodeDetailService.list(new LambdaQueryWrapper<NbPostalCodeDetailEntity>()
                .eq(NbPostalCodeDetailEntity::getGroupId,nbPostalCodeDetail.getGroupId())
                .eq(NbPostalCodeDetailEntity::getStartPostalCode, nbPostalCodeDetail.getStartPostalCode())
                .eq(NbPostalCodeDetailEntity::getEndPostalCode, nbPostalCodeDetail.getEndPostalCode()).ne(NbPostalCodeDetailEntity::getId,nbPostalCodeDetail.getId())
                .last("limit 1"));
        if (CollUtil.isNotEmpty(detailEntities)) {
            return R.failed("PostalCode detail interval repeats");
        }
        return R.ok(nbPostalCodeDetailService.updateById(nbPostalCodeDetail));
    }

    /**
     * 通过id删除邮编分区-明细
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除邮编分区-明细" , description = "通过id删除邮编分区-明细" )
    @SysLog("通过id删除邮编分区-明细" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbPostalCodeDetail_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(nbPostalCodeDetailService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbPostalCodeDetail 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbPostalCodeDetail_export')" )
    public List<NbPostalCodeDetailEntity> export(NbPostalCodeDetailEntity nbPostalCodeDetail,Long[] ids) {
        return nbPostalCodeDetailService.list(Wrappers.lambdaQuery(nbPostalCodeDetail).in(ArrayUtil.isNotEmpty(ids), NbPostalCodeDetailEntity::getId, ids));
    }

    @Operation(summary = "导入邮编分区明细" , description = "导入邮编分区明细" )
    @SysLog("导入邮编分区明细" )
    @PostMapping("/importDetail")
    @PreAuthorize("@pms.hasPermission('zxoms_nbPostalCodeDetail_import')" )
    public R importNbPostalCodeDetail(@RequestParam("file") MultipartFile file, @RequestParam("groupId") Long groupId) {
        return R.ok(nbPostalCodeDetailService.importNbPostalCodeDetail(file, groupId));
    }

}