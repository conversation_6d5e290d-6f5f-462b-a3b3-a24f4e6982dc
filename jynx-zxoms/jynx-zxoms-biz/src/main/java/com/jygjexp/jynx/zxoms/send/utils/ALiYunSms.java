package com.jygjexp.jynx.zxoms.send.utils;

import cn.hutool.json.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.jygjexp.jynx.common.core.util.R;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ALiYunSms {

    public static R sentMes(String number, String content) {
        if (!number.startsWith("1")) {
            number = "1" + number;
        }
        System.out.println("sendMobile=" + number);
        log.info("短信发送内容：sendMobile=" + number + ",content=" + content);
        DefaultProfile profile = DefaultProfile.getProfile("cn-beijing", "LTAI5tSbvRzzqJqvySa2nQC1", "******************************");
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendMessageToGlobe");
        request.putQueryParameter("To", number);
        request.putQueryParameter("Message", content);
        request.putQueryParameter("From", "18338581654");
        request.putQueryParameter("Type", "OTP");

        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            R.failed(e);
            throw new RuntimeException(e);
        }
        String data = response.getData();
        System.out.println(data);
        JSONObject jsonObject = new JSONObject(data);
        log.info("短信发送结果：" + jsonObject);
        String code = jsonObject.getStr("Code");
        if ("OK".equals(code)) {
            return R.ok(jsonObject);
        } else {
            return R.failed();
        }

    }

}


