package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbLtlPriceRuleEntity;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiQuoteInternationalVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiRequestParamsVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;

public interface NbLtlPriceRuleService extends IService<NbLtlPriceRuleEntity> {
    // 分页查询
    Page search(Page page, NbLtlPriceRuleEntity nbLtlPriceRule);

    // LTL询价
    OldResult ltl(ApiRequestParamsVo paramsVo, String destPostalCode, String senderPostalCode, String items, Integer[] surchargeIds);

}