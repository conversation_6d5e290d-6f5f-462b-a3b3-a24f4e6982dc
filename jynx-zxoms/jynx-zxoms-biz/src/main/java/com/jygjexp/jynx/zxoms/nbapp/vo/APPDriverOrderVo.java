package com.jygjexp.jynx.zxoms.nbapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/11 21:59
 */
@Data
public class APPDriverOrderVo {
    private Integer pageNo;
    private Integer pageSize;
    private String status;
    private String sortType;    // 排序类型
    @NotNull(message = "订单ID不能为空")
    private Integer orderId;

    @Schema(description = "上传文件")
    private List<MultipartFile> file;

    private Integer addrssTypeId;

    @Schema(description = "包裹号")
    private String pkgNo;   // 包裹号

    public APPDriverOrderVo() {
        this.pageNo = 1;
        this.pageSize = 50;
    }


}
