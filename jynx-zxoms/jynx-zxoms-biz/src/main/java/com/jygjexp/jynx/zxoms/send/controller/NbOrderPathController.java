package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderPathEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderPathService;
import com.jygjexp.jynx.zxoms.vo.NbOrderPathPageVo;
import com.jygjexp.jynx.zxoms.vo.OrderOs290PageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单路径
 *
 * <AUTHOR>
 * @date 2024-10-12 18:56:00
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrderPath" )
@Tag(description = "nbOrderPath" , name = "订单路径管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderPathController {

    private final NbOrderPathService nbOrderPathService;

    /**
     * 分页查询
     * @param vo 分页对象
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPath_view')" )
    public R getNbOrderPathPage(@ParameterObject Page page, @ParameterObject NbOrderPathPageVo vo) {
        return R.ok(nbOrderPathService.search(page, vo));
    }

    /**
     * 配送失败订单退回分拣中心
     * @return
     */
    @Operation(summary = "配送失败，退回到分拣中心" , description = "配送失败，退回到分拣中心" )
    @SysLog("配送失败，退回到分拣中心" )
    @GetMapping("/pageOrderOs290")
    public R pageOrderOs290(@ParameterObject Page page, @ParameterObject OrderOs290PageVo vo){
        return R.ok(nbOrderPathService.pageOrderOs290(page,vo));
    }

    /**
     * 通过id查询订单路径
     * @param pathId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{pathId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPath_view')" )
    public R getById(@PathVariable("pathId" ) Integer pathId) {
        return R.ok(nbOrderPathService.getById(pathId));
    }

    /**
     * 新增订单路径
     * @param zxOmsNbOrderPath 订单路径
     * @return R
     */
    @Operation(summary = "新增订单路径" , description = "新增订单路径" )
    @SysLog("新增订单路径" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPath_add')" )
    public R save(@RequestBody NbOrderPathEntity zxOmsNbOrderPath) {
        return R.ok(nbOrderPathService.save(zxOmsNbOrderPath));
    }

    /**
     * 修改订单路径
     * @param zxOmsNbOrderPath 订单路径
     * @return R
     */
    @Operation(summary = "修改订单路径" , description = "修改订单路径" )
    @SysLog("修改订单路径" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPath_edit')" )
    public R updateById(@RequestBody NbOrderPathEntity zxOmsNbOrderPath) {
        return R.ok(nbOrderPathService.updateById(zxOmsNbOrderPath));
    }

    /**
     * 通过id删除订单路径
     * @param ids pathId列表
     * @return R
     */
    @Operation(summary = "通过id删除订单路径" , description = "通过id删除订单路径" )
    @SysLog("通过id删除订单路径" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPath_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderPathService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param zxOmsNbOrderPath 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPath_export')" )
    public List<NbOrderPathEntity> export(NbOrderPathEntity zxOmsNbOrderPath,Integer[] ids) {
        return nbOrderPathService.list(Wrappers.lambdaQuery(zxOmsNbOrderPath).in(ArrayUtil.isNotEmpty(ids), NbOrderPathEntity::getPathId, ids));
    }
}