package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.zxoms.send.service.NbOrderCostService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderCostEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单费用 订单司机付款
 *
 * <AUTHOR>
 * @date 2024-09-30 23:38:39
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/orderCost" )
@Tag(description = "orderCost" , name = "订单费用管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderCostController {

    private final NbOrderCostService nbOrderCostService;
    private final NbOrderService nbOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param basicNbOrderCost 订单费用
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('basic_orderCost_view')" )
    public R getNbOrderCostPage(@RequestParam(value = "query_v_sub_batch_no", required = false) String v_sub_batch_no
            , @ParameterObject Page page, @ParameterObject NbOrderCostEntity basicNbOrderCost) {
        // 查询前逻辑 queryBefore
//        String v_sub_batch_no = ac.ctrl.get("query_v_sub_batch_no");
        if (StringUtils.isNotBlank(v_sub_batch_no)) {
            v_sub_batch_no = v_sub_batch_no.trim();

            // Db.use("nbd").update("SET SESSION group_concat_max_len=10240000");
            // "select GROUP_CONCAT(order_id) order_ids from nb_order where sub_batch_no = ? limit 1", v_sub_batch_no).getStr("order_ids");
//
//            System.out.println(orderIds);
//
//            String whereSql = orderIds.replaceAll("\\d+", "?");
//
//            String[] ids = orderIds.split(",");
//            ac.condition = " and order_id in (" + whereSql + ")";
//            for (String id : ids) {
//                ac.params.add(Integer.valueOf(id));
//            }
        }

//        R response = R.ok(nbOrderCostService.page(page, new LambdaQueryWrapper<NbOrderCostEntity>().orderByDesc(NbOrderCostEntity::getOrderId)));
        R response = R.ok(nbOrderCostService.search(page, basicNbOrderCost));

        //查询后逻辑 queryAfter
        try {
            Integer orderId = basicNbOrderCost.getOrderId();
            NbOrderEntity order = nbOrderService.getById(orderId);
            Map<String,Object> record = new HashMap<>();
            record.put("v_pick_no",order.getPkgNo());
            record.put("v_sub_batch_no",order.getSubBatchNo());
            record.put("v_dest_city",order.getDestCity());
            response.setData(record);
        } catch (Exception e) {
            return R.failed(e.getMessage());
        }
        return response;
    }


    /**
     * 通过id查询订单费用
     * @param orderId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{orderId}" )
    @PreAuthorize("@pms.hasPermission('basic_orderCost_view')" )
    public R getById(@PathVariable("orderId" ) Integer orderId) {
        return R.ok(nbOrderCostService.getById(orderId));
    }

    /**
     * 新增订单费用
     * @param basicNbOrderCost 订单费用
     * @return R
     */
    @Operation(summary = "新增订单费用" , description = "新增订单费用" )
    @SysLog("新增订单费用" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('basic_orderCost_add')" )
    public R save(@RequestBody NbOrderCostEntity basicNbOrderCost) {
        return R.ok(nbOrderCostService.save(basicNbOrderCost));
    }

    /**
     * 修改订单费用
     * @param basicNbOrderCost 订单费用
     * @return R
     */
    @Operation(summary = "修改订单费用" , description = "修改订单费用" )
    @SysLog("修改订单费用" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('basic_orderCost_edit')" )
    public R updateById(@RequestBody NbOrderCostEntity basicNbOrderCost) {
        return R.ok(nbOrderCostService.updateById(basicNbOrderCost));
    }

    /**
     * 通过id删除订单费用
     * @param ids orderId列表
     * @return R
     */
    @Operation(summary = "通过id删除订单费用" , description = "通过id删除订单费用" )
    @SysLog("通过id删除订单费用" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('basic_orderCost_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderCostService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param basicNbOrderCost 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('basic_orderCost_export')" )
    public List<NbOrderCostEntity> export(NbOrderCostEntity basicNbOrderCost, Integer[] ids) {
        return nbOrderCostService.list(Wrappers.lambdaQuery(basicNbOrderCost).in(ArrayUtil.isNotEmpty(ids), NbOrderCostEntity::getOrderId, ids));
    }
}