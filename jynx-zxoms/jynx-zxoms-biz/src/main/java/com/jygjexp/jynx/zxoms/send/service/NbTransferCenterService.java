package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbTransferCenterEntity;
import com.jygjexp.jynx.zxoms.excel.TransferCenterExcel;
import com.jygjexp.jynx.zxoms.send.vo.NbTransferCenterExcelVo;
import com.jygjexp.jynx.zxoms.vo.NbTransferCenterPageVo;

import java.util.List;

public interface NbTransferCenterService extends IService<NbTransferCenterEntity> {

    // 转运中心分页查询
    Page<NbTransferCenterPageVo> search(Page page, NbTransferCenterPageVo entity);

    NbTransferCenterEntity findByTcCode(String transferCenterCode);

    NbTransferCenterEntity findOneByTcCodeAndTcId(String transferCenterCode, Integer tcId);

    /**
     * 根据ScId和邮编查询转运中心
     * @param scId
     * @param beforePostalCode
     * @return
     */
    NbTransferCenterEntity findTc(Integer scId, String beforePostalCode);

    /**
     * 订单重新分配
     * @param tcId
     * @return
     */
    NbTransferCenterEntity orderReAssign(Integer tcId);

    /**
     * 校验前三字邮编
     * @param coverPostalCode
     * @param scId
     * @param tcId
     * @param basicNbTransferCenter
     * @return
     */
    String checkCoverPostalCode(String coverPostalCode, Integer scId, Integer tcId, NbTransferCenterEntity basicNbTransferCenter);

    /**
     * 解析转运中心Excel文件
     * @param dataList
     * @return
     */
    List<TransferCenterExcel> parseTransferCenterByExcel(List<TransferCenterExcel> dataList);

    // 根据scId查询转运中心
    List<NbTransferCenterEntity> listTcByScId(Integer scId);

    // 查询转运中心列表-公共接口
    List<NbTransferCenterPageVo> listTc();

    List<NbTransferCenterExcelVo> getExcel(NbTransferCenterPageVo vo, Integer[] ids);   // 转运中心导出Excel
}