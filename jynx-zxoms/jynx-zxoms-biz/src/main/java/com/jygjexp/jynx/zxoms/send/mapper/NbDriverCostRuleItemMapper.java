package com.jygjexp.jynx.zxoms.send.mapper;

import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.zxoms.entity.NbDriverCostRuleItemEntity;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;

@Mapper
public interface NbDriverCostRuleItemMapper extends JynxBaseMapper<NbDriverCostRuleItemEntity> {


    NbDriverCostRuleItemEntity getRuleItemByIdAndStartKg(Integer ruleId, BigDecimal useWeight);
}