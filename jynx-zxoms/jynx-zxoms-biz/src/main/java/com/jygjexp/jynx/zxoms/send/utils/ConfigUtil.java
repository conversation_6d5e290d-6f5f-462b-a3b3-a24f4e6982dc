package com.jygjexp.jynx.zxoms.send.utils;

import cn.hutool.json.JSONUtil;
import com.jygjexp.jynx.zxoms.entity.NbConfigEntity;
import com.jygjexp.jynx.zxoms.send.service.NbConfigService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/10/15 23:08
 */
@Data
@RequiredArgsConstructor
@Component
public class ConfigUtil extends NbConfigEntity {
    private final NbConfigService nbConfigService;

    /**
     * 文本
     */
    public static final int TYPE_1_TEXT = 1;
    public static final int TYPE_2_INTEGER = 2;
    public static final int TYPE_3_TEXT = 3;
    public static final int TYPE_4_DOUBLE = 4;
    public static final int TYPE_5_BOOLEAN = 5;
    public static final int TYPE_6_JSON = 6;
    public static final int TYPE_7_DATETIME = 7;

    /**
     * 每个包裹配送平均时长（分钟）
     */
    public static final String routific_duration = "routific_duration";
    /**
     * 每个路区平均时长
     */
    public static final String routific_area_hour = "routific_area_hour";
    /**
     * 开启司机承载力
     */
    public static final String routific_use_capacity = "routific_use_capacity";

    /**
     * 体积重国际CM
     */
    public static final String volume_weight_international_cm = "volume_weight_international_cm";
    /**
     * 体积重国际IN
     */
    public static final String volume_weight_international_in = "volume_weight_international_in";
    /**
     * 体积重国内CN
     */
    public static final String volume_weight_domestic_cm = "volume_weight_domestic_cm";
    /**
     * 体积重国内IN
     */
    public static final String volume_weight_domestic_in = "volume_weight_domestic_in";

    /**
     * 派送开始时间（秒）
     */
    public static final String r4m_route_time = "r4m_route_time";

    /**
     * 动态开始时间(1/0)
     */
    public static final String r4m_dynamic_start_time = "r4m_dynamic_start_time";

    /**
     * 开始日期加几天
     */
    public static final String r4m_start_date_plus_day = "r4m_start_date_plus_day";

    /**
     * 路区最大时长（秒）
     */
    public static final String r4m_route_max_duration = "r4m_route_max_duration";

    /**
     * 每个地址服务时长（秒）
     */
    public static final String r4m_stop_service_time = "r4m_stop_service_time";

    /**
     * Round Trip (rt is short for round trip, the optimization engine changes its behavior for round trip routes)
     */
    public static final String r4m_rt = "r4m_rt";

    /**
     * the minimum number of stops allowed in a subtour
     */
    public static final String r4m_min_tour_size = "r4m_min_tour_size";

    /**
     * Route balancing mode. Available values: 'distance', 'time', 'destinations_count'
     */
    public static final String r4m_balance = "r4m_balance";

    /**
     * The driving directions will be generated biased for this selection. Available values: "Distance", "Time", "timeWithTraffic"
     */
    public static final String r4m_optimize = "r4m_optimize";

    /**
     * 测算司机固定成本补贴
     */
    public static final String driver_income_subsidy = "driver_income_subsidy";

    /**
     * 自动创建新批次的重量临界值
     */
    public static final String nb_auto_order_batch_threshold_weight = "nb_auto_order_batch_threshold_weight";

    /**
     * 司机结算时，采用包裹重与体积重最大值
     */
    public static final String nb_driver_cost_settle_auto_volume_weight = "nb_driver_cost_settle_auto_volume_weight";

    /**
     * 佳邮自提码通知接口
     */
    public static final String jy_pickup_code_notify_url = "jy_pickup_code_notify_url";

    public Integer getValueToInt() {
        if (getCValue() == null) {
            return null;
        }

        try {
            return Integer.valueOf(getCValue().trim());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String getCacheKey(String key) {
        return "nb_driver_config_".concat(key);
    }

//    public NbConfigEntity findByKey(String key) {
//        return findFirst("select * from nb_config where `c_key` = ?", key);
//    }

    public NbConfigEntity findCacheByKey(String key) {
//		String cacheKey = getCacheKey(key);
//		Config config = Redis.use().get(cacheKey);
//		if (config == null) {
//			config = findFirst("select * from nb_config where `c_key` = ?", key);
//			Redis.use().setex(cacheKey, RedisTimeout.MINUTE_10, config);
//		}
//		return config;
//        return findFirst("select * from nb_config where `c_key` = ?", key);
        return nbConfigService.findByCkey(key);
    }

    public void clearByKey(String key) {
        String cacheKey = getCacheKey(key);
//		Redis.use().del(cacheKey);
    }

    public String findStrByKey(String key) {
        return findCacheByKey(key).getCValue();
    }

    public Boolean findBooleanByKey(String key) {
        String value = findStrByKey(key);
        return Boolean.valueOf(value) || "1".equals(value.trim());
    }

    public Integer findIntegerByKey(String key) {
        return Integer.valueOf(findStrByKey(key));
    }

//    public Double findDoubleByKey(String key) {
//        return Double.valueOf(findStrByKey(key));
//    }

    public BigDecimal findDecimalByKey(String key) {
        String valueStr = findStrByKey(key);
        if (valueStr == null || valueStr.trim().isEmpty()) {
            throw new IllegalArgumentException("Config value for key '" + key + "' is null or empty.");
        }
        try {
            return new BigDecimal(valueStr);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Config value for key '" + key + "' is not a valid number: " + valueStr, e);
        }
    }

    public Object getTargetValue() {
//		1=string,2=int,3=text,4=double,5=boolean,6=json,7=datetime
        int type = getCType();
        switch (type) {
            case 1:
                return getCValue();
            case 2:
                return Integer.valueOf(getCValue());
            case 3:
                return getCValue();
            case 5:
                String value = getCValue();
                if (StringUtils.isBlank(value)) {
                    return false;
                }
                if ("1".equals(value) || "true".equals(value.toLowerCase())) {
                    return true;
                }
                return Boolean.valueOf(getCValue());
            case 4:
                return Double.valueOf(getCValue());
            case 6:
                if (getCValue().startsWith("[")) {
                    return JSONUtil.parseArray(getCValue());
                } else {
                    return JSONUtil.toJsonStr(getCValue());
                }
            default:
                return getCValue();
        }
    }
}
