package com.jygjexp.jynx.zxoms.send.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderPathEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderSignImageEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderPathService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderSignImageService;
import com.jygjexp.jynx.zxoms.send.utils.PositionUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: chenchang
 * @Description: 【统计】距离计算
 * @Date: 2024/10/9 23:44
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class DistanceCalcTask {
    private final NbOrderSignImageService nbOrderSignImageService;
    private final NbOrderService nbOrderService;
    private final NbOrderPathService nbOrderPathService;

    @SneakyThrows
    @XxlJob("distanceCalcHandler")
    public void distanceCalcHandler() {
        XxlJobHelper.log("定时任务：【统计距离计算】于:{}，输入参数{}", LocalDateTime.now(), "运行中");

        dealOrderPath();    // 处理签收记录距离
        dealOrderSignImage();   // 处理POD2距离

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【统计距离计算】执行结束，时间: {}", LocalDateTime.now());
    }

    private void dealOrderSignImage() {
        //  select * from nb_order_sign_image where image_id > 944139 and distance = -1 limit 5000
        LambdaQueryWrapper<NbOrderSignImageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NbOrderSignImageEntity::getDistance, -1).gt(NbOrderSignImageEntity::getImageId, 944139)
                .last("limit 5000");
        List<NbOrderSignImageEntity> images = nbOrderSignImageService.list(wrapper);

        for (NbOrderSignImageEntity image : images) {
            NbOrderEntity order = nbOrderService.getById(image.getOrderId());

            if (image.getLat() == 0 || image.getLng() == 0) {
                image.setDistance(-100d);
                nbOrderSignImageService.updateById(image);
                continue;
            }

            if (order.getDestLat() == null || order.getDestLng() == null) {
                image.setDistance(-100d);
                nbOrderSignImageService.updateById(image);
                continue;
            }

            double distance = PositionUtil.getDistance2(order.getDestLng(), order.getDestLat(), image.getLng(), image.getLat());

            image.setDistance(distance);
            nbOrderSignImageService.updateById(image);
        }
    }

    private void dealOrderPath() {
        // SELECT * FROM nb_order_path WHERE path_id > 2180166 and order_status=205 and distance = -1 LIMIT 1000
        LambdaQueryWrapper<NbOrderPathEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.gt(NbOrderPathEntity::getPathId, 2180166);
        queryWrapper.eq(NbOrderPathEntity::getOrderStatus, OrderDto.ORDER_STATUS_205_DELIVERED);
        queryWrapper.eq(NbOrderPathEntity::getDistance, -1);
        queryWrapper.last("limit 1000");
        List<NbOrderPathEntity> paths = nbOrderPathService.list(queryWrapper);

        for (NbOrderPathEntity path : paths) {
            NbOrderEntity order = nbOrderService.getById(path.getOrderId());
            if (path.getScanLng() == 0 || path.getScanLat() == 0) {
                path.setDistance(-100d);
                nbOrderPathService.updateById(path);
                continue;
            }

            if (order.getDestLat() == null || order.getDestLng() == null) {
                path.setDistance(-100d);
                nbOrderPathService.updateById(path);
                continue;
            }

            double distance = PositionUtil.getDistance2(order.getDestLng(), order.getDestLat(), path.getScanLng(), path.getScanLat());
            path.setDistance(distance);
            nbOrderPathService.updateById(path);
        }
    }

}
