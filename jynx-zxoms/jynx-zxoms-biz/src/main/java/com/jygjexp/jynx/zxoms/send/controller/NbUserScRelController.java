package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbUserScRelEntity;
import com.jygjexp.jynx.zxoms.send.service.NbUserScRelService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import org.springdoc.api.annotations.ParameterObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 账号关联分拣中心-废弃
 *
 * <AUTHOR>
 * @date 2024-11-08 15:24:26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbUserScRel" )
//@Tag(description = "nbUserScRel" , name = "账号关联分拣中心-废弃管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbUserScRelController {

    private final  NbUserScRelService nbUserScRelService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbUserScRel 账号关联分拣中心-废弃
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUserScRel_view')" )
    public R getNbUserScRelPage(@ParameterObject Page page, @ParameterObject NbUserScRelEntity nbUserScRel) {
        LambdaQueryWrapper<NbUserScRelEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbUserScRelService.page(page, wrapper));
    }


    /**
     * 通过id查询账号关联分拣中心-废弃
     * @param relId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{relId}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUserScRel_view')" )
    public R getById(@PathVariable("relId" ) Integer relId) {
        return R.ok(nbUserScRelService.getById(relId));
    }

    /**
     * 新增账号关联分拣中心-废弃
     * @param nbUserScRel 账号关联分拣中心-废弃
     * @return R
     */
//    @Operation(summary = "新增账号关联分拣中心-废弃" , description = "新增账号关联分拣中心-废弃" )
//    @SysLog("新增账号关联分拣中心-废弃" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUserScRel_add')" )
    public R save(@RequestBody NbUserScRelEntity nbUserScRel) {
        return R.ok(nbUserScRelService.save(nbUserScRel));
    }

    /**
     * 修改账号关联分拣中心-废弃
     * @param nbUserScRel 账号关联分拣中心-废弃
     * @return R
     */
//    @Operation(summary = "修改账号关联分拣中心-废弃" , description = "修改账号关联分拣中心-废弃" )
//    @SysLog("修改账号关联分拣中心-废弃" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUserScRel_edit')" )
    public R updateById(@RequestBody NbUserScRelEntity nbUserScRel) {
        return R.ok(nbUserScRelService.updateById(nbUserScRel));
    }

    /**
     * 通过id删除账号关联分拣中心-废弃
     * @param ids relId列表
     * @return R
     */
//    @Operation(summary = "通过id删除账号关联分拣中心-废弃" , description = "通过id删除账号关联分拣中心-废弃" )
//    @SysLog("通过id删除账号关联分拣中心-废弃" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUserScRel_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbUserScRelService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbUserScRel 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbUserScRel_export')" )
    public List<NbUserScRelEntity> export(NbUserScRelEntity nbUserScRel,Integer[] ids) {
        return nbUserScRelService.list(Wrappers.lambdaQuery(nbUserScRel).in(ArrayUtil.isNotEmpty(ids), NbUserScRelEntity::getRelId, ids));
    }
}