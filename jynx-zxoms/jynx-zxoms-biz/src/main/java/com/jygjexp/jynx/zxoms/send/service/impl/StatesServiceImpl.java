package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.StatesEntity;
import com.jygjexp.jynx.zxoms.send.mapper.StatesMapper;
import com.jygjexp.jynx.zxoms.send.service.StatesService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 国家所在地区
 *
 * <AUTHOR>
 * @date 2024-09-30 17:48:13
 */
@Service
@RequiredArgsConstructor
public class StatesServiceImpl extends ServiceImpl<StatesMapper, StatesEntity> implements StatesService {
    private final StatesMapper statesMapper;
    @Override
    public List<StatesEntity> findProvincesListById39() {
        LambdaQueryWrapper<StatesEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StatesEntity::getId ,39);
        return list(queryWrapper);
    }

    @Override
    public StatesEntity findByCountryIdAndNameOrIso2(long countryId, String nameOrIso2) {
        return statesMapper.findByCountryIdAndNameOrIso2(countryId, nameOrIso2);
    }
}