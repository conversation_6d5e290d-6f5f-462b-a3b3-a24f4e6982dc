package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbUserUploadFileEntity;
import com.jygjexp.jynx.zxoms.send.service.NbUserUploadFileService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 上传的文件
 *
 * <AUTHOR>
 * @date 2024-11-12 09:36:08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbUserUploadFile" )
@Tag(description = "nbUserUploadFile" , name = "上传的文件管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbUserUploadFileController {

    private final  NbUserUploadFileService nbUserUploadFileService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbUserUploadFile 上传的文件
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbUserUploadFile_view')" )
    public R getNbUserUploadFilePage(@ParameterObject Page page, @ParameterObject NbUserUploadFileEntity nbUserUploadFile) {
        LambdaQueryWrapper<NbUserUploadFileEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbUserUploadFileService.page(page, wrapper));
    }


    /**
     * 通过id查询上传的文件
     * @param fileId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{fileId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbUserUploadFile_view')" )
    public R getById(@PathVariable("fileId" ) Integer fileId) {
        return R.ok(nbUserUploadFileService.getById(fileId));
    }

    /**
     * 新增上传的文件
     * @param nbUserUploadFile 上传的文件
     * @return R
     */
    @Operation(summary = "新增上传的文件" , description = "新增上传的文件" )
    @SysLog("新增上传的文件" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbUserUploadFile_add')" )
    public R save(@RequestBody NbUserUploadFileEntity nbUserUploadFile) {
        return R.ok(nbUserUploadFileService.save(nbUserUploadFile));
    }

    /**
     * 修改上传的文件
     * @param nbUserUploadFile 上传的文件
     * @return R
     */
    @Operation(summary = "修改上传的文件" , description = "修改上传的文件" )
    @SysLog("修改上传的文件" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbUserUploadFile_edit')" )
    public R updateById(@RequestBody NbUserUploadFileEntity nbUserUploadFile) {
        return R.ok(nbUserUploadFileService.updateById(nbUserUploadFile));
    }

    /**
     * 通过id删除上传的文件
     * @param ids fileId列表
     * @return R
     */
    @Operation(summary = "通过id删除上传的文件" , description = "通过id删除上传的文件" )
    @SysLog("通过id删除上传的文件" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbUserUploadFile_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbUserUploadFileService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbUserUploadFile 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbUserUploadFile_export')" )
    public List<NbUserUploadFileEntity> export(NbUserUploadFileEntity nbUserUploadFile,Integer[] ids) {
        return nbUserUploadFileService.list(Wrappers.lambdaQuery(nbUserUploadFile).in(ArrayUtil.isNotEmpty(ids), NbUserUploadFileEntity::getFileId, ids));
    }
}