package com.jygjexp.jynx.zxoms.nbapp.controller.manager;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbLoginSessionEntity;
import com.jygjexp.jynx.zxoms.entity.NbSortingCenterEntity;
import com.jygjexp.jynx.zxoms.entity.NbUserEntity;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.utils.AccessTokenBuilderKit;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.NbLoginSessionService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.send.service.NbUserService;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: chenchang
 * @Description: APP-用户相关
 * @Date: 2024/11/8 15:03
 */
@RestController
@RequiredArgsConstructor
@Tag(description = "appmanageruser" , name = "（弃用）APP-用户相关" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequestMapping("/app/manager/user")
public class UserController extends BaseController {
    private final NbUserService userService;
    private final NbSortingCenterService sortingCenterService;
    private final NbLoginSessionService loginSessionService;
    private final CommonDataUtil commonDataUtil;

    /**
     * APP用户登录
     * @param account
     * @param password
     * @return
     */
    @Operation(summary = "APP用户登录" , description = "APP用户登录" )
    @PostMapping("/login" )
    public R login(@RequestParam("account") String account, @RequestParam("password") String password, HttpServletRequest request) {
        if (StrUtil.isBlank(account)) {
            return LocalizedR.failed("user.account.not.empty", Optional.ofNullable(null));
        }
        if (StrUtil.isBlank(password)) {
            return LocalizedR.failed("user.password.not.empty", Optional.ofNullable(null));
        }

        // "select * from nb_user where account = ? and password = ? limit 1", account.trim(), password);
        NbUserEntity user = userService.getOne(new LambdaQueryWrapper<NbUserEntity>().eq(NbUserEntity::getAccount, account.trim()).eq(NbUserEntity::getPassword, password));
        if (user == null) {
            return LocalizedR.failed("user.account.or.password.error", Optional.ofNullable(null));
        }

        // "select sc.* from nb_sorting_center sc, nb_user_sc_rel r where sc.sc_id = r.sc_id and r.user_id = ?", user.getUserId());
        List<NbSortingCenterEntity> scList = sortingCenterService.findScByUserId(user.getUserId());
        JSONObject userJo = new JSONObject();
        userJo.set("account", user.getAccount());
        userJo.set("addTime", DateFormatUtils.format(user.getAddTime(), "yyyy-MM-dd HH:mm:ss"));

        JSONObject jo = new JSONObject();
        jo.set("user", userJo);
        jo.set("scList", scList.stream().map(sc -> {
            JSONObject scJo = new JSONObject();
            scJo.set("scId", sc.getScId());
            scJo.set("centerName", sc.getCenterName());
            scJo.set("address", sc.getAddress());
            scJo.set("postalCode", sc.getPostalCode());
            scJo.set("country", commonDataUtil.getCountryById(sc.getCountryId()));
            scJo.set("province", commonDataUtil.getProvinceById(sc.getProvinceId()));
            scJo.set("city", commonDataUtil.getCityById(sc.getCityId()));
            scJo.set("scCode", sc.getScCode());
            return scJo;
        }).collect(Collectors.toCollection(JSONArray::new)));

        String accessToken = AccessTokenBuilderKit.getAccessToken(request);

        // "select * from nb_login_session where user_id = ? limit 1", user.getUserId());
        NbLoginSessionEntity ls = loginSessionService.getOne(new LambdaQueryWrapper<NbLoginSessionEntity>().eq(NbLoginSessionEntity::getUserId, user.getUserId()));
        String userAgent = request.getHeader("user-agent");
        if (ls == null) {
            ls = new NbLoginSessionEntity();
            ls.setUserId(user.getUserId());
            ls.setAccessToken(accessToken);
            ls.setLoginTime(new Date());
            ls.setUserAgent(userAgent);
            loginSessionService.save(ls);
        } else {
            ls.setAccessToken(accessToken);
            ls.setUpdateTime(new Date());
            ls.setUserAgent(userAgent);
            loginSessionService.updateById(ls);
        }

        jo.set("accessToken", accessToken);
        return R.ok(jo);
    }

    /**
     * 退出登录
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "APP退出登录" , description = "APP退出登录" )
    @PostMapping("/logout" )
    public R logout() {
        NbUserEntity user = getLoginManager();
        // "select * from nb_login_session where user_id = ? limit 1", user.getUserId());
        NbLoginSessionEntity ls = loginSessionService.getOne(new LambdaQueryWrapper<NbLoginSessionEntity>().eq(NbLoginSessionEntity::getUserId, user.getUserId()));
        loginSessionService.removeById(ls);
        return R.ok();
    }

    @Operation(summary = "APP-info" , description = "APP-info" )
    @PostMapping("/info" )
    public R info(HttpServletRequest request) {
        NbUserEntity user = getLoginManager();
        // "select sc.* from nb_sorting_center sc, nb_user_sc_rel r where sc.sc_id = r.sc_id and r.user_id = ?", user.getUserId());
        List<NbSortingCenterEntity> scList = sortingCenterService.findScByUserId(user.getUserId());

        JSONObject userJo = new JSONObject();
        userJo.set("account", user.getAccount());
        userJo.set("addTime", DateFormatUtils.format(user.getAddTime(), "yyyy-MM-dd HH:mm:ss"));

        JSONObject jo = new JSONObject();
        jo.set("user", userJo);
        jo.set("scList", scList.stream().map(sc -> {
            JSONObject scJo = new JSONObject();
            scJo.set("scId", sc.getScId());
            scJo.set("centerName", sc.getCenterName());
            scJo.set("address", sc.getAddress());
            scJo.set("postalCode", sc.getPostalCode());
            scJo.set("country", commonDataUtil.getCountryById(sc.getCountryId()));
            scJo.set("province", commonDataUtil.getProvinceById(sc.getProvinceId()));
            scJo.set("city", commonDataUtil.getCityById(sc.getCityId()));
            scJo.set("scCode", sc.getScCode());
            return scJo;
        }).collect(Collectors.toCollection(JSONArray::new)));

        String accessToken = AccessTokenBuilderKit.getAccessToken(request);

        // "select * from nb_login_session where user_id = ? limit 1", user.getUserId());
        NbLoginSessionEntity ls = loginSessionService.getOne(new LambdaQueryWrapper<NbLoginSessionEntity>().eq(NbLoginSessionEntity::getUserId, user.getUserId()));
        String userAgent = request.getHeader("user-agent");
        if (ls == null) {
            ls = new NbLoginSessionEntity();
            ls.setUserId(user.getUserId());
            ls.setAccessToken(accessToken);
            ls.setLoginTime(new Date());
            ls.setUserAgent(userAgent);
            loginSessionService.save(ls);
        } else {
            ls.setAccessToken(accessToken);
            ls.setUpdateTime(new Date());
            ls.setUserAgent(userAgent);
            loginSessionService.updateById(ls);
        }

        return R.ok(jo);
    }


}
