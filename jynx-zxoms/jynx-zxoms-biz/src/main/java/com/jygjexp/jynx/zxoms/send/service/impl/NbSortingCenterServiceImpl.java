package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.entity.*;

import com.jygjexp.jynx.zxoms.nbapp.vo.ApiRequestParamsVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.mapper.*;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.send.vo.SortingCenterExcelVo;
import com.jygjexp.jynx.zxoms.utils.NBDUtils;
import com.jygjexp.jynx.zxoms.vo.NbSortingCenterPageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分拣中心
 *
 * <AUTHOR>
 * @date 2024-09-30 22:48:18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class NbSortingCenterServiceImpl extends ServiceImpl<NbSortingCenterMapper, NbSortingCenterEntity> implements NbSortingCenterService {
    private final NbSortingCenterMapper nbSortingCenterMapper;
    private final NbShelfPkgLogMapper nbShelfPkgLogMapper;
    private final NbTransferCenterMapper nbTransferCenterMapper;
    private final NbOrderMapper nbOrderMapper;
    private final NbShelfMapper nbShelfMapper;
    private final CommonDataUtil commonDataUtil;
    private final RemoteUserService remoteUserService;

    // 查询登录用户的分拣中心列表-公共接口
    @Override
    public List<NbSortingCenterPageVo> listSc() {
        //select sc_id id, center_name cn from nb_sorting_center where is_valid = true; ds=nbd;
        MPJLambdaWrapper<NbSortingCenterEntity> wrapper = new MPJLambdaWrapper<NbSortingCenterEntity>();
        wrapper.selectAll(NbSortingCenterEntity.class)
                .eq(NbSortingCenterEntity::getIsValid,true)
                .orderByDesc(NbSortingCenterEntity::getScId);
        List<Integer> idList = getNbdScIdThisUser();
        wrapper.in(ObjectUtil.isNotNull(idList) && !idList.isEmpty(),NbSortingCenterEntity::getScId, idList); // 筛选当前用户可见的分拣中心
        List<NbSortingCenterEntity> entityList = nbSortingCenterMapper.selectList(wrapper);
        List<NbSortingCenterPageVo> voList = entityList.stream().map(entity -> {
            NbSortingCenterPageVo vo = new NbSortingCenterPageVo();
            vo.setScId(entity.getScId());
            vo.setSortingCenterName(entity.getCenterName());    //设置分拣中心名称
            vo.setScCode(entity.getScCode());
            vo.setScTimezone(entity.getScTimezone());
            vo.setLat(entity.getLat());
            vo.setLng(entity.getLng());
            vo.setAddress(entity.getAddress());
            vo.setPostalCode(entity.getPostalCode());
            vo.setServiceTel(entity.getServiceTel());
            vo.setBusinessHours(entity.getBusinessHours());
            vo.setCreateUserName(entity.getCreateUserName());
            vo.setCreateTime(entity.getCreateTime());
            vo.setUpdateTime(entity.getUpdateTime());
            vo.setR4mMemberId(entity.getR4mMemberId());
            vo.setSelfPickupMaxStorage(entity.getSelfPickupMaxStorage());
            vo.setCityId(entity.getCityId());
            vo.setCountryId(entity.getCountryId());
            vo.setProvinceId(entity.getProvinceId());
            vo.setIsValid(entity.getIsValid());
            vo.setCenterName(entity.getCenterName());
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }

    // 用户管理-查询所有分拣中心列表
    @Override
    public List<NbSortingCenterPageVo> listAllSc() {
        //select sc_id id, center_name cn from nb_sorting_center where is_valid = true; ds=nbd;
        MPJLambdaWrapper<NbSortingCenterEntity> wrapper = new MPJLambdaWrapper<NbSortingCenterEntity>();
        wrapper.selectAll(NbSortingCenterEntity.class)
                .eq(NbSortingCenterEntity::getIsValid,true)
                .orderByDesc(NbSortingCenterEntity::getScId);
        List<NbSortingCenterEntity> entityList = nbSortingCenterMapper.selectList(wrapper);
        List<NbSortingCenterPageVo> voList = entityList.stream().map(entity -> {
            NbSortingCenterPageVo vo = new NbSortingCenterPageVo();
            vo.setScId(entity.getScId());
            vo.setSortingCenterName(entity.getCenterName());    //设置分拣中心名称
            vo.setScCode(entity.getScCode());
            vo.setScTimezone(entity.getScTimezone());
            vo.setLat(entity.getLat());
            vo.setLng(entity.getLng());
            vo.setAddress(entity.getAddress());
            vo.setPostalCode(entity.getPostalCode());
            vo.setServiceTel(entity.getServiceTel());
            vo.setBusinessHours(entity.getBusinessHours());
            vo.setCreateUserName(entity.getCreateUserName());
            vo.setCreateTime(entity.getCreateTime());
            vo.setUpdateTime(entity.getUpdateTime());
            vo.setR4mMemberId(entity.getR4mMemberId());
            vo.setSelfPickupMaxStorage(entity.getSelfPickupMaxStorage());
            vo.setCityId(entity.getCityId());
            vo.setCountryId(entity.getCountryId());
            vo.setProvinceId(entity.getProvinceId());
            vo.setIsValid(entity.getIsValid());
            vo.setCenterName(entity.getCenterName());
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }

    @Override
    public List<NbSortingCenterEntity> findScByUserId(Integer userId) {
        return nbSortingCenterMapper.findScByUserId(userId);
    }

    // 获取用户登录下的分拣中心
    @Override
    public List<Integer> getNbdScIdThisUser() {
        SysUser user = remoteUserService.getOneUserById(SecurityUtils.getUser().getId()).getData();
        LambdaQueryWrapper<NbSortingCenterEntity> wrapper = new LambdaQueryWrapper<>();
        String nbdScId = user.getNbdScId(); //获取用户登录下的分拣中心
        if (StringUtils.isNotBlank(nbdScId)) {
            // 如果该用户配置了分拣中心，则取该用户下的分拣中心关联
            List<Integer> idList = Arrays.stream(nbdScId.split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
            wrapper.in(NbSortingCenterEntity::getScId, idList);
        }
        List<NbSortingCenterEntity> scList = nbSortingCenterMapper.selectList(wrapper);
        List<Integer> nbdScIdList = scList.stream().map(NbSortingCenterEntity::getScId).collect(Collectors.toList());
        return nbdScIdList;
    }
    @Override
    public R doCheckCode(String scanCode, NbDriverEntity loginDriver) {
        if (StrUtil.isBlank(scanCode)) {
            return LocalizedR.failed("nbsortingCenter.invalid.code", Optional.ofNullable(null));
        }
        
        JSONObject retJo = new JSONObject();
        if (scanCode.startsWith("nbd/shelf/")) {
            scanCode = scanCode.replace("nbd/shelf/", "");

            retJo.put("type", "HJ");
            retJo.put("code", scanCode);

            // "select * from nb_shelf where shelf_code = ? limit 1", scanCode);
            NbShelfEntity shelf = nbShelfMapper.selectOne(new LambdaQueryWrapper<NbShelfEntity>().eq(NbShelfEntity::getShelfCode, scanCode));
            if (shelf == null) {
                return LocalizedR.failed("nbsortingCenter.the.shelf.does.not.exist", scanCode);
            }

            if (shelf.getIsValid() == false) {
                return LocalizedR.failed("nbsortingCenter.the.shelf.has.been.set.and.is.invalid", scanCode);
            }
            if (shelf.getTcId().intValue() != loginDriver.getTcId() && shelf.getScId().intValue() != loginDriver.getScId()) {
                return LocalizedR.failed("nbsortingCenter.the.shelves.are.not.under.your.jurisdiction", scanCode);
            }

            String timezone = null;
            if (shelf.getScId() > 0) {
                timezone = nbSortingCenterMapper.selectById(shelf.getScId()).getScTimezone();
            }
            if (shelf.getTcId() > 0) {
                // "select sc.* from nb_sorting_center sc, nb_transfer_center tc where sc.sc_id and tc.sc_id and tc._id = ?", shelf.getTcId()).getScTimezone();
                NbSortingCenterEntity sortingCenterEntity = nbSortingCenterMapper.findFirstSc(shelf.getTcId());
                timezone = sortingCenterEntity.getScTimezone();
            }

            Date localDatetime = NBDUtils.getLocalDate(timezone, Instant.now().toEpochMilli());
            String ymd = DateFormatUtils.format(localDatetime, "yyyy-MM-dd");

            int centerOrderTotal = 0;

            if (shelf.getScId() > 0) {
                // "select count(1) cont from nb_shelf_pkglog spl, nb_shelf s where spl.shelf_id = s.shelf_id and s.sc_id = ? and spl.putaway_datetime >= '" + ymd + " 00:00:00'", shelf.getScId()).getInt("cont");
                centerOrderTotal = nbShelfPkgLogMapper.findShelfPkgLogFirstByScId(shelf.getScId(), ymd);
            }
            if (shelf.getTcId() > 0) {
                // "select count(1) cont from nb_shelf_pkglog spl, nb_shelf s where spl.shelf_id = s.shelf_id and s.tc_id = ? and spl.putaway_datetime >= '" + ymd + " 00:00:00'", shelf.getTcId()).getInt("cont");
                centerOrderTotal = nbShelfPkgLogMapper.findShelfPkgLogFirstByTcId(shelf.getTcId(), ymd);
            }

            JSONObject shelfJo = new JSONObject();
            shelfJo.set("shelfId", shelf.getShelfId());
            shelfJo.set("shelfCode", shelf.getShelfCode());
            shelfJo.set("orderTotal", shelf.getOrderTotal());
            if (shelf.getScId() > 0) {
                NbSortingCenterEntity sc = nbSortingCenterMapper.selectById(shelf.getScId());
                JSONObject scjo = new JSONObject();
                scjo.set("scId", sc.getScId());
                scjo.set("centerName", sc.getCenterName());
                scjo.set("address", sc.getAddress());
                scjo.set("postalCode", sc.getPostalCode());
                scjo.set("country", commonDataUtil.getCountryById(sc.getCountryId()));
                scjo.set("province", commonDataUtil.getProvinceById(sc.getProvinceId()));
                scjo.set("city", commonDataUtil.getCityById(sc.getCityId()));
                scjo.set("scCode", sc.getScCode());
                shelfJo.set("sc", scjo);
            }
            if (shelf.getTcId() > 0) {
                NbTransferCenterEntity tc = nbTransferCenterMapper.selectById(shelf.getTcId());
                JSONObject tcjo = new JSONObject();
                tcjo.set("tcId", tc.getTcId());
                tcjo.set("code", tc.getTransferCenterCode());
                tcjo.set("provinceId", tc.getProvinceId());
                tcjo.set("province", commonDataUtil.getProvinceById(tc.getProvinceId()));
                tcjo.set("cityId", tc.getCityId());
                tcjo.set("city", commonDataUtil.getCityById(tc.getCityId()));
                tcjo.set("address", tc.getAddress());
                tcjo.set("postalCode", tc.getPostalCode());
                tcjo.set("centerName", tc.getCenterName());
                shelfJo.set("tc", tcjo);
            }
            shelfJo.set("centerOrderTotal", centerOrderTotal); // 中心总数

            retJo.set("shelf", shelfJo);
        } else {
            retJo.set("type", "PKG");
            retJo.set("code", scanCode);

            //  select * from nb_order where pkg_no = ? limit 1", scanCode);
            NbOrderEntity order = nbOrderMapper.selectOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo, scanCode));
            if (order == null) {
                return LocalizedR.failed("nbsortingCenter.the.package.does.not.exist", scanCode);
            }

            if (order.getOrderStatus() == OrderDto.ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE) {
                return LocalizedR.failed("nbsortingCenter.the.package.has.been.shelved", scanCode);
            }

            List<Integer> accessOrderStatus = Lists.newArrayList();
            accessOrderStatus.add(OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT);
            accessOrderStatus.add(OrderDto.ORDER_STATUS_280_FAILURE);
            accessOrderStatus.add(OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);

            if (!accessOrderStatus.contains(order.getOrderStatus())) {
                return LocalizedR.failed("nbsortingCenter.only.orders.that.have.failed.delivery.can.be.listed", Optional.ofNullable(null));
            }

            retJo.set("order", new OrderDto().toDriverListJson(order));
        }
        return R.ok(retJo);
    }

    // 分拣中心分页查询
    @Override
    public Page<NbSortingCenterPageVo> search(Page page, NbSortingCenterPageVo vo) {
        MPJLambdaWrapper wrapper = getWrapper(vo, null);
        return nbSortingCenterMapper.selectJoinPage(page, NbSortingCenterPageVo.class, wrapper);
    }

    // 导出分拣中心到Excel
    @Override
    public List<SortingCenterExcelVo> getExcel(NbSortingCenterPageVo vo, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids);
        return nbSortingCenterMapper.selectJoinList(SortingCenterExcelVo.class, wrapper);
    }

    private MPJLambdaWrapper getWrapper(NbSortingCenterPageVo vo, Integer[] ids) {
        MPJLambdaWrapper<NbSortingCenterEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbSortingCenterEntity.class)
                .like(StrUtil.isNotBlank(vo.getCenterName()), NbSortingCenterEntity::getCenterName, vo.getCenterName())     // 条件查询-分拣中心名称
                .like(StrUtil.isNotBlank(vo.getScCode()), NbSortingCenterEntity::getScCode, vo.getScCode())                 // 条件查询-分拣中心代码
                .eq(ObjectUtil.isNotNull(vo.getIsValid()), NbSortingCenterEntity::getIsValid, vo.getIsValid())              // 条件查询-是否启用
                .selectAs(CountriesEntity::getName, NbSortingCenterPageVo.Fields.countriesName)
                .selectAs(StatesEntity::getName, NbSortingCenterPageVo.Fields.statesName)
                .selectAs(CitiesEntity::getName, NbSortingCenterPageVo.Fields.cityName)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbSortingCenterPageVo::getScId, ids)
                .leftJoin(CountriesEntity.class, CountriesEntity::getId, NbSortingCenterEntity::getCountryId)
                .leftJoin(StatesEntity.class,StatesEntity::getId, NbSortingCenterEntity::getProvinceId)
                .leftJoin(CitiesEntity.class, CitiesEntity::getId, NbSortingCenterEntity::getCityId)
                .orderByDesc(NbSortingCenterEntity::getScId);

        String createDate = vo.getAddTime();   //创建时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        if (createDate != null) {
            int splitIndex = createDate.indexOf(":", createDate.indexOf(":") + 1) + 3;
            String startAddTime = createDate.substring(0, splitIndex);
            String endAddTime = createDate.substring(splitIndex + 1);
            wrapper.ge(NbSortingCenterEntity::getCreateTime, startAddTime).le(NbSortingCenterEntity::getCreateTime, endAddTime);    // 条件查询-创建时间
        }
        return wrapper;
    }

    // Api-获取仓库列表
    @Override
    public OldResult getWarehouseList(ApiRequestParamsVo paramsVo) {
        List<NbSortingCenterEntity> centers = list(new LambdaQueryWrapper<NbSortingCenterEntity>().eq(NbSortingCenterEntity::getIsValid, true));
        JSONArray ja = centers.stream().map(center -> {
            JSONObject jo = new JSONObject();
            jo.set("id", center.getScId());
            jo.set("name", center.getCenterName());
            jo.set("address", center.getAddress());
            jo.set("postalCode", center.getPostalCode());
            jo.set("country", commonDataUtil.getCountryById(center.getCountryId()));
            jo.set("province", commonDataUtil.getProvinceById(center.getProvinceId()));
            jo.set("city", commonDataUtil.getCityById(center.getCityId()));
            jo.set("warehouseCode", center.getScCode());
            return jo;
        }).collect(Collectors.toCollection(JSONArray::new));
        return OldResult.ok("0", ja);
    }

}