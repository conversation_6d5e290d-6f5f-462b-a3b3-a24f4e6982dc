package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbCustomerAddressEntity;
import com.jygjexp.jynx.zxoms.send.service.NbCustomerAddressService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import org.springdoc.api.annotations.ParameterObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户地址
 *
 * <AUTHOR>
 * @date 2024-11-11 23:41:51
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbCustomerAddress" )
//@Tag(description = "nbCustomerAddress" , name = "客户地址管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbCustomerAddressController {

    private final  NbCustomerAddressService nbCustomerAddressService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbCustomerAddress 客户地址
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbCustomerAddress_view')" )
    public R getNbCustomerAddressPage(@ParameterObject Page page, @ParameterObject NbCustomerAddressEntity nbCustomerAddress) {
        LambdaQueryWrapper<NbCustomerAddressEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbCustomerAddressService.page(page, wrapper));
    }


    /**
     * 通过id查询客户地址
     * @param addressId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{addressId}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbCustomerAddress_view')" )
    public R getById(@PathVariable("addressId" ) Integer addressId) {
        return R.ok(nbCustomerAddressService.getById(addressId));
    }

    /**
     * 新增客户地址
     * @param nbCustomerAddress 客户地址
     * @return R
     */
//    @Operation(summary = "新增客户地址" , description = "新增客户地址" )
//    @SysLog("新增客户地址" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbCustomerAddress_add')" )
    public R save(@RequestBody NbCustomerAddressEntity nbCustomerAddress) {
        return R.ok(nbCustomerAddressService.save(nbCustomerAddress));
    }

    /**
     * 修改客户地址
     * @param nbCustomerAddress 客户地址
     * @return R
     */
//    @Operation(summary = "修改客户地址" , description = "修改客户地址" )
//    @SysLog("修改客户地址" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbCustomerAddress_edit')" )
    public R updateById(@RequestBody NbCustomerAddressEntity nbCustomerAddress) {
        return R.ok(nbCustomerAddressService.updateById(nbCustomerAddress));
    }

    /**
     * 通过id删除客户地址
     * @param ids addressId列表
     * @return R
     */
//    @Operation(summary = "通过id删除客户地址" , description = "通过id删除客户地址" )
//    @SysLog("通过id删除客户地址" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbCustomerAddress_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbCustomerAddressService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbCustomerAddress 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbCustomerAddress_export')" )
    public List<NbCustomerAddressEntity> export(NbCustomerAddressEntity nbCustomerAddress,Integer[] ids) {
        return nbCustomerAddressService.list(Wrappers.lambdaQuery(nbCustomerAddress).in(ArrayUtil.isNotEmpty(ids), NbCustomerAddressEntity::getAddressId, ids));
    }
}