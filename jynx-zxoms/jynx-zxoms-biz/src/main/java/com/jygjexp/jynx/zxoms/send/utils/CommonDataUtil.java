package com.jygjexp.jynx.zxoms.send.utils;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.jygjexp.jynx.zxoms.entity.CitiesEntity;
import com.jygjexp.jynx.zxoms.entity.CountriesEntity;
import com.jygjexp.jynx.zxoms.entity.StatesEntity;
import com.jygjexp.jynx.zxoms.send.service.CitiesService;
import com.jygjexp.jynx.zxoms.send.service.CountriesService;
import com.jygjexp.jynx.zxoms.send.service.StatesService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
public class CommonDataUtil {
	private final CountriesService countriesService;
	private final StatesService statesService;
	private final CitiesService citiesService;
	
	/**
	 * 获取加拿大的省市区
	 * @return 
	 */
	public JSONObject getCaRegion() {
//		List<Record> countries = Db.find("select * from `" + NBDConstants.COMMON_DB_NAME + "`.countries where id = 39");
		List<CountriesEntity> countries = countriesService.findCountriesListById39();

		JSONArray countrysJa = countries.stream().map(c -> {
			JSONObject jo = new JSONObject();
			jo.set("id", c.getId());
			jo.set("enName", c.getName());
			jo.set("cnName", c.getName());
			jo.set("iso2", c.getIso2());
			return jo;
		}).collect(Collectors.toCollection(JSONArray::new));
		
//		List<Record> provinces = Db.find("select * from `" + NBDConstants.COMMON_DB_NAME + "`.states where country_id = 39");
		List<StatesEntity> provinces = statesService.findProvincesListById39();

		JSONArray provinceJa = provinces.stream().map(c -> {
			JSONObject jo = new JSONObject();
			jo.set("id", c.getId());
			jo.set("enName", c.getName());
			jo.set("cnName", c.getNameCn());
			jo.set("iso2", c.getIso2());
			jo.set("pid", c.getCountryId());
			return jo;
		}).collect(Collectors.toCollection(JSONArray::new));
		
//		List<Record> citys = Db.find("select * from `" + NBDConstants.COMMON_DB_NAME + "`.cities where country_id = 39");
		List<CitiesEntity> citys = citiesService.findCitysListById39();

		JSONArray cityJa = citys.stream().map(c -> {
			JSONObject jo = new JSONObject();
			jo.set("id", c.getId());
			jo.set("enName", c.getName());
			jo.set("cnName", c.getName());
//			jo.put("iso2", c.getIso2());	//无iso2
			jo.set("pid", c.getStateId());
			return jo;
		}).collect(Collectors.toCollection(JSONArray::new));
		
		JSONObject region = new JSONObject();
		region.set("countrys", countrysJa);
		region.set("provinces", provinceJa);
		region.set("citys", cityJa);
		
		return region;
	}
	
	public JSONObject getCountryById(int id) {
//		Record country = Db.findFirst("select * from `" + NBDConstants.COMMON_DB_NAME + "`.countries where id = ?", id);
		CountriesEntity country = countriesService.getById(id);

		JSONObject jo = new JSONObject();
		jo.set("id", country.getId());
		jo.set("enName", country.getName());
		jo.set("cnName", country.getName());
		jo.set("iso2", country.getIso2());
		return jo;
	}
	
	public JSONObject getProvinceById(int id) {
//		Record p = Db.findFirst("select * from `" + NBDConstants.COMMON_DB_NAME + "`.states where id = ?", id);
		StatesEntity statesEntity = statesService.getById(id);

		return stateToJson(statesEntity);
	}
	
	public JSONObject getCityById(int id) {
		String key = "nb_driver_city_id_" + id;
//		Record c = Redis.use().get(key);
//		if (c == null) {
//			c = Db.findFirst("select * from `" + NBDConstants.COMMON_DB_NAME + "`.cities where id = ?", id);
//			if (c != null) {
//				Redis.use().setex(key, RedisTimeout.HOUR_1, c);
//			}
//		}
//		Record c = Db.findFirst("select * from `" + NBDConstants.COMMON_DB_NAME + "`.cities where id = ?", id);
		CitiesEntity entity = citiesService.getById(id);

		JSONObject jo = new JSONObject();
		jo.set("id", entity.getId());
		jo.set("enName", entity.getName());
		jo.set("cnName", entity.getName());
		jo.set("pid", entity.getStateId());
		return jo;
	}

	public JSONObject getProvinceByNameOrIso2(long countryId, String nameOrIso2) {
//		Record c = Db.findFirst("select * from `" + NBDConstants.COMMON_DB_NAME + "`.states where country_id = ? and (name = ? or iso2 = ?)", countryId, nameOrIso2, nameOrIso2);
		StatesEntity entity = statesService.findByCountryIdAndNameOrIso2(countryId, nameOrIso2);
		return stateToJson(entity);
	}
	
	private static JSONObject stateToJson(StatesEntity states) {
		JSONObject jo = new JSONObject();
		jo.set("id", states.getId());
		jo.set("enName", states.getName());
		jo.set("cnName", states.getNameCn());
		jo.set("iso2", states.getIso2());
		jo.set("pid", states.getCountryId());
		jo.set("zoneId", states.getZoneId());
		return jo;
	}

	/**
	 * 根据省市，组合地址
	 * @param provinceId
	 * @param cityId
	 * @return
	 */
	public String getAddress(int provinceId, int cityId) {
		JSONObject provinceObj = getProvinceById(provinceId);
		JSONObject cityObj = getCityById(cityId);
		return cityObj.getStr("enName") + " " + provinceObj.getStr("enName");
	}

}
