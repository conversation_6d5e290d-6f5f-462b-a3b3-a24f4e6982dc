package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.dto.DriverScanedDto;
import com.jygjexp.jynx.zxoms.entity.NbDriverScanedEntity;
import com.jygjexp.jynx.zxoms.send.service.NbDriverScanedService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 司机扫描的快件
 *
 * <AUTHOR>
 * @date 2024-11-02 21:18:44
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbDriverScaned" )
@Tag(description = "nbDriverScaned" , name = "司机扫描的快件管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbDriverScanedController {

    private final  NbDriverScanedService nbDriverScanedService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbDriverScaned 司机扫描的快件
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScaned_view')" )
    public R getNbDriverScanedPage(@ParameterObject Page page, @ParameterObject NbDriverScanedEntity nbDriverScaned) {
        LambdaQueryWrapper<NbDriverScanedEntity> wrapper = Wrappers.lambdaQuery();
        Page<DriverScanedDto> pageRecords = nbDriverScanedService.page(page, wrapper);
        List<DriverScanedDto> records1 = pageRecords.getRecords();
        nbDriverScanedService.processQueryAfter(records1, nbDriverScaned);
        return R.ok(pageRecords);
    }


    /**
     * 通过id查询司机扫描的快件
     * @param scanedId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{scanedId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScaned_view')" )
    public R getById(@PathVariable("scanedId" ) Integer scanedId) {
        return R.ok(nbDriverScanedService.getById(scanedId));
    }

    /**
     * 新增司机扫描的快件
     * @param nbDriverScaned 司机扫描的快件
     * @return R
     */
    @Operation(summary = "新增司机扫描的快件" , description = "新增司机扫描的快件" )
    @SysLog("新增司机扫描的快件" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScaned_add')" )
    public R save(@RequestBody NbDriverScanedEntity nbDriverScaned) {
        return R.ok(nbDriverScanedService.save(nbDriverScaned));
    }

    /**
     * 修改司机扫描的快件
     * @param nbDriverScaned 司机扫描的快件
     * @return R
     */
    @Operation(summary = "修改司机扫描的快件" , description = "修改司机扫描的快件" )
    @SysLog("修改司机扫描的快件" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScaned_edit')" )
    public R updateById(@RequestBody NbDriverScanedEntity nbDriverScaned) {
        return R.ok(nbDriverScanedService.updateById(nbDriverScaned));
    }

    /**
     * 通过id删除司机扫描的快件
     * @param ids scanedId列表
     * @return R
     */
    @Operation(summary = "通过id删除司机扫描的快件" , description = "通过id删除司机扫描的快件" )
    @SysLog("通过id删除司机扫描的快件" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScaned_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbDriverScanedService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbDriverScaned 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbDriverScaned_export')" )
    public List<NbDriverScanedEntity> export(NbDriverScanedEntity nbDriverScaned,Integer[] ids) {
        return nbDriverScanedService.list(Wrappers.lambdaQuery(nbDriverScaned).in(ArrayUtil.isNotEmpty(ids), NbDriverScanedEntity::getScanedId, ids));
    }

    /**
     * 审核通过
     * @param batchId
     * @return
     */
    @Operation(summary = "司机扫描的快件审核通过" , description = "司机扫描的快件审核通过" )
    @SysLog("司机扫描的快件审核通过" )
    @PostMapping("/auditPass")
    public R auditPass(Integer batchId){
        return nbDriverScanedService.auditPass(batchId);
    }

    /**
     * 审核拒绝
     * @param batchId
     * @return
     */
    @Operation(summary = "司机扫描的快件审核拒绝" , description = "司机扫描的快件审核拒绝" )
    @SysLog("司机扫描的快件审核拒绝" )
    @PostMapping("/auditRefuse")
    public R auditRefuse(Integer batchId){
        return nbDriverScanedService.auditRefuse(batchId);
    }

}