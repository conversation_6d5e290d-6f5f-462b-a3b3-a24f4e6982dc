package com.jygjexp.jynx.zxoms.send.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.send.service.NbOrderStatService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.vo.OrderStatPageVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.HttpHeaders;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/19 20:26
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/nb/driver/home")
@Tag(description = "nbhome" , name = "首页管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class HomeController {
	private final NbSortingCenterService nbSortingCenterService;
	private final NbOrderStatService nbOrderStatService;
	private final CommonDataUtil commonDataUtil;

	@Operation(summary = "分拣中心查询", description = "分拣中心查询")
	@GetMapping("/listSc")
	public R listSc() {
		return R.ok(nbSortingCenterService.listSc());
	}

	@Operation(summary = "订单统计", description = "订单统计")
	@GetMapping("/orderStat")
	public R orderStat(@ParameterObject Page page, @ParameterObject OrderStatPageVo vo) {
		return R.ok(nbOrderStatService.orderStat(page, vo));
	}

	@Operation(summary = "订单信息", description = "订单信息")
	@GetMapping("/orderInfo")
	public void orderInfo(@ParameterObject Page page) {
		// paginate(pageNo, pageSize, "select *", "from nb_order_stat");
		nbOrderStatService.page(page);
	}

//    public void timeout() {
//        try {
//            Thread.sleep(1000 * 60 * 10);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }

}
