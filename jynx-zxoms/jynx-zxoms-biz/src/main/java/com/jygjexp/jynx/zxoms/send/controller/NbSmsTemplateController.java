package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbSmsTemplateEntity;
import com.jygjexp.jynx.zxoms.send.service.NbSmsTemplateService;
import com.jygjexp.jynx.zxoms.vo.SmsTemplatePageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 短信模板
 *
 * <AUTHOR>
 * @date 2024-10-11 17:52:47
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbSmsTemplate" )
@Tag(description = "nbSmsTemplate" , name = "短信模板管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbSmsTemplateController {

    private final NbSmsTemplateService nbSmsTemplateService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 短信模板
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('basic_nbSmsTemplate_view')" )
    public R getNbSmsTemplatePage(@ParameterObject Page page, @ParameterObject SmsTemplatePageVo vo) {
        return R.ok(nbSmsTemplateService.search(page, vo));
    }


    /**
     * 通过id查询短信模板
     * @param templateId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{templateId}" )
    @PreAuthorize("@pms.hasPermission('basic_nbSmsTemplate_view')" )
    public R getById(@PathVariable("templateId" ) Integer templateId) {
        return R.ok(nbSmsTemplateService.getById(templateId));
    }

    /**
     * 新增短信模板
     * @param basicNbSmsTemplate 短信模板
     * @return R
     */
    @Operation(summary = "新增短信模板" , description = "新增短信模板" )
    @SysLog("新增短信模板" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('basic_nbSmsTemplate_add')" )
    public R save(@RequestBody NbSmsTemplateEntity basicNbSmsTemplate) {
        return R.ok(nbSmsTemplateService.save(basicNbSmsTemplate));
    }

    /**
     * 修改短信模板
     * @param basicNbSmsTemplate 短信模板
     * @return R
     */
    @Operation(summary = "修改短信模板" , description = "修改短信模板" )
    @SysLog("修改短信模板" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('basic_nbSmsTemplate_edit')" )
    public R updateById(@RequestBody NbSmsTemplateEntity basicNbSmsTemplate) {
        return R.ok(nbSmsTemplateService.updateById(basicNbSmsTemplate));
    }

    /**
     * 通过id删除短信模板
     * @param ids templateId列表
     * @return R
     */
    @Operation(summary = "通过id删除短信模板" , description = "通过id删除短信模板" )
    @SysLog("通过id删除短信模板" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('basic_nbSmsTemplate_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbSmsTemplateService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param basicNbSmsTemplate 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('basic_nbSmsTemplate_export')" )
    public List<NbSmsTemplateEntity> export(NbSmsTemplateEntity basicNbSmsTemplate, Integer[] ids) {
        return nbSmsTemplateService.list(Wrappers.lambdaQuery(basicNbSmsTemplate).in(ArrayUtil.isNotEmpty(ids), NbSmsTemplateEntity::getTemplateId, ids));
    }
}