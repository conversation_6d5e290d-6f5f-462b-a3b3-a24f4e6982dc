package com.jygjexp.jynx.zxoms.nbapp.dto;

import com.google.common.collect.Maps;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import lombok.Data;

import java.util.Map;

/**
 * @Author: xiongpengfei
 * @Description: app-订单dto
 * @Date: 2024/11/5 15:50
 */
@Data
public class AppOrderDto extends NbOrderEntity {
    // 投递状态
    public static final int DELIVERY_STATUS_0_UNSTART = 0; // 未开始
    public static final int DELIVERY_STATUS_1_UN_PICKUP = 1; // 待取件
    public static final int DELIVERY_STATUS_2_READY = 2; // 已取件
    public static final int DELIVERY_STATUS_3_START = 3; // 开始
    public static final int DELIVERY_STATUS_4_FINISHED = 4; // 完成
    public static final int DELIVERY_STATUS_5_FAILURED = 5; // 失败

    public static final int DELIVERY_STATUS_10_SELF_PICKUP = 10; // 自提
    public static final Map<Integer, String> deliveryStatusMap = Maps.newHashMap();
    static {
        deliveryStatusMap.put(DELIVERY_STATUS_0_UNSTART, "-");
        deliveryStatusMap.put(DELIVERY_STATUS_1_UN_PICKUP, "un pickup");
        deliveryStatusMap.put(DELIVERY_STATUS_2_READY, "ready");
        deliveryStatusMap.put(DELIVERY_STATUS_3_START, "deliverying");
        deliveryStatusMap.put(DELIVERY_STATUS_4_FINISHED, "finished");
        deliveryStatusMap.put(DELIVERY_STATUS_5_FAILURED, "failured");
        deliveryStatusMap.put(DELIVERY_STATUS_10_SELF_PICKUP, "Customer PickUp");
    }

    // 包裹类型
    public static final int PKG_TYPE_1_NORMAL = 1; // 普通
    public static final Map<Integer, String> pkgTypeMap = Maps.newHashMap();
    static {
        pkgTypeMap.put(PKG_TYPE_1_NORMAL, "Normal");
    }


    // 配送类型
    public static final int DELIVERY_TYPE_1_NORMAL = 1; // 普通
    public static final Map<Integer, String> deliveryTypeMap = Maps.newHashMap();
    static {
        deliveryTypeMap.put(DELIVERY_TYPE_1_NORMAL, "Normal");
    }

    public static final int ORDER_STATUS_190_ORDER_RECEIVED = 190;
    public static final int ORDER_STATUS_191_ORDER_CANCELED = 191; // 暂时没有
    public static final int ORDER_STATUS_192_CUSTOM_HOLD = 192; // 暂时没有
    public static final int ORDER_STATUS_198_CUSTOM_RELEASE_DIRECT = 198;
    public static final int ORDER_STATUS_199_GATEWAY_TRANSIT = 199;
    public static final int ORDER_STATUS_200_PARCEL_SCANNED = 200; // 到达配送中心
    public static final int ORDER_STATUS_201_TO_TRANSIT_CENTER = 201; // 送往转运中心， truck load
    public static final int ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER  = 202; // 到达转运中心 at trans point
    public static final int ORDER_STATUS_203_LOAD_SCANNED = 203; // 司机扫码取件，配送中
    // 开始配送
    public static final int ORDER_STATUS_204_IN_TRANSIT = 204;
    public static final int ORDER_STATUS_205_DELIVERED = 205; // 配送完成

    public static final int ORDER_STATUS_280_FAILURE = 280; // 配送失败，待退回
    public static final int ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT = 286; // 配送失败，司机扫描装车 -> RETURN_OFFICE_FROM_TRANSIT
    public static final int ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE = 290; // 配送失败，退回到分拣中心 -> STORAGE_30_DAYS_FROM_OFFICE
    public static final int ORDER_STATUS_295_SELF_PICKUP = 295; // 自提成功


    public static final int ORDER_STATUS_210_FAILED_DELIVERY = 210;
    public static final int ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1 = 211;
    public static final int ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2 = 212;

    public static final int ORDER_STATUS_215_PARCEL_RETURN_TO_SENDER = 215;
    public static final int ORDER_STATUS_221_RETURNED_PARCEL_SCANNED = 221;
    public static final int ORDER_STATUS_223_SECOND_DELIVERY_CREATED = 223;
    public static final int ORDER_STATUS_225_THIRD_PARTY_LOGISTICS = 225;

    public static final int ORDER_STATUS_226_READY_FOR_PICK_UP = 226;

    public static final int ORDER_STATUS_236_SHIPPING_LABEL_PRINTED = 236; // 20240108 添加, 当选择打印面单后将产生此时间节点。此节点只在NB内部使用不做同步处理
    public static final int ORDER_STATUS_237_THIRD_PARTY_LOGISTICS_RECEIVED = 237; // 20240108添加，PDA做第三方转运出货操作，当订单不在NB派送范围内并已经更新到236状态时，通过出货扫描即可更新订单状态到237。此状态代表货品已经全部交付给第三方收货


//	public static final int ORDER_STATUS_306_WRONG_ADDRESS_FROM_TRANSIT = 306;

    public static final int ORDER_STATUS_206_FAILED_DELIVERY_WRONG_ADDRESS = 206; // 206也需要当做失败处理,和210,211,212一样 ->20230909
    public static final int ORDER_STATUS_207_SCANNED_PARCEL_MISSING = 207;
    public static final int ORDER_STATUS_208_SCANNED_PARCEL_FOUND = 208;

    public static final int ORDER_STATUS_319_WRONG_ROUTE_PARCEL = 319;
    //	public static final int ORDER_STATUS_311_RETURN_OFFICE_FROM_TRANSIT = 311;
//	public static final int ORDER_STATUS_312_STORAGE_30_DAYS_FROM_OFFICE = 312; // 20230909 这两个不用了,改成用对应的286和 290
    public static final int ORDER_STATUS_322_SCANNED_PARCEL_MISSING = 322;
    public static final int ORDER_STATUS_331_FAILED_DELIVERY_RETRY1 = 331;

    public static final int ORDER_STATUS_401_RESTART = 401; // 司机重新开启

    public static final int ORDER_STATUS_340_PACKAGE_INTERCEPTED = 340; // 拦截

    public static final Map<Integer, String> orderStatusMap = Maps.newHashMap();
    static {
        orderStatusMap.put(ORDER_STATUS_190_ORDER_RECEIVED, "ORDER_RECEIVED");
        orderStatusMap.put(ORDER_STATUS_198_CUSTOM_RELEASE_DIRECT, "CUSTOM_RELEASE_DIRECT");
        orderStatusMap.put(ORDER_STATUS_199_GATEWAY_TRANSIT, "GATEWAY_TRANSIT_IN");
        orderStatusMap.put(ORDER_STATUS_200_PARCEL_SCANNED, "PARCEL_SCANNED");

        orderStatusMap.put(ORDER_STATUS_201_TO_TRANSIT_CENTER, "TO_TRANSIT_CENTER");
        orderStatusMap.put(ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER, "ARRIVED_TRANSIT_CENTER");

        orderStatusMap.put(ORDER_STATUS_203_LOAD_SCANNED, "LOAD_SCANNED");
        orderStatusMap.put(ORDER_STATUS_204_IN_TRANSIT, "IN_TRANSIT");
        orderStatusMap.put(ORDER_STATUS_205_DELIVERED, "DELIVERED");

        orderStatusMap.put(ORDER_STATUS_210_FAILED_DELIVERY, "FAILED_DELIVERY");
        orderStatusMap.put(ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1, "FAILED_DELIVERY_RETRY_1");
        orderStatusMap.put(ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2, "FAILED_DELIVERY_RETRY_2");

        orderStatusMap.put(ORDER_STATUS_280_FAILURE, "FAILED_DELIVERY");
        orderStatusMap.put(ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT, "RETURN_OFFICE_FROM_TRANSIT");
        orderStatusMap.put(ORDER_STATUS_290_STORAGE_30_DAYS_FROM_OFFICE, "STORAGE_30_DAYS_FROM_OFFICE");
        orderStatusMap.put(ORDER_STATUS_295_SELF_PICKUP, "SELF_PICKUP");

//		orderStatusMap.put(ORDER_STATUS_306_WRONG_ADDRESS_FROM_TRANSIT, "WRONG_ADDRESS_FROM_TRANSIT");
        orderStatusMap.put(ORDER_STATUS_207_SCANNED_PARCEL_MISSING, "SCANNED_PARCEL_MISSING");
        orderStatusMap.put(ORDER_STATUS_208_SCANNED_PARCEL_FOUND, "SCANNED_PARCEL_FOUND");

        orderStatusMap.put(ORDER_STATUS_319_WRONG_ROUTE_PARCEL, "WRONG_ROUTE_PARCEL");
//		orderStatusMap.put(ORDER_STATUS_311_RETURN_OFFICE_FROM_TRANSIT, "RETURN_OFFICE_FROM_TRANSIT");
//		orderStatusMap.put(ORDER_STATUS_312_STORAGE_30_DAYS_FROM_OFFICE, "STORAGE 30 DAYS FROM OFFICE");
        orderStatusMap.put(ORDER_STATUS_322_SCANNED_PARCEL_MISSING, "SCANNED_PARCEL_MISSING");
        orderStatusMap.put(ORDER_STATUS_331_FAILED_DELIVERY_RETRY1, "FAILED_DELIVERY_RETRY1");

        orderStatusMap.put(ORDER_STATUS_340_PACKAGE_INTERCEPTED, "PACKAGE_INTERCEPTED");

        orderStatusMap.put(ORDER_STATUS_215_PARCEL_RETURN_TO_SENDER, "PARCEL_RETURN_TO_SENDER");
        orderStatusMap.put(ORDER_STATUS_221_RETURNED_PARCEL_SCANNED, "RETURNED_PARCEL_SCANNED");
        orderStatusMap.put(ORDER_STATUS_223_SECOND_DELIVERY_CREATED, "SECOND_DELIVERY_CREATED");
        orderStatusMap.put(ORDER_STATUS_225_THIRD_PARTY_LOGISTICS, "THIRD_PARTY_LOGISTICS");
        orderStatusMap.put(ORDER_STATUS_226_READY_FOR_PICK_UP, "READY_FOR_PICK_UP");

        orderStatusMap.put(ORDER_STATUS_236_SHIPPING_LABEL_PRINTED, "SHIPPING_LABEL_PRINTED");
        orderStatusMap.put(ORDER_STATUS_237_THIRD_PARTY_LOGISTICS_RECEIVED, "THIRD_PARTY_LOGISTICS_RECEIVED");

    }


}
