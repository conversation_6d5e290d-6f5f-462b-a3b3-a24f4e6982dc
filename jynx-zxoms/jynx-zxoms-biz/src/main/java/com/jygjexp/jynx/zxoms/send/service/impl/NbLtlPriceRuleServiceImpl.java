package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbLtlPriceRuleEntity;
import com.jygjexp.jynx.zxoms.entity.NbLtlSurchargeEntity;
import com.jygjexp.jynx.zxoms.entity.NbPriceDistrictEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbLtlPriceRuleMapper;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiRequestParamsVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;
import com.jygjexp.jynx.zxoms.send.service.NbLtlPriceRuleService;
import com.jygjexp.jynx.zxoms.send.service.NbLtlSurchargeService;
import com.jygjexp.jynx.zxoms.send.service.NbPriceDistrictService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * LTL报价规则
 *
 * <AUTHOR>
 * @date 2024-11-08 10:48:44
 */
@Service
@RequiredArgsConstructor
public class NbLtlPriceRuleServiceImpl extends ServiceImpl<NbLtlPriceRuleMapper, NbLtlPriceRuleEntity> implements NbLtlPriceRuleService {
    private final NbLtlPriceRuleMapper nbLtlPriceRuleMapper;
    private final NbPriceDistrictService priceDistrictService;
    private final NbLtlSurchargeService ltlSurchargeService;

    @Override
    public Page search(Page page, NbLtlPriceRuleEntity nbLtlPriceRule) {
        // select pd_id ID,district_code code from nb_price_district;ds=nbd;
        MPJLambdaWrapper<NbLtlPriceRuleEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbLtlPriceRuleEntity.class)
                .select(NbPriceDistrictEntity::getPdId,
                        NbPriceDistrictEntity::getDistrictCode)
                .leftJoin(NbPriceDistrictEntity.class, NbPriceDistrictEntity::getPdId, NbLtlPriceRuleEntity::getFromPdId);
        return nbLtlPriceRuleMapper.selectPage(page, wrapper);
    }

    @Override
    public OldResult ltl(ApiRequestParamsVo paramsVo, String destPostalCode, String senderPostalCode, String items, Integer[] surchargeIds) {
        Integer pallet = 1;
        int totalPallet = 0;
        try {
            JSONArray ja = JSONUtil.parseArray(items);
            for (int i = 0; i < ja.size(); i++) {
                JSONObject item = ja.getJSONObject(i);
                int pallets = item.getInt("pallets");

                totalPallet += pallets;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        pallet = totalPallet;
        if (pallet < 1) {
            return OldResult.fail("560", "pallet cannot be smaller than 1");
        }
        String preDestPostalCode = null;
        try {
            preDestPostalCode = destPostalCode.substring(0, 3).toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
            return OldResult.fail("570", "destination postal code invalid[" + destPostalCode + "]");
        }
        String preSenderPostalCode = null;
        try {
            preSenderPostalCode = senderPostalCode.substring(0, 3).toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
            return OldResult.fail("570", "sender postal code invalid[" + senderPostalCode + "]");
        }

        // "select * from nb_price_district where is_dest_addr = true and cover_postal_code like ?", "%" + preDestPostalCode + "%");
        NbPriceDistrictEntity targetPd = priceDistrictService.getOne(new LambdaQueryWrapper<NbPriceDistrictEntity>()
                .eq(NbPriceDistrictEntity::getIsDestAddr, true).like(NbPriceDistrictEntity::getCoverPostalCode, "%" + preDestPostalCode + "%"));
        if (targetPd == null) {
            return OldResult.fail("580", " Not within the delivery range. [" + destPostalCode + "]");
        }

        // "select * from nb_price_district where is_dest_addr = false and cover_postal_code like ?", "%" + preSenderPostalCode + "%");
        NbPriceDistrictEntity fromPd = priceDistrictService.getOne(new LambdaQueryWrapper<NbPriceDistrictEntity>()
                .eq(NbPriceDistrictEntity::getIsDestAddr, false).like(NbPriceDistrictEntity::getCoverPostalCode, "%" + preSenderPostalCode + "%"));
        if (fromPd == null) {
            return OldResult.fail("580", " Not within the sender range. [" + senderPostalCode + "]");
        }

        // "select * from nb_ltl_price_rule where from_pd_id = ? and target_pd_id = ? limit 1", fromPd.getPdId(), targetPd.getPdId());
        NbLtlPriceRuleEntity rule = nbLtlPriceRuleMapper.selectOne(new LambdaQueryWrapper<NbLtlPriceRuleEntity>()
                .eq(NbLtlPriceRuleEntity::getFromPdId, fromPd.getPdId()).eq(NbLtlPriceRuleEntity::getTargetPdId, targetPd.getPdId()));
        if (rule == null) {
            return OldResult.fail("590", "No price available for the sender to delivery area. Please contact the administrator." + senderPostalCode + "->" + destPostalCode);
        }

        BigDecimal surchargeTotal = BigDecimal.ZERO;
        if (surchargeIds != null && surchargeIds.length > 0) {
            String ids = StringUtils.join(surchargeIds, ",");
            // "select * from nb_ltl_surcharge where surcharge_id in (" + ids + ")");
            List<NbLtlSurchargeEntity> sList = ltlSurchargeService.list(new LambdaQueryWrapper<NbLtlSurchargeEntity>().in(NbLtlSurchargeEntity::getSurchargeId, ids));
//            surchargeTotal = sList.stream().mapToDouble(NbLtlSurchargeEntity::getCharges).sum();
            // 累加附加费用
            surchargeTotal = sList.stream().map(NbLtlSurchargeEntity::getCharges).filter(Objects::nonNull) // 过滤掉 null 值
                    .reduce(BigDecimal.ZERO, BigDecimal::add); // 累加
        }

        double basePrice = 0;
        if (pallet == 1) {
            basePrice = rule.getPallet1();
        } else if (pallet == 2) {
            basePrice = rule.getPallet2();
        } else if (pallet == 3) {
            basePrice = rule.getPallet3();
        } else if (pallet == 4) {
            basePrice = rule.getPallet4();
        } else if (pallet == 5) {
            basePrice = rule.getPallet5();
        } else if (pallet == 6) {
            basePrice = rule.getPallet6();
        } else if (pallet == 7) {
            basePrice = rule.getPallet7();
        } else if (pallet == 8) {
            basePrice = rule.getPallet8();
        } else if (pallet == 9) {
            basePrice = rule.getPallet9();
        } else if (pallet >= 10) {
            basePrice = rule.getPallet10();
        }
        JSONObject ret = new JSONObject();
        ret.set("basePrice", basePrice);
        ret.set("surcharge", surchargeTotal);
        return OldResult.ok("0", ret);
    }

}