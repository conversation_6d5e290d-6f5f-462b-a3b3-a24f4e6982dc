package com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton;

import com.jygjexp.jynx.zxoms.nbapp.validator.BooleanValueValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * @Author: chenchang
 * @Description: Boolean类型非空校验
 * @Date: 2024/11/7 17:30
 */
@Documented
@Constraint(validatedBy = BooleanValueValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface BooleanValue {
    String message() default "Must be a boolean value (true or false)";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
