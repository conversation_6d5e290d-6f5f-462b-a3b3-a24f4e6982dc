package com.jygjexp.jynx.zxoms.nbapp.controller.driver;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbSmsLogEntity;
import com.jygjexp.jynx.zxoms.entity.NbSmsTemplateEntity;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.vo.APPMiscVo;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbSmsLogService;
import com.jygjexp.jynx.zxoms.send.service.NbSmsTemplateService;
import com.jygjexp.jynx.zxoms.send.utils.ALiYunSms;
import freemarker.template.Configuration;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/11 21:31
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver/misc" )
@Tag(description = "appdrivermisc" , name = "APP-Misc" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class MiscController extends BaseController {
    private final NbSmsTemplateService smsTemplateService;
    private final NbOrderService orderService;
    private final NbSmsLogService smsLogService;

    @Autowired
    private Configuration freeMarkerConfig;

    /**
     * 列表地址类型
     */
    @Operation(summary = "列表地址类型" , description = "列表地址类型" )
    @PostMapping("/listAddressType" )
    public void listAddressType() {
        JSONObject type1 = new JSONObject();
        type1.set("id", 1);
        type1.set("name", "公寓");

        JSONObject type2 = new JSONObject();
        type2.set("id", 2);
        type2.set("name", "独立屋");

        JSONObject type3 = new JSONObject();
        type3.set("id", 3);
        type3.set("name", "联排别墅");

        JSONObject type4 = new JSONObject();
        type4.set("id", 4);
        type4.set("name", "公司");

        JSONArray ja = new JSONArray();
        ja.add(type1);
        ja.add(type2);
        ja.add(type3);
        ja.add(type4);
        renderAppData(ja);
    }

    /**
     * 列出短信模板
     */
    @Operation(summary = "列出短信模板" , description = "列出短信模板" )
    @PostMapping("/listSmsTemplate" )
    public void listSmsTemplate() {
        // "select * from nb_sms_template where is_valid = true ");
        List<NbSmsTemplateEntity> smsList = smsTemplateService.list(new LambdaQueryWrapper<NbSmsTemplateEntity>().eq(NbSmsTemplateEntity::getIsValid, true));
        JSONArray ja = smsList.stream().map(s->{
            JSONObject jo = new JSONObject();
            jo.set("id", s.getTemplateId());
            jo.set("name", s.getTitle());
            return jo;
        }).collect(Collectors.toCollection(JSONArray::new));
        renderAppData(ja);
    }

    /**
     * 发送短信
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "发送短信" , description = "发送短信" )
    @PostMapping("/sendSms" )
    public void sendSms(@Valid @RequestBody APPMiscVo vo) {
        Integer orderId = vo.getOrderId();
        Integer templateId = vo.getTemplateId();

        NbOrderEntity order = orderService.getById(orderId);
        if (order == null) {
            renderAppErr("-1", "order not exist");
            return;
        }

        NbDriverEntity loginDriver = getLoginDriver();
        if (order.getDriverId().intValue() != loginDriver.getDriverId()) {
            renderAppErr("-2", "not your order");
            return;
        }

        NbSmsTemplateEntity st = smsTemplateService.getById(templateId);
        if (st == null) {
            renderAppErr("-3", "sms template not exist");
            return;
        }

//        Kv kv = Kv.by("pkgNo", order.getPkgNo()).set("consignee", order.getDestName());
//
//        Engine engine = Engine.use();
//        engine.setDevMode(true);
//        engine.setToClassPathSourceFactory();
//        Template template = engine.getTemplateByString(st.getContent());
//        String smsContent = template.renderToString(kv);
        String smsContent = null;
        NbSmsLogEntity sl = new NbSmsLogEntity();
        try {
            smsContent = smsContent.replace("#(pkgNo)", order.getPkgNo()).replace("#(consignee)", order.getDestName());

            sl.setTemplateId(templateId);
            sl.setMobile(order.getDestTel());
            sl.setContent(smsContent);
            sl.setOrderId(order.getOrderId().toString());
            sl.setDriverId(loginDriver.getDriverId());
            sl.setSendTime(new Date());
            smsLogService.save(sl);

            order.setSmsTime(new Date());
            orderService.updateById(order);

            //		Ret sendRet = new SmsKit().twilioSend(order.getDestTel(), smsContent);
            if (StrUtil.isNotBlank(order.getDestTel())) {
                R sendRet = new ALiYunSms().sentMes(order.getDestTel(), smsContent);
                if (sendRet.isOk()) {
                    sl.setResponseStatus("ok");
                    smsLogService.updateById(sl);
                    renderAppSuc();
                } else {
                    String errmsg = sendRet.getMsg();
                    sl.setResponseStatus(errmsg);
                    smsLogService.updateById(sl);
                    renderAppErr("501", errmsg);
                }
                // renderAppSuc();
            } else {
                renderAppErr("-4", "dest tel is empty");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }



}

