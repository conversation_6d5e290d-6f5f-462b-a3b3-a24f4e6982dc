package com.jygjexp.jynx.zxoms.nbapp.validator;

import com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton.DoubleValue;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/11/7 17:37
 */
public class DoubleValueValidator implements ConstraintValidator<DoubleValue, Double> {

    @Override
    public boolean isValid(Double value, ConstraintValidatorContext context) {
        // 允许空值，如果不为 null 才检查是否是 Double 类型
        return value == null || value instanceof Double;
    }
}
