package com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton;

import com.jygjexp.jynx.zxoms.nbapp.validator.BigDecimalValueValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/12/18 15:23
 */
@Documented
@Constraint(validatedBy = BigDecimalValueValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface BigDecimalValue {
    String message() default "Must be a valid BigDecimal value";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
