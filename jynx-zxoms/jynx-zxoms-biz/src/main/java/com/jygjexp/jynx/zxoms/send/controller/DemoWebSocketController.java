package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.json.JSONObject;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.common.websocket.config.WebSocketMessageSender;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/10/19 1:31
 */
@RequestMapping("/msg")
@RestController
public class DemoWebSocketController {
    @Inner(value = false)
    @GetMapping("send")
    public R sendMsg(){
        JSONObject wsData = new JSONObject();
        wsData.set("aaa","hallo!");
        wsData.set("time",Instant.now().toEpochMilli());
        WebSocketMessageSender.send("admin",wsData.toString());
        return R.ok();
    }

    private void wsData(String id, String routeName, int addressSize) {
        try {
            com.alibaba.fastjson.JSONObject wsData = new com.alibaba.fastjson.JSONObject();
            wsData.put("type", "data");
            wsData.put("time", Instant.now().toEpochMilli());
            wsData.put("routeId", id);
            wsData.put("routeName", routeName);
            wsData.put("addressSize", addressSize);

//            Tio.sendToGroup(WsKit.use(), "_syncRoutes", WsResponse.fromText(wsData.toString(), "UTF-8"));
            WebSocketMessageSender.send("admin",wsData.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
