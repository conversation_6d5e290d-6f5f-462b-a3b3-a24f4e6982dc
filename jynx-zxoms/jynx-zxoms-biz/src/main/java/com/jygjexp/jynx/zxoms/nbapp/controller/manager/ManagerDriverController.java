package com.jygjexp.jynx.zxoms.nbapp.controller.manager;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.dto.DriverDto;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.send.service.NbDriverService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: xiongpengfei
 * @Description: 管理员查询，work_type_id=3司机类型为外包的
 * @Date: 2024/11/5 13:34
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/manager/driver" )
@Tag(description = "appmanagerdriver" , name = "APP-管理员查询司机类型为外包的列表" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ManagerDriverController {
    private final NbDriverService nbDriverService;

    /**
     * 管理员查询司机类型为外包的列表
     */
    @Operation(summary = "管理员查询司机类型为外包的列表" , description = "管理员查询司机类型为外包的列表" )
    @PostMapping("/listService" )
    public R listService() {
        JSONArray ja = null;
        List<NbDriverEntity> drivers = nbDriverService.list(new LambdaQueryWrapper<NbDriverEntity>().eq(NbDriverEntity::getWorkTypeId, DriverDto.WORK_TYPE_3_SERVICE));
        // 使用Stream API，将每个Driver对象转换为JSON
        ja = drivers.stream().map(driver -> new DriverDto().toAppJson(driver)).collect(Collectors.toCollection(JSONArray::new));
        return R.ok(ja);
    }


    public String judgeNextUrl(NbDriverEntity driver) {
        if (driver.getAuditStatus() == DriverDto.AUDIT_STATUS_4_REFUSE) {
            return "refuse";
        }
        if (driver.getAuditStatus() == DriverDto.AUDIT_STATUS_2_SUBMIT) {
            return "auditing";
        }

        String step = driver.getRegStep();
        if (step.charAt(0) == '0') {
            return "set_password";
        } else if (step.charAt(1) == '0') {
            return "set_archive";
        } else if (step.charAt(2) == '0') {
            return "set_profile";
        } else if (step.charAt(3) == '0') {
            return "set_file";
        } else if (step.charAt(4) == '0') {
            return "set_agreement";
        }

        return "index";
    }
}
