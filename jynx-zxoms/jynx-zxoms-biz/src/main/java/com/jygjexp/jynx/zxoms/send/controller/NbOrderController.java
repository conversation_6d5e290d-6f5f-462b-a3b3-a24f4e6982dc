package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderExcel2Vo;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderExcelVo;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderLate190ExcelVo;
import com.jygjexp.jynx.zxoms.send.vo.NbOrderSignPOD2ExcelVo;
import com.jygjexp.jynx.zxoms.vo.*;
import com.jygjexp.jynx.zxoms.vo.OrderSignPOD2PageVo;
import com.jygjexp.jynx.zxoms.vo.OrderVo;
import io.swagger.v3.oas.annotations.Parameter;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 订单
 *
 * <AUTHOR>
 * @date 2024-10-11 20:44:09
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrder" )
@Tag(description = "nbOrder" , name = "派送单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderController {
    private final NbOrderService nbOrderService;
    private final NbDriverService nbDriverService;
    private final NbOrderPathService nbOrderPathService;
    private final NbOrderSignImageService nbOrderSignImageService;
    private final NbOrderUpdateLogService nbOrderUpdateLogService;

    private static AtomicInteger counter = new AtomicInteger(1);

    /**
     * 派送单分页查询
     * @param vo
     * @return
     */
    @Operation(summary = "派送单分页查询" , description = "派送单分页查询" )
    @GetMapping("/search" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_view')" )
    public R search(@ParameterObject Page page, @ParameterObject OrderPageVo vo) {
        return R.ok(nbOrderService.search(page, vo));
    }

    /**
     * 查询订单所属Leader
     * @return
     */
    @Operation(summary = "查询订单所属司机Leader" , description = "查询订单所属司机Leader" )
    @GetMapping("getPDriverId")
    public R getPDriverId(@ParameterObject Page page, @ParameterObject NbDriverEntity entity){
        return R.ok(nbDriverService.pageListPDriverId(page, entity));
    }

    /**
     * 通过id查询订单
     * @param orderId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{orderId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_view')" )
    public R getById(@PathVariable("orderId" ) Integer orderId) {
        return R.ok(nbOrderService.getById(orderId));
    }


    @Operation(summary = "通过id查询订单轨迹" , description = "通过id查询订单轨迹" )
    @GetMapping("/listOrderPathByOrderId/{orderId}" )
    public R listOrderPathByOrderId(@PathVariable("orderId" ) Integer orderId){
        return R.ok(nbOrderPathService.listOrderPathByOrderId(orderId));
    }

    @Operation(summary = "通过id查询签收图片" , description = "通过id查询签收图片" )
    @GetMapping("/listOrderSignImageByOrderId/{orderId}" )
    public R listOrderSignImageByOrderId(@PathVariable("orderId" ) Integer orderId){
        return R.ok(nbOrderSignImageService.listOrderSignImageByOrderId(orderId));
    }

    /**
     * 通过id查询订单修改记录
     * @param orderId
     * @return
     */
    @Operation(summary = "通过id查询订单修改记录" , description = "通过id查询订单修改记录" )
    @GetMapping("/listOrderUpdateLogByOrderId/{orderId}" )
    public R listOrderUpdateLogByOrderId(@PathVariable("orderId" ) Integer orderId){
        return R.ok(nbOrderUpdateLogService.listOrderUpdateLogByOrderId(orderId));
    }

    /**
     * 新增派送单
     * @param zxOmsNbOrder 派送单
     * @return R
     */
    @Operation(summary = "新增派送单" , description = "新增派送单" )
    @SysLog("新增派送单" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_add')" )
    public R save(@RequestBody NbOrderEntity zxOmsNbOrder) {
        int sequence = counter.getAndUpdate(value -> value == 100 ? 1 : value + 1);
        zxOmsNbOrder.setOrderNo("IMP" + DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss") + String.format("%02d", sequence));
        zxOmsNbOrder.setOrderStatus(OrderDto.ORDER_STATUS_190_ORDER_RECEIVED);
        zxOmsNbOrder.setPickNo("0");
        zxOmsNbOrder.setAddTime(new Date());
        zxOmsNbOrder.setCarrierId(1);   // 默认承运商为NB自身
        return R.ok(nbOrderService.save(zxOmsNbOrder));
    }

    /**
     * 修改订单
     * @param zxOmsNbOrder 订单
     * @return R
     */
    @Operation(summary = "修改订单" , description = "修改订单" )
    @SysLog("修改订单" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_edit')" )
    public R updateById(@RequestBody NbOrderEntity zxOmsNbOrder) {
        return R.ok(nbOrderService.updateById(zxOmsNbOrder));
    }

    /**
     * 通过id删除订单
     * @param ids orderId列表
     * @return R
     */
    @Operation(summary = "通过id删除订单" , description = "通过id删除订单" )
    @SysLog("通过id删除订单" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }



    /**
     * 导出派送单Excel
     * @param vo 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @Operation(summary = "导出派送单Excel" , description = "导出派送单Excel" )
    @SysLog("导出派送单Excel" )
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_export')" )
    public List<NbOrderExcelVo> export(OrderPageVo vo, Integer[] ids) {
        return nbOrderService.getExcelNew(vo, ids);
    }

    /**
     * 导出派送单Excel2
     * @param vo 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @Operation(summary = "导出派送单Excel2" , description = "导出派送单Excel2" )
    @SysLog("导出派送单Excel2" )
    @GetMapping("/export2")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_export2')" )
    public List<NbOrderExcel2Vo> export2(OrderPageVo vo, Integer[] ids) {
        return nbOrderService.getExcelNew2(vo, ids);
    }

    /**
     * 导入订单 -- xls上传订单
     * @param file
     * @return
     */
    @Operation(summary = "导入派送单" , description = "导入派送单" )
    @SysLog("导入派送单" )
    @PostMapping("/_import")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_import')" )
    public R importOrders(@RequestParam("file") MultipartFile file,@RequestParam(value = "checkCustomerNoRepeat",
            required = false, defaultValue = "false") boolean checkCustomerNoRepeat) {
        return R.ok(nbOrderService.processFile(file, checkCustomerNoRepeat));
    }

//    @Operation(summary = "测试国际化异常" , description = "测试国际化异常" )
//    @SysLog("测试国际化异常" )
//    @PostMapping("/_importTest")
//    public R importTest(@RequestParam("file") MultipartFile file, @RequestParam(value = "checkCustomerNoRepeat",
//            required = false, defaultValue = "false") boolean checkCustomerNoRepeat) {
//        return LocalizedR.ok(nbOrderService.processFileTest(file, checkCustomerNoRepeat));
//    }

    @Operation(summary = "展示批次内的所有条形码" , description = "展示批次内的所有条形码" )
    @SysLog("展示批次内的所有条形码" )
    @PostMapping("/batchBarList")
    public R batchBarList(@RequestParam @NotNull(message = "批次号不能为空") Integer batchId) {
        return R.ok(nbOrderService.batchBarList(batchId));
    }

    /**
     * 分页查询滞留订单 当前时间的6天之前
     * 元数据编码nb_order_late190
     * @param vo
     * @return
     */
    @Operation(summary = "滞留订单" , description = "滞留订单" )
    @SysLog("滞留订单" )
    @GetMapping("/pageOrderLate190")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_retention')" )
    public R pageOrderLate190(@ParameterObject Page page, @ParameterObject OrderLate190PageVo vo){
        return R.ok(nbOrderService.pageOrderLate190(page, vo));
    }
    @ResponseExcel
    @Operation(summary = "导出滞留订单Excel" , description = "导出滞留订单Excel" )
    @SysLog("导出滞留订单Excel" )
    @GetMapping("/exportOrderLate190")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_export')" )
    public List<NbOrderLate190ExcelVo> exportOrderLate190(OrderLate190PageVo vo, Integer[] ids) {
        return nbOrderService.getOrderLate190Excel(vo, ids);
    }

    /**
     * 待打印订单分页查询
     * @param vo
     * @return
     */
    @Operation(summary = "待打印订单" , description = "待打印订单" )
    @SysLog("待打印订单" )
    @GetMapping("/pageUnprintedOrder")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_unprinted_view')" )
    public R pageUnprintedOrder(@ParameterObject Page page, @ParameterObject UnprintedOrderPageVo vo){
        return R.ok(nbOrderService.pageUnprintedOrder(page,vo));
    }

    /**
     * 打印电子面单
     * @param orderIds
     * @return
     */
    @Operation(summary = "打印电子面单" , description = "打印电子面单" )
    @SysLog("打印电子面单" )
    @PostMapping("/orderLabel")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_print_leu')" )
    public void orderLabel(@Parameter(description = "订单ID的字符串，多个订单ID用逗号分隔，例如 '1,2,3'") @RequestParam String orderIds, HttpServletResponse response){
        nbOrderService.orderLabel(orderIds, response);
    }

    @Operation(summary = "POD2分页查询" , description = "POD2分页查询" )
    @SysLog("POD2分页查询" )
    @GetMapping("/listOrderSignPOD2")
    public R listOrderSignPOD2(@ParameterObject Page page, @ParameterObject OrderSignPOD2PageVo vo){
        return R.ok(nbOrderService.pageOrderSignPOD2(page, vo));
    }
    @ResponseExcel
    @Operation(summary = "导出POD2" , description = "导出POD2" )
    @SysLog("导出POD2" )
    @GetMapping("/exportOrderSignPOD2")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_POD2_export')" )
    public List<NbOrderSignPOD2ExcelVo> exportOrderSignPOD2(OrderSignPOD2PageVo vo, Integer[] ids) {
        return nbOrderService.getPOD2Excel(vo, ids);
    }

    /**
     * 状态变更列表
     * @param orderId
     * @return
     */
    @SysLog("状态变更列表" )
    @Operation(summary = "状态变更列表" , description = "状态变更列表" )
    @PostMapping("/info")
    public R info(@RequestParam @NotNull(message = "订单ID不能为空") Integer orderId){
        return R.ok(nbOrderService.info(orderId));
    }

    /**
     * 修改订单状态
     * @param orderVo
     * @return
     */
    @Operation(summary = "修改订单状态" , description = "修改订单状态" )
    @SysLog("修改订单状态" )
    @PostMapping("/changeStatus")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_edit_status')" )
    public R changeStatus(@RequestBody OrderVo orderVo){
        return R.ok(nbOrderService.changeStatus(orderVo.getOrderId(), orderVo.getTargetOrderStatus(), orderVo.getChangeRemark()));
    }


    /**
     * 通过id批量修改订单状态
     * @param orderVo
     * @return R
     */
    @Operation(summary = "通过id批量修改订单状态" , description = "通过id批量修改订单状态" )
    @SysLog("通过id批量修改订单状态" )
    @PostMapping("/updateStatusByIds")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_statusIds')" )
    public R updateStatusByIds(@RequestBody OrderVo orderVo) {
        return R.ok(nbOrderService.updateStatusByIds(orderVo.getOrderIds(), orderVo.getTargetOrderStatus()));
    }

    /**
     * 可回退状态列表
     */
    @SysLog("可回退状态列表" )
    @Operation(summary = "可回退状态列表" , description = "可回退状态列表" )
    @PostMapping("/statusReturnEnableList")
    public R statusReturnEnableList(@RequestBody OrderVo orderVo) {
        return R.ok(nbOrderService.statusReturnEnableList(orderVo.getOrderId()));
    }

    /**
     * 订单状态回退
     */
    @Operation(summary = "订单状态回退" , description = "订单状态回退" )
    @SysLog("订单状态回退" )
    @PostMapping("/statusReturn")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_status_rollback')" )
    public R statusReturn(@RequestBody OrderVo orderVo) {
        return R.ok(nbOrderService.statusReturn(orderVo.getOrderId(), orderVo.getTargetOrderStatus(), orderVo.getChangeRemark()));
    }

    /**
     * 拷贝订单
     */
    @Operation(summary = "拷贝订单" , description = "拷贝订单" )
    @SysLog("拷贝订单" )
    @PostMapping("/copy")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrder_copy')" )
    public R copy(@RequestParam @NotNull(message = "订单ID不能为空") Integer orderId){
        return R.ok(nbOrderService.copy(orderId));
    }

    /**
     * 获取面单信息
     * @param code
     * @return
     */
    @Operation(summary = "获取面单信息" , description = "获取面单信息" )
    @SysLog("获取面单信息" )
    @PostMapping("/orderInfoByCustomNoPkgNo")
    public R orderInfoByCustomNoPkgNo(@RequestParam String code){
        return R.ok(nbOrderService.orderInfoByCustomNoPkgNo(code));
    }

    /**
     * 每单费用计算
     * @param orderIds
     * @return
     */
    @Operation(summary = "订单费用计算" , description = "订单费用计算" )
    @SysLog("订单费用计算" )
    @PostMapping("/orderCost")
    public R orderCost(@RequestParam String orderIds){
        Boolean isOrder = nbOrderService.computeOrderCost(orderIds);
        return R.ok(isOrder);
    }
}