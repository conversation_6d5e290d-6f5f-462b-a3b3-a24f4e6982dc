package com.jygjexp.jynx.zxoms.nbapp.vo;

import com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton.WeightToDimensionsInternationalMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/12/17 19:16
 */
@Data
@WeightToDimensionsInternationalMapping
public class ApiQuoteInternationalVo{
    @NotBlank(message = "口岸代码不能为空")
    @Schema(description = "口岸代码")
    private String portCode;

    @NotNull(message = "重量不能为空")
    @Positive(message = "重量必须大于0")
    @Schema(description = "重量")
    private BigDecimal weight;

    @NotBlank(message = "重量单位不能为空")
    @Pattern(regexp = "^(KG|LB)$", message = "重量单位必须是KG或者LB")
    @Schema(description = "重量单位")
    private String weightUom;   // KG, LB

    @NotNull(message = "长不能为空")
    @Positive(message = "长必须大于0")
    @Schema(description = "长")
    private BigDecimal length;

    @NotNull(message = "宽不能为空")
    @Positive(message = "宽必须大于0")
    @Schema(description = "宽")
    private BigDecimal width;

    @NotNull(message = "高不能为空")
    @Positive(message = "高必须大于0")
    @Schema(description = "高")
    private BigDecimal height;

    @NotBlank(message = "尺寸单位不能为空")
    @Pattern(regexp = "^(CM|IN)$", message = "尺寸单位必须是CM或者IN")
    @Schema(description = "尺寸单位")
    private String dimensionsUom;   // CM, IN

    private String baseUrl = "http://driver.neighbourexpress.ca";

    public ApiQuoteInternationalVo() {
        this.weight = BigDecimal.ZERO;
        this.length = BigDecimal.ZERO;
        this.width = BigDecimal.ZERO;
        this.height = BigDecimal.ZERO;
    }

    @NotBlank(message = "收件邮编不能为空")
    private String destPostalCode;

}
