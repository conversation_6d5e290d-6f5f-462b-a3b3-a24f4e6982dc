package com.jygjexp.jynx.zxoms.nbapp.controller.driver;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.send.constants.NBDConstants;
import com.jygjexp.jynx.zxoms.dto.DriverDto;
import com.jygjexp.jynx.zxoms.dto.DriverScanedDto;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton.AtomApi;
import com.jygjexp.jynx.zxoms.nbapp.vo.APPDriverOrderVo;
import com.jygjexp.jynx.zxoms.send.service.*;

import com.jygjexp.jynx.zxoms.send.utils.AliYunOSS;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: chenchang
 * @Description: 司机端用到的订单接口
 * @Date: 2024/11/11 21:57
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver/order" )
@Tag(description = "appdriverorder" , name = "APP-司机端用到的订单接口" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DriverOrderController extends BaseController {
    private final NbOrderService orderService;
    private final NbDriverIncomeService driverIncomeService;
    private final NbOrderSignImageService orderSignImageService;
    private final NbDriverScanedService driverScanedService;
    private final NbTransferBatchOrderService transferBatchOrderService;
    private final NbTransferCenterService transferCenterService;
    private final NbCustomerAddressService customerAddressService;
    private final NbReportedLogService reportedLogService;
    private final NbSortingCenterService sortingCenterService;
    private final NbDriverService driverService;
    private final NbTransferBatchService transferBatchService;
    private final NbOrderPathService orderPathService;
    private final CommonDataUtil commonDataUtil;

    /**
     * 获取配送中的订单
     */
    @Operation(summary = "获取配送中的订单" , description = "获取配送中的订单" )
    @PostMapping("/listByStatus" )
    public R listByStatus(@RequestBody APPDriverOrderVo vo) {
        Integer pageNo = vo.getPageNo();
        Integer pageSize = vo.getPageSize();
        String status = vo.getStatus();
        String sortType = vo.getSortType();
        int orderStatus = 0;

        NbDriverEntity driver = getLoginDriver();

        LambdaQueryWrapper<NbOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(NbOrderEntity::getDriverId, driver.getDriverId());
        //配送中 inDelivery   配送失败 deliveryFail  未扫描 unScan  已扫描 scaned  已配送 delivered
        if ("inDelivery".equals(status)) {
            qw.in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_203_LOAD_SCANNED, OrderDto.ORDER_STATUS_204_IN_TRANSIT)
                    .eq(NbOrderEntity::getDeliveryStatus, OrderDto.DELIVERY_STATUS_3_START);
        } else if ("deliveryFail".equals(status)) {
            qw.in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_203_LOAD_SCANNED, OrderDto.ORDER_STATUS_204_IN_TRANSIT, OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT)
                    .eq(NbOrderEntity::getDeliveryStatus, OrderDto.DELIVERY_STATUS_5_FAILURED);
        } else if ("unScan".equals(status)) {
            qw.in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED, OrderDto.ORDER_STATUS_201_TO_TRANSIT_CENTER, OrderDto.ORDER_STATUS_202_ARRIVED_TRANSIT_CENTER);
        } else if ("scaned".equals(status)) {
            qw.in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_203_LOAD_SCANNED, OrderDto.ORDER_STATUS_204_IN_TRANSIT);
        } else if ("delivered".equals(status)) {
            orderStatus= OrderDto.ORDER_STATUS_205_DELIVERED;
            qw.eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_205_DELIVERED)
                    .eq(NbOrderEntity::getDeliveryStatus, OrderDto.DELIVERY_STATUS_4_FINISHED);
        }

        // 判断排序条件
        if ("time".equals(sortType)) {
            qw.orderByDesc(NbOrderEntity::getDriverPickupTime);
        } else if ("line".equals(sortType)) {
            qw.orderByAsc(NbOrderEntity::getPickNo);
        } else if ("express".equals(sortType)) {
            qw.orderByDesc(NbOrderEntity::getDeliveryType);
        } else if ("redelivery".equals(sortType)) {
            qw.orderByDesc(NbOrderEntity::getDeliveryTry);
        }

        if (orderStatus > 0){
            qw.eq(NbOrderEntity::getOrderStatus, orderStatus);
        }

        Page<NbOrderEntity> page = orderService.page(new Page<>(pageNo, pageSize), qw);
        JSONArray ja = page.getRecords().stream().map(order -> new OrderDto().toDriverListJson(order)).collect(Collectors.toCollection(JSONArray::new));

        return R.ok(ja);
    }

    /**
     * 完成配送，并处理图片
     */
    @Transactional(rollbackFor = Exception.class)
    @AtomApi(params = "orderId")
    @Operation(summary = "完成配送" , description = "完成配送" )
    @PostMapping("/finished" )
    public R finished(@RequestParam("file1") MultipartFile file1, @RequestParam("file2") MultipartFile file2,@RequestParam("orderId") Integer orderId) {
        NbDriverEntity loginDriver = getLoginDriver();
        NbOrderEntity order = null;
        List<MultipartFile> file = new ArrayList<>();
        file.add(file1);
        file.add(file2);
        try {
            order = deliveryImageProcess(OrderDto.ORDER_STATUS_205_DELIVERED, OrderDto.DELIVERY_STATUS_4_FINISHED, orderId, file);
        } catch (IOException e) {
            log.error("订单图片处理失败：",e.getMessage());
            throw new RuntimeException(e);
        }
        if (order == null) {
            return null;
        }
        double lat = getDouble("__nb_lat", 0d);
        double lng = getDouble("__nb_lng", 0d);

        // 更新订单
        order.setOrderStatus(OrderDto.ORDER_STATUS_205_DELIVERED);
        order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_4_FINISHED);
        order.setFinishedTime(new Date());
        order.setDeliveryedTime(Date.from(Instant.now().atZone(ZoneId.systemDefault()).toInstant()));
        orderService.updateById(order);

        String timezone = ZoneId.systemDefault().getId();
        if (order.getDestProvince() != null) {
            JSONObject province = commonDataUtil.getProvinceByNameOrIso2(39, order.getDestProvince());
            if (province != null && province.containsKey("zoneId")) {
                timezone = province.getStr("zoneId");
            }
        }

        String address = order.getDestCity() + " " + order.getDestProvince();
        NbOrderPathEntity path = new OrderPathDto().createPath(order.getOrderId(), order.getOrderStatus(), loginDriver != null ? loginDriver.getDriverId() : 0, lat, lng, address, timezone);
        path.setScId(order.getScId());
        path.setTcId(order.getTcId());
        // 快件送达时，发送短信通知
        path.setSmsStatus(OrderPathDto.SMS_STATUS_10_UNSEND);
        orderPathService.save(path);

        // 计算收入
        NbDriverIncomeEntity di = new NbDriverIncomeEntity();
        di.setDriverId(loginDriver.getDriverId());
        di.setAmount(BigDecimal.ZERO);
        di.setAddTime(new Date());
        di.setOrderId(order.getOrderId());
        driverIncomeService.save(di);

        return R.ok();
    }

    /**
     * 配送后关于图片的通用处理
     * @return
     */
    private NbOrderEntity deliveryImageProcess(int targetOrderStatus, int deliveryStatus, Integer orderId, List<MultipartFile> fileList) throws IOException {
        List<MultipartFile> files = fileList;
        log.info(String.valueOf(files.size()));
        if (files == null || files.size() != 2) {
            renderAppErr("-1", "Two photos must be uploaded");
        }

        if (orderId == 0) {
            renderAppErr("-1", "The parameters are incomplete. [orderId]");
        }

        NbDriverEntity loginDriver = getLoginDriver();
        // "select * from nb_order where order_id = ? limit 1", orderId);
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getOrderId,orderId));
        if (order == null) {
            renderAppErr("-400", "Order does not exist");
        }
        if (order.getDriverId().intValue() != loginDriver.getDriverId()) {
            renderAppErr("-401", "It's not an order delivered by you.");
        }
        if (order.getOrderStatus() != OrderDto.ORDER_STATUS_203_LOAD_SCANNED || order.getOrderStatus() != OrderDto.ORDER_STATUS_204_IN_TRANSIT) {
            renderAppErr("-500", "Order status cannot be shipped[" + order.getOrderStatus() + "]");
        }
        if (order.getDeliveryStatus() != OrderDto.DELIVERY_STATUS_3_START) {
            renderAppErr("-501", "The order has not yet begun delivery.");
        }
        // 保存图片
        String signImagePath = NBDConstants.SIGN_IMAGE_PATH;
        String pkgOriFullPath = signImagePath + "/" + DateFormatUtils.format(System.currentTimeMillis(), "yyyy/MM/dd") + "/" + loginDriver.getDriverId() + "/" + order.getOrderId() + "/pkg_" + System.currentTimeMillis() + ".jpg";
        String putOriFullPath = signImagePath + "/" + DateFormatUtils.format(System.currentTimeMillis(), "yyyy/MM/dd") + "/" + loginDriver.getDriverId() + "/" + order.getOrderId() + "/put_" + System.currentTimeMillis() + ".jpg";

        log.info(pkgOriFullPath);
        log.info(putOriFullPath);

        NbOrderSignImageEntity image = new NbOrderSignImageEntity();
        image.setOrderId(order.getOrderId());
        image.setDriverId(loginDriver.getDriverId());
        image.setAddTime(new Date());

        if (targetOrderStatus == OrderDto.ORDER_STATUS_210_FAILED_DELIVERY) {
            if (order.getDeliveryTry() == 1) {
                targetOrderStatus = OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1;
            } else if (order.getDeliveryTry() == 2) {
                targetOrderStatus = OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2;
            }
        }

        image.setOrderStatus(targetOrderStatus);

        int index = 1;

        for (MultipartFile uf : files) {
            try {
                // 获取上传文件的文件名
                String originalFileName = uf.getOriginalFilename();
                String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(originalFileName);
                String dir = "SignImager";
                // 调用 sendToOssTwo 方法上传文件到 OSS 并获取 URL
                String fileUrl = AliYunOSS.sendToOssTwo(uf, dir, fileName);  // 调用 OSS 上传

                // 根据 index 判断是 pkgImage 还是 putImage，并设置相应的值
                if (index == 1) {
                    image.setPkgImage(fileUrl.replace(signImagePath, ""));
                    image.setOriPkgImage(fileUrl.replace(signImagePath, ""));
                } else if(index == 2){
                    image.setPutImage(fileUrl.replace(signImagePath, ""));
                    image.setOriPutImage(fileUrl.replace(signImagePath, ""));
                    image.setDeliveryStatus(deliveryStatus);
                }
            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException("文件处理失败", e);
            }
            index++;
        }

        //计算经纬度
        double lat = getDouble("__nb_lat", 0d);
        double lng = getDouble("__nb_lng", 0d);
        image.setLat(lat);
        image.setLng(lng);

        orderSignImageService.save(image);
        return order;
    }

    /**
     * 送达失败
     */
    @Transactional(rollbackFor = Exception.class)
    @AtomApi(params = "orderId")
    @Operation(summary = "送达失败" , description = "送达失败" )
    @PostMapping("/deliveryFailure" )
    public R deliveryFailure(@RequestParam("orderId") Integer orderId) {
//        NbOrderEntity order;
//        try {
//            order = deliveryImageProcess(OrderDto.ORDER_STATUS_210_FAILED_DELIVERY, OrderDto.DELIVERY_STATUS_4_FINISHED,orderId, file);
//        } catch (IOException e) {
//            log.error("订单图片处理失败：",e.getMessage());
//            throw new RuntimeException(e);
//        }
//        if (order == null) {
//            return R.failed("-1","Order processing failure");
//        }

        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getOrderId,orderId),false);
        if (null == order){
            return R.failed("-1", "Order processing failure.");
        }

        // 将订单状态改为210
        order.setOrderStatus(OrderDto.ORDER_STATUS_210_FAILED_DELIVERY);
        order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_5_FAILURED);
        order.setDeliveryedTime(Date.from(Instant.now().atZone(ZoneId.systemDefault()).toInstant()));
        orderService.updateById(order);
        return R.ok();
    }

    /**
     * 重新开启配送
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "重新开启配送" , description = "重新开启配送" )
    @PostMapping("/restartDelivery" )
    public R restartDelivery(@RequestBody APPDriverOrderVo vo) {
        Integer orderId = vo.getOrderId();
        Double lat = getDouble("__nb_lat", 0d);
        Double lng = getDouble("__nb_lng", 0d);

        NbOrderEntity order = orderService.getById(orderId);
        NbDriverEntity loginDriver = getLoginDriver();

        if (order.getDriverId().intValue() != loginDriver.getDriverId()) {
            return R.failed("-1", "This is not your package.");
        }

        if (order.getOrderStatus() != OrderDto.ORDER_STATUS_205_DELIVERED) {
            return R.failed("-1", "The order is not in a completed state.");
        }
        order.setOrderStatus(OrderDto.ORDER_STATUS_203_LOAD_SCANNED);
        order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_3_START);
        orderService.updateById(order);

        String timezone = ZoneId.systemDefault().getId();
        if (order.getDestProvince() != null) {
            JSONObject province = commonDataUtil.getProvinceByNameOrIso2(39, order.getDestProvince());
            if (province != null && province.containsKey("zoneId")) {
                timezone = province.getStr("zoneId");
            }
        }

        String address = order.getDestCity() + " " + order.getDestProvince();
        NbOrderPathEntity path = new OrderPathDto().createPath(order.getOrderId(), order.getOrderStatus(), loginDriver != null ? loginDriver.getDriverId() : 0, lat, lng, address, timezone);
        path.setScId(order.getScId());
        path.setTcId(order.getTcId());
        orderPathService.save(path);
        return R.ok();
    }

    /**
     * 再次配送
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "再次配送" , description = "再次配送" )
    @PostMapping("/redelivery" )
    public R redelivery(@RequestBody APPDriverOrderVo vo) {
        Integer orderId = vo.getOrderId();
//        Double lat = getDouble("lat", 0d);
//        Double lng = getDouble("lng", 0d);

        NbOrderEntity order = orderService.getById(orderId);
        NbDriverEntity loginDriver = getLoginDriver();

        if (order.getDriverId().intValue() != loginDriver.getDriverId()) {
            return R.failed("-1", "This is not your package.");
        }

        if (order.getOrderStatus() == OrderDto.ORDER_STATUS_280_FAILURE) {
            return R.failed("The order has been returned to the transshipment center."+order.getOrderStatus());
        }

        if (order.getOrderStatus() == OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT) {
            return R.failed("The order has been scanned back to warehouse."+order.getOrderStatus());
        }

        log.info("again delivery orderId:" + order.getOrderStatus());
        if (order.getOrderStatus() != OrderDto.ORDER_STATUS_210_FAILED_DELIVERY && order.getOrderStatus() != OrderDto.ORDER_STATUS_211_FAILED_DELIVERY_RETRY_1
                && order.getOrderStatus() != OrderDto.ORDER_STATUS_212_FAILED_DELIVERY_RETRY_2) {
            return R.failed("-1", "The order is not in a delivery state."+order.getOrderStatus());
        }

        if (order.getDeliveryStatus() != OrderDto.DELIVERY_STATUS_5_FAILURED) {
            return R.failed("-1", "The order is not in a delivery state."+order.getDeliveryStatus());
        }
        order.setOrderStatus(OrderDto.ORDER_STATUS_203_LOAD_SCANNED);
        order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_3_START);
        order.setDeliveryTry(order.getDeliveryTry() + 1);
        orderService.updateById(order);

        return R.ok();
    }

    /**
     * 发送开始配送短信
     */
    public void sendStartDeliverySms() {

    }

    /**
     * 扫描取件
     */
    @AtomApi(params = "orderNo")
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "扫描取件" , description = "扫描取件" )
    @PostMapping("/scanToPickup" )
    public R scanToPickup(@RequestParam("pkgNo") String pkgNo) {
//		Double lat = getDouble("lat", 0d);
//		Double lng = getDouble("lng", 0d);

        Double[] latlng = getLatLng();
        Double lat = 0d, lng = 0d;
        if (latlng != null) {
            lat = latlng[0];
            lng = latlng[1];
        }

        // "select * from nb_order where pkg_no = ? and order_status = ? limit 1", pkgNo, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED); // 2024-05-25 202改成200
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo, pkgNo).eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED));
        if (order == null) {
            return R.failed("-1", "The order does not exist.");
        }
        NbDriverEntity loginDriver = getLoginDriver();
        if (order.getDriverId().intValue() != loginDriver.getDriverId()) {
            return R.failed("-2", "This is not your package.");
        }

        // "select * from nb_driver_scaned where order_id = ? and audit_status in (?, ?, ?)", order.getOrderId(), DriverScaned.AUDIT_STATUS_1_SCANED, DriverScaned.AUDIT_STATUS_2_SUBMIT, DriverScaned.AUDIT_STATUS_3_PASS);
        NbDriverScanedEntity ds = driverScanedService.getOne(new LambdaQueryWrapper<NbDriverScanedEntity>()
                .eq(NbDriverScanedEntity::getOrderId,order.getOrderId()).in(NbDriverScanedEntity::getAuditStatus, DriverScanedDto.AUDIT_STATUS_1_SCANED, DriverScanedDto.AUDIT_STATUS_2_SUBMIT,DriverScanedDto.AUDIT_STATUS_3_PASS));
        if (ds != null) {
            if (ds.getDriverId().intValue() == loginDriver.getDriverId()) {
                return R.failed("-3", "Duplicate scan.");
            } else {
                return R.failed("-4", "Already scanned by someone else.");
            }
        }

        // "select * from nb_transfer_batch_order where order_id = ? order by batch_id desc limit 1");
        NbTransferBatchOrderEntity tbo = transferBatchOrderService.getOne(new LambdaQueryWrapper<NbTransferBatchOrderEntity>().eq(NbTransferBatchOrderEntity::getOrderId,order.getOrderId()).orderByDesc(NbTransferBatchOrderEntity::getBatchId),false);
        ds = new NbDriverScanedEntity();
        ds.setOrderId(order.getOrderId());
        ds.setDriverId(loginDriver.getDriverId());
        ds.setScanedTime(new Date());
        ds.setAuditStatus(DriverScanedDto.AUDIT_STATUS_1_SCANED);
        ds.setLat(lat);
        ds.setLng(lng);
        ds.setBatchId(0);
        ds.setTransferBatchId(tbo.getBatchId()); // 扫描的哪个路区的批次
        driverScanedService.save(ds);

        JSONObject jo = new JSONObject();
        jo.set("pkgNo", pkgNo);
        jo.set("pickNo", order.getPickNo());
        //扫描完成之后，将订单状态改至204
        order.setOrderStatus(204);
        orderService.save(order);
        return R.ok(jo);
    }

    /**
     * 获取扫描未提交的列表
     */
    @Operation(summary = "获取扫描未提交的列表" , description = "获取扫描未提交的列表" )
    @PostMapping("/pickupScanedUnSubmit" )
    public void pickupScanedUnSubmit() {
        NbDriverEntity loginDriver = getLoginDriver();

        JSONArray ja = new JSONArray();
        // "select o.order_id, o.pkg_no, o.pick_no from nb_driver_scaned ds, nb_order o where ds.order_id = o.order_id and ds.driver_id = ? and ds.audit_status = ?", loginDriver.getDriverId(), DriverScaned.AUDIT_STATUS_1_SCANED);
        MPJLambdaWrapper<NbOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(NbOrderEntity::getOrderId,NbOrderEntity::getPkgNo,NbOrderEntity::getPickNo)
                .leftJoin(NbDriverScanedEntity.class, NbDriverScanedEntity::getOrderId, NbOrderEntity::getOrderId)
                .eq(NbDriverScanedEntity::getDriverId, loginDriver.getDriverId()).eq(NbDriverScanedEntity::getAuditStatus, DriverScanedDto.AUDIT_STATUS_1_SCANED);
        List<NbOrderEntity> scanedList = orderService.selectJoinList(NbOrderEntity.class, wrapper);
        for (NbOrderEntity order : scanedList) {
            JSONObject jo = new JSONObject();
            jo.set("orderId", order.getOrderId());
            jo.set("pkgNo", order.getPkgNo());
            jo.set("pickNo", order.getPickNo());
            ja.add(jo);
        }
        renderAppData(ja);
    }

    /**
     * 已经扫描的记录
     */
    public void scanedList() {

    }

    /**
     * 生成扫描报告供审核
     */
    @AtomApi()
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "生成扫描报告供审核" , description = "生成扫描报告供审核" )
    @PostMapping("/createBatch" )
    public R createBatch() {
        NbDriverEntity loginDriver = getLoginDriver();
        Double[] latlng = getLatLng();
        driverScanedService.getList(loginDriver, latlng);
        return R.ok();
    }

    /**
     * 修改地址类型
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "修改地址类型" , description = "修改地址类型" )
    @PostMapping("/changeAddressType" )
    public R changeAddressType(@RequestBody APPDriverOrderVo vo) {
        Integer addrssTypeId = vo.getAddrssTypeId();
        if (addrssTypeId == null) {
            return R.failed("-1", "params invalid");
        }
        Integer orderId = vo.getOrderId();
        if (orderId == null) {
            return R.failed("-1", "params invalid");
        }

        NbOrderEntity order = orderService.getById(orderId);
        if (order == null) {
            return R.failed("-1", "order not exist");
        }
        NbDriverEntity loginDriver = getLoginDriver();
        if (order.getDriverId().intValue() != loginDriver.getDriverId()) {
            return R.failed("-2", "not your order");
        }
        order.setDestAddressType(addrssTypeId);
        orderService.updateById(order);

        // "select * from nb_customer_address where country = ? and province = ? and city = ? and postal_code = ? and address1 = ? limit 1",
        // order.getDestCountry(), order.getDestProvince(), order.getDestCity(), order.getDestPostalCode(), order.getDestAddress1());
        NbCustomerAddressEntity ca = customerAddressService.getOne(new LambdaQueryWrapper<NbCustomerAddressEntity>()
                .eq(NbCustomerAddressEntity::getCountry, order.getDestCountry())
                .eq(NbCustomerAddressEntity::getProvince, order.getDestProvince())
                .eq(NbCustomerAddressEntity::getCity, order.getDestCity())
                .eq(NbCustomerAddressEntity::getPostalCode, order.getDestPostalCode())
                .eq(NbCustomerAddressEntity::getAddress1, order.getDestAddress1())
        );
        if (ca == null) {
            ca = new NbCustomerAddressEntity();
            ca.setName(order.getDestName());
            ca.setTel(order.getDestTel());
            ca.setCountry(order.getDestCountry());
            ca.setProvince(order.getDestProvince());
            ca.setCity(order.getDestCity());
            ca.setPostalCode(order.getDestPostalCode());
            ca.setAddress1(order.getDestAddress1());
            ca.setCreateTime(new Date());
            ca.setAddressType(addrssTypeId);
            customerAddressService.save(ca);
        }
        NbReportedLogEntity rl = new NbReportedLogEntity();
        rl.setDriverId(loginDriver.getDriverId());
        rl.setType(1);
        rl.setAddressType(addrssTypeId);
        rl.setReportedTime(new Date());
        reportedLogService.save(rl);

        return R.ok();
    }

    /**
     * 获取订单信息
     */
    @Operation(summary = "获取订单信息" , description = "获取订单信息" )
    @PostMapping("/info" )
    public R info(@RequestBody APPDriverOrderVo vo) {
        Integer orderId = vo.getOrderId();
        NbOrderEntity order = orderService.getById(orderId);
        if (order == null) {
            return R.failed("-1", "order not exist");
        }

        NbDriverEntity loginDriver = getLoginDriver();
        if (order.getDriverId().intValue() != loginDriver.getDriverId()) {
            return R.failed("-2", "not your order");
        }
        JSONObject jo = new OrderDto().toDriverListJson(order);
        return R.ok(jo);
    }

    /**
     * 根据包裹号获取订单信息
     */
    @Operation(summary = "根据包裹号获取订单信息" , description = "根据包裹号获取订单信息" )
    @PostMapping("/infoByPkgNo" )
    public R infoByPkgNo(@RequestBody APPDriverOrderVo vo) {
        String pkgNo = vo.getPkgNo();
        if (pkgNo == null) {
            return R.failed("-1", "package no is empty");
        }

        //获取登录司机信息
        NbDriverEntity loginDriver = getLoginDriver();
        // "select * from nb_order where pkg_no = ? limit 1", pkgNo);
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo,pkgNo).eq(NbOrderEntity::getDriverId,loginDriver.getDriverId()));
        if (order == null) {
            return R.failed("-1", "This package information does not exist under the current driver ID." + pkgNo);
        }

        JSONObject jo = new OrderDto().toDriverListJson(order);
        Integer scId = order.getScId();
        if (scId != null && scId > 0) {
            NbSortingCenterEntity sc = sortingCenterService.getById(scId);
            JSONObject scJo = new JSONObject();
            scJo.set("scId", sc.getScId());
            scJo.set("centerName", sc.getCenterName());
            scJo.set("address", sc.getAddress());
            scJo.set("postalCode", sc.getPostalCode());
            scJo.set("country", commonDataUtil.getCountryById(sc.getCountryId()));
            scJo.set("province", commonDataUtil.getProvinceById(sc.getProvinceId()));
            scJo.set("city", commonDataUtil.getCityById(sc.getCityId()));
            scJo.set("scCode", sc.getScCode());
            jo.set("sc", scJo);
        }
        Integer tcId = order.getTcId();
        if (tcId != null && tcId > 0) {
            NbTransferCenterEntity tc = transferCenterService.getById(tcId);
            JSONObject tcJo = new JSONObject();
            tcJo.set("tcId", tc.getTcId());
            tcJo.set("code", tc.getTransferCenterCode());
            tcJo.set("provinceId", tc.getProvinceId());
            tcJo.set("province", commonDataUtil.getProvinceById(tc.getProvinceId()));
            tcJo.set("cityId", tc.getCityId());
            tcJo.set("city", commonDataUtil.getCityById(tc.getCityId()));
            tcJo.set("address", tc.getAddress());
            tcJo.set("postalCode", tc.getPostalCode());
            tcJo.set("centerName", tc.getCenterName());
            jo.set("tc", tcJo);
        }

        Integer driverId = order.getDriverId();
        if (driverId != null && driverId > 0) {
            NbDriverEntity driver = driverService.getById(driverId);
            jo.set("driver", new DriverDto().toAppJson(driver));
        }
        // "select * from nb_transfer_batch tb, nb_transfer_batch_order tbo where tb.batch_id = tbo.batch_id and tbo.order_id = ? limit 1", order.getOrderId());
        MPJLambdaWrapper<NbTransferBatchEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbTransferBatchEntity.class) // 选择 TransferBatch 表的所有字段
                .innerJoin(NbTransferBatchOrderEntity.class, NbTransferBatchOrderEntity::getBatchId, NbTransferBatchEntity::getBatchId)
                .eq(NbTransferBatchOrderEntity::getOrderId, order.getOrderId())
                .last("limit 1"); // 限制查询结果为一条记录
        NbTransferBatchEntity tb = transferBatchService.selectJoinOne(NbTransferBatchEntity.class, wrapper);
        if (tb != null) {
            JSONObject tbJo = new JSONObject();
            tbJo.set("batchId", tb.getBatchId());
            tbJo.set("batchNo", tb.getBatchNo());
            tbJo.set("batchCode", tb.getBatchCode());
            tbJo.set("driverId", tb.getDriverId());
            tbJo.set("orderTotal", tb.getOrderTotal());
            tbJo.set("estimatedHour", tb.getEstimatedHour());
            jo.set("transferBatch", tbJo);
        }
        return  R.ok(jo);
    }

    /**
     * 退回转运中心
     */
    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "退回转运中心" , description = "退回转运中心" )
    @PostMapping("/returnTransitCenter" )
    public R returnTransitCenter(@RequestBody APPDriverOrderVo vo) {
        Integer orderId = vo.getOrderId();
        NbDriverEntity loginDriver = getLoginDriver();
        NbOrderEntity order = orderService.getById(orderId);
        if (order == null) {
            return R.failed("-1", "order not exist");
        }
        if (order.getDriverId().intValue() != loginDriver.getDriverId()) {
            return R.failed("-2", "not your package");
        }
        if (order.getOrderStatus() != OrderDto.ORDER_STATUS_210_FAILED_DELIVERY) {
            return R.failed("-3", "The order status does not allow for this operation.");
        }
//        if (order.getDeliveryTry() < 3) {
//            return R.failed("-4", "The order must be delivered at least 3 times.");
//        }
        order.setOrderStatus(OrderDto.ORDER_STATUS_280_FAILURE);
        order.setDeliveryStatus(OrderDto.DELIVERY_STATUS_5_FAILURED);
        orderService.updateById(order);

        Double lat = null, lng = null;
        Double[] latlng = getLatLng();
        if (latlng != null) {
            lat = latlng[0]; lng = latlng[1];
        }
        String address = null;
        String timezone = ZoneId.systemDefault().getId();
        NbOrderPathEntity path = new OrderPathDto().createPath(order.getOrderId(), order.getOrderStatus(),loginDriver != null ? loginDriver.getDriverId() : 0, lat, lng, address, timezone);
        path.setScId(order.getScId());
        path.setTcId(order.getTcId());
        orderPathService.save(path);

        return R.ok();
    }
    /**
     * 待扫描数据
     */
    public void toBeScaned() {

    }

}
