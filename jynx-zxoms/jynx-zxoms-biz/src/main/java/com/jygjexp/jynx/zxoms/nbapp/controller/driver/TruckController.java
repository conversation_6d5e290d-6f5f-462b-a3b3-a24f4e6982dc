package com.jygjexp.jynx.zxoms.nbapp.controller.driver;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.send.service.NbOrderPathService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbTransferBatchService;
import com.jygjexp.jynx.zxoms.send.service.NbTransferCenterService;
import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: chenchang
 * @Description: 卡车司机的一些操作
 * @Date: 2024/11/12 0:14
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/driver/truck")
@Tag(description = "appdrivertruck", name = "APP-卡车司机的一些操作")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TruckController extends BaseController {
    private final NbTransferBatchService transferBatchService;
    private final NbTransferCenterService transferCenterService;
    private final NbOrderService orderService;
    private final NbOrderPathService orderPathService;
    private final CommonDataUtil commonDataUtil;

    /**
     * 列表地址类型
     */
    @Operation(summary = "列表地址类型", description = "列表地址类型")
    @PostMapping("/serviceStat")
    public void serviceStat() {
        NbDriverEntity loginDriver = getLoginDriver();
        // "select count(1) cont from nb_transfer_batch where tranfer_driver_id = ? and unloading_time is null", loginDriver.getDriverId()).getInt("cont");
        Long count = transferBatchService.count(new LambdaQueryWrapper<NbTransferBatchEntity>()
                .eq(NbTransferBatchEntity::getTranferDriverId, loginDriver.getDriverId()).eq(NbTransferBatchEntity::getUnloadingTime, null));
        int total = Math.toIntExact(count);

        JSONObject stat = new JSONObject();
        stat.set("unloadingTotal", total); // 待卸货的路区总数

        renderAppData(stat);
    }

    @Operation(summary = "获取转运中心", description = "获取转运中心")
    @PostMapping("/getTransferCenter")
    public void getTransferCenter(@RequestParam("code") String code) {
        if (StrUtil.isBlank(code)) {
            renderAppErr("-1", "扫描内容为空");
            return;
        }

        String[] groups = code.split("/");
        if (groups.length == 3) {
            code = groups[2];
        }
        // "select * from nb_transfer_center where transfer_center_code = ? limit 1", code);
        NbTransferCenterEntity tc = transferCenterService.getOne(new LambdaQueryWrapper<NbTransferCenterEntity>().eq(NbTransferCenterEntity::getTransferCenterCode, code));
        if (tc == null) {
            renderAppErr("-1", "没有找到对应的转运中心，请检查二维码是否正确[" + code + "]");
            return;
        }

        // "select count(1) cont from nb_order where order_status in (?) and tc_id = ?", OrderDto.ORDER_STATUS_280_FAILURE, tc.getTcId()).getInt("cont");
        Long count = orderService.count(new LambdaQueryWrapper<NbOrderEntity>().in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_280_FAILURE)
                .eq(NbOrderEntity::getTcId, tc.getTcId()));
        int returnTotal = Math.toIntExact(count);
        JSONObject jo = new JSONObject();
        JSONObject tcJo = new JSONObject();
        tcJo.set("tcId", tc.getTcId());
        tcJo.set("code", tc.getTransferCenterCode());
        tcJo.set("provinceId", tc.getProvinceId());
        tcJo.set("province", commonDataUtil.getProvinceById(tc.getProvinceId()));
        tcJo.set("cityId", tc.getCityId());
        tcJo.set("city", commonDataUtil.getCityById(tc.getCityId()));
        tcJo.set("address", tc.getAddress());
        tcJo.set("postalCode", tc.getPostalCode());
        tcJo.set("centerName", tc.getCenterName());
        jo.set("transferCenter", tcJo);
        jo.set("returnTotal", returnTotal);

        renderAppData(jo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "returnScan", description = "returnScan")
    @PostMapping("/returnScan")
    public void returnScan(@RequestParam("code") String code, @RequestParam("tcId") Integer tcId) {
        // "select * from nb_order where pkg_no = ? limit 1", code);
        NbOrderEntity order = orderService.getOne(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getPkgNo, code), false);
        if (order == null) {
            renderAppErr("-1", "包裹不存在[" + code + "]");
            return;
        }
        if (order.getOrderStatus() != OrderDto.ORDER_STATUS_280_FAILURE) {
            renderAppErr("-2", "包裹状态不可退回[" + code + "]");
            return;
        }
        if (order.getTcId() != tcId) {
            renderAppErr("-3", "扫描的包裹与登记的转运中心不符");
            return;
        }

        NbDriverEntity loginDriver = getLoginDriver();

        order.setOrderStatus(OrderDto.ORDER_STATUS_286_RETURN_OFFICE_FROM_TRANSIT);
        orderService.updateById(order);

        Double[] latlng = getLatLng();
        Double lat = null, lng = null;
        if (latlng != null) {
            lat = latlng[0];
            lng = latlng[1];
        }

        NbTransferCenterEntity tc = transferCenterService.getById(tcId);
        String address = commonDataUtil.getAddress(tc.getProvinceId(), tc.getCityId());
        String timezone = tc.getTcTimezone();

        NbOrderPathEntity op = new OrderPathDto().build(order.getOrderId(), order.getOrderStatus(), loginDriver != null ? loginDriver.getDriverId() : 0, lat, lng, address, timezone);
        op.setTcId(tcId);
        orderPathService.save(op);

        renderAppSuc();
    }


}
