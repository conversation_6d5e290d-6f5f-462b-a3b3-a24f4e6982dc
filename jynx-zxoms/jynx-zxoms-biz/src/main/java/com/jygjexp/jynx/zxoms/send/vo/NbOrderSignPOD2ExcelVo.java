package com.jygjexp.jynx.zxoms.send.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: chenchang
 * @Description: POD2导出实体
 * @Date: 2024/12/8 1:36
 */
@Data
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 10) // 设置表头字体样式
@Schema(description = "POD2导出实体")
public class NbOrderSignPOD2ExcelVo {

    @ColumnWidth(15)
    @ExcelProperty("(Order ID)订单ID")
    @Schema(description="订单ID")
    private Integer orderId;

    @ColumnWidth(15)
    @ExcelProperty("(Order number)订单编号")
    @Schema(description="订单编号")
    private String orderNo;

    @ColumnWidth(15)
    @ExcelProperty("(Package number)包裹编号")
    @Schema(description="包裹编号")
    private String pkgNo;

    @ColumnWidth(10)
    @ExcelProperty("(Order status)订单状态")
    @Schema(description="订单状态")
    private Integer orderStatus;

    @ColumnWidth(25)
    @ExcelProperty("(Package drawing)包裹图")
    @Schema(description = "包裹图")
    private String vPkgurl;       //包裹图

    @ColumnWidth(25)
    @ExcelProperty("(Placement diagram)放置图")
    @Schema(description = "放置图")
    private String vPuturl;       //放置图

    @ColumnWidth(20)
    @ExcelProperty("(Signing time)签收时间")
    @Schema(description = "签收时间")
    private String signTime;

    @ColumnWidth(15)
    @ExcelProperty("(driver)司机")
    @Schema(description = "司机")
    private String vDriver;       //司机

    @ColumnWidth(10)
    @ExcelProperty("(Employee ID)员工ID")
    @Schema(description = "员工")
    private Integer vStaff;       //员工

    @ColumnWidth(15)
    @ExcelProperty("(latitude)纬度")
    @Schema(description="纬度")
    private Double lat;

    @ColumnWidth(15)
    @ExcelProperty("(longitude)经度")
    @Schema(description="经度")
    private Double lng;

    @ColumnWidth(25)
    @ExcelProperty("(Longitude and latitude)经纬度")
    @Schema(description = "经纬度")
    private String vLatlng;       //经纬度

    @ColumnWidth(15)
    @ExcelProperty("(distance)距离")
    @Schema(description = "距离")
    private Double vDistance;       //距离

}
