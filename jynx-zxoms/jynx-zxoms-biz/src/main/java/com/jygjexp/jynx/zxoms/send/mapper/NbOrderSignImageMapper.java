package com.jygjexp.jynx.zxoms.send.mapper;

import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.zxoms.entity.NbOrderSignImageEntity;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiOrderVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface NbOrderSignImageMapper extends JynxBaseMapper<NbOrderSignImageEntity> {


    List<ApiOrderVo> findImagesByOrderIds(List<Integer> orderIds);

}