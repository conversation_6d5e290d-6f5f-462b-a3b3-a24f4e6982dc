package com.jygjexp.jynx.zxoms.nbapp.validator;

import com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton.BigDecimalValue;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/12/18 15:24
 */
public class BigDecimalValueValidator implements ConstraintValidator<BigDecimalValue, BigDecimal> {

    @Override
    public boolean isValid(BigDecimal value, ConstraintValidatorContext context) {
        // 允许空值，如果不为 null 才检查是否是有效 BigDecimal 类型
        return value == null || isValidBigDecimal(value);
    }

    private boolean isValidBigDecimal(BigDecimal value) {
        try {
            // 这里只要确保 BigDecimal 本身是有效的数值即可
            value.toString(); // 若 BigDecimal 为非法数值，将抛出异常
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
