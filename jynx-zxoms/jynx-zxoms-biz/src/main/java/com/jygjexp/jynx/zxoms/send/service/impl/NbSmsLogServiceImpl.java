package com.jygjexp.jynx.zxoms.send.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderPathEntity;
import com.jygjexp.jynx.zxoms.entity.NbSmsLogEntity;
import com.jygjexp.jynx.zxoms.entity.NbSmsTemplateEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbSmsLogMapper;
import com.jygjexp.jynx.zxoms.send.service.NbSmsLogService;
import com.jygjexp.jynx.zxoms.send.vo.NbSmsLogExcelVo;
import com.jygjexp.jynx.zxoms.vo.SmsLogPageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 短信记录
 *
 * <AUTHOR>
 * @date 2024-10-12 22:16:54
 */
@Service
@RequiredArgsConstructor
public class NbSmsLogServiceImpl extends ServiceImpl<NbSmsLogMapper, NbSmsLogEntity> implements NbSmsLogService {
    private final NbSmsLogMapper smsLogMapper;

    @Override
    public Page<SmsLogPageVo> search(Page page, SmsLogPageVo vo) {
        //select template_id, title from nb_sms_template; ds=nbd;
        //select driver_id, first_name, last_name, mobile from nb_driver; ds=nbd;
        //select path_id, order_status from nb_order_path; ds=nbd;
        MPJLambdaWrapper wrapper = getWrapper(vo, null);
        return smsLogMapper.selectJoinPage(page, SmsLogPageVo.class, wrapper);
    }

    @Override
    public List<NbSmsLogExcelVo> getExcel(SmsLogPageVo vo, Integer[] ids) {
        MPJLambdaWrapper wrapper = getWrapper(vo, ids);
        return smsLogMapper.selectJoinList(NbSmsLogExcelVo.class, wrapper);
    }
    private MPJLambdaWrapper getWrapper(SmsLogPageVo vo, Integer[] ids) {
        MPJLambdaWrapper<NbSmsLogEntity> wrapper = new MPJLambdaWrapper<NbSmsLogEntity>();
        wrapper.eq(StrUtil.isNotBlank(vo.getOrderId()), NbSmsLogEntity::getOrderId, vo.getOrderId())          // 条件查询-订单ID
                .eq(ObjectUtil.isNotNull(vo.getSmsStatus()), NbSmsLogEntity::getSmsStatus, vo.getSmsStatus())   // 条件查询-短信状态
                .like(StrUtil.isNotBlank(vo.getMobile()), NbSmsLogEntity::getMobile, vo.getMobile())            // 条件查询-手机号
                .eq(ObjectUtil.isNotNull(vo.getDriverId()),NbSmsLogEntity::getDriverId, vo.getDriverId())       // 条件查询-司机ID
                .eq(ObjectUtil.isNotNull(vo.getSmsType()), NbSmsLogEntity::getSmsType, vo.getSmsType());        // 条件查询-短信类型


        String sendTime = vo.getQuerySendTime();    // 发送时间："2024-03-25 00:00:00-2024-12-31 00:00:00";
        if (sendTime != null) {
            int splitIndex = sendTime.indexOf(":", sendTime.indexOf(":") + 1) + 3;
            String startTime = sendTime.substring(0, splitIndex);
            String endTime = sendTime.substring(splitIndex + 1);
            wrapper.ge(NbSmsLogEntity::getSendTime, startTime).le(NbSmsLogEntity::getSendTime, endTime);    // 条件查询-发送时间
        }
        wrapper.selectAll(NbSmsLogEntity.class)
                .selectAs(NbSmsTemplateEntity::getTitle, SmsLogPageVo.Fields.smsTitle)
                .select(NbDriverEntity::getDriverId, NbDriverEntity::getDriverName)
                .select(NbOrderPathEntity::getPathId, NbOrderPathEntity::getOrderStatus)
                .leftJoin(NbSmsTemplateEntity.class, NbSmsTemplateEntity::getTemplateId, NbSmsLogEntity::getTemplateId)
                .leftJoin(NbDriverEntity.class, NbDriverEntity::getDriverId, NbSmsLogEntity::getDriverId)
                .leftJoin(NbOrderPathEntity.class, NbOrderPathEntity::getPathId, NbSmsLogEntity::getPathId)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, NbSmsLogEntity::getLogId, ids)
                .orderByDesc(NbSmsLogEntity::getLogId);
        return wrapper;
    }
}