package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbPostalCodeDetailEntity;
import com.jygjexp.jynx.zxoms.vo.NbPostalCodeDetailPageVo;
import org.springframework.web.multipart.MultipartFile;

public interface NbPostalCodeDetailService extends IService<NbPostalCodeDetailEntity> {

    R importNbPostalCodeDetail(MultipartFile file, Long groupId); // 导入邮编分区明细

    Page<NbPostalCodeDetailPageVo> search(Page page, NbPostalCodeDetailPageVo vo);

}