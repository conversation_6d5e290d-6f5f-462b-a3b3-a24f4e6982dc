package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.zxoms.entity.NbAppVersionEntity;
import com.jygjexp.jynx.zxoms.send.service.NbAppVersionService;
import com.jygjexp.jynx.zxoms.vo.NbAppVersionPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * app版本管理
 *
 * <AUTHOR>
 * @date 2024-11-12 00:27:59
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbAppVersion" )
@Tag(description = "nbAppVersion" , name = "app版本管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbAppVersionController {

    private final  NbAppVersionService nbAppVersionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo app版本管理
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersion_view')" )
    public R getNbAppVersionPage(@ParameterObject Page page, @ParameterObject NbAppVersionPageVo vo) {
        LambdaQueryWrapper<NbAppVersionEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjectUtil.isNotNull(vo.getAppType()), NbAppVersionEntity::getAppType, vo.getAppType())  // 条件查询 app类型
                .eq(ObjectUtil.isNotNull(vo.getIsValid()), NbAppVersionEntity::getIsValid, vo.getIsValid());// 条件查询 启用状态

        String createDate = vo.getQueryTime();   //创建时间 2024-03-25 00:00:00-2024-12-31 00:00:00
        if (createDate != null) {
            int splitIndex = createDate.indexOf(":", createDate.indexOf(":") + 1) + 3;
            String startAddTime = createDate.substring(0, splitIndex);
            String endAddTime = createDate.substring(splitIndex + 1);
            wrapper.ge(NbAppVersionEntity::getCreateDate, startAddTime).le(NbAppVersionEntity::getCreateDate, endAddTime);   // 条件查询 发布时间
        }
        wrapper.orderByDesc(NbAppVersionEntity::getCreateDate);
        return R.ok(nbAppVersionService.page(page, wrapper));
    }


    /**
     * 通过id查询app版本管理
     * @param appId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{appId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersion_view')" )
    public R getById(@PathVariable("appId" ) Integer appId) {
        return R.ok(nbAppVersionService.getById(appId));
    }

    /**
     * 通过type查询app版本管理
     * @param appType
     * @return R
     */
    @Operation(summary = "通过app_type查询" , description = "通过app_type查询" )
    @Inner(value = false)
    @GetMapping("/getByAppType/{appType}" )
    public R getByAppType(@PathVariable("appType") Integer appType) {
        return R.ok(nbAppVersionService.getOne(new LambdaQueryWrapper<NbAppVersionEntity>().eq(NbAppVersionEntity::getAppType, appType).eq(NbAppVersionEntity::getIsValid, true)
                .orderByDesc(NbAppVersionEntity::getVersionCode).orderByDesc(NbAppVersionEntity::getCreateDate).last("limit 1")));
    }

    /**
     * 新增app版本管理
     * @param nbAppVersion app版本管理
     * @return R
     */
    @Operation(summary = "新增app版本管理" , description = "新增app版本管理" )
    @SysLog("新增app版本管理" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersion_add')" )
    public R save(@RequestBody NbAppVersionEntity nbAppVersion) {
        return R.ok(nbAppVersionService.save(nbAppVersion));
    }

    /**
     * 修改app版本管理
     * @param nbAppVersion app版本管理
     * @return R
     */
    @Operation(summary = "修改app版本管理" , description = "修改app版本管理" )
    @SysLog("修改app版本管理" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersion_edit')" )
    public R updateById(@RequestBody NbAppVersionEntity nbAppVersion) {
        return R.ok(nbAppVersionService.updateById(nbAppVersion));
    }

    /**
     * 通过id删除app版本管理
     * @param ids appId列表
     * @return R
     */
    @Operation(summary = "通过id删除app版本管理" , description = "通过id删除app版本管理" )
    @SysLog("通过id删除app版本管理" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersion_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbAppVersionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbAppVersion 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbAppVersion_export')" )
    public List<NbAppVersionEntity> export(NbAppVersionEntity nbAppVersion,Integer[] ids) {
        return nbAppVersionService.list(Wrappers.lambdaQuery(nbAppVersion).in(ArrayUtil.isNotEmpty(ids), NbAppVersionEntity::getAppId, ids));
    }

    @Inner(value = false)
    @Operation(summary = "获取最新版本", description = "获取最新版本")
    @SysLog("获取最新版本")
    @GetMapping("/getLatestVersion")
    public R getLatestVersion(Integer appType) {
        return R.ok(nbAppVersionService.getOne(new LambdaQueryWrapper<NbAppVersionEntity>().eq(NbAppVersionEntity::getIsValid, true)
                // 下载地址是以apk结尾的
                .like(NbAppVersionEntity::getDownloadUrl, ".apk")
                .eq(NbAppVersionEntity::getAppType, appType)
                .orderByDesc(NbAppVersionEntity::getVersionCode).orderByDesc(NbAppVersionEntity::getCreateDate).last("limit 1")));
    }

}