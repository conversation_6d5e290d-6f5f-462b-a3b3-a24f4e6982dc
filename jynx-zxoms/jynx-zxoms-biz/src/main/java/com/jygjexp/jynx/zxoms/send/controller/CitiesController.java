package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.zxoms.send.service.CitiesService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.CitiesEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 城市地区
 *
 * <AUTHOR>
 * @date 2024-09-30 18:58:30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/cities" )
//@Tag(description = "cities" , name = "城市地区管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CitiesController {

    private final CitiesService citiesService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param basicCities 城市地区
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('basic_cities_view')" )
    public R getBasicCitiesPage(@ParameterObject Page page, @ParameterObject CitiesEntity basicCities) {
        LambdaQueryWrapper<CitiesEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(citiesService.page(page, wrapper));
    }


    /**
     * 通过id查询城市地区
     * @param id id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('basic_cities_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(citiesService.getById(id));
    }

    /**
     * 新增城市地区
     * @param basicCities 城市地区
     * @return R
     */
//    @Operation(summary = "新增城市地区" , description = "新增城市地区" )
    @SysLog("新增城市地区" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('basic_cities_add')" )
    public R save(@RequestBody CitiesEntity basicCities) {
        return R.ok(citiesService.save(basicCities));
    }

    /**
     * 修改城市地区
     * @param basicCities 城市地区
     * @return R
     */
//    @Operation(summary = "修改城市地区" , description = "修改城市地区" )
    @SysLog("修改城市地区" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('basic_cities_edit')" )
    public R updateById(@RequestBody CitiesEntity basicCities) {
        return R.ok(citiesService.updateById(basicCities));
    }

    /**
     * 通过id删除城市地区
     * @param ids id列表
     * @return R
     */
//    @Operation(summary = "通过id删除城市地区" , description = "通过id删除城市地区" )
    @SysLog("通过id删除城市地区" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('basic_cities_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(citiesService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param basicCities 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('basic_cities_export')" )
    public List<CitiesEntity> export(CitiesEntity basicCities, Integer[] ids) {
        return citiesService.list(Wrappers.lambdaQuery(basicCities).in(ArrayUtil.isNotEmpty(ids), CitiesEntity::getId, ids));
    }
}