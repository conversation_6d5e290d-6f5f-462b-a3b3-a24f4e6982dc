package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbSmsLogEntity;
import com.jygjexp.jynx.zxoms.send.service.NbSmsLogService;
import com.jygjexp.jynx.zxoms.send.vo.NbSmsLogExcelVo;
import com.jygjexp.jynx.zxoms.vo.SmsLogPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 短信记录
 *
 * <AUTHOR>
 * @date 2024-10-12 22:16:54
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/certificate/smsLog" )
@Tag(description = "smsLog" , name = "短信记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbSmsLogController {

    private final NbSmsLogService nbSmsLogService;

    /**
     * 分页查询
     * @param smsLogPageVo 分页对象
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/search" )
    @PreAuthorize("@pms.hasPermission('zxoms_smsLog_view')" )
    public R search(@ParameterObject Page page, @ParameterObject SmsLogPageVo smsLogPageVo) {
        return R.ok(nbSmsLogService.search(page, smsLogPageVo));
    }

    /**
     * 通过id查询短信记录
     * @param logId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{logId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_smsLog_view')" )
    public R getById(@PathVariable("logId" ) Integer logId) {
        return R.ok(nbSmsLogService.getById(logId));
    }

    /**
     * 新增短信记录
     * @param basicNbSmsLog 短信记录
     * @return R
     */
    @Operation(summary = "新增短信记录" , description = "新增短信记录" )
    @SysLog("新增短信记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_smsLog_add')" )
    public R save(@RequestBody NbSmsLogEntity basicNbSmsLog) {
        return R.ok(nbSmsLogService.save(basicNbSmsLog));
    }

    /**
     * 修改短信记录
     * @param basicNbSmsLog 短信记录
     * @return R
     */
    @Operation(summary = "修改短信记录" , description = "修改短信记录" )
    @SysLog("修改短信记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_smsLog_edit')" )
    public R updateById(@RequestBody NbSmsLogEntity basicNbSmsLog) {
        return R.ok(nbSmsLogService.updateById(basicNbSmsLog));
    }

    /**
     * 通过id删除短信记录
     * @param ids logId列表
     * @return R
     */
    @Operation(summary = "通过id删除短信记录" , description = "通过id删除短信记录" )
    @SysLog("通过id删除短信记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_smsLog_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbSmsLogService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param vo 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @Operation(summary = "导出短信记录" , description = "导出短信记录" )
    @SysLog("导出短信记录" )
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_smsLog_export')" )
    public List<NbSmsLogExcelVo> export(SmsLogPageVo vo, Integer[] ids) {
        return nbSmsLogService.getExcel(vo, ids);
    }
}