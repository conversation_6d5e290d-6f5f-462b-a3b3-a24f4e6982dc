package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbWeightDetailEntity;
import com.jygjexp.jynx.zxoms.send.service.NbWeightDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 重量段-明细
 *
 * <AUTHOR>
 * @date 2025-01-09 13:56:36
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbWeightDetail" )
@Tag(description = "nbWeightDetail" , name = "重量段-明细管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbWeightDetailController {

    private final  NbWeightDetailService nbWeightDetailService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbWeightDetail 重量段-明细
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightDetail_view')" )
    public R getNbWeightDetailPage(@ParameterObject Page page, @ParameterObject NbWeightDetailEntity nbWeightDetail) {
        LambdaQueryWrapper<NbWeightDetailEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjectUtil.isNotNull(nbWeightDetail.getGroupId()),NbWeightDetailEntity::getGroupId,nbWeightDetail.getGroupId());
        return R.ok(nbWeightDetailService.page(page, wrapper));
    }


    /**
     * 通过id查询重量段-明细
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightDetail_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(nbWeightDetailService.getById(id));
    }

    /**
     * 新增重量段-明细
     * @param nbWeightDetail 重量段-明细
     * @return R
     */
    @Operation(summary = "新增重量段-明细" , description = "新增重量段-明细" )
    @SysLog("新增重量段-明细" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightDetail_add')" )
    public R save(@RequestBody NbWeightDetailEntity nbWeightDetail) {
        if (nbWeightDetail.getStartWeight().compareTo(nbWeightDetail.getEndWeight())>=0){
            return R.failed("The cut-off weight cannot be less than the starting weight");
        }

        //判断新增重量开始和截止区间是否重复
        List<NbWeightDetailEntity> weightEntities = nbWeightDetailService.list(new LambdaQueryWrapper<NbWeightDetailEntity>()
                .eq(NbWeightDetailEntity::getGroupId,nbWeightDetail.getGroupId())
                .eq(NbWeightDetailEntity::getStartWeight, nbWeightDetail.getStartWeight())
                .eq(NbWeightDetailEntity::getEndWeight, nbWeightDetail.getEndWeight()).last("limit 1"));
        if (CollUtil.isNotEmpty(weightEntities)) {
            return R.failed("Weight detail interval repeats");
        }
        return R.ok(nbWeightDetailService.save(nbWeightDetail));
    }

    /**
     * 修改重量段-明细
     * @param nbWeightDetail 重量段-明细
     * @return R
     */
    @Operation(summary = "修改重量段-明细" , description = "修改重量段-明细" )
    @SysLog("修改重量段-明细" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightDetail_edit')" )
    public R updateById(@RequestBody NbWeightDetailEntity nbWeightDetail) {
        //判断修改重量开始和截止区间是否重复
        List<NbWeightDetailEntity> weightEntities = nbWeightDetailService.list(new LambdaQueryWrapper<NbWeightDetailEntity>()
                .eq(NbWeightDetailEntity::getGroupId,nbWeightDetail.getGroupId())
                .eq(NbWeightDetailEntity::getStartWeight, nbWeightDetail.getStartWeight())
                .eq(NbWeightDetailEntity::getEndWeight, nbWeightDetail.getEndWeight()).ne(NbWeightDetailEntity::getId,nbWeightDetail.getId())
                .last("limit 1"));
        if (CollUtil.isNotEmpty(weightEntities)) {
            return R.failed("Weight detail interval repeats");
        }
        return R.ok(nbWeightDetailService.updateById(nbWeightDetail));
    }

    /**
     * 通过id删除重量段-明细
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除重量段-明细" , description = "通过id删除重量段-明细" )
    @SysLog("通过id删除重量段-明细" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightDetail_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(nbWeightDetailService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbWeightDetail 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightDetail_export')" )
    public List<NbWeightDetailEntity> export(NbWeightDetailEntity nbWeightDetail,Long[] ids) {
        return nbWeightDetailService.list(Wrappers.lambdaQuery(nbWeightDetail).in(ArrayUtil.isNotEmpty(ids), NbWeightDetailEntity::getId, ids));
    }



    /**
     * 导入重量明细
     * @param file
     * @return
     */
    @Operation(summary = "导入重量明细" , description = "导入重量明细" )
    @SysLog("导入重量明细" )
    @PostMapping("/_import")
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightDetail_import')")
    public R importOrders(@RequestParam("file") MultipartFile file,@RequestParam("groupId") Long groupId) {
        return nbWeightDetailService.processFile(file,groupId);
    }

}