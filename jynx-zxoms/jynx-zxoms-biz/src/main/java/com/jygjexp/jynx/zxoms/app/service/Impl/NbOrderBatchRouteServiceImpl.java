package com.jygjexp.jynx.zxoms.app.service.Impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.common.websocket.config.WebSocketMessageSender;
import com.jygjexp.jynx.zxoms.app.service.NbOrderBatchRouteService;
import com.jygjexp.jynx.zxoms.app.vo.OrderBatchRouteVo;
import com.jygjexp.jynx.zxoms.dto.*;
import com.jygjexp.jynx.zxoms.entity.*;

import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.dto.OrderSyncR4mData;
import com.jygjexp.jynx.zxoms.send.dto.R4mTobeUpdateRouter;
import com.jygjexp.jynx.zxoms.send.mapper.*;
import com.jygjexp.jynx.zxoms.send.service.NbDriverService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderBatchService;
import com.jygjexp.jynx.zxoms.send.service.NbTransferBatchService;

import com.jygjexp.jynx.zxoms.send.utils.CommonDataUtil;
import com.jygjexp.jynx.zxoms.send.utils.ConfigUtil;
import com.jygjexp.jynx.zxoms.send.utils.Route4MeUtil;
import com.jygjexp.jynx.zxoms.vo.NbOrderTransferBatchOrderVo;
import com.route4me.sdk.exception.APIException;
import com.route4me.sdk.services.orders.OrderStatus;
import com.route4me.sdk.services.routing.*;
import com.route4me.sdk.services.routing.balance.Balance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.client.HttpClientErrorException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author: xiongpengfei
 * @Description: 路线规划实现层
 * @Date: 2024/10/30 17:28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class NbOrderBatchRouteServiceImpl extends ServiceImpl<NbOrderBatchMapper, NbOrderBatchEntity> implements NbOrderBatchRouteService {
    private final NbSortingCenterMapper nbSortingCenterMapper;
    private final NbDriverService nbDriverService;
    private final NbTransferBatchService nbTransferBatchService;
    private final NbTransferBatchOrderMapper nbTransferBatchOrderMapper;
    private final NbOrderMapper nbOrderMapper;
    private final NbTransferCenterMapper nbTransferCenterMapper;
    private final NbOrderMpsMapper nbOrderMpsMapper;
    private final RemoteUserService remoteUserService;
    private final CommonDataUtil CommonDataUtil;
    private final Route4MeUtil route4MeUtil;
    private final NbOrderMapper orderMapper;
    private final ConfigUtil configUtil;
    private final NbOrderBatchMapper orderBatchMapper;
    private final NbOrderBatchService orderBatchService;



    /**
     * 导出给route4me，Order     基于自动规划（sync_to_r4m_order_btn.html） 上传之后会有定时作业自动调度执行-RouteBatchTask
     */
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, isolation = Isolation.READ_UNCOMMITTED)
    @Override
    public R exportToR4mOrder(String batchIds) {
        if (StrUtil.isBlank(batchIds)) {
            return LocalizedR.failed("nborderbatchrouteno.selection.batch", Optional.ofNullable(null));
        }

        String[] batchIdArr = batchIds.split(",");
        List<Integer> batchIdList = new ArrayList<>();
        for (String batchIdStr : batchIdArr) {
            batchIdList.add(Integer.valueOf(batchIdStr));
        }

        //一些跟R4M相关的数据实体
        List<OrderSyncR4mData> syncData = new ArrayList<>();

        //负责校验+订单相关分组
        int orderTotal = 0;
        for (Integer batchId : batchIdList) {
            //遍历每个批次id
            NbOrderBatchEntity batch = getById(batchId);
            log.info("检查批次数据合规性" + batch.getBatchNo());
            //发送消息
            wsMessage(batchIds, 0, 0, "检查批次数据合规性" + batch.getBatchNo());

            //批次R4M同步的状态：1未开始,2同步中,3同步成功,4同步失败
            if (batch.getSyncR4mOrderStatus() == OrderBatchDto.R4M_SYNC_ORDER_STATUS_3_SUCCESS) {
                return LocalizedR.failed("nborderbatchrouteno.batch.has.been.synchronized", batch.getBatchNo());
            }

            if (batch.getSyncR4mOrderStatus() == OrderBatchDto.R4M_SYNC_ORDER_STATUS_2_SYNCING) {
                return LocalizedR.failed("nborderbatchrouteno.do.not.open.it.again", batch.getBatchNo());
            }

            if (batch.getSyncR4mOrderStatus() == OrderBatchDto.R4M_SYNC_ORDER_STATUS_4_FAILURE) {
                return LocalizedR.failed("nborderbatchrouteno.the.batch.planning.fails.contact.the.administrator", batch.getBatchNo());
            }

            //定义一个orders后续接收订单
            List<NbOrderEntity> orders;
            // 未到货且主批次的订单
            List<NbOrderEntity> ordersInMainWith190 = null;

            //区分批次订单是主/子，根据订单表扫描出相应的订单
            if (batch.getIsMain()) {
                //  select * from nb_order where batch_no = ? and order_status in (?, ?, ?) and r4m_order_id = 0", batch.getBatchNo(), Order.ORDER_STATUS_190_ORDER_RECEIVED, Order.ORDER_STATUS_199_GATEWAY_TRANSIT, Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的主批次
                LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
                wrapper.eq(NbOrderEntity::getBatchNo, batch.getBatchNo())
                        .and(i -> i.in(NbOrderEntity::getOrderStatus,
                                OrderDto.ORDER_STATUS_190_ORDER_RECEIVED, OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED))
                        .eq(NbOrderEntity::getR4mOrderId, 0);
                orders = nbOrderMapper.selectList(wrapper);
            } else {
                //  select * from nb_order where sub_batch_no = ? and order_status in (?, ?) and r4m_order_id = 0", batch.getBatchNo(), Order.ORDER_STATUS_199_GATEWAY_TRANSIT, Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的子批次
                LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
                wrapper.eq(NbOrderEntity::getSubBatchNo, batch.getBatchNo())
                        .and(i -> i.in(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_199_GATEWAY_TRANSIT, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED))
                        .eq(NbOrderEntity::getR4mOrderId, 0);
                orders = nbOrderMapper.selectList(wrapper);
            }

            //自动生成的卡派批次
            if (batch.getIsLtl()) {
                String log1 = "批次：" + batch.getBatchNo() + "为卡派批次，开始筛选主订单作为同步订单";
                log.info(log1);
                wsMessage(batchIds, 0, 0, log1);

                //存储有客户单号
                Map<String, NbOrderEntity> mainOrderMap = new HashMap<>();
                //存储没客户单号的订单
                List<NbOrderEntity> unsetCustomerPOrderNo = new ArrayList<>();

                //区分批次订单有无客户单号
                for (NbOrderEntity order : orders) {
                    //getCustomerPOrderNo（）：客户单号   getPkgNo（）：包裹编号
                    if (StrUtil.isNotBlank(order.getCustomerPOrderNo())) {

                        //订单跟踪
                        NbOrderMpsEntity mp = new NbOrderMpsEntity();
                        if (mainOrderMap.containsKey(order.getCustomerPOrderNo())) {
                            String logSubTip = "批次：" + batch.getBatchNo() + "订单：" + order.getPkgNo() + "为子订单，主订单号：" + order.getCustomerPOrderNo() + ",跳过";
                            log.info(logSubTip);
                            wsMessage(batchIds, 0, 0, logSubTip);

                            NbOrderEntity pOrder = mainOrderMap.get(order.getCustomerPOrderNo());

                            mp.setOrderId(order.getOrderId());
                            mp.setPOrderId(pOrder.getOrderId());
                            mp.setAddTime(new Date());
                            nbOrderMpsMapper.insert(mp);
                        } else {
                            String logSubTip = "批次：" + batch.getBatchNo() + "订单：" + order.getPkgNo() + "为主订单";
                            log.info(logSubTip);
                            wsMessage(batchIds, 0, 0, logSubTip);
                            mainOrderMap.put(order.getCustomerPOrderNo(), order);

                            mp.setOrderId(order.getOrderId());
                            mp.setPOrderId(0);
                            mp.setAddTime(new Date());
                            nbOrderMpsMapper.insert(mp);
                        }

                    } else {
                        unsetCustomerPOrderNo.add(order);
                    }

                }

                //订单合并
                List<NbOrderEntity> mainOrderList = mainOrderMap.values().stream().collect(Collectors.toList());
                if (mainOrderList.size() > 0) {
                    unsetCustomerPOrderNo.addAll(mainOrderList);
                }

                orders = unsetCustomerPOrderNo;
            }

            //订单条目数
            orderTotal += orders.size();

            String logStatTip = "批次" + batch.getBatchNo() + "共" + orders.size() + "个订单";
            log.info(logStatTip);
            wsMessage(batchIds, 0, 0, logStatTip);

            if (orders.size() == 0) {
                return LocalizedR.failed("nborderbatchrouteno.there.are.no.orders.to.plan", batch.getBatchNo());
            }

            //存储转运中心-订单的分组
            Map<Integer, NbTransferCenterEntity> tcMap = new HashMap<>();
            //存储大区-订单的分组
            Map<String, List<NbOrderEntity>> orderMap = new HashMap<>();
            //存储对应大区对应的转运中心
            Map<String, List<NbTransferCenterEntity>> tcRegionCodeMap = new HashMap<>();

            //进行各种数据组合分组
            NbSortingCenterEntity sc = null;
            for (NbOrderEntity order : orders) {
                Integer tcId = order.getTcId();
                if (tcId == null || tcId == 0) {
                    log.info("发现有未分配转运中心的订单，暂时不规划： " + order.getOrderId());

                    batch.setRoutedErrmsg("发现有未分配转运中心的订单，暂时不能同步： " + order.getOrderId());
                    orderBatchMapper.updateById(batch);
                    return LocalizedR.failed("nborderbatchrouteno.orders.unassigned.transfer.centers", order.getOrderId());
                }

                //自提件不参与同步
                if (order.getExpressType() == OrderDto.EXPRESS_TYPE_2_PICKUP) {
                    log.info("自提件不同步orderId=" + order.getOrderId() + ",pkgNo=" + order.getPkgNo() + ",expressType=" + order.getExpressType());
                    continue;
                }

                //将转运中心和对应订单进行一一分组
                List<NbOrderEntity> tcOrders;
                NbTransferCenterEntity tc;
                if (tcMap.containsKey(tcId)) {
                    tc = tcMap.get(tcId);
                } else {
                    tc = nbTransferCenterMapper.selectById(tcId);
                    tcMap.put(tcId, tc);
                }

                if (StrUtil.isBlank(tc.getRegionCode())) {
                    batch.setRoutedErrmsg("转运中心[" + tc.getCenterName() + "]没有设置大区号,无法为订单完成分组,请检查");
//                    update();
                    orderBatchMapper.updateById(batch);
                    return LocalizedR.failed("nborderbatchrouteno.region.code.not.set", batch.getRoutedErrmsg());
                }

                //将大区号和相应订单进行分组1
                if (orderMap.containsKey(tc.getRegionCode())) {
                    tcOrders = orderMap.get(tc.getRegionCode());
                } else {
                    tcOrders = Lists.newArrayList();
                }
                tcOrders.add(order);
                orderMap.put(tc.getRegionCode(), tcOrders);

                //插入对应大区和下面的转运中心给tcRegionCodeMap
                List<NbTransferCenterEntity> tcs = tcRegionCodeMap.getOrDefault(tc.getRegionCode(), new ArrayList<NbTransferCenterEntity>());
                tcs.add(tc);
                tcRegionCodeMap.put(tc.getRegionCode(), tcs);

                if (sc == null) {
                    sc = nbSortingCenterMapper.selectById(order.getScId());
                }
            }

            //OrderSyncR4mData一些跟R4M相关的数据实体; -将上面存储的数据插入
            OrderSyncR4mData data = new OrderSyncR4mData();
            //分拣中心
            data.setSc(sc);
            //批次
            data.setOrderBatch(batch);
            //存储大区-订单的分组
            data.setOrderMap(orderMap);
            //存储转运中心-订单的分组
            data.setTcMap(tcMap);
            //存储对应大区对应的转运中心的分组
            data.setTcRegionCodeMap(tcRegionCodeMap);

            syncData.add(data);

            //ordersInMainWith190：未到货且主批次
            if (ordersInMainWith190 != null && ordersInMainWith190.size() > 0) {
                // "select * from nb_order_batch where batch_type = 5 order by batch_id desc limit 1"); // 获取最新的未到货批次
                LambdaQueryWrapper<NbOrderBatchEntity> wrapper = Wrappers.lambdaQuery(NbOrderBatchEntity.class);
                wrapper.eq(NbOrderBatchEntity::getBatchType, 5)
                        .orderByDesc(NbOrderBatchEntity::getBatchId);
                NbOrderBatchEntity nonReceiptBatch = getOne(wrapper);
                //将批次号设为子批次
                for (NbOrderEntity order190 : ordersInMainWith190) {
                    order190.setSubBatchNo(nonReceiptBatch.getBatchNo());
                    orderMapper.updateById(order190);
                }

                // "select count(1) cont from nb_order where sub_batch_no = ? limit 1", nonReceiptBatch.getBatchNo()).getInt("cont");
                LambdaQueryWrapper<NbOrderEntity> wrapper1 = Wrappers.lambdaQuery(NbOrderEntity.class);
                wrapper1.eq(NbOrderEntity::getSubBatchNo, nonReceiptBatch.getBatchNo());
                int total = Math.toIntExact(nbOrderMapper.selectCount(wrapper1));
                nonReceiptBatch.setOrderTotal(total);
                nonReceiptBatch.setBatchTotal(total);
                nonReceiptBatch.setImpTotal(total);
                orderBatchMapper.updateById(nonReceiptBatch);
            }
        }

//--------------------------------------------------以下数据同步阶段-------------------------------------------------------------------

        int finalOrderTotal = orderTotal;

        //保证线程安全且具有原子性的操作
        AtomicInteger current = new AtomicInteger(0);
        AtomicInteger allcount = new AtomicInteger(0);

        //返回路线信息集合
        Map<String, Map<String, List<Address>>> r4MList = new HashMap<>();
        log.info("导入批次数",syncData.size());
        for (OrderSyncR4mData data : syncData) {
            //拿到批次号
            NbOrderBatchEntity batch = data.getOrderBatch();

            wsMessage(batchIds, 0, 0, "开始同步" + batch.getBatchNo());
            //拿到分拣中心
            NbSortingCenterEntity sc = data.getSc();
            //拿到大区-订单的分组
            Map<String, List<NbOrderEntity>> orderMap = data.getOrderMap();
            //拿到大区-转运中心的分组
            Map<String, List<NbTransferCenterEntity>> tcRegionCodeMap = data.getTcRegionCodeMap();
            //批次R4M同步的状态：1未开始,2同步中,3同步成功,4同步失败-----当前修改状态为同步中
            batch.setSyncR4mOrderStatus(OrderBatchDto.R4M_SYNC_ORDER_STATUS_2_SYNCING);
//            update();
            orderBatchMapper.updateById(batch);


            String timezone = sc.getScTimezone();
            //根据当前时间和指定的时区 timezone，生成一个格式为 "yyyy-MM-dd" 的日期字符串。
//            String scheduledFor = Instant.now().atOffset(ZoneOffset.of(timezone)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String scheduledFor = Instant.now().atZone(ZoneId.of(timezone)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            log.info("共" + orderMap.size() + "个大区等待导入R4M");

            // 创建一个固定大小的线程池，大小为 10
            ExecutorService executorService = Executors.newFixedThreadPool(10);
            // 用于存储 Future 对象
            List<Future<Void>> futures = new ArrayList<>();

            //把大区订单进行遍历
            Iterator<Map.Entry<String, List<NbOrderEntity>>> iter = orderMap.entrySet().iterator();
            while (iter.hasNext()) {
                Map.Entry<String, List<NbOrderEntity>> entry = iter.next();
                //拿到大区
                String regionCode = entry.getKey();
                //拿到大区对应订单
                List<NbOrderEntity> tcOrders = entry.getValue();

                //获取大区下面的转运中心信息列表
                List<NbTransferCenterEntity> tcs = tcRegionCodeMap.get(regionCode);
                List<NbTransferCenterEntity> uniqueTcs = new ArrayList<>();
                List<Integer> checked = Lists.newArrayList();
                //将tcid去重，插入checked集合，转运中心数据插入uniqueTcs集合
                for (NbTransferCenterEntity tc : tcs) {
                    if (checked.contains(tc.getTcId())) {
                        continue;
                    }
                    uniqueTcs.add(tc);
                    checked.add(tc.getTcId());
                }

                for (NbOrderEntity order : tcOrders) {
                    // 使用 executorService.submit() 提交 Callable 任务    Future 异步   任务完成后不会返回具体结果，但可以处理任务执行过程中可能抛出的异常。
                    Future<Void> future = executorService.submit(new Callable<Void>() {
                        @Override
                        public Void call() throws Exception {
                            com.route4me.sdk.services.orders.Order r4mOrder = new com.route4me.sdk.services.orders.Order();
                            r4mOrder.setStatusID(OrderStatus.NEW.getValue());
                            r4mOrder.setCachedLatitude(order.getDestLat());
                            r4mOrder.setCachedLongitude(order.getDestLng());
                            r4mOrder.setAddressZip(StringUtils.deleteWhitespace(order.getDestPostalCode()));
                            r4mOrder.setAddressCountryID(order.getDestCountry());
                            r4mOrder.setStateID(order.getDestProvince());
                            r4mOrder.setCity(order.getDestCity());
                            r4mOrder.setAddress1(order.getDestAddress1());
                            r4mOrder.setAddress2(order.getDestAddress2());
                            r4mOrder.setGroup(batch.getBatchNo());
                            r4mOrder.setWeight(order.getPkgWeight().setScale(2, RoundingMode.HALF_UP).doubleValue());
                            r4mOrder.setAddressAlias(order.getOrderId().toString());
                            r4mOrder.setFirstName(order.getDestName());
                            r4mOrder.setServiceTime(300);

                            r4mOrder.setPhone(order.getDestTel());
                            r4mOrder.setEmail(order.getDestEmail());
                            r4mOrder.setTrackingNumber(order.getPkgNo());
                            r4mOrder.setAddressStopType(Constants.AddressStopType.DELIVERY.getValue());
                            r4mOrder.setDateScheduled(scheduledFor);
                            r4mOrder.setWeight(order.getPkgWeight().setScale(2, RoundingMode.HALF_UP).doubleValue());

                            // 导入 Route4Me
                            R r;
                            try {
                                r = route4MeUtil.importOrder(r4mOrder);
                                if (!r.isOk()) {
                                    // 如果内部方法返回失败结果，在这里抛出异常进行事务回滚
                                    throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Route4Me Import Order Error: " + order.getOrderNo());
                                }
                            } catch (RuntimeException e) {
                                // 捕获 RuntimeException，检查其根本原因是否为 APIException
                                Throwable cause = e.getCause();
                                if (cause instanceof APIException) {
                                    // 如果是 APIException，提取错误信息
                                    APIException apiException = (APIException) cause;
                                    String errorMessage = apiException.getMessage();

                                    // 使用 Hutool 解析 JSON
                                    String jsonStr = errorMessage.substring(errorMessage.indexOf("{"));
                                    JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
                                    JSONArray errorsArray = jsonObject.getJSONArray("errors");
                                    String errors = errorsArray.getStr(0);

                                    // 构造完整的错误信息
                                    String fullErrorMessage = order.getOrderNo() + "--" + "Route4Me 订单导入失败: " + errors;
                                    log.error(fullErrorMessage, apiException);
                                    throw new RuntimeException(fullErrorMessage, apiException);
                                } else {
                                    // 如果是其他 RuntimeException，直接抛出
                                    log.error("Route4Me 订单导入失败", e);
                                    throw new RuntimeException("Route4Me Import Order Error: " + order.getOrderNo(), e);
                                }
                            } catch (Exception e) {
                                // 捕获其他异常
                                log.error("Route4Me 订单导入失败", e);
                                throw new RuntimeException("Route4Me Import Order Error: " + order.getOrderNo(), e);
                            }

                            int innerAllCount = allcount.incrementAndGet();
                            if (r.isOk()) {
                                com.route4me.sdk.services.orders.Order r4mOrderSaved = (com.route4me.sdk.services.orders.Order) r.getData();
                                // Route4Me 返回的唯一订单 id
                                order.setR4mOrderId(r4mOrderSaved.getId());
                                orderMapper.updateById(order);
                                int innerCurrent = current.incrementAndGet();
                                // wsMessage(batchIds, innerCurrent, finalOrderTotal, null);
                            } else {
                                String errmsg = r.getMsg();
                                String json = errmsg.substring(errmsg.indexOf("{"));
                                com.alibaba.fastjson.JSONObject jo = JSON.parseObject(json);
                                errmsg = jo.getJSONArray("errors").getString(0);
                                // wsMessage(batchIds, current.get(), finalOrderTotal, "导入失败：tracknumber=" + order.getPkgNo() + ", errmsg=" + errmsg + "," + innerAllCount);
                            }

                            return null; // Callable 需要返回一个值，这里返回 null
                        }
                    });
                    futures.add(future);
                }
            }
            // 有序关闭线程池
            executorService.shutdown();

            // 用于存储异常
            List<Exception> exceptions = new ArrayList<>();
            // 检查所有任务是否完成，并处理异常
            for (Future<Void> future : futures) {
                try {
                    future.get(); // 获取任务结果，如果任务抛出异常，会在这里捕获
                } catch (ExecutionException e) {
                    // 捕获任务中抛出的异常
                    Throwable cause = e.getCause();
                    log.error("任务执行失败", cause);
                    exceptions.add(new RuntimeException(cause)); // 将异常存储起来
                    //return R.failed(cause.getMessage());
                } catch (InterruptedException e) {
                    // 处理线程中断异常
                    log.error("任务被中断", e);
                    Thread.currentThread().interrupt(); // 恢复中断状态
                    exceptions.add(new RuntimeException("Task interrupted", e)); // 将异常存储起来
                    //return R.failed("Task interrupted");
                }
            }

            // 如果有异常，抛出第一个异常
            if (!exceptions.isEmpty()) {
                String errorMessage = exceptions.get(0).getMessage();
                // 去掉 "java.lang.RuntimeException: " 前缀
                if (errorMessage.startsWith("java.lang.RuntimeException: ")) {
                    errorMessage = errorMessage.substring("java.lang.RuntimeException: ".length());
                }
                // 手动回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                // 返回自定义错误响应
                return R.failed(errorMessage);
            }

            batch.setRoutedErrmsg(null);
            if (batch.getBatchType() == OrderBatchDto.BATCH_TYPE_5_NOT_ARRIVED) {
                batch.setSyncOrderTotal(finalOrderTotal + batch.getSyncOrderTotal());
            } else {
                batch.setSyncR4mOrderStatus(OrderBatchDto.R4M_SYNC_ORDER_STATUS_3_SUCCESS);
                batch.setSyncOrderTotal(finalOrderTotal);
            }
            batch.setSyncR4mOrderTime(Instant.now().getEpochSecond() * 1000);
            orderBatchMapper.updateById(batch);
            log.info("批次同步记录batchId=" + batch.getBatchId() + ",orderTotal=" + finalOrderTotal);

            //路线生成
            Map<String, List<Address>> process = new HashMap<>();
            // 定义一个 Set，用于记录已处理的批次 ID 防止线程并发访问情况
            Set<String> processedBatches = new HashSet<>();
            try {
                if (!processedBatches.contains(batch.getBatchNo())) {
                    //根据导入订单生成路线
                    log.info("开始调用 process() 方法，批次 ID: {}", batch.getBatchNo());
                    process = process(batch.getBatchNo());
                    log.info("process() 方法调用完成，批次 ID: {}", batch.getBatchNo());
                    if (process == null) {
                        log.info("该批次路线生成为空", batch.getBatchNo());
                        // 手动回滚
                        //TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        //该批次生成路线失败，将导入的订单同步删除
//                        LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
//                        List<NbOrderEntity> r4mOrder;
//                        if (batch.getIsMain()) {
//                            wrapper.eq(NbOrderEntity::getBatchNo, batch.getBatchNo()).ne(NbOrderEntity::getR4mOrderId, 0);
//                            r4mOrder = nbOrderMapper.selectList(wrapper);
//                        } else {
//                            wrapper.eq(NbOrderEntity::getSubBatchNo, batch.getBatchNo()).ne(NbOrderEntity::getR4mOrderId, 0);
//                            r4mOrder = nbOrderMapper.selectList(wrapper);
//                        }
//                        Long[] r4mOrderIds = r4mOrder.stream()
//                                .map(NbOrderEntity::getR4mOrderId)
//                                .toArray(Long[]::new);
//                        route4MeUtil.deleteOrder(r4mOrderIds);
//                        //将R4M订单id置为0
//                        UpdateWrapper<NbOrderEntity> updateWrapper = new UpdateWrapper<>();
//                        updateWrapper.in("r4m_order_id", r4mOrderIds).set("r4m_order_id", 0);
//                        nbOrderMapper.update(null,updateWrapper);

                        return LocalizedR.failed("route4me.batch.create.empty", batch.getBatchNo());
                    }
                    processedBatches.add(batch.getBatchNo());
                }

            } catch (Exception e) {
                // 手动回滚
                //TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                //该批次生成路线失败，将导入的订单同步删除
//                LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
//                List<NbOrderEntity> r4mOrder;
//                if (batch.getIsMain()) {
//                    wrapper.eq(NbOrderEntity::getBatchNo, batch.getBatchNo()).ne(NbOrderEntity::getR4mOrderId, 0);
//                    r4mOrder = nbOrderMapper.selectList(wrapper);
//                } else {
//                    wrapper.eq(NbOrderEntity::getSubBatchNo, batch.getBatchNo()).ne(NbOrderEntity::getR4mOrderId, 0);
//                    r4mOrder = nbOrderMapper.selectList(wrapper);
//                }
//                Long[] r4mOrderIds = r4mOrder.stream()
//                        .map(NbOrderEntity::getR4mOrderId)
//                        .toArray(Long[]::new);
//                route4MeUtil.deleteOrder(r4mOrderIds);
//                //将R4M订单id置为0
//                UpdateWrapper<NbOrderEntity> updateWrapper = new UpdateWrapper<>();
//                updateWrapper.in("r4m_order_id", r4mOrderIds).set("r4m_order_id", 0);
//                nbOrderMapper.update(null,updateWrapper);
                // 捕获process方法内部抛出的异常
                log.error("路径规划过程异常错误如下：{}", e.getMessage(), e);
                return LocalizedR.failed("route4me.batch.create.error", batch.getBatchNo() +"  error:"+ e.getMessage());
            }
            r4MList.put(batch.getBatchNo(), process);
        }
        return R.ok(r4MList);
    }

    @Override
    public List<NbOrderTransferBatchOrderVo> getPkgNoList(String pkg_no) {
        return nbOrderMapper.getPkgNoList(pkg_no);
    }




    /**
     * 把订单批次按转运中心进行分组    --一个批次订单会存在不同转运中心  基于手动规划（route4me.html）
     */
    @Override
    public R tcGroup(Integer batchId) {
        //根据批次id去查询是否存在
        NbOrderBatchEntity batch = getById(batchId);
        if (batchId == null) {
            return LocalizedR.failed("nborderbatchrouteno.batch.not.exist", Optional.ofNullable(null));
        }
        if (batch.getIsRouted()) {
            return LocalizedR.failed("nborderbatchrouteno.have.planned=", Optional.ofNullable(null));
        }

        List<NbOrderEntity> orders;
        //IsMain():主批次    判断批次是主/子,进行不同的扫描，查询出订单
        if (batch.getIsMain()) {
            // 扫描过的主批次
            // select * from nb_order where batch_no = ? and order_status = ? and is_routed = false and express_type in (0,1)", batch.getBatchNo(), OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
            LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
            wrapper.eq(NbOrderEntity::getBatchNo, batch.getBatchNo())
                    .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                    .eq(NbOrderEntity::getIsRouted, false)
                    .in(NbOrderEntity::getExpressType, 0, 1);
            orders = nbOrderMapper.selectList(wrapper);
        } else {
            // 扫描过的子批次
            //"select * from nb_order where sub_batch_no = ? and order_status = ? and is_routed = false and express_type in (0,1)", batch.getBatchNo(), OrderDto.ORDER_STATUS_200_PARCEL_SCANNED);
            LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
            wrapper.eq(NbOrderEntity::getSubBatchNo, batch.getBatchNo())
                    .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                    .eq(NbOrderEntity::getIsRouted, false)
                    .in(NbOrderEntity::getExpressType, 0, 1);
            orders = nbOrderMapper.selectList(wrapper);
        }

        if (orders.size() == 0) {
            return LocalizedR.failed("nborderbatchrouteno.there.are.no.orders.to.plan", Optional.ofNullable(null));
        }

//        if (orders.size() == 1) {
//            return R.failed("-1", "一个订单无法规划");
//        }

        Map<Integer, List<NbOrderEntity>> orderMap = new HashMap<>();
        for (NbOrderEntity order : orders) {
            //转运中心id
            Integer tcId = order.getTcId();
            if (tcId == null || tcId == 0) {
                log.info("发现有未分配转运中心的订单，暂时不规划： " + order.getOrderId());
                //批次表-RoutedErrmsg:规划返回数据
                batch.setRoutedErrmsg("发现有未分配转运中心的订单，暂时不规划： " + order.getOrderId());
//                update();
                orderBatchMapper.updateById(batch);
                return LocalizedR.failed("nborderbatchrouteno.orders.unassigned.transfer.centers", order.getOrderId());
            }

            List<NbOrderEntity> tcOrders;
            //检查tcid当前这个转运中心是否存在这个map的key中，如果存在则返回true
            if (orderMap.containsKey(tcId)) {
                tcOrders = orderMap.get(tcId);
            } else {
                tcOrders = Lists.newArrayList();
            }

            tcOrders.add(order);
            //将转运中心id+对应批次订单写入Map--分组
            orderMap.put(tcId, tcOrders);
        }
        log.info("共" + orderMap.size() + "个转运中心参与");

        JSONArray ja = new JSONArray();

        //迭代器遍历这个Map
        Iterator<Map.Entry<Integer, List<NbOrderEntity>>> iter = orderMap.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<Integer, List<NbOrderEntity>> entry = iter.next();
            //对每个转运中心下的订单进行逐一遍历
            int tcId = entry.getKey();
            List<NbOrderEntity> tcOrders = entry.getValue();

            //查询当前这个转运中心下的司机列表
            //  select * from nb_driver where audit_status = 3 and is_valid = true and tc_id = ? and auto_route_planning = true and route4me_member_id > 0", tcId);
            LambdaQueryWrapper<NbDriverEntity> wrapper = Wrappers.lambdaQuery(NbDriverEntity.class);
            wrapper.eq(NbDriverEntity::getAuditStatus, DriverDto.AUDIT_STATUS_3_PASS)
                    .eq(NbDriverEntity::getIsValid, true)
                    .eq(NbDriverEntity::getTcId, tcId)
                    .eq(NbDriverEntity::getAutoRoutePlanning, true)
                    .gt(NbDriverEntity::getRoute4meMemberId, 0);
            List<NbDriverEntity> drivers = nbDriverService.list(wrapper);

            NbTransferCenterEntity tc = nbTransferCenterMapper.selectById(tcId);
            JSONObject item = new JSONObject();
            //转运中心
            item.put("tc", tc);
            //该tcid下的司机信息列表
            item.put("drivers", drivers);
            //该tcid下的订单信息
            item.put("orders", tcOrders);

            //包含转运中心、司机、订单的分组
            ja.add(item);
        }

        //查询有效的分拣中心
        //  select * from nb_sorting_center where is_valid = true
        LambdaQueryWrapper<NbSortingCenterEntity> wrapper = Wrappers.lambdaQuery(NbSortingCenterEntity.class);
        wrapper.eq(NbSortingCenterEntity::getIsValid, true);
        List<NbSortingCenterEntity> scList = nbSortingCenterMapper.selectList(wrapper);

        com.alibaba.fastjson.JSONArray scJa = new com.alibaba.fastjson.JSONArray();

        for (NbSortingCenterEntity entity : scList) {
            //把分拣中心部分信息转为JSON
            JSONObject jo = new JSONObject();
            jo.set("scId", entity.getScId());
            jo.set("centerName", entity.getCenterName());
            jo.set("address", entity.getAddress());
            jo.set("postalCode", entity.getPostalCode());
            //国家-根据id查询对应国家名称
            jo.set("country", CommonDataUtil.getCountryById(entity.getCountryId()));
            //省份-根据id查询对应国省份名称
            jo.set("province", CommonDataUtil.getProvinceById(entity.getProvinceId()));
            //城市-根据id查询对应城市名称
            jo.set("city", CommonDataUtil.getCityById(entity.getCityId()));
            jo.set("scCode", entity.getScCode());
            scJa.add(jo);
        }

        JSONObject ret = new JSONObject();
        //批次号订单
        ret.set("batch", batch);
        //批次相应转运中心的司机、订单的分组
        ret.set("tcGroup", ja);
        //分拣中心
        ret.set("sortingCenters", scJa);

        log.info(ret.toString());
        return R.ok(ret);
    }

    /**
     * 按一个转运中心进行路径规划--基于手动规划（route4me.html）
     */
    @Override
    public R optimizationByTc(OrderBatchRouteVo vo) {
        //时间
        String timeOrSize = vo.getTimeOrSize();
        //批次id
        Integer batchId = vo.getBatchId();
        //开始转运中心
        Integer startTcId = vo.getTcId();
        //开始分拣中心
        Integer startScId = vo.getScId();
        //批量转运中心id
        String tcIdsStr = vo.getTcIds();
        String[] tcIdsStrArr = tcIdsStr.split(",");
        List<Integer> tcIds = new ArrayList<>();
        for (String tcIdStr : tcIdsStrArr) {
            tcIds.add(Integer.valueOf(tcIdStr));
        }

        NbOrderBatchEntity ob = getById(batchId);
        if (ob == null) {
            return LocalizedR.failed("nborderbatchrouteno.batch.not.exist", Optional.ofNullable(null));
        }
        if (ob.getIsRouted()) {
            return LocalizedR.failed("nborderbatchrouteno.have.planned", Optional.ofNullable(null));
        }

        String routeName = null;
        NbTransferCenterEntity startTc = null;
        String timezone = null;
        Integer scId = null;

        if (startTcId != null && startTcId > 0) {
            startTc = nbTransferCenterMapper.selectById(startTcId);
            if (startTc == null) {
                return LocalizedR.failed("nborderbatchrouteno.start.transit.center.does.not.exist", Optional.ofNullable(null));
            }

            //根据开始转运中心查询出对应的分拣中心
            NbSortingCenterEntity sc = nbSortingCenterMapper.selectById(startTc.getScId());
            //将分拣中心代码赋值给routeName
            routeName = sc.getScCode();
            //将分拣中心-时区赋值给timezone
            timezone = sc.getScTimezone();
            scId = sc.getScId();
        }

        NbSortingCenterEntity startSc = null;
        if (startScId != null && startScId > 0) {
            startSc = nbSortingCenterMapper.selectById(startScId);
            if (startSc == null) {
                return LocalizedR.failed("nborderbatchrouteno.start.transit.center.does.not.exist", Optional.ofNullable(null));

            }

            routeName = startSc.getScCode();
            timezone = startSc.getScTimezone();
            scId = startSc.getScId();
        }

        if (startTc == null && startSc == null) {
            return LocalizedR.failed("nborderbatchrouteno.sorting.center.or.transshipment.center.choose.at.least.one", Optional.ofNullable(null));
        }

        //拿到批量tcid中每个tcid的订单
        List<NbOrderEntity> allOrders = new ArrayList<>();
        for (Integer tcId : tcIds) {
            NbTransferCenterEntity tc = nbTransferCenterMapper.selectById(tcId);
            if (tc == null) {
                return LocalizedR.failed("nborderbatchrouteno.transit.center.does.not.exist", Optional.ofNullable(null));
            }

            //判断批次是主/子，对订单表进行获取相对应的订单
            List<NbOrderEntity> orders;
            if (ob.getIsMain()) {
                //  select * from nb_order where batch_no = ? and tc_id = ? and order_status = ? and is_routed = false and express_type in (0,1)", ob.getBatchNo(), tcId, Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的主批次
                LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
                wrapper.eq(NbOrderEntity::getBatchNo, ob.getBatchNo())
                        .eq(NbOrderEntity::getTcId, tcId)
                        .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                        .eq(NbOrderEntity::getIsRouted, false)
                        .in(NbOrderEntity::getExpressType, 0, 1);
                orders = nbOrderMapper.selectList(wrapper);
            } else {
                //  select * from nb_order where sub_batch_no = ? and tc_id = ? and order_status = ? and is_routed = false and express_type in (0,1)", ob.getBatchNo(), tcId, Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的子批次
                LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
                wrapper.eq(NbOrderEntity::getSubBatchNo, ob.getBatchNo())
                        .eq(NbOrderEntity::getTcId, tcId)
                        .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                        .eq(NbOrderEntity::getIsRouted, false)
                        .in(NbOrderEntity::getExpressType, 0, 1);
                orders = nbOrderMapper.selectList(wrapper);
            }
            //追加每个转运中心下面的订单至allOrders
            allOrders.addAll(orders);
        }

        //---------进行route4meSelectParamsd的处理
        String route4meSelectParams = vo.getRoute4meSelectParams();
        JSONObject params = JSONUtil.parseObj(route4meSelectParams);
        Integer routeMaxDuration = params.getInt("routeMaxDuration");
        //size怎么来的？？？？
        if (timeOrSize.equals("size")) {
            routeMaxDuration = null; // 20230826 时间和数量只能二选一
        }

        if (routeMaxDuration == null) {
            //路区最大时长（秒）
//            routeMaxDuration = ConfigDto.findIntegerByKey(ConfigDto.r4m_route_max_duration);
            routeMaxDuration = configUtil.findIntegerByKey(ConfigUtil.r4m_route_max_duration);
        } else {
            routeMaxDuration = routeMaxDuration * 60;
        }


        String balance = "distance";
        String algorithmType = "3";
        String optimize = "Distance";
        Integer maxTourSize = params.getInt("maxTourSize");
        if (timeOrSize.equals("time")) {
            maxTourSize = null;
        }

        Boolean isRt = params.getBool("isRt");
        Boolean isDynamicStartTime = params.getBool("isDynamicStartTime");
        JSONArray driverIds = params.getJSONArray("driverIds");

        //路线规划参数
        Parameters innerPara = new Parameters();
        innerPara.setAlgorithmType(algorithmType);
        innerPara.setRouteMaxDuration(routeMaxDuration); // How many seconds a route can last at most. Default is 24 hours = 86400 seconds

        if (StrUtil.isNotBlank(optimize)) {
            innerPara.setOptimize(optimize.trim()); // "Distance", "Time", "timeWithTraffic"
        }

        ZonedDateTime now = null;
        String today = null;
        String yyyyMMdd = null;
        if (StrUtil.isNotBlank(timezone)) {
            now = ZonedDateTime.now();
        } else {
            now = ZonedDateTime.now(ZoneId.of(timezone));
        }
        today = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        yyyyMMdd = now.format(DateTimeFormatter.ofPattern("yyMMdd"));

        routeName += ("-" + yyyyMMdd);

        //转运批次(路区)
        //  select count(1) cont from nb_transfer_batch where owner_sc_id = ? and add_time >= ? and add_time <= ?", scId, today + " 00:00:00", today + " 23:59:59").getInt("cont");
        LambdaQueryWrapper<NbTransferBatchEntity> qw = Wrappers.lambdaQuery(NbTransferBatchEntity.class);
        qw.eq(NbTransferBatchEntity::getOwnerScId, scId)
                .ge(NbTransferBatchEntity::getAddTime, today + " 00:00:00")
                .le(NbTransferBatchEntity::getAddTime, today + " 23:59:59");
        int todayLQTotal = Math.toIntExact(nbTransferBatchService.count(qw));

        log.info("当前路区数量:" + todayLQTotal + ",today=" + today + ",owner_sc_id=" + scId + ",timezone=" + timezone);

        routeName += ("-" + String.format("%02d", todayLQTotal));
        innerPara.setRouteName(routeName); // 20230829 修改路区命名规则

        innerPara.setTravelMode(Constants.TravelMode.DRIVING.toString()); // ["Driving", "Walking", "Bicycling"],
        innerPara.setDistanceUnit(Constants.DistanceUnit.MI.toString());
        innerPara.setIsDynamicStartTime(isDynamicStartTime);
        innerPara.setRt(isRt);

        if (maxTourSize != null) {
            innerPara.setMaxTourSize(maxTourSize);
        }
        if (StrUtil.isNotBlank(balance)) {
            innerPara.setBalance(new Balance(balance.trim()));
        }

        //查询出司机列表
        List<NbDriverEntity> drivers = new ArrayList<>();
        if (driverIds != null && driverIds.size() > 0) {
            //  select * from nb_driver where is_valid = true and driver_id in (" + StringUtils.join(driverIds, ',') + ")");
            LambdaQueryWrapper<NbDriverEntity> wrapper = Wrappers.lambdaQuery(NbDriverEntity.class);
            wrapper.eq(NbDriverEntity::getIsValid, true);
            wrapper.in(NbDriverEntity::getDriverId, driverIds);
            drivers = nbDriverService.list(wrapper);
        }

        //        User user = getUser();
        R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
        SysUser user = sysUserR.getData();
        /*
        *   batchId：可能是当前优化任务的批次或订单编号，用于区分或标识该优化任务。
            startTc 和 startSc：通常表示起点的坐标信息，可能是经纬度或其他位置信息。
            allOrders：包含所有待优化的订单或停靠点的信息。
            drivers：包含可用司机的信息，用于分配任务。
            innerPara：可能包含用于优化的参数配置，如时间窗、距离、容量限制等。
            false：通常为布尔值参数，可能用于设置是否需要立即执行或其他控制选项。
            user.getUserId().intValue()：代表当前用户的ID，用于区分或管理用户的优化请求。
            返回值：DataObject，可能包含优化后的路线详情（例如优化ID、优化结果、路径、时间等信息）。

            通过指定的批次号、订单、司机及其他参数，向 Route4Me 请求一次优化，生成最优路径。
        * */
        DataObject dataObject = route4MeUtil.createAnOptimization(batchId, startTc, startSc, allOrders, drivers, innerPara, false, user.getUserId().intValue());

        /*
         *   保存并记录优化的结果信息
         * */
        saveOptimizationResult(dataObject, ob, startTc, startSc, scId, today, now);

        NbOrderEntity existOrder;
        // 检查本批次是否全部完成
        if (ob.getIsMain()) {
            // "select * from nb_order where batch_no = ? and order_status = ? and is_routed = false and express_type in (0,1) limit 1", ob.getBatchNo(), Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的主批次
            LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
            wrapper.eq(NbOrderEntity::getBatchNo, ob.getBatchNo())
                    .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                    .eq(NbOrderEntity::getIsRouted, false)
                    .in(NbOrderEntity::getExpressType, 0, 1);
            existOrder = nbOrderMapper.selectOne(wrapper);
        } else {
            //  select * from nb_order where sub_batch_no = ? and order_status = ? and is_routed = false and express_type in (0,1) limit 1", ob.getBatchNo(), Order.ORDER_STATUS_200_PARCEL_SCANNED); // 扫描过的子批次
            LambdaQueryWrapper<NbOrderEntity> wrapper = Wrappers.lambdaQuery(NbOrderEntity.class);
            wrapper.eq(NbOrderEntity::getSubBatchNo, ob.getBatchNo())
                    .eq(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                    .eq(NbOrderEntity::getIsRouted, false)
                    .in(NbOrderEntity::getExpressType, 0, 1);
            existOrder = nbOrderMapper.selectOne(wrapper);
        }

        if (existOrder == null) {
            ob.setIsRouted(true);
            ob.setRoutedTime(new Date());
            ob.setIsScaning(0);
//            update();
            orderBatchMapper.updateById(ob);
        }
        return R.ok();
    }



    /*
     *  更新roture路径
     * */
    private void saveOptimizationResult(DataObject dataObject, NbOrderBatchEntity batch, NbTransferCenterEntity startTc, NbSortingCenterEntity startSc, Integer ownerScId, String today, ZonedDateTime now) {
        if (dataObject != null) {
            String problemId = dataObject.getOptimizationProblemId();
            Links links = dataObject.getLinks();

            Constants.OptimizationState os = Constants.OptimizationState.get(dataObject.getState().intValue());
            log.info("state:" + dataObject.getState() + "," + (os == null ? "-" : os.name()));

            List<Route> routes = dataObject.getRoutes();

            Map<String, List<Address>> route4meAddressMap = new HashMap<>();
            List<Address> route4meAddressList = dataObject.getAddresses();
            for (Address route4meAdderss : route4meAddressList) {
                if (route4meAdderss.getRouteId() == null) {
                    continue;
                }
                List<Address> innerList = route4meAddressMap.getOrDefault(route4meAdderss.getRouteId(), Lists.newArrayList());
                innerList.add(route4meAdderss);

                route4meAddressMap.put(route4meAdderss.getRouteId(), innerList);
            }

            String ymdStr = now.format(DateTimeFormatter.ofPattern("yyMMdd"));

            String fullDateTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date timezoneDate = null;
            try {
                timezoneDate = sdf.parse(fullDateTime);
            } catch (ParseException e1) {
                e1.printStackTrace();
            }

            List<R4mTobeUpdateRouter> tobeUpdateRouterList = new ArrayList<>();

            NbSortingCenterEntity sc = nbSortingCenterMapper.selectById(ownerScId);
            // "select count(1) cont from nb_transfer_batch where owner_sc_id = ? and add_time >= ? and add_time <= ?", ownerScId, today + " 00:00:00", today + " 23:59:59").getInt("cont");
            LambdaQueryWrapper<NbTransferBatchEntity> queryWrapper = Wrappers.lambdaQuery(NbTransferBatchEntity.class);
            queryWrapper.eq(NbTransferBatchEntity::getOwnerScId, ownerScId);
            queryWrapper.ge(NbTransferBatchEntity::getAddTime, today + " 00:00:00");
            queryWrapper.le(NbTransferBatchEntity::getAddTime, today + " 23:59:59");
            Long count = nbTransferBatchService.count(queryWrapper);
            int todayLQTotal = Math.toIntExact(count);
            for (Route route : routes) {
                log.info("处理Route:" + route);
                String routeView = route.getLinks().getRoute();

                int driverId = 0;
                Long memberId = route.getMemberId();
                if (memberId != null && memberId > 0) {
                    //  select * from nb_driver where route4me_member_id = ? limit 1
                    LambdaQueryWrapper<NbDriverEntity> wrapper = Wrappers.lambdaQuery(NbDriverEntity.class);
                    wrapper.eq(NbDriverEntity::getRoute4meMemberId, memberId);
                    NbDriverEntity driver = nbDriverService.getOne(wrapper);

                    if (driver != null) {
                        driverId = driver.getDriverId();
                    }
                }

                NbTransferBatchEntity tb = new NbTransferBatchEntity();
                tb.setBatchNo(sc.getScCode() + "-" + ymdStr + "-" + String.format("%02d", (todayLQTotal + 1)));
                tb.setAddTime(timezoneDate);
                tb.setDriverId(driverId);
                tb.setOrderTotal(route.getRoutePieces());

//                tb.setEstimatedHour(new BigDecimal(route.getPlannedTotalRouteDuration() / 60 / 60.0d).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                BigDecimal plannedDuration = BigDecimal.valueOf(route.getPlannedTotalRouteDuration());
                BigDecimal estimatedHour = plannedDuration.divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP); // 将总秒数转换为小时，保留两位小数
                tb.setEstimatedHour(estimatedHour);

                try {
                    // int hour = route.getPlannedTotalRouteDuration() / 3600;
                    // int min = (route.getPlannedTotalRouteDuration() % 3600) / 60;
                    // Double hour2 = Double.valueOf(hour + "." + min);
                    // tb.setEstimatedHour(hour2);
                    BigDecimal[] hourAndMinute = plannedDuration.divideAndRemainder(BigDecimal.valueOf(3600)); // 小时部分和剩余秒数
                    BigDecimal hour = hourAndMinute[0]; // 小时部分
                    BigDecimal minute = hourAndMinute[1].divide(BigDecimal.valueOf(60), 0, RoundingMode.FLOOR); // 转换为整分钟

                    // 合并小时和分钟为小数形式（例如：2.15 表示 2 小时 15 分钟）
                    BigDecimal hourWithMinute = hour.add(minute.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
                    tb.setEstimatedHour(hourWithMinute);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (driverId == 0) {
                    tb.setGetType(TransferBatchDto.GET_TYPE_2_SNATCH);
                } else {
                    tb.setGetType(TransferBatchDto.GET_TYPE_1_ALLOCATION);
                }

                tb.setBatchCode(tb.getBatchNo());
                tb.setTranferDriverId(0);
                if (startTc != null) {
                    tb.setTcId(startTc.getTcId()); // -> 这里在用
                }
                if (startSc != null) {
                    tb.setScId(startSc.getScId());
                }
                tb.setRouteId(route.getId());
                tb.setOptimizationProblemId(problemId);
                tb.setTripDistance(route.getTripDistance());
                tb.setPlannedTotalRouteDuration(route.getPlannedTotalRouteDuration());
                tb.setMemberId(route.getMemberId());
                tb.setRouteView(routeView);
                tb.setOwnerScId(ownerScId);
                tb.setOrderBatchId(batch.getBatchId());
                nbTransferBatchService.save(tb);

                Parameters parameters = route.getParameters();
                parameters.setRouteName(tb.getBatchNo());
                route.setParameters(parameters);

                List<Address> routeAddressList = route.getAddresses();
                int index = 1;
                for (Address address : routeAddressList) {
                    if (address.getDepot()) {
                        log.info("仓库跳过->" + address);
                        continue;
                    }

                    int sequenceNo = address.getSequenceNo().intValue();

                    Integer orderId = Integer.valueOf(address.getCustom_fields().get("__orderId").toString());

                    String pickNo = today.split("-")[2] + "-" + String.format("%02d", (todayLQTotal + 1)) + "-" + String.format("%02d", sequenceNo);

                    NbTransferBatchOrderEntity tbo = new NbTransferBatchOrderEntity();
                    tbo.setBatchId(tb.getBatchId());
                    tbo.setOrderId(orderId);
                    tbo.setPickNo(pickNo);
                    tbo.setRouteDestinationId(address.getRouteDestinationId());
                    tbo.setMemberId(address.getMemberId().longValue());
                    tbo.setRouteId(address.getRouteId());
                    tbo.setOptimizationProblemId(address.getOptimizationProblemId());
                    tbo.setSequenceNo(address.getSequenceNo().intValue());
                    tbo.setChannelName(address.getChannelName());
                    tbo.setDriveTimetoNextDestination(address.getDriveTimetoNextDestination());
                    tbo.setTrackingNumber(address.getTrackingNumber());
                    nbTransferBatchOrderMapper.insert(tbo);

                    address.setAlias(pickNo);

                    //  NbOrderEntity order = nbOrderMapper.selectById(tbo.getOrderId());
                    LambdaUpdateWrapper<NbOrderEntity> updateWrapper = Wrappers.lambdaUpdate(NbOrderEntity.class);
                    updateWrapper.eq(NbOrderEntity::getOrderId, tbo.getOrderId());
                    //  order.setPickNo(tbo.getPickNo());
                    updateWrapper.set(NbOrderEntity::getPickNo, tbo.getPickNo());
                    if (driverId > 0) {
                        //  order.setDriverId(driverId);
                        updateWrapper.set(NbOrderEntity::getDriverId, driverId);
                    }
                    //  order.setIsRouted(true);
                    updateWrapper.set(NbOrderEntity::getIsRouted, true);
                    nbOrderMapper.update(updateWrapper);

                    index ++;
                }

                tobeUpdateRouterList.add(new R4mTobeUpdateRouter(route));
                todayLQTotal++;
            }

            // 开始更新
            route4MeUtil.updateRoute(tobeUpdateRouterList);
        }
    }


    private void wsMessage(String batchId, int current, Integer total, String errmsg) {
        try {
            com.alibaba.fastjson.JSONObject wsData = new com.alibaba.fastjson.JSONObject();
            wsData.put("batchId", batchId);
            wsData.put("current", current);
            wsData.put("total", total);
            if (StrUtil.isNotBlank(errmsg)) {
                wsData.put("errmsg", errmsg);
            }
            // Tio.sendToGroup(WsKit.use(), "_syncToRoute4meOrder." + batchId, WsResponse.fromText(wsData.toString(), "UTF-8"));
            R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());
            SysUser user = sysUserR.getData();
            String username = user.getUsername();
            WebSocketMessageSender.send(username, wsData.toString());
            log.info("sessionId {} ,msg {}", username, wsData);
        } catch (Exception e) {
            e.printStackTrace();

        }
    }

    /**
     * 订单批次按路线规划    --执行规划和返回数据阶段
     */
    public  Map<String, List<Address>> process(String batchNo) {
        log.info("【规划】订单批次路径规划于:{}，输入参数{}", LocalDateTime.now(), "运行中");
        //order_total = batch_total  ： 实际扫描数量=批次期望数量    is_routed:是否规划
        List<NbOrderBatchEntity> batches  = orderBatchService.findNoRouted(batchNo);  // 盲扫齐全，立马规划
        //返回路线地址集合
        Map<String, List<Address>> AddressMap = null;
        if (batches != null && !batches.isEmpty()) {
            for (NbOrderBatchEntity batch : batches) {
                // "select * from nb_order where batch_no = ?", batch.getBatchNo());
                List<NbOrderEntity> orders = nbOrderMapper.selectList(new LambdaQueryWrapper<NbOrderEntity>().eq(NbOrderEntity::getBatchNo, batch.getBatchNo()).or().eq(NbOrderEntity::getSubBatchNo, batch.getBatchNo()));
                Map<Integer, List<NbOrderEntity>> orderMap = new HashMap<>();
                for (NbOrderEntity order : orders) {
                    Integer tcId = order.getTcId();
                    if (tcId == null || tcId == 0) {
                        log.info("发现有未分配转运中心的订单，暂时不规划： " + order.getOrderId());

                        batch.setRoutedErrmsg("发现有未分配转运中心的订单，暂时不规划： " + order.getOrderId());
                        orderBatchMapper.updateById(batch);
                    }

                    List<NbOrderEntity> tcOrders;
                    if (orderMap.containsKey(tcId)) {
                        tcOrders = orderMap.get(tcId);
                    } else {
                        tcOrders = Lists.newArrayList();
                    }

                    tcOrders.add(order);
                    //转运中心订单分组
                    orderMap.put(tcId, tcOrders);
                }

                //路径规划参数区域----------------------------------------------
                //用于在优化路线时平衡距离和时效（时间）的优先级 true：启用平衡模式。系统会在距离最短和时间最短之间寻找一个折中点. false（默认）：优化目标是单一的（通常是距离最短或时间最短）。
                String balance = "true";
                /*
                *   优化算法类型1: 单条优化路线（Single Driver Route）。
                    2: 多条优化路线（Multiple Driver Route）。
                    3: 驾驶距离矩阵生成（Distance Matrix Generation）。
                    4: 驾驶时长矩阵生成（Time Matrix Generation）。
                    5: 圆形路线优化（TSP Circle Route Optimization）。
                    6: 时间窗优化（TSP Time Windows Route Optimization）。
                    7: 混合优化（Mixed Fleet Optimization）。
                    * 注：多个司机的路线，使用 algorithmType: 2。如果仅需计算距离矩阵，可选择 algorithmType: 3 或 4
                * */
                String algorithmType = "3";
                /*
                *   指定优化目标，即系统优先优化的指标。  配合balance使用
                * "Distance"：优先优化路线的总行驶距离。
                    "Time"：优先优化总耗时。
                    "Balanced"：在距离和时间之间寻找一个折中点（需要启用 balance: true）
                * */
                String optimize = "Distance";
                //最大停靠数
                //Integer maxTourSize = 50;
                //是否启用实时优化功能
                Boolean isRt = false;
                //启用起始时间
                Boolean isDynamicStartTime = false;

                //路线规划参数
                Parameters innerPara = new Parameters();
                innerPara.setAlgorithmType(algorithmType);
                //一条路线的最大持续时间
                innerPara.setRouteMaxDuration(86400); // How many seconds a route can last at most. Default is 24 hours = 86400 seconds
                //innerPara.setRouteName(routeName); // 20230829 修改路区命名规则
                innerPara.setTravelMode(Constants.TravelMode.DRIVING.toString()); // ["Driving", "Walking", "Bicycling"],
                innerPara.setDistanceUnit(Constants.DistanceUnit.MI.toString());
                innerPara.setIsDynamicStartTime(isDynamicStartTime);
                innerPara.setRt(isRt);

                if (StrUtil.isNotBlank(balance)) {
                    innerPara.setBalance(new Balance(balance.trim()));
                }
                //------------------------------------------------------------参数结束


                if (StrUtil.isNotBlank(optimize)) {
                    innerPara.setOptimize(optimize.trim()); // "Distance", "Time", "timeWithTraffic"
                }

                Iterator<Map.Entry<Integer, List<NbOrderEntity>>> iter = orderMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry<Integer, List<NbOrderEntity>> entry = iter.next();
                    //转运中心
                    int tcId = entry.getKey();
                    //转运中心下对应的订单列表
                    List<NbOrderEntity> tcOrders = entry.getValue();
                    /*
                     *      查询当前转运中心下的 audit_status = 3：司机审核状态已通过      auto_route_planning = true：自动路径规划表示是        route4me_member_id> 0：route成员大于0 的列表
                     * */
                    // "select * from nb_driver where audit_status = 3 and tc_id = ? and auto_route_planning = true and route4me_member_id > 0", tcId);
                    LambdaQueryWrapper<NbDriverEntity> driverWrapper = new LambdaQueryWrapper<>();
                    driverWrapper.eq(NbDriverEntity::getAuditStatus, 3)
                            .eq(NbDriverEntity::getTcId, tcId)
                            .eq(NbDriverEntity::getAutoRoutePlanning, true)
                            .gt(NbDriverEntity::getRoute4meMemberId, 0);
                    List<NbDriverEntity> drivers = nbDriverService.list(driverWrapper);
                    NbTransferCenterEntity tc = nbTransferCenterMapper.selectById(tcId);

                    //创建路线并返回路线规划数据
                    DataObject dataObject = null;
                    try {
                        dataObject = route4MeUtil.createAnOptimization(batch.getBatchId(), tc, null, tcOrders, drivers, innerPara, true, 0);
                    } catch (HttpClientErrorException e) {
                        log.error("批次规划路径失败: batchId=" + batch.getBatchId() + ", 错误信息: " + e.getMessage(), e);
                        // 将详细的异常信息抛给外部调用者
                        throw new RuntimeException(e.getMessage(), e);
                    } catch (Exception e) {
                        log.error("批次路径规划失败: batchId=" + batch.getBatchId() + ", 错误信息: " + e.getMessage(), e);
                        // 将其他异常抛给外部调用者
                        throw new RuntimeException(e.getMessage(), e);
                    }

                    if (dataObject != null) {
                        log.info("-----------------------------------------批次" + batchNo + "路线规划创建成功-------------------------------------------");
                        //优化问题id
                        String problemId = dataObject.getOptimizationProblemId();
                        //接口地址链接
                        Links links = dataObject.getLinks();

                        //根据传入的整数值来查找并返回对应的 OptimizationState 枚举常量       dataObject.getState().intValue():获取优化任务的状态，并将其转换为整数值
                        Constants.OptimizationState os = Constants.OptimizationState.get(dataObject.getState().intValue());
                        String state = Constants.OptimizationState.get(dataObject.getState().intValue()).name();
                        log.info("state:" + dataObject.getState() + "," + (os == null ? "-" : os.name()));

                        //获取路线
                        List<Route> routes = dataObject.getRoutes();
                        //[Route(id=EEEEE061D44BA5547097462ED286E0FA, memberId=2623213, memberEmail=<EMAIL>, isUnrouted=false, vehicleAlias=null, routeCost=0.0, routeRevenue=0.0, netRevenuePerDistanceUnit=0.0, created_timestamp=1687270911, mpg=10.0, tripDistance=200.44, gasPrice=2.0, routeDurationSec=21057, plannedTotalRouteDuration=26157, uduDistanceUnit=km, uduTripDistance=322.58, routeWeight=118.63, routeCube=0.0, routePieces=17, totalWaitTime=0, uduActualTravelDistance=0.0, actualTravelDistance=0.0, actualFootsteps=0, workingTime=0, drivingTime=0, idlingTime=0, payingMiles=0.0, destinationCount=17, notesCount=0, actualTravelTime=0, vehicle=null, routeDirections=[], notes=[], path=[], uniqueDestinationCount=17)]

                        //获取到路线中的信息，并将routeid和路线信息进行分组绑定
                        Map<String, List<Address>> route4meAddressMap = new HashMap<>();
                        List<Address> route4meAddressList = dataObject.getAddresses();
                        for (Address route4meAdderss : route4meAddressList) {
                            if (route4meAdderss.getRouteId() == null) {
                                continue;
                            }
                            List<Address> innerList = route4meAddressMap.getOrDefault(route4meAdderss.getRouteId(), Lists.newArrayList());
                            innerList.add(route4meAdderss);

                            route4meAddressMap.put(route4meAdderss.getRouteId(), innerList);
                        }


                        //插入规划id
                        batch.setOptimizationProblemId(problemId);
                        //是否规划，改变成true
                        batch.setIsRouted(true);
                        //插入规划时间
                        batch.setRoutedTime(new Date());
                        //规划返回提示
                        batch.setRoutedErrmsg("");
                        //插入route4me view
                        batch.setBatchView(links.getView());
                        orderBatchMapper.updateById(batch);

                        String ymdStr = DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss");
                        String today = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd");
                        String startTime = today + " 00:00:00";
                        String endTime = today + " 23:59:59";

                        // "select count(1) cont from nb_transfer_batch where add_time >= ? and add_time <= ?", today + " 00:00:00", today + " 23:59:59").getInt("cont");
                        LambdaQueryWrapper<NbTransferBatchEntity> wrapper = new LambdaQueryWrapper<>();
                        wrapper.ge(NbTransferBatchEntity::getAddTime, startTime)
                                .le(NbTransferBatchEntity::getAddTime, endTime);
                        Long count = nbTransferBatchService.count(wrapper);
                        int todayLQTotal = Math.toIntExact(count);

                        //返回路线地址集合
                        AddressMap = new HashMap<>();
                        for (Route route : routes) {
                            log.info("处理Route:" + route);
                            String routeView = route.getLinks().getRoute();

                            int driverId = 0;
                            Long memberId = route.getMemberId();
                            if (memberId != null && memberId > 0) {
                                // "select * from nb_driver where route4me_member_id = ? limit 1", memberId);
                                LambdaQueryWrapper<NbDriverEntity> wrapper1 = new LambdaQueryWrapper<>();
                                wrapper1.eq(NbDriverEntity::getRoute4meMemberId, memberId);
                                NbDriverEntity driver = nbDriverService.getOne(wrapper1, false);
                                if (driver != null) {
                                    driverId = driver.getDriverId();
                                }
                            }

                            NbTransferBatchEntity tb = new NbTransferBatchEntity();
                            tb.setBatchNo("LQ" + ymdStr + String.valueOf(todayLQTotal) + batch.getBatchId());
                            tb.setAddTime(new Date());
                            tb.setStartTime(new Date());            // 路区开始时间
                            tb.setCompletedTotal(todayLQTotal);     // 完成数量
                            tb.setDriverId(driverId);
                            tb.setOrderTotal(route.getRoutePieces());
                            // tb.setEstimatedHour(new BigDecimal(route.getPlannedTotalRouteDuration() / 60 / 60.0d).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                            // 使用 BigDecimal 计算预估小时
                            BigDecimal plannedDuration = BigDecimal.valueOf(route.getPlannedTotalRouteDuration()); // 总时长（秒）
                            BigDecimal estimatedHour = plannedDuration
                                    .divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_UP); // 将总秒数转换为小时，保留两位小数
                            tb.setEstimatedHour(estimatedHour);

                            //1分配，2抢单
                            if (driverId == 0) {
                                tb.setGetType(2);
                            } else {
                                tb.setGetType(1);
                            }

                            tb.setBatchCode(tb.getBatchNo());
                            tb.setTranferDriverId(0);
                            tb.setTcId(tc.getTcId());
                            tb.setOwnerScId(tc.getScId());
                            tb.setRouteId(route.getId());
                            tb.setOptimizationProblemId(problemId);
                            tb.setTripDistance(route.getTripDistance());
                            tb.setPlannedTotalRouteDuration(route.getPlannedTotalRouteDuration());
                            tb.setMemberId(route.getMemberId());
                            tb.setRouteView(routeView);
                            nbTransferBatchService.save(tb);

                            List<Address> routeAddressList = route.getAddresses();
                            int index = 1;
                            for (Address address : routeAddressList) {
                                //Depot仓库
                                if (address.getDepot()) {
                                    log.info("仓库跳过->" + address);
                                    continue;
                                }
                                //String alias = address.getAlias();
                                Integer orderId = Integer.valueOf(address.getCustom_fields().get("__orderId").toString());

                                //路区订单
                                NbTransferBatchOrderEntity tbo = new NbTransferBatchOrderEntity();
                                tbo.setBatchId(tb.getBatchId());
                                tbo.setOrderId(orderId);
                                tbo.setPickNo(today.split("-")[2] + "-" + String.format("%02d", (todayLQTotal + 1)) + "-" + String.format("%02d", index));
                                tbo.setRouteDestinationId(address.getRouteDestinationId());
                                tbo.setMemberId(address.getMemberId().longValue());
                                tbo.setRouteId(address.getRouteId());
                                tbo.setOptimizationProblemId(address.getOptimizationProblemId());
                                tbo.setSequenceNo(Math.toIntExact(address.getSequenceNo()));
                                tbo.setChannelName(address.getChannelName());
                                tbo.setDriveTimetoNextDestination(address.getDriveTimetoNextDestination());
                                tbo.setTrackingNumber(address.getTrackingNumber());
                                nbTransferBatchOrderMapper.insert(tbo);

                                NbOrderEntity order = orderMapper.selectById(tbo.getOrderId());
                                order.setPickNo(tbo.getPickNo());
                                if (driverId > 0) {
                                    order.setDriverId(driverId);
                                }
                                orderMapper.updateById(order);

                                index++;
                            }
                            todayLQTotal++;
                            AddressMap.put(route.getId(), routeAddressList);
                        }
                        log.info("Route4Me数据处理完成-------------------------------------");
                    }
                }
            }
        }

        return AddressMap;
    }


}
