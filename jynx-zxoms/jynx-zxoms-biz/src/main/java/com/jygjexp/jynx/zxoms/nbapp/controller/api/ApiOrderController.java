package com.jygjexp.jynx.zxoms.nbapp.controller.api;

import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.zxoms.nbapp.controller.BaseController;
import com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton.AtomApi;
import com.jygjexp.jynx.zxoms.nbapp.vo.*;
import com.jygjexp.jynx.zxoms.send.service.NbOrderBatchService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbPreAlertBatchService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * @Author: chenchang
 * @Description: 接口用的api
 * @Date: 2024/11/6 17:34
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/order")
@Tag(description = "apporder", name = "APP-接口用的api")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ApiOrderController extends BaseController {
    private final NbOrderBatchService orderBatchService;
    private final NbOrderService orderService;
    private final NbPreAlertBatchService preAlertBatchService;

    /**
     * 创建订单
     */
    @Operation(summary = "API-创建订单", description = "API-创建订单")
    @Inner(value = false)
    @PostMapping("/add")
    public OldResult add(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @Validated @ModelAttribute ApiOrderAddVo vo) {
        return orderService.add(paramsVo, vo);
    }

    /**
     * 更新订单状态
     */
    @Operation(summary = "API-更新订单状态", description = "API-更新订单状态")
    @PostMapping("/updateState")
    @Inner(value = false)
    @Transactional(rollbackFor = Exception.class)
    public OldResult updateState(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @RequestParam("pkgNo") @NotBlank(message = "包裹编号不能为空") String pkgNo,
                                 @RequestParam("state") @Parameter(description = "状态-CUSTOM_RELEASE_DIRECT(198)/GATEWAY_TRANSIT(199)") @NotBlank(message = "状态不能为空") String state) {
        return orderService.updateState(paramsVo, pkgNo, state);
    }

    /**
     * 小包获取轨迹
     */
    @Operation(summary = "小包获取轨迹", description = "小包获取轨迹")
    @PostMapping("/track")
    @Inner(value = false)
    public OldResult track(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @RequestParam("pkgNo") @NotBlank(message = "包裹编号不能为空") String pkgNo) {
        return orderService.track(paramsVo, pkgNo);
    }

    /**
     * 小包批量获取轨迹
     */
    @Operation(summary = "小包批量获取轨迹", description = "小包批量获取轨迹")
    @PostMapping("/tracks")
    @Inner(value = false)
    public OldResult tracks(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @RequestParam("pkgNos") @Parameter(description = "包裹号，多个用逗号分割") @NotBlank String pkgNos) {
        return orderService.tracks(paramsVo, pkgNos);
    }

    /**
     * 创建Pre-alert batch
     */
    @Transactional(rollbackFor = Exception.class)
    @AtomApi(params = "appId")
    @Operation(summary = "APP-创建Pre-alert batch", description = "APP-创建Pre-alert batch")
    @PostMapping("/createPreAlertBatch")
    @Inner(value = false)
    public OldResult createPreAlertBatch(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @RequestParam("body") @NotBlank(message = "json不能空") String body) {
        return preAlertBatchService.createPreAlertBatch(paramsVo, body);
    }

    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "APP-取消Pre-alert batch", description = "APP-取消Pre-alert batch")
    @PostMapping("/cancelPreAlertBatch")
    @Inner(value = false)
    public OldResult cancelPreAlertBatch(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @RequestParam("json") @NotBlank(message = "json不能是空") String json) {
        return preAlertBatchService.cancelPreAlertBatch(paramsVo, json);
    }

    /**
     * 获取面单
     */
    @Operation(summary = "APP-获取面单", description = "APP-获取面单")
    @PostMapping("/label")
    @Inner(value = false)
    public OldResult label(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @RequestParam("pkgNo") @NotBlank(message = "包裹编号不能为空") String pkgNo, HttpServletResponse response) {
        return orderService.label(paramsVo, pkgNo, response);
    }

    /**
     * 更新批次信息
     */
    @Operation(summary = "APP-更新批次信息", description = "APP-更新批次信息")
    @PostMapping("/updateBatch")
    @Inner(value = false)
    public OldResult updateBatch(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @Valid @ModelAttribute ApiOrderUpdateVo vo) {
        return orderBatchService.updateBatch(paramsVo, vo);
    }

    /**
     * 批量拉取POD
     */
    @Operation(summary = "APP-批量拉取POD", description = "APP-批量拉取POD")
    @PostMapping("/pods")
    @Inner(value = false)
    public OldResult pods(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @Parameter(description = "包裹编号-多个用逗号分割") @RequestParam("pkgNos") @NotBlank(message = "包裹编号不能为空") String pkgNos) {
        return orderService.pods(paramsVo, pkgNos);
    }

    /**
     * 获取佳邮跟踪号
     */
    @Operation(summary = "APP-获取佳邮跟踪号", description = "APP-获取佳邮跟踪号")
    @PostMapping("/getJyTrackingNumber")
    @Inner(value = false)
    public OldResult getJyTrackingNumber(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @RequestParam("orderNo") @NotBlank(message = "orderNo can not be empty") String orderNo) {
        return orderService.getJyTrackingNumber(paramsVo, orderNo);
    }

    /**
     * 添加轨迹
     */
    @Inner(value = false)
    @Operation(summary = "API-添加轨迹", description = "API-添加轨迹")
    @PostMapping("/addPath")
    @Transactional(rollbackFor = Exception.class)
    public OldResult addPath(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @Valid @ModelAttribute ApiOrderAddPathVo vo) {
        return orderService.addPath(paramsVo, vo);
    }

    /**
     * 更新订单主批次
     */
    @Inner(value = false)
    @Operation(summary = "APP-更新订单主批次", description = "APP-更新订单主批次")
    @PostMapping("/updateOrderBatchNo")
    @AtomApi(params = {"appId", "batchNo"})
    public OldResult updateOrderBatchNo(@Validated @ModelAttribute ApiRequestParamsVo paramsVo, @RequestParam("batchNo") @NotBlank(message = "批次号不能为空") String batchNo,
                                         @RequestParam("pkgNos") @Parameter(description = "包裹编号,多个用逗号分割") @NotBlank(message = "包裹编号不能为空") String pkgNos) {
        return orderBatchService.updateOrderBatchNo(paramsVo, batchNo, pkgNos);
    }

}
