package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbConfigEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbConfigMapper;
import com.jygjexp.jynx.zxoms.send.service.NbConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
/**
 * 单位配置项
 *
 * <AUTHOR>
 * @date 2024-10-11 17:41:12
 */
@Service
@RequiredArgsConstructor
public class NbConfigServiceImpl extends ServiceImpl<NbConfigMapper, NbConfigEntity> implements NbConfigService {
    private final NbConfigMapper nbConfigMapper;

    @Override
    public NbConfigEntity findByCkey(String key) {
        // findFirst("select * from nb_config where `c_key` = ?", key);
        return nbConfigMapper.selectOne(new LambdaQueryWrapper<NbConfigEntity>().eq(NbConfigEntity::getCKey, key));
    }
}