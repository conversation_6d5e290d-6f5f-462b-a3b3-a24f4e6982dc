package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbOrderStatEntity;
import com.jygjexp.jynx.zxoms.vo.OrderStatPageVo;

import java.util.List;

public interface NbOrderStatService extends IService<NbOrderStatEntity> {
    // 订单统计分页查询
    Page<OrderStatPageVo> orderStat(Page page, OrderStatPageVo vo);

}