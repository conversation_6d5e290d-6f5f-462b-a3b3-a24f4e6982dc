package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.api.feign.RemoteReturnService;
import com.jygjexp.jynx.zxoms.dto.SmsLogDto;
import com.jygjexp.jynx.zxoms.dto.SmsTemplateDto;
import com.jygjexp.jynx.zxoms.entity.NbCustomerAddressEntity;
import com.jygjexp.jynx.zxoms.entity.NbSmsLogEntity;
import com.jygjexp.jynx.zxoms.entity.NbSmsTemplateEntity;
import com.jygjexp.jynx.zxoms.entity.ReturnSmsOrderVo;
import com.jygjexp.jynx.zxoms.send.mapper.NbCustomerAddressMapper;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.ALiYunSms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 客户地址
 *
 * <AUTHOR>
 * @date 2024-11-11 23:41:51
 */
@Service
public class NbCustomerAddressServiceImpl extends ServiceImpl<NbCustomerAddressMapper, NbCustomerAddressEntity> implements NbCustomerAddressService {

    @Autowired
    private NbSmsTemplateService nbSmsTemplateService;
    @Autowired
    private NbSmsLogService nbSmsLogService;
    @Autowired
    private RemoteReturnService returnSmsService;

    //发送短信
    @Override
    public Boolean sendSms(String phone,String postName,String orderNo) {
        NbSmsTemplateEntity st = nbSmsTemplateService.getOne(new LambdaQueryWrapper<NbSmsTemplateEntity>().eq(NbSmsTemplateEntity::getTplKey, SmsTemplateDto.KEY_RETURN_POST_SMS).last(" limit 1"), false);
        String template = st.getContent();
            if (StringUtils.isNotBlank(phone)) {
                String msg = template.replace("#(orderNo)", orderNo)
                        .replace("#(postName)", postName)
                        .replace("#(date)", LocalDateTime.now().toString());
                System.out.println(msg);
                // 发送短信
                ALiYunSms kit = new ALiYunSms();
                R r;
                try {
                    r = kit.sentMes(phone, msg);  // 发送短信
                } catch (Exception e) {
                    System.err.println("短信发送失败: " + e.getMessage());
                    return false;  // 如果发送短信时出现异常，返回失败
                }
                NbSmsLogEntity sl = new NbSmsLogEntity();
                sl.setTemplateId(st.getTemplateId());
                sl.setOrderId(orderNo);
                sl.setMobile(phone);
                sl.setContent(msg);
                sl.setDriverId(0);
                sl.setSendTime(new Date());
                sl.setSmsStatus(r.isOk() ? SmsLogDto.SMS_STATUS_15_SEND_SUC : SmsLogDto.SMS_STATUS_20_SEND_FAIL);
                sl.setSmsType(SmsLogDto.SMS_TYPE_4_POST_IN);
                nbSmsLogService.save(sl);
        }
        return true;
    }
}