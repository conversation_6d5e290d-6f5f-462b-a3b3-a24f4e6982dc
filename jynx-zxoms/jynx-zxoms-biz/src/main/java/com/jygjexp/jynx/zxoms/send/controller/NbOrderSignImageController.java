package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderMpsEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderSignImageEntity;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.NbOrderMpsService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderSignImageService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * POD
 *
 * <AUTHOR>
 * @date 2024-10-18 00:13:19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrderSignImage" )
@Tag(description = "nbOrderSignImage" , name = "POD管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderSignImageController {

    private final  NbOrderSignImageService nbOrderSignImageService;
    private final NbOrderService nbOrderService;
    private final NbOrderMpsService nbOrderMpsService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbOrderSignImage POD
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('orderSignImage_view')" )
    public R getNbOrderSignImagePage(@ParameterObject Page page, @ParameterObject NbOrderSignImageEntity nbOrderSignImage) {
        return R.ok(nbOrderSignImageService.search(page, nbOrderSignImage));
    }


    /**
     * 通过id查询POD
     * @param imageId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{imageId}" )
    @PreAuthorize("@pms.hasPermission('orderSignImage_view')" )
    public R getById(@PathVariable("imageId" ) Integer imageId) {
        return R.ok(nbOrderSignImageService.getById(imageId));
    }

    /**
     * 新增POD
     * @param nbOrderSignImage POD
     * @return R
     */
    @Operation(summary = "新增POD" , description = "新增POD" )
    @SysLog("新增POD" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('orderSignImage_add')" )
    public R save(@RequestBody NbOrderSignImageEntity nbOrderSignImage) {
//        Record record = ac.record;
//        record.set("add_time", new Date());
//        record.set("staff_id", ac.UID());
        Integer orderId = nbOrderSignImage.getOrderId();
        NbOrderEntity order = nbOrderService.getById(orderId);
        if (order.getIsMps()) {
            NbOrderMpsEntity om = nbOrderMpsService.getById(order.getOrderId());
            if (om != null && om.getPOrderId() > 0) {
                return LocalizedR.failed("nborderSignImage.uploaded.images.to.the.main.order", Optional.ofNullable(null));
            }
        }
//        record.set("order_status", order.getOrderStatus());
//        record.set("delivery_status", order.getDeliveryStatus());

        return R.ok(nbOrderSignImageService.save(nbOrderSignImage));
    }

    /**
     * 修改POD
     * @param nbOrderSignImage POD
     * @return R
     */
    @Operation(summary = "修改POD" , description = "修改POD" )
    @SysLog("修改POD" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('orderSignImage_edit')" )
    public R updateById(@RequestBody NbOrderSignImageEntity nbOrderSignImage) {
        return R.ok(nbOrderSignImageService.updateById(nbOrderSignImage));
    }

    /**
     * 通过id删除POD
     * @param ids imageId列表
     * @return R
     */
    @Operation(summary = "通过id删除POD" , description = "通过id删除POD" )
    @SysLog("通过id删除POD" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('orderSignImage_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderSignImageService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbOrderSignImage 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('orderSignImage_export')" )
    public List<NbOrderSignImageEntity> export(NbOrderSignImageEntity nbOrderSignImage,Integer[] ids) {
        return nbOrderSignImageService.list(Wrappers.lambdaQuery(nbOrderSignImage).in(ArrayUtil.isNotEmpty(ids), NbOrderSignImageEntity::getImageId, ids));
    }
}