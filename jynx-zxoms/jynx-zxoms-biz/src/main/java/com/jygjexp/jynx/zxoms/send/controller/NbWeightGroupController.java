package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbPostalCodeGroupEntity;
import com.jygjexp.jynx.zxoms.entity.NbWeightGroupEntity;
import com.jygjexp.jynx.zxoms.send.service.NbPostalCodeGroupService;
import com.jygjexp.jynx.zxoms.send.service.NbWeightGroupService;
import com.jygjexp.jynx.zxoms.vo.NbWeightGroupVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 重量段组
 *
 * <AUTHOR>
 * @date 2025-01-09 13:49:59
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbWeightGroup" )
@Tag(description = "nbWeightGroup" , name = "重量段组管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbWeightGroupController {

    private final  NbWeightGroupService nbWeightGroupService;
    private final NbPostalCodeGroupService nbPostalCodeGroupService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbWeightGroup 重量段组
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightGroup_view')" )
    public R getNbWeightGroupPage(@ParameterObject Page page, @ParameterObject NbWeightGroupVo nbWeightGroup) {
        LambdaQueryWrapper<NbWeightGroupEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotBlank(nbWeightGroup.getNameZh()),NbWeightGroupEntity::getNameZh,nbWeightGroup.getNameZh())
                .like(StrUtil.isNotBlank(nbWeightGroup.getNameEn()),NbWeightGroupEntity::getNameEn,nbWeightGroup.getNameEn());
        //获取创建时间
        String time = nbWeightGroup.getCreateTimeVo();
        if (StringUtils.isNotBlank(time)) {
            String[] dates = time.split("-");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
            LocalDate startDate = LocalDate.parse(dates[0], formatter);
            LocalDate endDate = LocalDate.parse(dates[1], formatter);
            LocalDateTime startDateTime = startDate.atStartOfDay(); // 默认时间为 00:00:00
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59); // 默认时间为 23:59:59
            wrapper.between(NbWeightGroupEntity::getCreateTime,startDateTime,endDateTime);
        }
        wrapper.orderByDesc(NbWeightGroupEntity::getCreateTime);
        return R.ok(nbWeightGroupService.page(page, wrapper));
    }


    /**
     * 通过id查询重量段组
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightGroup_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(nbWeightGroupService.getById(id));
    }

    /**
     * 新增重量段组
     * @param nbWeightGroup 重量段组
     * @return R
     */
    @Operation(summary = "新增重量段组" , description = "新增重量段组" )
    @SysLog("新增重量段组" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightGroup_add')" )
    public R save(@RequestBody NbWeightGroupEntity nbWeightGroup) {
        return R.ok(nbWeightGroupService.save(nbWeightGroup));
    }

    /**
     * 修改重量段组
     * @param nbWeightGroup 重量段组
     * @return R
     */
    @Operation(summary = "修改重量段组" , description = "修改重量段组" )
    @SysLog("修改重量段组" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightGroup_edit')" )
    public R updateById(@RequestBody NbWeightGroupEntity nbWeightGroup) {
        return R.ok(nbWeightGroupService.updateById(nbWeightGroup));
    }

    /**
     * 通过id删除重量段组
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除重量段组" , description = "通过id删除重量段组" )
    @SysLog("通过id删除重量段组" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightGroup_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(nbWeightGroupService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 查询邮编分区、重量段下拉列表
     */
    @Operation(summary = "查询邮编分区、重量段下拉列表" , description = "查询邮编分区、重量段下拉列表" )
    @GetMapping("/selectDataList")
    public R<Map<String,Object>> selectDataList() {
        HashMap<String,Object> map = new HashMap<>();
        //查询邮编分区下拉列表
        LambdaQueryWrapper<NbPostalCodeGroupEntity> postWrapper = Wrappers.lambdaQuery();
        postWrapper.select(NbPostalCodeGroupEntity::getId,NbPostalCodeGroupEntity::getNameZh,NbPostalCodeGroupEntity::getNameEn,NbPostalCodeGroupEntity::getIsValid)
                .groupBy(NbPostalCodeGroupEntity::getId);
        List<NbPostalCodeGroupEntity> psotList = nbPostalCodeGroupService.list(postWrapper);

        //查询重量段下拉列表
        LambdaQueryWrapper<NbWeightGroupEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(NbWeightGroupEntity::getId,NbWeightGroupEntity::getNameZh,NbWeightGroupEntity::getNameEn,NbWeightGroupEntity::getStatus)
                .groupBy(NbWeightGroupEntity::getId);
        List<NbWeightGroupEntity> weightList = nbWeightGroupService.list(wrapper);

        map.put("psotList",psotList);
        map.put("weightList",weightList);

        return R.ok(map);
    }


    /**
     * 导出excel 表格
     * @param nbWeightGroup 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbWeightGroup_export')" )
    public List<NbWeightGroupEntity> export(NbWeightGroupEntity nbWeightGroup,Long[] ids) {
        return nbWeightGroupService.list(Wrappers.lambdaQuery(nbWeightGroup).in(ArrayUtil.isNotEmpty(ids), NbWeightGroupEntity::getId, ids));
    }


}