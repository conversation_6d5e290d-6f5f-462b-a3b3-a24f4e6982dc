package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbTransferBatchOrderEntity;
import com.jygjexp.jynx.zxoms.nbapp.vo.TransferBatchOrderVo;
import com.jygjexp.jynx.zxoms.vo.TransferBatchOrderListVo;

import java.util.List;

public interface NbTransferBatchOrderService extends IService<NbTransferBatchOrderEntity> {

    TransferBatchOrderVo findFirstOrderId(Integer orderId);

    // 通过id查询路区订单
    List<TransferBatchOrderListVo> listTransferBatchOrderByTbId(Integer batchId);

    List<NbOrderEntity> findByTboBatchId(Integer batchId);  // Route4Me查询路区订单
}