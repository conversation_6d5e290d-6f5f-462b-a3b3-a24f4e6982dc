package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbConfigEntity;
import com.jygjexp.jynx.zxoms.send.service.NbConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 单位配置项
 *
 * <AUTHOR>
 * @date 2024-10-11 17:41:12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbConfig" )
@Tag(description = "nbConfig" , name = "单位配置项管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbConfigController {

    private final NbConfigService nbConfigService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param basicNbConfig 单位配置项
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('basic_nbConfig_view')" )
    public R getNbConfigPage(@ParameterObject Page page, @ParameterObject NbConfigEntity basicNbConfig) {
        LambdaQueryWrapper<NbConfigEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbConfigService.page(page, wrapper));
    }


    /**
     * 通过id查询单位配置项
     * @param configId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{configId}" )
    @PreAuthorize("@pms.hasPermission('basic_nbConfig_view')" )
    public R getById(@PathVariable("configId" ) Integer configId) {
        return R.ok(nbConfigService.getById(configId));
    }

    /**
     * 新增单位配置项
     * @param basicNbConfig 单位配置项
     * @return R
     */
    @Operation(summary = "新增单位配置项" , description = "新增单位配置项" )
    @SysLog("新增单位配置项" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('basic_nbConfig_add')" )
    public R save(@RequestBody NbConfigEntity basicNbConfig) {
        return R.ok(nbConfigService.save(basicNbConfig));
    }

    /**
     * 修改单位配置项
     * @param basicNbConfig 单位配置项
     * @return R
     */
    @Operation(summary = "修改单位配置项" , description = "修改单位配置项" )
    @SysLog("修改单位配置项" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('basic_nbConfig_edit')" )
    public R updateById(@RequestBody NbConfigEntity basicNbConfig) {
        return R.ok(nbConfigService.updateById(basicNbConfig));
    }

    /**
     * 通过id删除单位配置项
     * @param ids configId列表
     * @return R
     */
    @Operation(summary = "通过id删除单位配置项" , description = "通过id删除单位配置项" )
    @SysLog("通过id删除单位配置项" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('basic_nbConfig_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbConfigService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param basicNbConfig 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('basic_nbConfig_export')" )
    public List<NbConfigEntity> export(NbConfigEntity basicNbConfig, Integer[] ids) {
        return nbConfigService.list(Wrappers.lambdaQuery(basicNbConfig).in(ArrayUtil.isNotEmpty(ids), NbConfigEntity::getConfigId, ids));
    }
}