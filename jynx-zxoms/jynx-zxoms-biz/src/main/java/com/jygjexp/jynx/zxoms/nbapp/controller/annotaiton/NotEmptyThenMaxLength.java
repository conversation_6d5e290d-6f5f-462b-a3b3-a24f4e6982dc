package com.jygjexp.jynx.zxoms.nbapp.controller.annotaiton;

import com.jygjexp.jynx.zxoms.nbapp.validator.NotEmptyThenMaxLengthValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * @Author: chenchang
 * @Description: String非空长度校验
 * @Date: 2024/11/7 17:53
 */
@Documented
@Constraint(validatedBy = NotEmptyThenMaxLengthValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface NotEmptyThenMaxLength {

    String message() default "Invalid length";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};

    int max() default Integer.MAX_VALUE;
}
