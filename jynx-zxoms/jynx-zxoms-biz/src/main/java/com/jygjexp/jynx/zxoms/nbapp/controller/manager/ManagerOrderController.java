package com.jygjexp.jynx.zxoms.nbapp.controller.manager;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.entity.NbOrderBatchEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.response.LocalizedR;
import com.jygjexp.jynx.zxoms.send.service.NbOrderBatchService;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.vo.OrderVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: chenchang
 * @Description: 订单相关逻辑
 * @Date: 2024/11/4 10:12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/manager/order" )
@Tag(description = "apporder" , name = "APP-订单相关逻辑" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ManagerOrderController {
    private final NbOrderBatchService orderBatchService;
    private final NbOrderService orderService;

    /**
     * 列出批次
     */
    @Operation(summary = "列出批次" , description = "列出批次" )
    @PostMapping("/listBatch" )
    public R listBatch() {
        Integer pageNo = 1;
        Integer pageSize = 10;
        // paginate(pageNo, pageSize, "select *", "from nb_order_batch order by batch_id desc");
        Page<NbOrderBatchEntity> page = orderBatchService.page(new Page<>(pageNo, pageSize),
                new LambdaQueryWrapper<>(NbOrderBatchEntity.class).orderByDesc(NbOrderBatchEntity::getBatchId));
        JSONArray ja = page.getRecords().stream().map(orderBatch -> {
            JSONObject jo = new JSONObject();
            jo.put("batchId", orderBatch.getBatchId());
            jo.put("batchNo", orderBatch.getBatchNo());
            jo.put("orderTotal", orderBatch.getOrderTotal());
            jo.put("note", orderBatch.getNote());
            jo.put("createTime", DateFormatUtils.format(orderBatch.getCreateTime(), "yyyy-MM-dd"));
            return jo;
        }).collect(Collectors.toCollection(JSONArray::new));

        return R.ok(ja);
    }

    /**
     * 计算选择的批次数据
     */
    @Operation(summary = "计算选择的批次数据" , description = "计算选择的批次数据")
    @PostMapping("/calBatch" )
    public R calBatch(@RequestParam("batchIds") @Parameter(description = "批次ID-多个用逗号分割") @NotBlank(message = "批次ID不能为空.") String batchIds) {
        if (StrUtil.isBlank(batchIds)) {
            return LocalizedR.failed("managerorder.no.batch.selected", Optional.ofNullable(null));
        }
        List<Integer> batchIdList = new ArrayList<>();
        try {
            for (String batchIdStr : batchIds.split(",")) {
                batchIdList.add(Integer.valueOf(batchIdStr.trim()));
            }
        } catch (NumberFormatException e) {
            return LocalizedR.failed("managerorder.batchId.format.error", Optional.ofNullable(null));
        }
        if (batchIdList.isEmpty()) {
            return R.failed("-1", "The batch selection is invalid.");
        }

        // "select ifnull(sum(if(driver=0, 0, 1), 0) orderTotal from nb_order o, nb_order_batch ob where o.batch_no = ob.batch_no and ob.batch_id in (" + StringUtils.join(batchIds, ",") + ")");
        OrderVo vo = orderService.findOrderByBatchId(batchIdList);
        JSONObject jo = new JSONObject();
        jo.put("orderTotal", vo.getOrderTotal()); // 未分配数量

        return R.ok(jo);
    }

    /**
     * 将订单分配给对应司机
     */
    @Operation(summary = "将订单分配给对应司机" , description = "将订单分配给对应司机")
    @PostMapping("/allotBatch" )
    public R allotBatch(@RequestParam("batchIds") @Parameter(description = "批次ID-多个用逗号分割") @NotBlank(message = "批次ID不能为空.") String batchIds,
                        @RequestParam("driverId") @NotNull(message = "司机ID不能为空.") Integer driverId) {
        if (StrUtil.isBlank(batchIds)) {
            return R.failed("-1", "The batch selection is invalid.");
        }
        if (driverId == null || driverId == 0) {
            return R.failed("-2", "Please select a driver.");
        }
        List<Integer> batchIdList = new ArrayList<>();
        try {
            for (String batchIdStr : batchIds.split(",")) {
                batchIdList.add(Integer.valueOf(batchIdStr.trim()));
            }
        } catch (NumberFormatException e) {
            return LocalizedR.failed("managerorder.batchId.format.error", Optional.ofNullable(null));
        }

        // "select * from nb_order o, nb_order_batch ob where o.batch_no = ob.batch_no and ob.batch_id in (" + StringUtils.join(batchIds, ",") + ") and o.driver_id = 0");
        MPJLambdaWrapper<NbOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbOrderEntity.class)
                .in(NbOrderBatchEntity::getBatchId, batchIdList).eq(NbOrderEntity::getDriverId, 0);
        List<NbOrderEntity> orderList = orderService.selectJoinList(NbOrderEntity.class, wrapper);
        for (NbOrderEntity order : orderList) {
            processOrder(order, driverId);
        }

        return R.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean processOrder(NbOrderEntity order, Integer driverId) {
        order.setDriverId(driverId);
        order.setOrderStatus(OrderDto.ORDER_STATUS_203_LOAD_SCANNED);
        Wrappers.lambdaUpdate(order);

        // 根据uniexpress网站的数据，此时没有轨迹记录
        return true;
    }

}
