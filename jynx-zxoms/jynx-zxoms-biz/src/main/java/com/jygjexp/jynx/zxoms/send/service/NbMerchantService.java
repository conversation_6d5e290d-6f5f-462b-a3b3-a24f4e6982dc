package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.zxoms.entity.NbMerchantEntity;
import com.jygjexp.jynx.zxoms.vo.MerchantPageVo;
import com.jygjexp.jynx.zxoms.send.vo.NbMerchantExcelVo;
import com.jygjexp.jynx.zxoms.send.vo.NbMerchantQuoteExcelVo;

import java.util.List;

public interface NbMerchantService extends IService<NbMerchantEntity> {

    Page search(Page page, MerchantPageVo vo);

    List<NbMerchantExcelVo> getExcel(MerchantPageVo vo, Integer[] ids); // 客户导出Excel

    Page searchQuote(Page page, MerchantPageVo vo); // 客户报价分页查询

    List<NbMerchantQuoteExcelVo> getQuoteExcel(MerchantPageVo vo, Integer[] ids);   // 客户报价导出Excel

    Boolean checkMerchantCode(NbMerchantEntity basicNbMerchant);  // 校验唯一编码在客户列表中是否已存在，如果存在则提示不能重复添加
}