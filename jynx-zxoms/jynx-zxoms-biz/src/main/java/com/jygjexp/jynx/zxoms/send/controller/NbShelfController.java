package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.service.NbShelfService;
import com.jygjexp.jynx.zxoms.send.service.NbSortingCenterService;
import com.jygjexp.jynx.zxoms.send.service.NbTransferCenterService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.zxoms.send.vo.NbShelfExcelVo;
import com.jygjexp.jynx.zxoms.vo.ShelfPageVo;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 货架
 *
 * <AUTHOR>
 * @date 2024-09-30 23:04:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/shelf" )
@Tag(description = "shelf" , name = "货架管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbShelfController {

    private final NbShelfService nbShelfService;
    private final NbSortingCenterService nbSortingCenterService;
    private final NbTransferCenterService nbTransferCenterService;

    /**
     * 分页查询
     * @param entity
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/search" )
    @PreAuthorize("@pms.hasPermission('basic_shelf_view')" )
    public R search(@ParameterObject Page page, @ParameterObject ShelfPageVo entity) {
        entity.setAddUserId(SecurityUtils.getUser().getId());
        entity.setAddUserName(SecurityUtils.getUser().getUsername());   //设置创建人名称
        return R.ok(nbShelfService.search(page, entity));
    }

    // 登录用户的分拣中心列表-很多接口需用到
    @Operation(summary = "登录用户的分拣中心列表-公共接口" , description = "登录用户的分拣中心列表-公共接口" )
    @GetMapping("/listSc")
    public R listSc(){
        return R.ok(nbSortingCenterService.listSc());
    }

    // 查询所有分拣中心
    @Operation(summary = "查询所有分拣中心" , description = "查询所有分拣中心" )
    @GetMapping("/listAllSc")
    public R listAllSc(){
        return R.ok(nbSortingCenterService.listAllSc());
    }

    // 分页查询转运中心-很多接口需用到
    @Operation(summary = "转运中心列表-公共接口" , description = "转运中心列表-公共接口" )
    @GetMapping("/listTc")
    public R listTc(){
        return R.ok(nbTransferCenterService.listTc());
    }

    @Operation(summary = "根据scId查询转运中心" , description = "根据scId查询转运中心" )
    @PostMapping("/listTcByScId")
    public R<List<NbTransferCenterEntity>> listTcByScId(@RequestParam("scId") Integer scId){
        return R.ok(nbTransferCenterService.listTcByScId(scId));
    }

    /**
     * 通过id查询货架
     * @param shelfId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{shelfId}" )
    @PreAuthorize("@pms.hasPermission('basic_shelf_view')" )
    public R getById(@PathVariable("shelfId" ) Integer shelfId) {
        return R.ok(nbShelfService.getById(shelfId));
    }

    /**
     * 新增货架
     * @param nbShelfEntity 货架
     * @return R
     */
    @Operation(summary = "新增货架" , description = "新增货架" )
    @SysLog("新增货架" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('basic_shelf_add')" )
    public R save(@RequestBody NbShelfEntity nbShelfEntity) {
        return R.ok(nbShelfService.addShelf(nbShelfEntity));
    }

    /**
     * 修改货架
     * @param nbShelfEntity 货架
     * @return R
     */
    @Operation(summary = "修改货架" , description = "修改货架" )
    @SysLog("修改货架" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('basic_shelf_edit')" )
    public R updateById(@RequestBody NbShelfEntity nbShelfEntity) {
        return R.ok(nbShelfService.updateById(nbShelfEntity));
    }

    /**
     * 通过id删除货架
     * @param ids shelfId列表
     * @return R
     */
    @Operation(summary = "通过id删除货架" , description = "通过id删除货架" )
    @SysLog("通过id删除货架" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('basic_shelf_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbShelfService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param vo 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('basic_shelf_export')" )
    public List<NbShelfExcelVo> export(ShelfPageVo vo, Integer[] ids) {
        return nbShelfService.getExcel(vo, ids);
    }
    /**
     * 查看货架二维码
     * @param shelfId
     * @return
     */
    @Operation(summary = "查看货架二维码" , description = "查看货架二维码" )
    @GetMapping("/viewQr/{shelfId}" )
    @PreAuthorize("@pms.hasPermission('basic_shelf_qr')" )
    public R viewQr(@PathVariable("shelfId" ) Integer shelfId) {
        return R.ok(nbShelfService.getById(shelfId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Operation(summary = "下架退件" , description = "下架退件" )
    @PostMapping("/markTo215" )
    @PreAuthorize("@pms.hasPermission('basic_shelf_markTo215')" )
    public R markTo215(@RequestParam("logId") Integer logId) {
        return R.ok(nbShelfService.markTo215(logId));
    }

}