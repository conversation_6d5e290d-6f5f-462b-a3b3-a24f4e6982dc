package com.jygjexp.jynx.zxoms.send.mapper;

import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.vo.NbOrderTransferBatchOrderVo;
import com.jygjexp.jynx.zxoms.vo.OrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NbOrderMapper extends JynxBaseMapper<NbOrderEntity> {

    String findCustomerOrderNosByMidAndCustomerNo(@Param("mid") Integer mid, @Param("customerNoArr") List<String> customerNoArr);

    List<NbOrderEntity> findInterval3DayByFunType2(int funType2SyncJy);

    NbOrderEntity findTboByBatchIdAndStatus(Integer batchId, int orderStatus200ParcelScanned);

    OrderVo findOrderByBatchId(List<Integer> batchIdList);

    List<NbOrderEntity> findOrderByTransferBatchId(Integer batchId);

    // 根据pkg_no查询订单详情-路线规划
    List<NbOrderTransferBatchOrderVo> getPkgNoList(String pkg_no);

}