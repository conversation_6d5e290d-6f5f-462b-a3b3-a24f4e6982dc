package com.jygjexp.jynx.zxoms.handler;

import com.jygjexp.jynx.zxoms.entity.NbMerchantEntity;
import com.jygjexp.jynx.zxoms.nbapp.vo.ApiRequestParamsVo;
import com.jygjexp.jynx.zxoms.nbapp.vo.OldResult;
import com.jygjexp.jynx.zxoms.send.service.NbMerchantService;
import com.jygjexp.jynx.zxoms.send.utils.Md5Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/12/17 15:46
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class ApiSignatureValidatorHandler {
    private final NbMerchantService merchantService;

    // 定义切点，拦截控制器包下指定方法
    @Pointcut("execution(* com.jygjexp.jynx.zxoms.nbapp.controller.api..*(..)) &&" +
            "!execution(* com.jygjexp.jynx.zxoms.nbapp.controller.api.Route4meController.*(..)) && " +
            "!execution(* com.jygjexp.jynx.zxoms.nbapp.controller.api.QuoteController.ltl(..)) && " +
            "!execution(* com.jygjexp.jynx.zxoms.nbapp.controller.api.QuoteController.ltlSurcharge(..)) && " +
            "!execution(* com.jygjexp.jynx.zxoms.nbapp.controller.api.QuoteController.testDomestic(..)) && " +
            "!execution(* com.jygjexp.jynx.zxoms.nbapp.controller.api.QuoteController.testInternational(..))")
    public void controllerMethods() {
    }

    // 环绕通知，拦截并校验参数
    @Around("controllerMethods()")
    public Object validateParameters(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        // 获取方法签名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        // 获取参数名
        String[] parameterNames = methodSignature.getParameterNames();

        // 将参数名和值对应存入 Map
        Map<String, Object> paramMap = new HashMap<>();
        for (int i = 0; i < parameterNames.length; i++) {
            paramMap.put(parameterNames[i], args[i]);
        }
        Object Object = paramMap.get("paramsVo");
        ApiRequestParamsVo paramsVo = null;
        if (Object instanceof ApiRequestParamsVo) {
            paramsVo = (ApiRequestParamsVo) Object;
            OldResult result = validateSignature(paramsVo);
            if (result != null) {
                return OldResult.fail(result.getCode(), result.getErrmsg());
            }
        }
        return joinPoint.proceed();
    }

    //    @Before("@annotation(org.springframework.web.bind.annotation.RequestMapping) || " +
//            "@annotation(org.springframework.web.bind.annotation.GetMapping) || " +
//            "@annotation(org.springframework.web.bind.annotation.PostMapping) && args(params,..)")
    private OldResult validateSignature(ApiRequestParamsVo params) {
        if (params == null) {
            return OldResult.fail("40070", "Request parameters are missing");
        }
        // 获取参数  appId=1001&randomstr=Jo1Y6d7U7F&timestamp=1733454261&sign=ba589d8c53e75b863861c1e92acdc2e9
        String appId = params.getAppId();
        String randomstr = params.getRandomstr();
        Long timestamp = params.getTimestamp();
        String sign = params.getSign();

        // 获取客户信息
        NbMerchantEntity merchant = merchantService.getById(appId);
        if (merchant == null) {
            log.error("Merchant not found for appId: {}", appId);
            return OldResult.fail("40071", "appId inexistence");
        }

        long checkTime = (System.currentTimeMillis() / 1000) - timestamp;
        if (checkTime < -60 || checkTime > 60 * 5) {
            return OldResult.fail("40072", "[timestamp] The duration cannot exceed 5 minutes");
        }

        // 获取请求的其他参数，并组装待签名字符串
        Map<String, String[]> paramsMap = params.getParaMap(); // 获取所有请求参数
        Map<String, String> tm = new TreeMap<>(); // 用于存放排序后的参数
        Iterator<Map.Entry<String, String[]>> iter = paramsMap.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<String, String[]> entry = iter.next();
            // 排除 sign 参数，并确保值不为空
            if (entry.getValue() != null && entry.getValue().length > 0 && entry.getValue()[0].length() > 0 && !entry.getKey().equals("sign")) {
                tm.put(entry.getKey(), entry.getValue()[0]);
            }
        }

        // 构建待签名字符串
        StringBuffer sb = new StringBuffer();
        Iterator<Map.Entry<String, String>> tmIter = tm.entrySet().iterator();
        while (tmIter.hasNext()) {
            Map.Entry<String, String> entry = tmIter.next();
            try {
                sb.append(entry.getKey()).append("=").append(java.net.URLEncoder.encode(entry.getValue(), "UTF-8")).append("&");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace(); // 可以根据需要进行处理
            }
        }

        // 添加 secretkey 到待签名字符串末尾
        sb.append("secretkey=").append(merchant.getApiSecret());

        // 使用 MD5 对待签名字符串进行加密
        String sysSign = Md5Util.getMD5(sb.toString());
        log.info("beforeStr: {}, md5: {}", sb.toString(), sysSign);

        // 校验签名
//        if (!sysSign.equals(sign)) {
//            return OldResult.fail("40073", "sign invalid");
//        }

        return null;
    }

}
