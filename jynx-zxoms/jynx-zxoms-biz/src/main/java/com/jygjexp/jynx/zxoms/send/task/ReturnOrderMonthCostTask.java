package com.jygjexp.jynx.zxoms.send.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.zxoms.api.feign.RemoteReturnService;
import com.jygjexp.jynx.zxoms.entity.NbPostalCodeDetailEntity;
import com.jygjexp.jynx.zxoms.entity.NbPostalCodeGroupEntity;
import com.jygjexp.jynx.zxoms.entity.NbWeightDetailEntity;
import com.jygjexp.jynx.zxoms.entity.NbWeightGroupEntity;
import com.jygjexp.jynx.zxoms.send.service.NbPostalCodeDetailService;
import com.jygjexp.jynx.zxoms.send.service.NbPostalCodeGroupService;
import com.jygjexp.jynx.zxoms.send.service.NbWeightDetailService;
import com.jygjexp.jynx.zxoms.send.service.NbWeightGroupService;
import com.jygjexp.jynx.zxoms.vo.ReturnExpenseRuleVo;
import com.jygjexp.jynx.zxoms.vo.ReturnSheinCodeVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: xiongpengfei
 * @Description: 【退件单】计算指定时间范围订单费用
 * @Date: 2025/02/06 15:42
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ReturnOrderMonthCostTask {
    private final NbPostalCodeGroupService nbPostalCodeGroupService;
    private final NbPostalCodeDetailService nbPostalCodeDetailService;
    private final NbWeightGroupService nbWeightGroupService;
    private final NbWeightDetailService nbWeightDetailService;
    private final RemoteReturnService remoteReturnService;

    @SneakyThrows
    @XxlJob("orderSectionCostTaskHandler")
    public void computeOrderCost() {
           XxlJobHelper.log("定时任务：【订单费用计算】于:{}，输入参数{}", LocalDateTime.now(), "运行中");
        try {
            // 盲扫查询需要计算的退件订单
            List<ReturnSheinCodeVo> returnSheinCodeVos = remoteReturnService.selectRetuenSectionOrderList();

            // 如果没有数据，直接返回空列表
            if (returnSheinCodeVos.isEmpty()) {
                XxlJobHelper.handleSuccess(); // 设置任务结果
                XxlJobHelper.log("定时任务：【退件订单费用计算】时间范围内未查询到订单数据，执行时间: {}", LocalDateTime.now());
            }

            // 遍历获取订单的邮编分区和价格
            boolean isMatched = false;
            //new一个新对象，用来更新订单费用和备注
            ReturnSheinCodeVo vo = new ReturnSheinCodeVo();
            for (ReturnSheinCodeVo order : returnSheinCodeVos) {
                // 根据订单的合作商id获取对应的退件费用规则，通过规则去匹配邮编分区和价格
                List<ReturnExpenseRuleVo> returnExpenseRules = remoteReturnService.getReturnExpenseRule(order.getAuthId());

                // 遍历费用规则
                for (ReturnExpenseRuleVo rule : returnExpenseRules) {
                    // 获取邮编分区组
                    NbPostalCodeGroupEntity postalCodeGroup = nbPostalCodeGroupService.getOne(new LambdaQueryWrapper<NbPostalCodeGroupEntity>()
                            .eq(NbPostalCodeGroupEntity::getIsValid, 1)
                            .eq(NbPostalCodeGroupEntity::getId, rule.getPostalId()));

                    List<NbPostalCodeDetailEntity> postalCodeDetails = new ArrayList<>();
                    if (null != postalCodeGroup && null != postalCodeGroup.getId()) {
                        // 获取邮编分区明细
                        postalCodeDetails = nbPostalCodeDetailService.list(new LambdaQueryWrapper<NbPostalCodeDetailEntity>()
                                .eq(NbPostalCodeDetailEntity::getGroupId, postalCodeGroup.getId()));
                    }
                    // 获取重量段
                    NbWeightGroupEntity weightGroup = nbWeightGroupService.getOne(new LambdaQueryWrapper<NbWeightGroupEntity>()
                            .eq(NbWeightGroupEntity::getStatus, 1)
                            .eq(NbWeightGroupEntity::getId, rule.getWeightId())); // 假设费用规则中有ruleId字段

                    List<NbWeightDetailEntity> weightDetails = new ArrayList<>();
                    if (null != weightGroup && null != weightGroup.getId()) {
                        // 获取重量段明细
                        weightDetails = nbWeightDetailService.list(new LambdaQueryWrapper<NbWeightDetailEntity>()
                                .eq(NbWeightDetailEntity::getGroupId, weightGroup.getId()));
                    }
                    // 根据订单收件驿站的邮编去匹配
                    String destPostalCode = order.getZip();

                    // 判断该订单的邮编，是否在邮编分区中
                    for (NbPostalCodeDetailEntity detail : postalCodeDetails) {
                        // 获取起始邮编和结束邮编
                        String startPostalCode = detail.getStartPostalCode().replaceAll("\\s", "").toUpperCase();
                        String endPostalCode = detail.getEndPostalCode().replaceAll("\\s", "").toUpperCase();
                        String orderPostalCode = destPostalCode.replaceAll("\\s", "").toUpperCase();

                        // 比较字符串的字典顺序
                        if (orderPostalCode.compareTo(startPostalCode) >= 0 && orderPostalCode.compareTo(endPostalCode) <= 0) {
                            // 获取该订单的重量--如果同时存在预报重量和实际重量，优先采用实际重量
                            BigDecimal weight = order.getWeight();
                            if (null == weight) {
                                weight = order.getPackageWidth();
                            }

                            // 判断该订单的重量，是否在重量段中
                            for (NbWeightDetailEntity weightDetailEntity : weightDetails) {
                                if (weight.compareTo(weightDetailEntity.getStartWeight()) >= 0 && weight.compareTo(weightDetailEntity.getEndWeight()) <= 0) {
                                    // 计算价格
                                    BigDecimal orderCost = weightDetailEntity.getPieceWeight();
                                    // 将计算后的价格更新到订单中
                                    vo.setId(order.getId());
                                    vo.setCalculateAmount(orderCost);
                                    remoteReturnService.updateOrdercost(vo);
                                    isMatched = true;
                                    break; // 找到匹配的重量段，跳出循环
                                }
                            }
                            if (isMatched) {
                                break; // 跳出邮编分区循环
                            }
                        }
                    }
                    if (isMatched) {
                        break; // 跳出费用规则循环
                    }
                }

                // 如果未匹配到任何邮编分区组和重量段，则添加订单备注告知客户原因
//                if (order.getCalculateAmount() == null || order.getCalculateAmount().compareTo(BigDecimal.ZERO) == 0) {
//                    vo.setId(order.getId());
//                    vo.setNote("(Order cost calculation: This postcode is not covered)订单费用计算：该邮编未被覆盖");
//                    remoteReturnService.updateOrdercost(vo);
//                }
            }

            XxlJobHelper.handleSuccess(); // 设置任务结果
            XxlJobHelper.log("定时任务：【退件订单费用指定范围计算】执行结束，时间: {}", LocalDateTime.now());
        } catch (Exception e) {
            log.error("订单费用计算定时任务执行失败：", e);
            XxlJobHelper.log("任务失败，原因：{}", e.getMessage());
            XxlJobHelper.handleFail();
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "请求过程中发生错误：" + e.getMessage(), e);
        }
    }


}
