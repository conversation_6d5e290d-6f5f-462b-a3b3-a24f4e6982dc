package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderPathBakEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderPathBakService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单路径删除记录
 *
 * <AUTHOR>
 * @date 2024-10-24 01:11:15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrderPathBak" )
//@Tag(description = "nbOrderPathBak" , name = "订单路径删除记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderPathBakController {

    private final  NbOrderPathBakService nbOrderPathBakService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbOrderPathBak 订单路径删除记录
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPathBak_view')" )
    public R getNbOrderPathBakPage(@ParameterObject Page page, @ParameterObject NbOrderPathBakEntity nbOrderPathBak) {
        LambdaQueryWrapper<NbOrderPathBakEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbOrderPathBakService.page(page, wrapper));
    }


    /**
     * 通过id查询订单路径删除记录
     * @param pathId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{pathId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPathBak_view')" )
    public R getById(@PathVariable("pathId" ) Integer pathId) {
        return R.ok(nbOrderPathBakService.getById(pathId));
    }

    /**
     * 新增订单路径删除记录
     * @param nbOrderPathBak 订单路径删除记录
     * @return R
     */
//    @Operation(summary = "新增订单路径删除记录" , description = "新增订单路径删除记录" )
    @SysLog("新增订单路径删除记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPathBak_add')" )
    public R save(@RequestBody NbOrderPathBakEntity nbOrderPathBak) {
        return R.ok(nbOrderPathBakService.save(nbOrderPathBak));
    }

    /**
     * 修改订单路径删除记录
     * @param nbOrderPathBak 订单路径删除记录
     * @return R
     */
//    @Operation(summary = "修改订单路径删除记录" , description = "修改订单路径删除记录" )
    @SysLog("修改订单路径删除记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPathBak_edit')" )
    public R updateById(@RequestBody NbOrderPathBakEntity nbOrderPathBak) {
        return R.ok(nbOrderPathBakService.updateById(nbOrderPathBak));
    }

    /**
     * 通过id删除订单路径删除记录
     * @param ids pathId列表
     * @return R
     */
//    @Operation(summary = "通过id删除订单路径删除记录" , description = "通过id删除订单路径删除记录" )
    @SysLog("通过id删除订单路径删除记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPathBak_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderPathBakService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbOrderPathBak 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderPathBak_export')" )
    public List<NbOrderPathBakEntity> export(NbOrderPathBakEntity nbOrderPathBak,Integer[] ids) {
        return nbOrderPathBakService.list(Wrappers.lambdaQuery(nbOrderPathBak).in(ArrayUtil.isNotEmpty(ids), NbOrderPathBakEntity::getPathId, ids));
    }
}