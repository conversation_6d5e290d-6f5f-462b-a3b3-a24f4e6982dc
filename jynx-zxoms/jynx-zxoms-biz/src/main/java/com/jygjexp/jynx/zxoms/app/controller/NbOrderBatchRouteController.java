package com.jygjexp.jynx.zxoms.app.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.app.service.NbOrderBatchRouteService;
import com.jygjexp.jynx.zxoms.entity.*;
import com.jygjexp.jynx.zxoms.send.service.*;
import com.jygjexp.jynx.zxoms.send.utils.Route4MeUtil;
import com.route4me.sdk.services.routing.*;
import com.route4me.sdk.services.routing.balance.Balance;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Author: xiongpengfei
 * @Description: 订单批次路线规划
 * @Date: 2024/10/29 16:24
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/nbRoute" )
@Tag(description = "nbRoute" , name = "订单批次路线规划" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderBatchRouteController {
    private final NbOrderBatchRouteService nbOrderBatchRouteService;
    private final NbOrderBatchService nbOrderBatchService;
    private final NbOrderService nbOrderService;
    private final NbDriverService nbDriverService;
    private final NbTransferCenterService nbTransferCenterService;
    private final NbTransferBatchService nbTransferBatchService;
    private final NbTransferBatchOrderService nbTransferBatchOrderService;
    private final Route4MeUtil route4MeUtil;


    /**
     * 创建规划
     */
    @Operation(summary = "路线规划测试接口" , description = "路线规划测试接口" )
    @GetMapping("/createRoute" )
    //@PreAuthorize("@pms.hasPermission('zxoms_nbRoute_view')" )
    public R createRoute() {
        //System.out.println("输出一下是否进来111111111111111111111111111111111111111111111");
        //Route4MeUtil.getRoute 获取规划的路线-addresses属性
        //  Route route = Route4MeUtil.getRoute("********************************");
        //  return R.ok(nbOrderBatchRouteService.tcGroup(16));
        //return R.ok(nbOrderBatchRouteService.exportToR4mOrder("16"));
        return R.ok(route4MeUtil.removeRoute("0"));
    }

    /**
     * 导入批次订单至Route4Me
     */
    @Operation(summary = "导入订单至R4M" , description = "导入订单至R4M" )
    @PostMapping("/exportToR4mOrder")
    @PreAuthorize("@pms.hasPermission('orderBatch_exportToR4mOrder')" )
    public R exportToR4mOrder(@RequestParam("batchIds") String batchIds){
        R r = nbOrderBatchRouteService.exportToR4mOrder(batchIds);
        return r;
    }

    /**
     * 包裹详情列表
     */
    @Operation(summary = "包裹详情列表" , description = "包裹详情列表" )
    @PostMapping("/pakNoList")
    public R pakNoList(@RequestParam("pkg_no") String pkg_no){
        return R.ok(nbOrderBatchRouteService.getPkgNoList(pkg_no));
    }



}
