package com.jygjexp.jynx.zxoms.send.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbTransferCenterEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbTransferCenterService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

/**
 * @Author: chenchang
 * @Description: 【订单】分配转运中心
 * @Date: 2024/10/9 21:46
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OrderMatchTransferCenterTask {

    private final NbOrderService nbOrderService;
    private final NbTransferCenterService nbTransferCenterService;

    @SneakyThrows
    @XxlJob("orderMatchTransferCenterHandler")
    public void orderMatchTransferCenterHandler() {
//        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务：【订单分配转运中心】于:{}，输入参数{}", LocalDateTime.now(), "运行中");

            // "select * from nb_order where sys_status = 1 and order_status <= ? order by order_id desc limit 100", Order.ORDER_STATUS_200_PARCEL_SCANNED); // 盲扫后就开始处理
        LambdaQueryWrapper<NbOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NbOrderEntity::getSysStatus, 1).le(NbOrderEntity::getOrderStatus, OrderDto.ORDER_STATUS_200_PARCEL_SCANNED)
                .orderByDesc(NbOrderEntity::getOrderId).last("limit 100");
        List<NbOrderEntity> orders = nbOrderService.list(wrapper);
            Map<String, NbTransferCenterEntity> postalCodeMap = new HashMap<>();
            Set<String> barchNoSet = new HashSet<>();

            for (NbOrderEntity order : orders) {
                if (order.getDestPostalCode() == null) {
                    continue;
                }

                String postalCode = order.getDestPostalCode().replaceAll(" ", "");
                Integer scId = order.getScId();
                if (postalCode.length() > 3) {
                    String beforePostalCode = postalCode.substring(0, 3);
                    String unique_key = scId + "_" + beforePostalCode;
                    NbTransferCenterEntity tc;
                    if (postalCodeMap.containsKey(unique_key)) {
                        tc = postalCodeMap.get(unique_key);
                    } else {
                        // select * from nb_transfer_center where is_valid = true and sc_id = ? and cover_postal_code like '%" + beforePostalCode + "%' limit 1", scId); // TOOD 效率优化
                        LambdaQueryWrapper<NbTransferCenterEntity> wrapper1 = new LambdaQueryWrapper<>();
                        wrapper1.eq(NbTransferCenterEntity::getIsValid, true).eq(NbTransferCenterEntity::getScId, scId)
                                .like(NbTransferCenterEntity::getCoverPostalCode, "%" + beforePostalCode + "%");
                        tc = nbTransferCenterService.getOne(wrapper1, false);
                        if (tc == null) {
                            order.setSysStatus(10);
                            order.setTcErr("邮编未覆盖" + postalCode);
                            nbOrderService.updateById(order);
                            log.error("邮编未覆盖" + postalCode);
                            XxlJobHelper.log("邮编未覆盖" + postalCode);
                            continue;
                        }

                        postalCodeMap.put(unique_key, tc);
                    }

                    order.setRegionCode(tc.getRegionCode());
                    order.setTcId(tc.getTcId());
                    order.setSysStatus(2);  // 已经匹配转运中心，下一步待全部完成后，就进行路径规划
                    order.setTcErr("");
                    nbOrderService.updateById(order);
                    XxlJobHelper.log("订单pkgNo=" + order.getPkgNo() + ",orderId=" + order.getOrderId() + ",tcId=" + tc.getTcId() + ",完成转运中心匹配");

                    barchNoSet.add(order.getBatchNo());
                }
            }

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【订单分配转运中心】执行结束，时间: {}", LocalDateTime.now());
    }

}
