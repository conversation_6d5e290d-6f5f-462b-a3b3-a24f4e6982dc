package com.jygjexp.jynx.zxoms.nbapp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: chenchang
 * @Description: 添加轨迹
 * @Date: 2024/11/29 14:31
 */
@Data
public class ApiOrderAddPathVo {
    @NotBlank(message = "包裹编号不能为空")
    @Schema(description = "包裹编号")
    private String pkgNo;

    @NotNull(message = "状态码不能为空")
    @Schema(description = "状态码")
    private Integer statusCode;

    @NotBlank(message = "地址不能为空")
    @Schema(description = "地址")
    private String address;

    @NotBlank(message = "时间不能为空")
    @Schema(description = "时间")
    private String time;

    @NotNull(message = "经度不能为空")
    @Schema(description = "经度")
    private Double lat;

    @NotNull(message = "纬度不能为空")
    @Schema(description = "纬度")
    private Double lng;

    public ApiOrderAddPathVo() {
        this.statusCode = 0;
        this.lat = 0d;
        this.lng = 0d;
    }
}
