package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderUpdateLogEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderUpdateLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单修改记录
 *
 * <AUTHOR>
 * @date 2024-10-23 23:47:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrderUpdateLog" )
//@Tag(description = "nbOrderUpdateLog" , name = "订单修改记录管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderUpdateLogController {

    private final  NbOrderUpdateLogService nbOrderUpdateLogService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbOrderUpdateLog 订单修改记录
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderUpdateLog_view')" )
    public R getNbOrderUpdateLogPage(@ParameterObject Page page, @ParameterObject NbOrderUpdateLogEntity nbOrderUpdateLog) {
        LambdaQueryWrapper<NbOrderUpdateLogEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbOrderUpdateLogService.page(page, new LambdaQueryWrapper<NbOrderUpdateLogEntity>().orderByDesc(NbOrderUpdateLogEntity::getLogId)));
    }


    /**
     * 通过id查询订单修改记录
     * @param logId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{logId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderUpdateLog_view')" )
    public R getById(@PathVariable("logId" ) Integer logId) {
        return R.ok(nbOrderUpdateLogService.getById(logId));
    }

    /**
     * 新增订单修改记录
     * @param nbOrderUpdateLog 订单修改记录
     * @return R
     */
//    @Operation(summary = "新增订单修改记录" , description = "新增订单修改记录" )
    @SysLog("新增订单修改记录" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderUpdateLog_add')" )
    public R save(@RequestBody NbOrderUpdateLogEntity nbOrderUpdateLog) {
        return R.ok(nbOrderUpdateLogService.save(nbOrderUpdateLog));
    }

    /**
     * 修改订单修改记录
     * @param nbOrderUpdateLog 订单修改记录
     * @return R
     */
//    @Operation(summary = "修改订单修改记录" , description = "修改订单修改记录" )
    @SysLog("修改订单修改记录" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderUpdateLog_edit')" )
    public R updateById(@RequestBody NbOrderUpdateLogEntity nbOrderUpdateLog) {
        return R.ok(nbOrderUpdateLogService.updateById(nbOrderUpdateLog));
    }

    /**
     * 通过id删除订单修改记录
     * @param ids logId列表
     * @return R
     */
//    @Operation(summary = "通过id删除订单修改记录" , description = "通过id删除订单修改记录" )
    @SysLog("通过id删除订单修改记录" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderUpdateLog_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderUpdateLogService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbOrderUpdateLog 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderUpdateLog_export')" )
    public List<NbOrderUpdateLogEntity> export(NbOrderUpdateLogEntity nbOrderUpdateLog,Integer[] ids) {
        return nbOrderUpdateLogService.list(Wrappers.lambdaQuery(nbOrderUpdateLog).in(ArrayUtil.isNotEmpty(ids), NbOrderUpdateLogEntity::getLogId, ids));
    }
}