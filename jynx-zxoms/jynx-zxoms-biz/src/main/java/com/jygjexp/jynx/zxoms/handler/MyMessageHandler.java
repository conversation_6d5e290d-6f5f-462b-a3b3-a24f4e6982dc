package com.jygjexp.jynx.zxoms.handler;

import com.jygjexp.jynx.common.websocket.handler.PlanTextMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.WebSocketSession;

/**
 * @Author: chenchang
 * @Description:
 * @Date: 2024/10/19 2:00
 */
@Slf4j
public class MyMessageHandler implements PlanTextMessageHandler {

    /**
     * 普通文本消息处理
     * @param session 当前接收消息的session
     * @param message 文本消息
     */
    @Override
    public void handle(WebSocketSession session, String message) {
        //接收到Web客户端的消息 并处理
        log.info("sessionId {} ,msg {}", session.getId(), message);

    }
}
