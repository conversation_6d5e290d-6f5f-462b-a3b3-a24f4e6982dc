package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbReportedLogEntity;
import com.jygjexp.jynx.zxoms.send.service.NbReportedLogService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import org.springdoc.api.annotations.ParameterObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 上报记录
 *
 * <AUTHOR>
 * @date 2024-11-11 23:45:45
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbReportedLog" )
//@Tag(description = "nbReportedLog" , name = "上报记录管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbReportedLogController {

    private final  NbReportedLogService nbReportedLogService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbReportedLog 上报记录
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbReportedLog_view')" )
    public R getNbReportedLogPage(@ParameterObject Page page, @ParameterObject NbReportedLogEntity nbReportedLog) {
        LambdaQueryWrapper<NbReportedLogEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbReportedLogService.page(page, wrapper));
    }


    /**
     * 通过id查询上报记录
     * @param logId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{logId}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbReportedLog_view')" )
    public R getById(@PathVariable("logId" ) Integer logId) {
        return R.ok(nbReportedLogService.getById(logId));
    }

    /**
     * 新增上报记录
     * @param nbReportedLog 上报记录
     * @return R
     */
//    @Operation(summary = "新增上报记录" , description = "新增上报记录" )
//    @SysLog("新增上报记录" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbReportedLog_add')" )
    public R save(@RequestBody NbReportedLogEntity nbReportedLog) {
        return R.ok(nbReportedLogService.save(nbReportedLog));
    }

    /**
     * 修改上报记录
     * @param nbReportedLog 上报记录
     * @return R
     */
//    @Operation(summary = "修改上报记录" , description = "修改上报记录" )
//    @SysLog("修改上报记录" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbReportedLog_edit')" )
    public R updateById(@RequestBody NbReportedLogEntity nbReportedLog) {
        return R.ok(nbReportedLogService.updateById(nbReportedLog));
    }

    /**
     * 通过id删除上报记录
     * @param ids logId列表
     * @return R
     */
//    @Operation(summary = "通过id删除上报记录" , description = "通过id删除上报记录" )
//    @SysLog("通过id删除上报记录" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbReportedLog_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbReportedLogService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbReportedLog 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbReportedLog_export')" )
    public List<NbReportedLogEntity> export(NbReportedLogEntity nbReportedLog,Integer[] ids) {
        return nbReportedLogService.list(Wrappers.lambdaQuery(nbReportedLog).in(ArrayUtil.isNotEmpty(ids), NbReportedLogEntity::getLogId, ids));
    }
}