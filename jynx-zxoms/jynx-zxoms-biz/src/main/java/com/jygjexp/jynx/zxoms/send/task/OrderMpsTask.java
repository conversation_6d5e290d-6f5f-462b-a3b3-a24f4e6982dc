package com.jygjexp.jynx.zxoms.send.task;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderMpsDto;
import com.jygjexp.jynx.zxoms.entity.NbOrderEntity;
import com.jygjexp.jynx.zxoms.entity.NbOrderMpsEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderMapper;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderMpsMapper;
import com.jygjexp.jynx.zxoms.vo.OrderMpsVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: chenchang
 * @Description: 【订单】LTL子订单状态同步
 * @Date: 2024/10/9 23:39
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OrderMpsTask {
    private final NbOrderMpsMapper nbOrderMpsMapper;
    private final NbOrderMapper nbOrderMapper;
    @SneakyThrows
    @XxlJob("orderMpsHandler")
    public void orderMpsHandler() {
//        String param = XxlJobHelper.getJobParam();
        followOrderStatus();

        XxlJobHelper.handleSuccess(); // 设置任务结果
    }

    private void followOrderStatus() {
        // "select om.*, o.order_status from nb_order_mps om, nb_order o where om.order_id = o.order_id and om.follow_status = " + OrderMps.FOLLOW_STATUS_1_RUNNING + " and om.p_order_id = 0");
        MPJLambdaWrapper<NbOrderMpsEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(NbOrderMpsEntity.class)
                .select(NbOrderEntity::getOrderStatus)
                .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbOrderMpsEntity::getOrderId)
                .eq(NbOrderMpsEntity::getFollowStatus, OrderMpsDto.FOLLOW_STATUS_1_RUNNING)
                .eq(NbOrderMpsEntity::getPOrderId, 0);
        List<OrderMpsVo> mpsList = nbOrderMpsMapper.selectJoinList(OrderMpsVo.class, wrapper);
        for (OrderMpsVo mps : mpsList) {
            Integer orderStatus = mps.getOrderStatus();

            // "select o.* from nb_order o, nb_order_mps om where o.order_id = om.order_id and om.p_order_id = ? and o.order_status <> ?", mps.getOrderId(), orderStatus);
            MPJLambdaWrapper<NbOrderMpsEntity> wrapper1 = new MPJLambdaWrapper<>();
            wrapper1.selectAll(NbOrderEntity.class)
                    .leftJoin(NbOrderEntity.class, NbOrderEntity::getOrderId, NbOrderMpsEntity::getOrderId)
                    .eq(NbOrderMpsEntity::getPOrderId, mps.getOrderId())
                    .ne(NbOrderEntity::getOrderStatus, orderStatus);
            List<NbOrderEntity> subOrders = nbOrderMpsMapper.selectJoinList(NbOrderEntity.class, wrapper1);
            for (NbOrderEntity subOrder : subOrders) {
                subOrder.setOrderStatus(orderStatus);
                nbOrderMapper.updateById(subOrder);

                XxlJobHelper.log("LTL子订单状态更新" + orderStatus + ", orderId=" + subOrder.getOrderId() + ",pOrderId=" + mps.getOrderId());
            }

            if (orderStatus == OrderDto.ORDER_STATUS_205_DELIVERED) {
                // mps.setFollowStatus(OrderMpsDto.FOLLOW_STATUS_10_COMPLETED);
                // mps.setFollowCompletedTime(new Date());
                nbOrderMpsMapper.update(new LambdaUpdateWrapper<NbOrderMpsEntity>().eq(NbOrderMpsEntity::getMpsId, mps.getMpsId())
                        .set(NbOrderMpsEntity::getFollowStatus, mps.getFollowStatus())
                        .set(NbOrderMpsEntity::getFollowCompletedTime, mps.getFollowCompletedTime()));
            }

        }
    }

}
