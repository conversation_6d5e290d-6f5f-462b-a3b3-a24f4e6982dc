package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.entity.NbTransferBatchEntity;
import com.jygjexp.jynx.zxoms.send.service.NbTransferBatchOrderService;
import com.jygjexp.jynx.zxoms.send.service.NbTransferBatchService;
import com.jygjexp.jynx.zxoms.send.vo.NbTransferBatchExcelVo;
import com.jygjexp.jynx.zxoms.vo.*;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * 路区
 *
 * <AUTHOR>
 * @date 2024-10-12 18:42:07
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbTransferBatch" )
@Tag(description = "nbTransferBatch" , name = "路区管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbTransferBatchController {

    private final NbTransferBatchService nbTransferBatchService;
    private final NbTransferBatchOrderService nbTransferBatchOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 路区
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatch_view')" )
    public R getNbTransferBatchPage(@ParameterObject Page page, @ParameterObject NbTransferBatchPageVo vo) {
        return R.ok(nbTransferBatchService.search(page, vo));
    }

    /**
     * 查询当前用户分拣中心下的转运中心的司机
     * @return
     */
    @Operation(summary = "查询当前用户分拣中心下的转运中心的司机" , description = "查询当前用户分拣中心下的转运中心的司机" )
    @GetMapping("/pageAllDriver" )
    public R pageAllDriver(@ParameterObject Page page, @ParameterObject NbDriverEntity entity){
        return R.ok(nbTransferBatchService.pageAllDriver(page, entity));
    }

    /**
     * 通过id查询路区
     * @param batchId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{batchId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatch_view')" )
    public R getById(@PathVariable("batchId" ) Integer batchId) {
        return R.ok(nbTransferBatchService.getById(batchId));
    }

    /**
     * 通过id查询路区订单
     * @param batchId
     * @return
     */
    @Operation(summary = "通过id查询路区订单" , description = "通过id查询路区订单" )
    @GetMapping("/listTransferBatchOrderByTbId/{batchId}")
    public R listTransferBatchOrderByTbId(@PathVariable("batchId" ) Integer batchId){
        return R.ok(nbTransferBatchOrderService.listTransferBatchOrderByTbId(batchId));
    }

    /**
     * 新增路区
     * @param zxOmsNbTransferBatch 路区
     * @return R
     */
    @Operation(summary = "新增路区" , description = "新增路区" )
    @SysLog("新增路区" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatch_add')" )
    public R save(@RequestBody NbTransferBatchEntity zxOmsNbTransferBatch) {
        return R.ok(nbTransferBatchService.save(zxOmsNbTransferBatch));
    }

    /**
     * 修改路区
     * @param zxOmsNbTransferBatch 路区
     * @return R
     */
    @Operation(summary = "修改路区" , description = "修改路区" )
    @SysLog("修改路区" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatch_edit')" )
    public R updateById(@RequestBody NbTransferBatchEntity zxOmsNbTransferBatch) {
        return R.ok(nbTransferBatchService.updateById(zxOmsNbTransferBatch));
    }

    /**
     * 通过id删除路区
     * @param ids batchId列表
     * @return R
     */
    @Operation(summary = "通过id删除路区" , description = "通过id删除路区" )
    @SysLog("通过id删除路区" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatch_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbTransferBatchService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param vo 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatch_export')" )
    public List<NbTransferBatchExcelVo> export(NbTransferBatchPageVo vo, Integer[] ids) {
//        return nbTransferBatchService.list(Wrappers.lambdaQuery(zxOmsNbTransferBatch).in(ArrayUtil.isNotEmpty(ids), NbTransferBatchEntity::getBatchId, ids));
        return nbTransferBatchService.getExcel(vo, ids);
    }

    /**
     * 修改路区价格-添加补贴
     */
    @Operation(summary = "修改路区价格-添加补贴" , description = "修改路区价格-添加补贴" )
    @SysLog("修改路区价格-添加补贴" )
    @PostMapping("/addSubsidy")
    public R addSubsidy(@Valid @RequestBody TransferBatchCostVo transferBatchCostVo) {
        return R.ok(nbTransferBatchService.addSubsidy(transferBatchCostVo.getBatchIds(), transferBatchCostVo.getAmount(), transferBatchCostVo.getNote()));
    }

    /**
     * nb_transfer_batch_cost   查询路区价格
     * 数据表还是nb_transfer_batch
     * @param page
     * @param pageVo
     * @return
     */
    @Operation(summary = "路区价格列表" , description = "路区价格列表" )
    @SysLog("路区价格列表" )
    @GetMapping("/pageTransferBatchCost")
    public R pageTransferBatchCost(@ParameterObject Page page, @ParameterObject TransferBatchCostPageVo pageVo){
        return R.ok(nbTransferBatchService.pageTransferBatchCost(page, pageVo));
    }

    @Operation(summary = "准备优化" , description = "准备优化" )
    @SysLog("准备优化" )
    @PostMapping("/fixOptimization")
    public R fixOptimization(@Valid @RequestBody TransferBatchFixOptimizationVo vo){
        return nbTransferBatchService.fixOptimization(vo.getRouteId(), vo.getOptimizationProblemID());
    }

    @Operation(summary = "列出路区" , description = "列出路区" )
    @SysLog("列出路区" )
    @PostMapping("/list")
    public R list(@RequestParam @NotNull(message = "批次号不能为空") Integer batchId){
        return nbTransferBatchService.list(batchId);
    }

    @Operation(summary = "删除路区" , description = "删除路区" )
    @SysLog("删除路区" )
    @PostMapping("/remove")
    public R remove(@RequestParam @NotNull(message = "批次号不能为空") Integer batchId){
        return nbTransferBatchService.remove(batchId);
    }

    @Operation(summary = "同步规划完的路区" , description = "同步规划完的路区" )
    @SysLog("同步规划完的路区" )
    @PostMapping("/syncRoutes")
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatch_tb')" )
    public R syncRoutes(){
        return nbTransferBatchService.syncRoutes();
    }

    /**
     * 同步指定路区
     * @return
     */
    @Operation(summary = "同步一个路区" , description = "同步一个路区" )
    @SysLog("同步一个路区" )
    @PostMapping("/syncOneRoutes")
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatch_one')" )
    public R syncOneRoutes(@RequestParam String routeId){
        return nbTransferBatchService.syncOneRoutes(routeId);   // routeId是在R4me中生成的
    }

    @Operation(summary = "往route4me同步线路号" , description = "往route4me同步线路号" )
    @SysLog("往route4me同步线路号" )
    @PostMapping("/syncLineNo")
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatch_number')" )
    public R syncLineNo(@RequestParam @NotNull(message = "路区Id不能为空") Integer batchId){
        return nbTransferBatchService.syncLineNo(batchId);
    }

}