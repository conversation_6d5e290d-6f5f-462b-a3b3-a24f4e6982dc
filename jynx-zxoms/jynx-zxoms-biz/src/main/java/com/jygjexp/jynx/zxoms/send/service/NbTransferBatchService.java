package com.jygjexp.jynx.zxoms.send.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbDriverEntity;
import com.jygjexp.jynx.zxoms.entity.NbTransferBatchEntity;
import com.jygjexp.jynx.zxoms.send.vo.NbTransferBatchExcelVo;
import com.jygjexp.jynx.zxoms.vo.NbTransferBatchPageVo;
import com.jygjexp.jynx.zxoms.vo.TransferBatchCostPageVo;

import java.math.BigDecimal;
import java.util.List;

public interface NbTransferBatchService extends IService<NbTransferBatchEntity> {

    Page<NbTransferBatchPageVo> search(Page page, NbTransferBatchPageVo vo); // 分页查询路区

    R addSubsidy(String batchIds, BigDecimal amount, String note);

    R fixOptimization(String routeId, String optimizationProblemID);  //准备优化

    R list(Integer batchId);    //列出路区

    R remove(Integer batchId);  //删除路区

    R syncRoutes(); //同步规划完的路区

    R syncLineNo(Integer batchId);  //往route4me同步线路号

    Page<TransferBatchCostPageVo> pageTransferBatchCost(Page page, TransferBatchCostPageVo pageVo);   // 查询路区价格

    NbTransferBatchEntity selectJoinOne(Class<NbTransferBatchEntity> nbTransferBatchEntityClass, MPJLambdaWrapper<NbTransferBatchEntity> wrapper);

    R syncOneRoutes(String routeId);    // 同步一个路区

    Page<NbDriverEntity> pageAllDriver(Page page, NbDriverEntity entity);   // 查询全部司机

    List<NbTransferBatchExcelVo> getExcel(NbTransferBatchPageVo vo, Integer[] ids); // 导出路区到Excel
}