package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.zxoms.entity.NbLtlPriceRuleEntity;
import com.jygjexp.jynx.zxoms.send.service.NbLtlPriceRuleService;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import org.springdoc.api.annotations.ParameterObject;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * LTL报价规则
 *
 * <AUTHOR>
 * @date 2024-11-08 10:48:44
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbLtlPriceRule" )
//@Tag(description = "nbLtlPriceRule" , name = "LTL报价规则管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbLtlPriceRuleController {

    private final  NbLtlPriceRuleService nbLtlPriceRuleService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbLtlPriceRule LTL报价规则
     * @return
     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlPriceRule_view')" )
    public R getNbLtlPriceRulePage(@ParameterObject Page page, @ParameterObject NbLtlPriceRuleEntity nbLtlPriceRule) {
        return R.ok(nbLtlPriceRuleService.search(page, nbLtlPriceRule));
    }


    /**
     * 通过id查询LTL报价规则
     * @param ltlRuleId id
     * @return R
     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{ltlRuleId}" )
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlPriceRule_view')" )
    public R getById(@PathVariable("ltlRuleId" ) Integer ltlRuleId) {
        return R.ok(nbLtlPriceRuleService.getById(ltlRuleId));
    }

    /**
     * 新增LTL报价规则
     * @param nbLtlPriceRule LTL报价规则
     * @return R
     */
//    @Operation(summary = "新增LTL报价规则" , description = "新增LTL报价规则" )
//    @SysLog("新增LTL报价规则" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlPriceRule_add')" )
    public R save(@RequestBody NbLtlPriceRuleEntity nbLtlPriceRule) {
        return R.ok(nbLtlPriceRuleService.save(nbLtlPriceRule));
    }

    /**
     * 修改LTL报价规则
     * @param nbLtlPriceRule LTL报价规则
     * @return R
     */
//    @Operation(summary = "修改LTL报价规则" , description = "修改LTL报价规则" )
//    @SysLog("修改LTL报价规则" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlPriceRule_edit')" )
    public R updateById(@RequestBody NbLtlPriceRuleEntity nbLtlPriceRule) {
        return R.ok(nbLtlPriceRuleService.updateById(nbLtlPriceRule));
    }

    /**
     * 通过id删除LTL报价规则
     * @param ids ltlRuleId列表
     * @return R
     */
//    @Operation(summary = "通过id删除LTL报价规则" , description = "通过id删除LTL报价规则" )
//    @SysLog("通过id删除LTL报价规则" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlPriceRule_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbLtlPriceRuleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbLtlPriceRule 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('zxoms_nbLtlPriceRule_export')" )
    public List<NbLtlPriceRuleEntity> export(NbLtlPriceRuleEntity nbLtlPriceRule,Integer[] ids) {
        return nbLtlPriceRuleService.list(Wrappers.lambdaQuery(nbLtlPriceRule).in(ArrayUtil.isNotEmpty(ids), NbLtlPriceRuleEntity::getLtlRuleId, ids));
    }
}