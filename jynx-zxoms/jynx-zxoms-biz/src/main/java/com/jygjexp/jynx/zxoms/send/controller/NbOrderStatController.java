package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbOrderStatEntity;
import com.jygjexp.jynx.zxoms.send.service.NbOrderStatService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单统计
 *
 * <AUTHOR>
 * @date 2024-10-14 15:55:56
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbOrderStat" )
@Tag(description = "nbOrderStat" , name = "订单统计管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbOrderStatController {

    private final NbOrderStatService nbOrderStatService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param basicNbOrderStat 订单统计
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStat_view')" )
    public R getNbOrderStatPage(@ParameterObject Page page, @ParameterObject NbOrderStatEntity basicNbOrderStat) {
        LambdaQueryWrapper<NbOrderStatEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbOrderStatService.page(page, wrapper));
    }


    /**
     * 通过id查询订单统计
     * @param statId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{statId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStat_view')" )
    public R getById(@PathVariable("statId" ) Integer statId) {
        return R.ok(nbOrderStatService.getById(statId));
    }

    /**
     * 新增订单统计
     * @param basicNbOrderStat 订单统计
     * @return R
     */
    @Operation(summary = "新增订单统计" , description = "新增订单统计" )
    @SysLog("新增订单统计" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStat_add')" )
    public R save(@RequestBody NbOrderStatEntity basicNbOrderStat) {
        return R.ok(nbOrderStatService.save(basicNbOrderStat));
    }

    /**
     * 修改订单统计
     * @param basicNbOrderStat 订单统计
     * @return R
     */
    @Operation(summary = "修改订单统计" , description = "修改订单统计" )
    @SysLog("修改订单统计" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStat_edit')" )
    public R updateById(@RequestBody NbOrderStatEntity basicNbOrderStat) {
        return R.ok(nbOrderStatService.updateById(basicNbOrderStat));
    }

    /**
     * 通过id删除订单统计
     * @param ids statId列表
     * @return R
     */
    @Operation(summary = "通过id删除订单统计" , description = "通过id删除订单统计" )
    @SysLog("通过id删除订单统计" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStat_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbOrderStatService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param basicNbOrderStat 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbOrderStat_export')" )
    public List<NbOrderStatEntity> export(NbOrderStatEntity basicNbOrderStat, Integer[] ids) {
        return nbOrderStatService.list(Wrappers.lambdaQuery(basicNbOrderStat).in(ArrayUtil.isNotEmpty(ids), NbOrderStatEntity::getStatId, ids));
    }
}