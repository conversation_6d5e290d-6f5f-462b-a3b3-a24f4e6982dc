package com.jygjexp.jynx.zxoms.send.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.zxoms.entity.NbTransferBatchOrderEntity;
import com.jygjexp.jynx.zxoms.send.service.NbTransferBatchOrderService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 路区订单order
 *
 * <AUTHOR>
 * @date 2024-10-12 18:49:09
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbTransferBatchOrder" )
@Tag(description = "nbTransferBatchOrder" , name = "路区订单order管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbTransferBatchOrderController {

    private final NbTransferBatchOrderService nbTransferBatchOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param zxOmsNbTransferBatchOrder 路区订单order
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatchOrder_view')" )
    public R getNbTransferBatchOrderPage(@ParameterObject Page page, @ParameterObject NbTransferBatchOrderEntity zxOmsNbTransferBatchOrder) {
        LambdaQueryWrapper<NbTransferBatchOrderEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(nbTransferBatchOrderService.page(page, wrapper));
    }


    /**
     * 通过id查询路区订单order
     * @param boId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{boId}" )
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatchOrder_view')" )
    public R getById(@PathVariable("boId" ) Integer boId) {
        return R.ok(nbTransferBatchOrderService.getById(boId));
    }

    /**
     * 新增路区订单order
     * @param zxOmsNbTransferBatchOrder 路区订单order
     * @return R
     */
    @Operation(summary = "新增路区订单order" , description = "新增路区订单order" )
    @SysLog("新增路区订单order" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatchOrder_add')" )
    public R save(@RequestBody NbTransferBatchOrderEntity zxOmsNbTransferBatchOrder) {
        return R.ok(nbTransferBatchOrderService.save(zxOmsNbTransferBatchOrder));
    }

    /**
     * 修改路区订单order
     * @param zxOmsNbTransferBatchOrder 路区订单order
     * @return R
     */
    @Operation(summary = "修改路区订单order" , description = "修改路区订单order" )
    @SysLog("修改路区订单order" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatchOrder_edit')" )
    public R updateById(@RequestBody NbTransferBatchOrderEntity zxOmsNbTransferBatchOrder) {
        return R.ok(nbTransferBatchOrderService.updateById(zxOmsNbTransferBatchOrder));
    }

    /**
     * 通过id删除路区订单order
     * @param ids boId列表
     * @return R
     */
    @Operation(summary = "通过id删除路区订单order" , description = "通过id删除路区订单order" )
    @SysLog("通过id删除路区订单order" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatchOrder_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(nbTransferBatchOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param zxOmsNbTransferBatchOrder 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('zxoms_nbTransferBatchOrder_export')" )
    public List<NbTransferBatchOrderEntity> export(NbTransferBatchOrderEntity zxOmsNbTransferBatchOrder,Integer[] ids) {
        return nbTransferBatchOrderService.list(Wrappers.lambdaQuery(zxOmsNbTransferBatchOrder).in(ArrayUtil.isNotEmpty(ids), NbTransferBatchOrderEntity::getBoId, ids));
    }
}