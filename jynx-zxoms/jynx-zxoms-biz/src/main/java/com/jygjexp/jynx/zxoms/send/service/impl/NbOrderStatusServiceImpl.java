package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbOrderStatusEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbOrderStatusMapper;
import com.jygjexp.jynx.zxoms.send.service.NbOrderStatusService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单状态维护
 *
 * <AUTHOR>
 * @date 2024-10-14 15:50:03
 */
@Service
public class NbOrderStatusServiceImpl extends ServiceImpl<NbOrderStatusMapper, NbOrderStatusEntity> implements NbOrderStatusService {
    @Override
    public List<NbOrderStatusEntity> findAll() {
        return list();
    }

}