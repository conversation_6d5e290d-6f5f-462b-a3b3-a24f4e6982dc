package com.jygjexp.jynx.zxoms.send.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.zxoms.entity.NbDriverScanedBatchEntity;
import com.jygjexp.jynx.zxoms.send.mapper.NbDriverScanedBatchMapper;
import com.jygjexp.jynx.zxoms.send.service.NbDriverScanedBatchService;
import org.springframework.stereotype.Service;
/**
 * 扫描的批次
 *
 * <AUTHOR>
 * @date 2024-11-11 23:06:00
 */
@Service
public class NbDriverScanedBatchServiceImpl extends ServiceImpl<NbDriverScanedBatchMapper, NbDriverScanedBatchEntity> implements NbDriverScanedBatchService {
}