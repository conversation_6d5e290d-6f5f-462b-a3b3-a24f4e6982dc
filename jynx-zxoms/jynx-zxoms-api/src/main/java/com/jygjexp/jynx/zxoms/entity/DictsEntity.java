package com.jygjexp.jynx.zxoms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据字典
 *
 * <AUTHOR>
 * @date 2024-09-30 18:22:31
 */
@Data
@TableName("dicts")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据字典")
public class DictsEntity extends Model<DictsEntity> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="id")
    private Integer id;

    /**
     * 字典值
     */
    @Schema(description="字典值")
    private String value;

    /**
     * 字典中文
     */
    @Schema(description="字典中文")
    private String name;

    /**
     * 表名
     */
    @Schema(description="表名")
    private String object;

    /**
     * 字段名
     */
    @Schema(description="字段名")
    private String field;

    /**
     * 扩展Json
     */
    @Schema(description="扩展Json")
    private String ext;
}