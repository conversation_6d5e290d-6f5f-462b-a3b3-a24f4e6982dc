package com.jygjexp.jynx.zxoms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 账号关联分拣中心-废弃
 *
 * <AUTHOR>
 * @date 2024-11-08 15:24:26
 */
@Data
@TableName("nb_user_sc_rel")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "账号关联分拣中心-废弃")
public class NbUserScRelEntity extends Model<NbUserScRelEntity> {


    /**
     * relId
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="relId")
    private Integer relId;

    /**
     * userId
     */
    @Schema(description="userId")
    private Integer userId;

    /**
     * scId
     */
    @Schema(description="scId")
    private Integer scId;

    /**
     * addTime
     */
    @Schema(description="addTime")
    private LocalDateTime addTime;
}