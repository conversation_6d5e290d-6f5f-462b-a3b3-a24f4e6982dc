package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 上报记录
 *
 * <AUTHOR>
 * @date 2024-11-11 23:45:45
 */
@Data
@TableName("nb_reported_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "上报记录")
public class NbReportedLogEntity extends Model<NbReportedLogEntity> {


    /**
     * logId
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="logId")
    private Integer logId;

    /**
     * 司机
     */
    @Schema(description="司机")
    private Integer driverId;

    /**
     * 类型：1=地址
     */
    @Schema(description="类型：1=地址")
    private Integer type;

    /**
     * 地址类型
     */
    @Schema(description="地址类型")
    private Integer addressType;

    /**
     * 上报时间
     */
    @Schema(description="上报时间")
    private Date reportedTime;
}