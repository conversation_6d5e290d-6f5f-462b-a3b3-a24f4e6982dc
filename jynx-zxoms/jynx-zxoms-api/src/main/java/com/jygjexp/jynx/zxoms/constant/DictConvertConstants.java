package com.jygjexp.jynx.zxoms.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public class DictConvertConstants {
    //baseKey
    public static final String BASE_KEY = "1:dict_details::";
    //正向司机业务类型(对应字典管理中的类型字段,同下)
    public static final String BUSINESS_TYPE_ZXOMS = "business_type";
    //正向司机工作方式
    public static final String WORK_TYPE_ZXOMS = "work_type_id";
    //正向司机审核状态
    public static final String AUDIT_STATUS_ZXOMS = "audit_status";

}
