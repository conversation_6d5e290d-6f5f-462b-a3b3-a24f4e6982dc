package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单费用
 *
 * <AUTHOR>
 * @date 2024-09-30 23:38:39
 */
@Data
@TableName("nb_order_cost")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单费用")
public class NbOrderCostEntity extends Model<NbOrderCostEntity> {


    /**
     * orderId
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="orderId")
    private Integer orderId;

    /**
     * 司机ID
     */
    @Schema(description="司机ID")
    private Integer driverId;

    /**
     * 订单号
     */
    @Schema(description="订单号")
    private String orderNo;

    /**
     * 包裹号
     */
    @Schema(description="包裹号")
    private String pkgNo;

    /**
     * 三字邮编
     */
    @Schema(description="三字邮编")
    private String postalCode;

    /**
     * 司机基准费
     */
    @Schema(description="司机基准费")
    private BigDecimal driverBase;

    /**
     * 重量补贴
     */
    @Schema(description="重量补贴")
    private BigDecimal driverWeightSubsidy;

    /**
     * 司机补贴
     */
    @Schema(description="司机补贴")
    private BigDecimal driverSubsidy;

    /**
     * 状态：1=预估；2=结算；3=范围外
     */
    @Schema(description="状态：1=预估；2=结算；3=范围外")
    private Integer costStatus;

    /**
     * estimateTime
     */
    @Schema(description="estimateTime")
    private Date estimateTime;

    /**
     * settleTime
     */
    @Schema(description="settleTime")
    private Date settleTime;

    /**
     * 包裹重
     */
    @Schema(description="包裹重")
    private BigDecimal pkgWeight;

    /**
     * 体积重
     */
    @Schema(description="体积重")
    private BigDecimal pkgVolumeWeight;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String note;

    /**
     * 使用的规则
     */
    @Schema(description="使用的规则")
    private Integer costRuleItemId;

    /**
     * estimateTimestamp
     */
    @Schema(description="estimateTimestamp")
    private Long estimateTimestamp;

    /**
     * settleTimestamp
     */
    @Schema(description="settleTimestamp")
    private Long settleTimestamp;

    /**
     * addTime
     */
    @Schema(description="addTime")
    private Date addTime;

    /**
     * 分拣中心
     */
    @Schema(description="分拣中心")
    private Integer scId;

    /**
     * 转动中心
     */
    @Schema(description="转动中心")
    private Integer tcId;

    /**
     * 盲扫时间
     */
    @Schema(description="盲扫时间")
    private Date parcelScannedTime;

    /**
     * leader司机ID
     */
    @Schema(description="leader司机ID")
    private Integer pDriverId;

    /**
     * 路区补贴ID
     */
    @Schema(description="路区补贴ID")
    private Integer subsidyLogId;
}