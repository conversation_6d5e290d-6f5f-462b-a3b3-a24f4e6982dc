package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 短信模板
 *
 * <AUTHOR>
 * @date 2024-10-11 17:52:47
 */
@Data
@TenantTable
@TableName("nb_sms_template")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "短信模板")
public class NbSmsTemplateEntity extends Model<NbSmsTemplateEntity> {


    /**
     * 模版ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="模版ID")
    private Integer templateId;

    /**
     * 短信类型
     */
    @Schema(description="短信类型")
    private String title;

    /**
     * 触发条件
     */
    @Schema(description="触发条件")
    private String triggerCondition;

    /**
     * 短信内容
     */
    @Schema(description="短信内容")
    private String content;

    /**
     * 描述
     */
    @Schema(description="描述")
    private String description;

    /**
     * 有效
     */
    @Schema(description="有效")
    private Boolean isValid;

    /**
     * 唯一标识
     */
    @Schema(description="唯一标识")
    private String tplKey;

    /**
     * 1默认，10模板
     */
    @Schema(description="1默认，10模板")
    private Integer tplType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}