package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自提存储柜（货架）
 *
 * <AUTHOR>
 * @date 2024-09-30 23:04:18
 */
@Data
@TenantTable
@TableName("nb_shelf")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "自提存储柜（货架）")
public class NbShelfEntity extends Model<NbShelfEntity> {

    /**
     * 货架ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="货架ID")
    private Integer shelfId;

    /**
     * 分拣中心
     */
    @Schema(description="分拣中心")
    private Integer scId;

    /**
     * 转运中心
     */
    @Schema(description="转运中心")
    private Integer tcId;

    /**
     * 货架编码
     */
    @Schema(description="货架编码")
    private String shelfCode;

    /**
     * addTime
     */
    @Schema(description="addTime")
    private Long addTime;

    /**
     * 当前包裹数量
     */
    @Schema(description="当前包裹数量")
    private Integer orderTotal;

    /**
     * 创建者
     */
    @Schema(description="创建者")
    private Long addUserId;

    /**
     * 创建者名称
     */
    @Schema(description="创建者名称")
    private String addUserName;

    /**
     * 是否有效
     */
    @Schema(description="是否有效")
    private Boolean isValid;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}