package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 短信日志
 *
 * <AUTHOR>
 * @date 2024-10-12 22:16:54
 */
@Data
@TenantTable
@TableName("nb_sms_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "短信日志")
public class NbSmsLogEntity extends Model<NbSmsLogEntity> {


    /**
     * 日志ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="日志ID")
    private Integer logId;

    /**
     * 模板ID
     */
    @Schema(description="模板ID")
    private Integer templateId;

    /**
     * 订单ID
     */
    @Schema(description="订单ID")
    private String orderId;

    /**
     * 手机号
     */
    @Schema(description="手机号")
    private String mobile;

    /**
     * 内容
     */
    @Schema(description="内容")
    private String content;

    /**
     * 错误信息（逆向）
     */
    @Schema(description="错误信息")
    private String log;

    /**
     * 司机ID
     */
    @Schema(description="司机ID")
    private Integer driverId;

    /**
     * 发送时间
     */
    @Schema(description="发送时间")
    private Date sendTime;

    /**
     * 接收结果
     */
    @Schema(description="接收结果")
    private String responseStatus;

    /**
     * 对应轨迹
     */
    @Schema(description="对应轨迹")
    private Integer pathId;

    /**
     * 1=自提通知，2=签收通知
     */
    @Schema(description="1=自提通知，2=签收通知")
    private Integer smsType;

    /**
     * 10=待发，15=发成功，20=发失败
     */
    @Schema(description="10=待发，15=发成功，20=发失败")
    private Integer smsStatus;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;

}