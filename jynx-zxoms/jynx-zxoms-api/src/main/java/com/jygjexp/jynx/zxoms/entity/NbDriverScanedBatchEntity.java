package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 扫描的批次
 *
 * <AUTHOR>
 * @date 2024-11-11 23:06:00
 */
@Data
@TableName("nb_driver_scaned_batch")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "扫描的批次")
public class NbDriverScanedBatchEntity extends Model<NbDriverScanedBatchEntity> {


    /**
     * batchId
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="batchId")
    private Integer batchId;

    /**
     * driverId
     */
    @Schema(description="driverId")
    private Integer driverId;

    /**
     * orderTotal
     */
    @Schema(description="orderTotal")
    private Integer orderTotal;

    /**
     * 应扫数量
     */
    @Schema(description="应扫数量")
    private Integer shouldTotal;

    /**
     * auditTime
     */
    @Schema(description="auditTime")
    private Date auditTime;

    /**
     * auditStatus
     */
    @Schema(description="auditStatus")
    private Integer auditStatus;

    /**
     * createTime
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="createTime")
    private Date createTime;
}