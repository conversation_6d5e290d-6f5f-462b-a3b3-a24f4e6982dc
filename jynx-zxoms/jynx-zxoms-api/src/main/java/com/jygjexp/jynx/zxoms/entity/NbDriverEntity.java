package com.jygjexp.jynx.zxoms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 司机
 *
 * <AUTHOR>
 * @date 2024-10-15 22:42:15
 */
@Data
@TenantTable
@TableName("nb_driver")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机")
public class NbDriverEntity extends Model<NbDriverEntity> {


    /**
     * 司机ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="司机ID")
    private Integer driverId;

    /**
     * 国家
     */
    @Schema(description="国家")
    private String country;

    /**
     * 手机号
     */
    @Schema(description="手机号")
    private String mobile;

    /**
     * 密码
     */
    @Schema(description="密码")
    private String password;

    /**
     * 账单密码
     */
    @Schema(description="账单密码")
    private String settlePassword;

    /**
     * 名
     */
    @Schema(description="名")
    private String firstName;

    /**
     * 中间名
     */
    @Schema(description="中间名")
    private String middleName;

    /**
     * 姓
     */
    @Schema(description="姓")
    private String lastName;

    /**
     * 司机全名  定义insert和update两个触发器
     *
     * CREATE TRIGGER update_driver_name_on_insert
     * BEFORE INSERT ON nb_driver
     * FOR EACH ROW
     * SET NEW.driver_name = CONCAT(NEW.first_name, ' ', IFNULL(NEW.middle_name, ''), ' ', NEW.last_name);
     *
     *
     * CREATE TRIGGER update_driver_name_on_update
     * BEFORE UPDATE ON nb_driver
     * FOR EACH ROW
     * SET NEW.driver_name = CONCAT(NEW.first_name, ' ', IFNULL(NEW.middle_name, ''), ' ', NEW.last_name);
     *
     *
     * UPDATE nb_driver
     * SET driver_name = CONCAT(first_name, ' ', IFNULL(middle_name, ''), ' ', last_name);
     */
    @Schema(description="司机全名")
    private String driverName;

    /**
     * 邮箱
     */
    @Schema(description="邮箱")
    private String email;

    /**
     * 地址
     */
    @Schema(description="地址")
    private String address;

    /**
     * 国家ID
     */
    @Schema(description="国家ID")
    private Integer countryId;

    /**
     * 省ID
     */
    @Schema(description="省ID")
    private Integer provinceId;

    /**
     * 市ID
     */
    @Schema(description="市ID")
    private Integer cityId;

    /**
     * 邮编
     */
    @Schema(description="邮编")
    private String postalCode;

    /**
     * 车型类型
     */
    @Schema(description="车型类型")
    private Integer vehicleTypeId;

    /**
     * 车牌号码
     */
    @Schema(description="车牌号码")
    private String plateNumber;

    /**
     * 审核状态：1=创建，2=审核中，3=通过，4=拒绝
     */
    @Schema(description="审核状态：1=待提交，2=待审核，3=通过，4=拒绝")
    private Integer auditStatus;

    /**
     * 查账时间
     */
    @Schema(description="审核时间")
    private Date auditTime;

    /**
     * 类型：1=全职，2=兼职, 3=服务号
     */
    @Schema(description="类型：1=全职，2=兼职, 3=服务号")
    private Integer workTypeId;

    /**
     * 转运权限
     */
    @Schema(description="转运权限")
    private Boolean isTransfer;

    /**
     * 注册时间
     */
    @Schema(description="注册时间")
    private Date regTime;

    /**
     * 上传登录时间
     */
    @Schema(description="上传登录时间")
    private Date lastLoginTime;

    /**
     * 上次登录ip
     */
    @Schema(description="上次登录ip")
    private String lastLoginIp;

    /**
     * sessionId
     */
    @Schema(description="sessionId")
    private String sessionId;

    /**
     * 工时
     */
    @Schema(description="工时")
    private Integer workHour;

    /**
     * 开启扫描
     */
    @Schema(description="开启扫描[仓储权限]")
    private Integer sortingCenterScan;

    /**
     * 司机所属的转运中心ID
     */
    @Schema(description="司机所属的转运中心ID")
    private Integer tcId;

    /**
     * 结束纬度
     */
    @Schema(description="结束纬度")
    private Double endLocLat;

    /**
     * 结束经度
     */
    @Schema(description="结束经度")
    private Double endLocLng;

    /**
     * 使用结束位置
     */
    @Schema(description="使用结束位置")
    private Boolean useEndLoc;

    /**
     * 自动路径规划
     */
    @Schema(description="自动路径规划")
    private Integer autoRoutePlanning;

    /**
     * route4me id
     */
    @Schema(description="route4me id")
    private String route4meVehicleId;

    /**
     * route4me memberid
     */
    @Schema(description="route4me memberid")
    private Integer route4meMemberId;

    /**
     * 失效
     */
    @Schema(description="是否有效")
    private Boolean isValid;

    /**
     * 生日
     */
    @Schema(description="生日")
    private Date birthday;

    /**
     * SIN
     */
    @Schema(description="SIN")
    private String sin;

    /**
     * 紧急联系人
     */
    @Schema(description="紧急联系人")
    private String emergencyContact;

    /**
     * 紧急联系人电话
     */
    @Schema(description="紧急联系人电话")
    private String emergencyContactTel;

    /**
     * 保险单
     */
    @Schema(description="保险单")
    private String insurancePolicy;

    /**
     * 车辆注册文件
     */
    @Schema(description="车辆注册文件")
    private String vehicleRegDoc;

    /**
     * 驾照正面
     */
    @Schema(description="驾照正面")
    private String drivingLicenseFront;

    /**
     * 驾照背面
     */
    @Schema(description="驾照背面")
    private String drivingLicenseBack;

    /**
     * 车险
     */
    @Schema(description="车险")
    private String carInsurance;

    /**
     * sinPic
     */
    @Schema(description="sinPic")
    private String sinPic;

    /**
     * void cheque or direct deposit form
     */
    @Schema(description="空头支票/直接存款表格")
    private String vcOrDdf;

    /**
     * 载重量
     */
    @Schema(description="载重量")
    private Integer loadCapacity;

    /**
     * 合同类型
     */
    @Schema(description="合同类型")
    private Integer contractType;

    /**
     * 合同有效期
     */
    @Schema(description="合同有效期")
    private Date contractExpire;

    /**
     * 分拣中心ID
     */
    @Schema(description="分拣中心ID")
    private Integer scId;

    /**
     * 密码,司机档案,司机信息,车辆信息,上传文档,确认协议
     */
    @Schema(description="密码,司机档案,司机信息,车辆信息,上传文档,确认协议")
    private String regStep;

    /**
     * 驾驶证号
     */
    @Schema(description="驾驶证号")
    private String licenseNumber;

    /**
     * 驾驶证有效期
     */
    @Schema(description="驾驶证有效期")
    private Date licenseNumberExpire;

    /**
     * 社保号
     */
    @Schema(description="社保号")
    private String socialNumber;

    /**
     * 驳回原因
     */
    @Schema(description="驳回原因")
    private String refuseReason;

    /**
     * 车辆品牌
     */
    @Schema(description="r4mVehicleMake")
    private String r4mVehicleMake;

    /**
     * r4mVehicleType
     */
    @Schema(description="r4mVehicleType")
    private String r4mVehicleType;

    /**
     * r4mFuelType
     */
    @Schema(description="r4mFuelType")
    private String r4mFuelType;

    /**
     * r4mMaxVolume
     */
    @Schema(description="r4mMaxVolume")
    private Double r4mMaxVolume;

    /**
     * r4mMaxItems
     */
    @Schema(description="r4mMaxItems")
    private Integer r4mMaxItems;

    /**
     * r4mMaxWeight
     */
    @Schema(description="r4mMaxWeight")
    private Double r4mMaxWeight;

    /**
     * r4mVehicleCapacityProfileId
     */
    @Schema(description="R4M CPID")
    private String r4mVehicleCapacityProfileId;

    /**
     * 司机备注
     */
    @Schema(description="司机备注")
    private String remark;

    /**
     * 是否司机Leader
     */
    @Schema(description="是否司机Leader")
    private Boolean isLeader;

    /**
     * 父司机
     */
    @Schema(description="所属Leader")
    private Integer pDriverId;

    /**
     * 添加方式：1用户注册，2后台录入
     */
    @Schema(description="添加方式：1用户注册，2后台录入")
    private Integer addType;

    @Schema(description="业务类型：1派送；2退件；3派送和退件")
    private Integer businessType;

//    --------------------------------------------------------------------------退件---------------------------------------------
    /**
     * lng经纬度
     */
    @Schema(description="lng")
    private String lng;
    /**
     * lat经纬度
     */
    @Schema(description="lat")
    private String lat;
    /**
     * 微信id
     */
    @Schema(description="微信id")
    private String weixinId;
    /**
     * 用户id
     */
    @Schema(description="用户id")
    private Integer userId;
    /**
     * 仓库id
     */
    @Schema(description="仓库id")
    private Integer warehouseId;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    private LocalDateTime createDate;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}