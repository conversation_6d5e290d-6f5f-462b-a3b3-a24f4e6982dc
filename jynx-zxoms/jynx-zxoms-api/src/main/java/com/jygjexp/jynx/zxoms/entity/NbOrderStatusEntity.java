package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 订单状态维护
 *
 * <AUTHOR>
 * @date 2024-10-14 15:50:03
 */
@Data
@TenantTable
@TableName("nb_order_status")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单状态维护")
public class NbOrderStatusEntity extends Model<NbOrderStatusEntity> {


    /**
     * id
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="id")
    private Integer id;

    /**
     * 标题
     */
    @Schema(description="标题")
    private String title;

    /**
     * 内容
     */
    @Schema(description="内容")
    private String content;

    /**
     * 状态类型：派送、退件
     */
    @Schema(description="状态类型")
    private String statusType;

    /**
     * 添加时间
     */
    @Schema(description="添加时间")
    private Date addTime;

    /**
     * 触发方式
     */
    @Schema(description="触发方式")
    private String triggerType;

    /**
     * 一级含义
     */
    @Schema(description="一级含义")
    private String level1Caption;

    /**
     * 二级含义
     */
    @Schema(description="二级含义")
    private String level2Caption;

    /**
     * 发生地点
     */
    @Schema(description="发生地点")
    private String eventLocation;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}