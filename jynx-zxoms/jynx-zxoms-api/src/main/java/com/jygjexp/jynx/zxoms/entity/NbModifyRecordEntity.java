package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 通用修改记录
 *
 * <AUTHOR>
 * @date 2024-10-17 01:48:45
 */
@Data
@TableName("nb_modify_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "通用修改记录")
public class NbModifyRecordEntity extends Model<NbModifyRecordEntity> {


    /**
     * 主键ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Integer recordId;

    /**
     * 添加时间
     */
    @Schema(description="添加时间")
    private LocalDateTime addTime;

    /**
     * 表名
     */
    @Schema(description="表名")
    private String tableName;

    /**
     * primaryId
     */
    @Schema(description="primaryId")
    private Integer primaryId;

    /**
     * fromValue
     */
    @Schema(description="fromValue")
    private String fromValue;

    /**
     * toValue
     */
    @Schema(description="toValue")
    private String toValue;

    /**
     * description
     */
    @Schema(description="description")
    private String description;

    /**
     * 1系统，2管理员，3司机
     */
    @Schema(description="1系统，2管理员，3司机")
    private Integer modifyOperator;

    /**
     * adminId
     */
    @Schema(description="adminId")
    private Integer adminId;

    /**
     * 司机ID
     */
    @Schema(description="司机ID")
    private Integer driverId;
}