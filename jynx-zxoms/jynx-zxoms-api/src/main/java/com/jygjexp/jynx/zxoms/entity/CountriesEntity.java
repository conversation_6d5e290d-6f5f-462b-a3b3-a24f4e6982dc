package com.jygjexp.jynx.zxoms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 国家
 *
 * <AUTHOR>
 * @date 2024-09-30 18:40:38
 */
@Data
@TableName("countries")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "国家")
public class CountriesEntity extends Model<CountriesEntity> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="id")
    private Integer id;

    /**
     * name
     */
    @Schema(description="name")
    private String name;

    /**
     * iso3
     */
    @Schema(description="iso3")
    private String iso3;

    /**
     * numericCode
     */
    @Schema(description="numericCode")
    private String numericCode;

    /**
     * iso2
     */
    @Schema(description="iso2")
    private String iso2;

    /**
     * phonecode
     */
    @Schema(description="phonecode")
    private String phonecode;

    /**
     * capital
     */
    @Schema(description="capital")
    private String capital;

    /**
     * currency
     */
    @Schema(description="currency")
    private String currency;

    /**
     * currencyName
     */
    @Schema(description="currencyName")
    private String currencyName;

    /**
     * currencySymbol
     */
    @Schema(description="currencySymbol")
    private String currencySymbol;

    /**
     * tld
     */
    @Schema(description="tld")
    private String tld;

    /**
     * native是关键字不能使用，这里替换为 local
     */
    private String local;

    /**
     * region
     */
    @Schema(description="region")
    private String region;

    /**
     * subregion
     */
    @Schema(description="subregion")
    private String subregion;

    /**
     * timezones
     */
    @Schema(description="timezones")
    private String timezones;

    /**
     * translations
     */
    @Schema(description="translations")
    private String translations;

    /**
     * latitude
     */
    @Schema(description="latitude")
    private BigDecimal latitude;

    /**
     * longitude
     */
    @Schema(description="longitude")
    private BigDecimal longitude;

    /**
     * emoji
     */
    @Schema(description="emoji")
    private String emoji;

    /**
     * emojiU
     */
    @Schema(description="emojiU")
    private String emojiU;

    /**
     * createdAt
     */
    @Schema(description="createdAt")
    private LocalDateTime createdAt;

    /**
     * updatedAt
     */
    @Schema(description="updatedAt")
    private LocalDateTime updatedAt;

    /**
     * flag
     */
    @Schema(description="flag")
    private Integer flag;

    /**
     * Rapid API GeoDB Cities
     */
    @Schema(description="Rapid API GeoDB Cities")
    private String wikiDataId;
}