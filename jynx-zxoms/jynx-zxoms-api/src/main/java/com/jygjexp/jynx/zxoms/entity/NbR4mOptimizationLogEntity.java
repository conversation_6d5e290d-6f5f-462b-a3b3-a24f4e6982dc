package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * Route4Me优化记录
 *
 * <AUTHOR>
 * @date 2024-10-15 23:48:00
 */
@Data
@TableName("nb_r4m_optimization_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Route4Me优化记录")
public class NbR4mOptimizationLogEntity extends Model<NbR4mOptimizationLogEntity> {


    /**
     * 优化日志主键ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="优化日志主键ID")
    private Integer logId;

    /**
     * 订单批次
     */
    @Schema(description="订单批次")
    private Integer orderBatchId;

    /**
     * 转运中心
     */
    @Schema(description="转运中心")
    private Integer tcId;

    /**
     * 订单
     */
    @Schema(description="订单")
    private String orderIds;

    /**
     * 司机ID合集
     */
    @Schema(description="司机ID合集")
    private String driverIds;

    /**
     * 算法类型
     */
    @Schema(description="算法类型")
    private String algorithmType;

    /**
     * 优化
     */
    @TableField("`optimize`")
    @Schema(description="优化")
    private String optimize;

    /**
     * minTourSize
     */
    @Schema(description="minTourSize")
    private String minTourSize;

    /**
     * isRt
     */
    @Schema(description="isRt")
    private String isRt;

    /**
     * balance
     */
    @Schema(description="balance")
    private String balance;

    /**
     * 路由_最大持续时间
     */
    @Schema(description="路由_最大持续时间")
    private String routeMaxDuration;

    /**
     * 优化时间
     */
    @Schema(description="优化时间")
    private Date optimizationTime;

    /**
     * 自动预设参数
     */
    @Schema(description="自动预设参数")
    private Boolean isAuto;

    /**
     * 操作的管理员
     */
    @Schema(description="操作的管理员")
    private Integer adminId;

    /**
     * 路由错误信息
     */
    @Schema(description="路由错误信息")
    private String routedErrmsg;

    /**
     * 优化问题ID
     */
    @Schema(description="优化问题ID")
    private String optimiationProblemId;

    /**
     * 查看url
     */
    @Schema(description="查看url")
    private String viewUrl;

    /**
     * New = 0, Initial = 1, MatrixProcessing = 2, Optimizing = 3, Optimized = 4, Error = 5, ComputingDirections = 6, InQueue = 7
     */
    @Schema(description="New = 0, Initial = 1, MatrixProcessing = 2, Optimizing = 3, Optimized = 4, Error = 5, ComputingDirections = 6, InQueue = 7")
    private Integer r4mState;

    /**
     * 分拣中心ID
     */
    @Schema(description="分拣中心ID")
    private Integer scId;

    /**
     * Maximum Tour Size
     */
    @Schema(description="Maximum Tour Size")
    private Integer maxTourSize;
}