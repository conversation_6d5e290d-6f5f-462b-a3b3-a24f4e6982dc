package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 价格区域
 *
 * <AUTHOR>
 * @date 2024-11-08 09:56:07
 */
@Data
@TableName("nb_price_district")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "价格区域")
public class NbPriceDistrictEntity extends Model<NbPriceDistrictEntity> {


    /**
     * 主键ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Integer pdId;

    /**
     * 区域编码
     */
    @Schema(description="区域编码")
    private String districtCode;

    /**
     * 三字邮编范围
     */
    @Schema(description="三字邮编范围")
    private String coverPostalCode;

    /**
     * 收件地址/发件地址
     */
    @Schema(description="收件地址/发件地址")
    private Boolean isDestAddr;

    /**
     * 创建人ID
     */
    @Schema(description="创建人ID")
    private Long createUserId;

    /**
     * 使用范围：1=国际，2=国内
     */
    @Schema(description="使用范围：1=国际，2=国内")
    private Integer applyArea;

    /**
     * 分拣中心ID
     */
    @Schema(description="分拣中心ID")
    private Integer scId;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Long revision;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记,1:已删除,0:正常")
    private String delFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;
}