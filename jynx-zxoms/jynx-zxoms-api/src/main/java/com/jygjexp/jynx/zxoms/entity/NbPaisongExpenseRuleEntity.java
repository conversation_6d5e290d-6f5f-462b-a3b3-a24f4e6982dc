package com.jygjexp.jynx.zxoms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 派送费用规则
 *
 * <AUTHOR>
 * @date 2025-01-10 19:47:36
 */
@Data
@TenantTable
@TableName("nb_paisong_expense_rule")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "派送费用规则")
public class NbPaisongExpenseRuleEntity extends Model<NbPaisongExpenseRuleEntity> {


    /**
     * 派送费用规则主键
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="派送费用规则主键")
    private Long reId;

    /**
     * 计费仓库
     */
    @Schema(description="计费仓库")
    private String billingWarehouse;

    /**
     * 启用状态
     */
    @Schema(description="启用状态")
    private Integer status;

    /**
     * 邮编分区id
     */
    @Schema(description="邮编分区id")
    private Long postalId;

    /**
     * 重量段id
     */
    @Schema(description="重量段id")
    private Long weightId;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 有效开始时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @Schema(description="有效开始时间")
    private LocalDateTime startTime;

    /**
     * 失效结束时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @Schema(description="失效结束时间")
    private LocalDateTime endTime;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Long revision;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}