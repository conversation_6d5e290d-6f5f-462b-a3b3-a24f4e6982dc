package com.jygjexp.jynx.zxoms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 城市地区
 *
 * <AUTHOR>
 * @date 2024-09-30 18:58:30
 */
@Data
@TableName("cities")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "城市地区")
public class CitiesEntity extends Model<CitiesEntity> {


    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="id")
    private Integer id;

    /**
     * name
     */
    @Schema(description="name")
    private String name;

    /**
     * stateId
     */
    @Schema(description="stateId")
    private Integer stateId;

    /**
     * stateCode
     */
    @Schema(description="stateCode")
    private String stateCode;

    /**
     * countryId
     */
    @Schema(description="countryId")
    private Integer countryId;

    /**
     * countryCode
     */
    @Schema(description="countryCode")
    private String countryCode;

    /**
     * latitude
     */
    @Schema(description="latitude")
    private BigDecimal latitude;

    /**
     * longitude
     */
    @Schema(description="longitude")
    private BigDecimal longitude;

    /**
     * createdAt
     */
    @Schema(description="createdAt")
    private LocalDateTime createdAt;

    /**
     * updatedAt
     */
    @Schema(description="updatedAt")
    private LocalDateTime updatedAt;

    /**
     * flag
     */
    @Schema(description="flag")
    private Integer flag;

    /**
     * Rapid API GeoDB Cities
     */
    @Schema(description="Rapid API GeoDB Cities")
    private String wikiDataId;
}