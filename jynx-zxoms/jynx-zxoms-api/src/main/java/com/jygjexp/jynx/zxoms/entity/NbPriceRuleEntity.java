package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 报价表
 *
 * <AUTHOR>
 * @date 2024-11-08 10:35:26
 */
@Data
@TableName("nb_price_rule")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "报价表")
public class NbPriceRuleEntity extends Model<NbPriceRuleEntity> {


    /**
     * 主键ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Integer ruleId;

    /**
     * 商家ID
     */
    @Schema(description="商家ID")
    private Integer merchantId;

    /**
     * 国际件
     */
    @Schema(description="国际件")
    private Boolean isInternational;

    /**
     * KG
     */
    @Schema(description="KG")
    private BigDecimal weightKg;

    /**
     * LB
     */
    @Schema(description="LB")
    private BigDecimal weightLb;

    /**
     * 目标区域
     */
    @Schema(description="目标区域")
    private Integer targetPdId;

    /**
     * 普通价
     */
    @Schema(description="普通价")
    private BigDecimal priceNornal;

    /**
     * 加急价
     */
    @Schema(description="加急价")
    private BigDecimal priceExpress;

    /**
     * 多件价
     */
    @Schema(description="多件价")
    private BigDecimal priceMulti;

    /**
     * 添加者ID
     */
    @Schema(description="添加者ID")
    private Integer createUserId;

    /**
     * 发件区域
     */
    @Schema(description="发件区域")
    private Integer fromPdId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;
}