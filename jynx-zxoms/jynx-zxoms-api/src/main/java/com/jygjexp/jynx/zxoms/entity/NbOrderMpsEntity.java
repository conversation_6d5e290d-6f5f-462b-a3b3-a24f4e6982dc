package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 订单跟踪
 *
 * <AUTHOR>
 * @date 2024-10-17 21:38:26
 */
@Data
@TableName("nb_order_mps")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单跟踪")
public class NbOrderMpsEntity extends Model<NbOrderMpsEntity> {


    /**
     * 主键ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Integer mpsId;

    /**
     * 订单ID
     */
    @Schema(description="订单ID")
    private Integer orderId;

    /**
     * pOrderId
     */
    @Schema(description="pOrderId")
    private Integer pOrderId;

    /**
     * 添加时间
     */
    @Schema(description="添加时间")
    private Date addTime;

    /**
     * 1跟踪中，10跟踪结束
     */
    @Schema(description="1跟踪中，10跟踪结束")
    private Integer followStatus;

    /**
     * 跟踪结束时间
     */
    @Schema(description="跟踪结束时间")
    private Date followCompletedTime;
}