package com.jygjexp.jynx.zxoms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * APP用户-废弃
 *
 * <AUTHOR>
 * @date 2024-11-08 15:17:26
 */
@Data
@TableName("nb_user")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "APP用户-废弃")
public class NbUserEntity extends Model<NbUserEntity> {


    /**
     * 用户主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description="用户主键ID")
    private Integer userId;

    /**
     * 账号
     */
    @Schema(description="账号")
    private String account;

    /**
     * 密码
     */
    @Schema(description="密码")
    private String password;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    private Date addTime;

    /**
     * 上次登录时间
     */
    @Schema(description="上次登录时间")
    private LocalDateTime lastLoginTime;
}