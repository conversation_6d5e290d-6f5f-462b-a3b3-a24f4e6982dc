package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 订单路径删除记录
 *
 * <AUTHOR>
 * @date 2024-10-24 01:11:15
 */
@Data
@TableName("nb_order_path_bak")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单路径删除记录")
public class NbOrderPathBakEntity extends Model<NbOrderPathBakEntity> {


    /**
     * 主键ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Integer pathId;

    /**
     * 订单ID
     */
    @Schema(description="订单ID")
    private Integer orderId;

    /**
     * 状态
     */
    @Schema(description="状态")
    private Integer orderStatus;

    /**
     * 添加时间
     */
    @Schema(description="添加时间")
    private Date addTime;

    /**
     * 分拣中心ID
     */
    @Schema(description="分拣中心ID")
    private Integer scId;

    /**
     * 转运中心ID
     */
    @Schema(description="转运中心ID")
    private Integer tcId;

    /**
     * 司机ID
     */
    @Schema(description="司机ID")
    private Integer driverId;

    /**
     * 员工ID
     */
    @Schema(description="员工ID")
    private Long staffId;

    /**
     * 路径地址
     */
    @Schema(description="路径地址")
    private String pathAddr;

    /**
     * scanLat
     */
    @Schema(description="scanLat")
    private Double scanLat;

    /**
     * scanLng
     */
    @Schema(description="scanLng")
    private Double scanLng;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * city
     */
    @Schema(description="city")
    private String city;

    /**
     * 时间戳
     */
    @Schema(description="时间戳")
    private Long addTimestamp;

    /**
     * 轨迹时区
     */
    @Schema(description="轨迹时区")
    private String pathTimezone;

    /**
     * 0未同步，1已同步，2同步失败
     */
    @Schema(description="0未同步，1已同步，2同步失败")
    private Integer syncJyStatus;

    /**
     * 同步时间
     */
    @Schema(description="同步时间")
    private LocalDateTime syncTime;

    /**
     * 0=不发，10=待发，15=发成功，20=发失败
     */
    @Schema(description="0=不发，10=待发，15=发成功，20=发失败")
    private Integer smsStatus;

    /**
     * 发送时间
     */
    @Schema(description="发送时间")
    private Date smsDate;

    /**
     * 离收件距离
     */
    @Schema(description="离收件距离")
    private Double distance;

    /**
     * 删除时间
     */
    @Schema(description="删除时间")
    private Date deleteTime;

    /**
     * 删除者
     */
    @Schema(description="删除者")
    private Long deleteUid;
}