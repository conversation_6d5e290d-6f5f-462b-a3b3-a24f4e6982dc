package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 商家
 *
 * <AUTHOR>
 * @date 2024-09-30 22:34:37
 */
@Data
@TenantTable
@TableName("nb_merchant")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商家")
public class NbMerchantEntity extends Model<NbMerchantEntity> {


    /**
     * 商家ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="商家ID")
    private Integer merchantId;

    /**
     * 商家名称
     */
    @Schema(description="商家名称")
    private String name;

    /**
     * 密钥
     */
    @Schema(description="密钥")
    private String apiSecret;

    /**
     * 是否有效
     */
    @Schema(description="是否有效")
    private Boolean isValid;

    /**
     * 多次配送
     */
    @Schema(description="多次配送")
    private Integer isMultiDelivery;

    /**
     * 唯一编码
     */
    @Schema(description="唯一编码")
    private String merchantCode;

    /**
     * 默认订单状态
     */
    @Schema(description="默认订单状态")
    private Integer defaultOrderStatus;

    /**
     * 折扣比例
     */
    @Schema(description="折扣比例")
    private Double discount;

    /**
     * 计费方式：1=实际重，2=体积重，3=自动最大值
     */
    @Schema(description="计费方式：1=实际重，2=体积重，3=自动最大值")
    private Integer chargeModel;

    /**
     * 一票多件最低重量KG
     */
    @Schema(description="一票多件最低重量KG")
    private BigDecimal multiMinWeightKg;

    /**
     * 一票多件最低重量LB
     */
    @Schema(description="一票多件最低重量LB")
    private BigDecimal multiMinWeightLb;

    /**
     * 一票多件最低重量
     */
    @Schema(description = "一票多件最低重量")
    private String multiMinWeight;

    /**
     * 默认时区
     */
    @Schema(description="默认时区")
    private String defaultTimezone;

    /**
     * 1：无操作，2同步到佳邮
     */
    @Schema(description="1：无操作，2同步到佳邮")
    private Integer funType;

    /**
     * 默认省市
     */
    @Schema(description="默认省市")
    private String defaultAddress;

    /**
     * 上门取件
     */
    @Schema(description="上门取件")
    private Integer pickUpService;

    /**
     * 仓库国家
     */
    @Schema(description="仓库国家")
    private Integer whCountryId;

    /**
     * 仓库省
     */
    @Schema(description="仓库省")
    private Integer whProvinceId;

    /**
     * 仓库市
     */
    @Schema(description="仓库市")
    private Integer whCityId;

    /**
     * 仓库地址
     */
    @Schema(description="仓库地址")
    private String whAddress;

    /**
     * 仓库邮编
     */
    @Schema(description="仓库邮编")
    private String whPostalCode;

    /**
     * 仓库联系人
     */
    @Schema(description="仓库联系人")
    private String whContact;

    /**
     * 仓库联系电话
     */
    @Schema(description="仓库联系电话")
    private String whTel;

    /**
     * 仓库营业时间
     */
    @Schema(description="仓库营业时间")
    private String whBizHour;

    /**
     * 仓库纬度
     */
    @Schema(description = "仓库纬度")
    private Double whLat;

    /**
     * 仓库经度
     */
    @Schema(description = "仓库经度")
    private Double whLng;

    /**
     * createTime
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="createTime")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人名")
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private Date updateTime;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;

}