package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 需要修复轨迹的订单
 *
 * <AUTHOR>
 * @date 2024-10-18 00:33:11
 */
@Data
@TableName("nb_patch_path_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "需要修复轨迹的订单")
public class NbPatchPathOrderEntity extends Model<NbPatchPathOrderEntity> {


    /**
     * 订单ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="订单ID")
    private Integer orderId;

    /**
     * 添加时间
     */
    @Schema(description="添加时间")
    private Date addTime;

    /**
     * 1:uni
     */
    @Schema(description="1:uni")
    private Integer sourceType;

    /**
     * 是否完成
     */
    @Schema(description="是否完成")
    private Boolean finished;

    /**
     * 完成时间
     */
    @Schema(description="完成时间")
    private Date finishedTime;

    /**
     * 最后同步时间
     */
    @Schema(description="最后同步时间")
    private Date lastSyncTime;

    /**
     * 最后数据
     */
    @Schema(description="最后数据")
    private String lastData;
}