package com.jygjexp.jynx.zxoms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 分拣中心
 *
 * <AUTHOR>
 * @date 2024-09-30 22:48:18
 */
@Data
@TenantTable
@TableName("nb_sorting_center")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分拣中心")
public class NbSortingCenterEntity extends Model<NbSortingCenterEntity> {


    /**
     * ID
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="ID")
    private Integer scId;

    /**
     * 名称
     */
    @Schema(description="名称")
    private String centerName;

    /**
     * 国家
     */
    @Schema(description="国家")
    private Integer countryId;

    /**
     * 省
     */
    @Schema(description="省")
    private Integer provinceId;

    /**
     * 区
     */
    @Schema(description="区")
    private Integer cityId;

    /**
     * 是否有效
     */
    @Schema(description="是否有效")
    private Boolean isValid;

    /**
     * 地址
     */
    @Schema(description="地址")
    private String address;

    /**
     * 邮编
     */
    @Schema(description="邮编")
    private String postalCode;

    /**
     * 创建人
     */
    @Schema(description="创建人")
    private Long createUserId;

    /**
     * 创建人名
     */
    @Schema(description="创建人名")
    private String createUserName;

    /**
     * 分拣中心代码
     */
    @Schema(description="分拣中心代码")
    private String scCode;

    /**
     * 纬度
     */
    @Schema(description="纬度")
    private Double lat;

    /**
     * 经度
     */
    @Schema(description="经度")
    private Double lng;

    /**
     * Route4Me管理员ID
     */
    @Schema(description="Route4Me管理员ID")
    private Integer r4mMemberId;

    /**
     * 所在时区
     */
    @Schema(description="所在时区")
    private String scTimezone;

    /**
     * 自提最大存储数量
     */
    @Schema(description="自提最大存储数量")
    private Integer selfPickupMaxStorage;

    /**
     * 营业时间
     */
    @Schema(description="营业时间")
    private String businessHours;

    /**
     * 客服电话
     */
    @Schema(description="客服电话")
    private String serviceTel;

    /**
     * createTime
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="createTime")
    private LocalDateTime createTime;

    /**
     * updateTime
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="updateTime")
    private LocalDateTime updateTime;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}